{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport {\n  Container,\n  Paper,\n  TextInput,\n  PasswordInput,\n  Button,\n  Title,\n  Text,\n  Alert,\n  Stack,\n} from '@mantine/core';\nimport { useForm } from '@mantine/form';\nimport { notifications } from '@mantine/notifications';\nimport { IconAlertCircle } from '@tabler/icons-react';\n\nexport default function LoginPage() {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const redirectTo = searchParams.get('redirect') || '/admin';\n\n  const form = useForm({\n    initialValues: {\n      username: '',\n      password: '',\n    },\n    validate: {\n      username: (value) => (!value ? 'Username is required' : null),\n      password: (value) => (!value ? 'Password is required' : null),\n    },\n  });\n\n  const handleSubmit = async (values: typeof form.values) => {\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(values),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        setError(data.error || 'Login failed');\n        return;\n      }\n\n      notifications.show({\n        title: 'Success',\n        message: 'Logged in successfully',\n        color: 'green',\n      });\n\n      router.push(redirectTo);\n    } catch (error) {\n      console.error('Login error:', error);\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container size={420} my={40}>\n      <Title ta=\"center\" mb=\"md\">\n        Admin Login\n      </Title>\n      <Text c=\"dimmed\" size=\"sm\" ta=\"center\" mb=\"xl\">\n        Sign in to access the admin dashboard\n      </Text>\n\n      <Paper withBorder shadow=\"md\" p={30} mt={30} radius=\"md\">\n        <form onSubmit={form.onSubmit(handleSubmit)}>\n          <Stack>\n            {error && (\n              <Alert\n                icon={<IconAlertCircle size=\"1rem\" />}\n                title=\"Login Error\"\n                color=\"red\"\n              >\n                {error}\n              </Alert>\n            )}\n\n            <TextInput\n              label=\"Username\"\n              placeholder=\"Enter your username\"\n              required\n              {...form.getInputProps('username')}\n            />\n\n            <PasswordInput\n              label=\"Password\"\n              placeholder=\"Enter your password\"\n              required\n              {...form.getInputProps('password')}\n            />\n\n            <Button type=\"submit\" fullWidth loading={loading}>\n              Sign In\n            </Button>\n          </Stack>\n        </form>\n      </Paper>\n\n      <Text c=\"dimmed\" size=\"sm\" ta=\"center\" mt=\"md\">\n        Use your admin credentials to sign in\n      </Text>\n    </Container>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAjBA;;;;;;;;AAmBe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,aAAa,aAAa,GAAG,CAAC,eAAe;IAEnD,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE;QACnB,eAAe;YACb,UAAU;YACV,UAAU;QACZ;QACA,UAAU;YACR,UAAU,CAAC,QAAW,CAAC,QAAQ,yBAAyB;YACxD,UAAU,CAAC,QAAW,CAAC,QAAQ,yBAAyB;QAC1D;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,SAAS,KAAK,KAAK,IAAI;gBACvB;YACF;YAEA,4KAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;gBACjB,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YAEA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,iLAAA,CAAA,YAAS;QAAC,MAAM;QAAK,IAAI;;0BACxB,8OAAC,yKAAA,CAAA,QAAK;gBAAC,IAAG;gBAAS,IAAG;0BAAK;;;;;;0BAG3B,8OAAC,uKAAA,CAAA,OAAI;gBAAC,GAAE;gBAAS,MAAK;gBAAK,IAAG;gBAAS,IAAG;0BAAK;;;;;;0BAI/C,8OAAC,yKAAA,CAAA,QAAK;gBAAC,UAAU;gBAAC,QAAO;gBAAK,GAAG;gBAAI,IAAI;gBAAI,QAAO;0BAClD,cAAA,8OAAC;oBAAK,UAAU,KAAK,QAAQ,CAAC;8BAC5B,cAAA,8OAAC,yKAAA,CAAA,QAAK;;4BACH,uBACC,8OAAC,yKAAA,CAAA,QAAK;gCACJ,oBAAM,8OAAC,oOAAA,CAAA,kBAAe;oCAAC,MAAK;;;;;;gCAC5B,OAAM;gCACN,OAAM;0CAEL;;;;;;0CAIL,8OAAC,iLAAA,CAAA,YAAS;gCACR,OAAM;gCACN,aAAY;gCACZ,QAAQ;gCACP,GAAG,KAAK,aAAa,CAAC,WAAW;;;;;;0CAGpC,8OAAC,yLAAA,CAAA,gBAAa;gCACZ,OAAM;gCACN,aAAY;gCACZ,QAAQ;gCACP,GAAG,KAAK,aAAa,CAAC,WAAW;;;;;;0CAGpC,8OAAC,2KAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAS;gCAAC,SAAS;0CAAS;;;;;;;;;;;;;;;;;;;;;;0BAOxD,8OAAC,uKAAA,CAAA,OAAI;gBAAC,GAAE;gBAAS,MAAK;gBAAK,IAAG;gBAAS,IAAG;0BAAK;;;;;;;;;;;;AAKrD", "debugId": null}}]}