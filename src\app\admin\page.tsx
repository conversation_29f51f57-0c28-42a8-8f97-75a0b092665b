'use client';

import { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  Text,
  Title,
  Group,
  Stack,
  Badge,
  Loader,
  Center,
} from '@mantine/core';
import {
  IconPackage,
  IconMessages,
  IconMessageCircle,
  IconTrendingUp,
} from '@tabler/icons-react';

interface DashboardStats {
  totalProducts: number;
  totalMessages: number;
  totalResponses: number;
  recentMessages: any[];
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      // Load products count
      const productsResponse = await fetch('/api/products?limit=1');
      const productsData = await productsResponse.json();
      
      // Load messages count
      const messagesResponse = await fetch('/api/messages?limit=10');
      const messagesData = await messagesResponse.json();

      setStats({
        totalProducts: productsData.products?.length || 0,
        totalMessages: messagesData.messages?.length || 0,
        totalResponses: messagesData.messages?.reduce((acc: number, msg: any) => 
          acc + (msg.responses?.length || 0), 0) || 0,
        recentMessages: messagesData.messages?.slice(0, 5) || [],
      });
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Center h={400}>
        <Loader size="lg" />
      </Center>
    );
  }

  return (
    <Stack>
      <Title order={1}>Dashboard Overview</Title>

      <Grid>
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder>
            <Group justify="space-between">
              <div>
                <Text c="dimmed" size="sm" tt="uppercase" fw={700}>
                  Total Products
                </Text>
                <Text fw={700} size="xl">
                  {stats?.totalProducts || 0}
                </Text>
              </div>
              <IconPackage size="2rem" color="blue" />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder>
            <Group justify="space-between">
              <div>
                <Text c="dimmed" size="sm" tt="uppercase" fw={700}>
                  Total Messages
                </Text>
                <Text fw={700} size="xl">
                  {stats?.totalMessages || 0}
                </Text>
              </div>
              <IconMessages size="2rem" color="green" />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder>
            <Group justify="space-between">
              <div>
                <Text c="dimmed" size="sm" tt="uppercase" fw={700}>
                  Total Responses
                </Text>
                <Text fw={700} size="xl">
                  {stats?.totalResponses || 0}
                </Text>
              </div>
              <IconMessageCircle size="2rem" color="orange" />
            </Group>
          </Card>
        </Grid.Col>
      </Grid>

      <Card withBorder>
        <Title order={3} mb="md">Recent Messages</Title>
        {stats?.recentMessages.length === 0 ? (
          <Text c="dimmed">No messages yet</Text>
        ) : (
          <Stack>
            {stats?.recentMessages.map((message: any) => (
              <Group key={message.id} justify="space-between">
                <div>
                  <Text fw={500}>{message.sender_name || message.sender_id}</Text>
                  <Text size="sm" c="dimmed" truncate>
                    {message.message_text}
                  </Text>
                </div>
                <div>
                  <Badge
                    color={message.responses?.length > 0 ? 'green' : 'yellow'}
                    variant="light"
                  >
                    {message.responses?.length > 0 ? 'Responded' : 'Pending'}
                  </Badge>
                  <Text size="xs" c="dimmed">
                    {new Date(message.created_at).toLocaleDateString()}
                  </Text>
                </div>
              </Group>
            ))}
          </Stack>
        )}
      </Card>
    </Stack>
  );
}
