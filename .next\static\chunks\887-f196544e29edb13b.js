(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[887],{180:(e,t,r)=>{"use strict";function o(e,t){if("rtl"===e&&(t.includes("right")||t.includes("left"))){let[e,r]=t.split("-"),o="right"===e?"left":"right";return void 0===r?o:"".concat(o,"-").concat(r)}return t}r.d(t,{getFloatingPosition:()=>o})},572:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={tooltip:"m_1b3c8819",arrow:"m_f898399f"}},840:(e,t,r)=>{"use strict";r.d(t,{TagsInput:()=>k});var o=r(95155),n=r(12115),a=r(64173),l=r(88551),i=r(57613),s=r(86028),c=r(53791),u=r(43664),d=r(99537),p=r(36960),m=r(79483),v=r(48509),f=r(14449),h=r(75843),g=r(87160),b=r(24225),y=r(68001),x=r(28843),w=r(83731);function C(e){let{splitChars:t,allowDuplicates:r,maxTags:o,value:n,currentTags:a}=e,l=t?n.split(new RegExp("[".concat(t.join(""),"]"))).map(e=>e.trim()).filter(e=>""!==e):[n],i=r?[...a,...l]:[...new Set([...a,...l])];return o?i.slice(0,o):i}let S={maxTags:1/0,acceptValueOnBlur:!0,splitChars:[","],hiddenInputValuesDivider:","},k=(0,p.factory)((e,t)=>{let r=(0,u.useProps)("TagsInput",S,e),{classNames:p,className:b,style:k,styles:E,unstyled:P,vars:j,size:R,value:D,defaultValue:I,onChange:A,onKeyDown:T,maxTags:_,allowDuplicates:M,onDuplicate:N,variant:z,data:O,dropdownOpened:L,defaultDropdownOpened:B,onDropdownOpen:F,onDropdownClose:V,selectFirstOptionOnChange:H,onOptionSubmit:U,comboboxProps:G,filter:W,limit:q,withScrollArea:K,maxDropdownHeight:X,searchValue:Z,defaultSearchValue:Y,onSearchChange:Q,readOnly:$,disabled:J,splitChars:ee,onFocus:et,onBlur:er,onPaste:eo,radius:en,rightSection:ea,rightSectionWidth:el,rightSectionPointerEvents:ei,rightSectionProps:es,leftSection:ec,leftSectionWidth:eu,leftSectionPointerEvents:ed,leftSectionProps:ep,inputContainer:em,inputWrapperOrder:ev,withAsterisk:ef,required:eh,labelProps:eg,descriptionProps:eb,errorProps:ey,wrapperProps:ex,description:ew,label:eC,error:eS,withErrorStyles:ek,name:eE,form:eP,id:ej,clearable:eR,clearButtonProps:eD,hiddenInputProps:eI,hiddenInputValuesDivider:eA,mod:eT,renderOption:e_,onRemove:eM,onClear:eN,scrollAreaProps:ez,acceptValueOnBlur:eO,isDuplicate:eL,...eB}=r,eF=(0,a.useId)(ej),eV=(0,m.getParsedComboboxData)(O),eH=(0,v.getOptionsLockup)(eV),eU=(0,n.useRef)(null),eG=(0,l.useMergedRef)(eU,t),eW=(0,g.useCombobox)({opened:L,defaultOpened:B,onDropdownOpen:F,onDropdownClose:()=>{null==V||V(),eW.resetSelectedOption()}}),{styleProps:eq,rest:{type:eK,autoComplete:eX,...eZ}}=(0,d.extractStyleProps)(eB),[eY,eQ]=(0,i.useUncontrolled)({value:D,defaultValue:I,finalValue:[],onChange:A}),[e$,eJ]=(0,i.useUncontrolled)({value:Z,defaultValue:Y,finalValue:"",onChange:Q}),e0=e=>{eJ(e),eW.resetSelectedOption()},e1=(0,c.useStyles)({name:"TagsInput",classes:{},props:r,classNames:p,styles:E,unstyled:P}),{resolvedClassNames:e5,resolvedStyles:e6}=(0,s.useResolvedStylesApi)({props:r,styles:E,classNames:p}),e9=e=>{let t=eL?eL(e,eY):eY.some(t=>t.toLowerCase()===e.toLowerCase());t&&(null==N||N(e)),(!t||t&&M)&&eY.length<_&&(null==U||U(e),e0(""),e.length>0&&eQ([...eY,e]))},e3=eY.map((e,t)=>(0,o.jsx)(y.Pill,{withRemoveButton:!$,onRemove:()=>{let r=eY.slice();r.splice(t,1),eQ(r),null==eM||eM(e)},unstyled:P,disabled:J,...e1("pill"),children:e},"".concat(e,"-").concat(t)));(0,n.useEffect)(()=>{H&&eW.selectFirstOption()},[H,eY,e$]);let e4=(0,o.jsx)(f.Combobox.ClearButton,{...eD,onClear:()=>{var e;eQ([]),e0(""),null==(e=eU.current)||e.focus(),eW.openDropdown(),null==eN||eN()}});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(f.Combobox,{store:eW,classNames:e5,styles:e6,unstyled:P,size:R,readOnly:$,__staticSelector:"TagsInput",onOptionSubmit:e=>{null==U||U(e),e0(""),eY.length<_&&eQ([...eY,eH[e].label]),eW.resetSelectedOption()},...G,children:[(0,o.jsx)(f.Combobox.DropdownTarget,{children:(0,o.jsx)(x.PillsInput,{...eq,__staticSelector:"TagsInput",classNames:e5,styles:e6,unstyled:P,size:R,className:b,style:k,variant:z,disabled:J,radius:en,rightSection:ea,__clearSection:e4,__clearable:eR&&eY.length>0&&!J&&!$,rightSectionWidth:el,rightSectionPointerEvents:ei,rightSectionProps:es,leftSection:ec,leftSectionWidth:eu,leftSectionPointerEvents:ed,leftSectionProps:ep,inputContainer:em,inputWrapperOrder:ev,withAsterisk:ef,required:eh,labelProps:eg,descriptionProps:eb,errorProps:ey,wrapperProps:ex,description:ew,label:eC,error:eS,withErrorStyles:ek,__stylesApiProps:{...r,multiline:!0},id:eF,mod:eT,children:(0,o.jsxs)(y.Pill.Group,{disabled:J,unstyled:P,...e1("pillsList"),children:[e3,(0,o.jsx)(f.Combobox.EventsTarget,{autoComplete:eX,children:(0,o.jsx)(x.PillsInput.Field,{...eZ,ref:eG,...e1("inputField"),unstyled:P,onKeyDown:e=>{if(null==T||T(e),e.isPropagationStopped())return;let t=e$.trim(),{length:r}=t;if(ee.includes(e.key)&&r>0&&(eQ(C({splitChars:ee,allowDuplicates:M,maxTags:_,value:e$,currentTags:eY})),e0(""),e.preventDefault()),"Enter"===e.key&&r>0&&!e.nativeEvent.isComposing){if(e.preventDefault(),document.querySelector("#".concat(eW.listId," [data-combobox-option][data-combobox-selected]")))return;e9(t)}"Backspace"===e.key&&0===r&&eY.length>0&&!e.nativeEvent.isComposing&&(null==eM||eM(eY[eY.length-1]),eQ(eY.slice(0,eY.length-1)))},onFocus:e=>{null==et||et(e),eW.openDropdown()},onBlur:e=>{null==er||er(e),eO&&e9(e$),eW.closeDropdown()},onPaste:e=>{if(null==eo||eo(e),e.preventDefault(),e.clipboardData){let t=e.clipboardData.getData("text/plain");eQ(C({splitChars:ee,allowDuplicates:M,maxTags:_,value:"".concat(e$).concat(t),currentTags:eY})),e0("")}},value:e$,onChange:e=>e0(e.currentTarget.value),required:eh&&0===eY.length,disabled:J,readOnly:$,id:eF})})]})})}),(0,o.jsx)(h.OptionsDropdown,{data:function(e){let{data:t,value:r}=e,o=r.map(e=>e.trim().toLowerCase());return t.reduce((e,t)=>((0,w.isOptionsGroup)(t)?e.push({group:t.group,items:t.items.filter(e=>-1===o.indexOf(e.label.toLowerCase().trim()))}):-1===o.indexOf(t.label.toLowerCase().trim())&&e.push(t),e),[])}({data:eV,value:eY}),hidden:$||J,filter:W,search:e$,limit:q,hiddenWhenEmpty:!0,withScrollArea:K,maxDropdownHeight:X,unstyled:P,labelId:eC?"".concat(eF,"-label"):void 0,"aria-label":eC?void 0:eB["aria-label"],renderOption:e_,scrollAreaProps:ez})]}),(0,o.jsx)(f.Combobox.HiddenInput,{name:eE,form:eP,value:eY,valuesDivider:eA,disabled:J,...eI})]})});k.classes={...b.InputBase.classes,...f.Combobox.classes},k.displayName="@mantine/core/TagsInput"},1259:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a,useThrottledCallback:()=>l});var o=r(12115),n=r(25067);function a(e,t){let r=(0,n.useCallbackRef)(e),a=(0,o.useRef)(null),l=(0,o.useRef)(null),i=(0,o.useRef)(!0),s=(0,o.useRef)(t),c=(0,o.useRef)(-1),u=(0,o.useCallback)(function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];r(...t),a.current=t,l.current=t,i.current=!1},[r]),d=(0,o.useCallback)(()=>{a.current&&a.current!==l.current?(u(...a.current),c.current=window.setTimeout(d,s.current)):i.current=!0},[u]),p=(0,o.useCallback)(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];i.current?(u(...t),c.current=window.setTimeout(d,s.current)):a.current=t},[u,d]);return(0,o.useEffect)(()=>{s.current=t},[t]),[p,()=>window.clearTimeout(c.current)]}function l(e,t){return a(e,t)[0]}},1563:(e,t,r)=>{"use strict";r.d(t,{ProgressLabel:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(85794),s=r(75752);let c={},u=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:s,styles:u,vars:d,...p}=(0,n.useProps)("ProgressLabel",c,e),m=(0,i.Q)();return(0,o.jsx)(a.Box,{ref:t,...m.getStyles("label",{className:l,style:s,classNames:r,styles:u}),...p})});u.classes=s.A,u.displayName="@mantine/core/ProgressLabel"},1633:(e,t,r)=>{"use strict";r.d(t,{Progress:()=>p});var o=r(95155);r(12115);var n=r(86028),a=r(43664),l=r(36960),i=r(1563),s=r(76259),c=r(80415),u=r(75752);let d={},p=(0,l.factory)((e,t)=>{let r=(0,a.useProps)("Progress",d,e),{value:l,classNames:i,styles:u,vars:p,color:m,striped:v,animated:f,"aria-label":h,...g}=r,{resolvedClassNames:b,resolvedStyles:y}=(0,n.useResolvedStylesApi)({classNames:i,styles:u,props:r});return(0,o.jsx)(s.ProgressRoot,{ref:t,classNames:b,styles:y,vars:p,...g,children:(0,o.jsx)(c.ProgressSection,{value:l,color:m,striped:v,animated:f,"aria-label":h})})});p.classes=u.A,p.displayName="@mantine/core/Progress",p.Section=c.ProgressSection,p.Root=s.ProgressRoot,p.Label=i.ProgressLabel},2198:(e,t,r)=>{"use strict";r.d(t,{getCSSColorVariables:()=>a});var o=r(30128);r(12115),r(95155);var n=r(70714);function a(e){let{theme:t,color:r,colorScheme:a,name:l=r,withColorValues:i=!0}=e;if(!t.colors[r])return{};if("light"===a){let e=(0,o.getPrimaryShade)(t,"light"),a={["--mantine-color-".concat(l,"-text")]:"var(--mantine-color-".concat(l,"-filled)"),["--mantine-color-".concat(l,"-filled")]:"var(--mantine-color-".concat(l,"-").concat(e,")"),["--mantine-color-".concat(l,"-filled-hover")]:"var(--mantine-color-".concat(l,"-").concat(9===e?8:e+1,")"),["--mantine-color-".concat(l,"-light")]:(0,n.X)(t.colors[r][e],.1),["--mantine-color-".concat(l,"-light-hover")]:(0,n.X)(t.colors[r][e],.12),["--mantine-color-".concat(l,"-light-color")]:"var(--mantine-color-".concat(l,"-").concat(e,")"),["--mantine-color-".concat(l,"-outline")]:"var(--mantine-color-".concat(l,"-").concat(e,")"),["--mantine-color-".concat(l,"-outline-hover")]:(0,n.X)(t.colors[r][e],.05)};return i?{["--mantine-color-".concat(l,"-0")]:t.colors[r][0],["--mantine-color-".concat(l,"-1")]:t.colors[r][1],["--mantine-color-".concat(l,"-2")]:t.colors[r][2],["--mantine-color-".concat(l,"-3")]:t.colors[r][3],["--mantine-color-".concat(l,"-4")]:t.colors[r][4],["--mantine-color-".concat(l,"-5")]:t.colors[r][5],["--mantine-color-".concat(l,"-6")]:t.colors[r][6],["--mantine-color-".concat(l,"-7")]:t.colors[r][7],["--mantine-color-".concat(l,"-8")]:t.colors[r][8],["--mantine-color-".concat(l,"-9")]:t.colors[r][9],...a}:a}let s=(0,o.getPrimaryShade)(t,"dark"),c={["--mantine-color-".concat(l,"-text")]:"var(--mantine-color-".concat(l,"-4)"),["--mantine-color-".concat(l,"-filled")]:"var(--mantine-color-".concat(l,"-").concat(s,")"),["--mantine-color-".concat(l,"-filled-hover")]:"var(--mantine-color-".concat(l,"-").concat(9===s?8:s+1,")"),["--mantine-color-".concat(l,"-light")]:(0,n.X)(t.colors[r][Math.max(0,s-2)],.15),["--mantine-color-".concat(l,"-light-hover")]:(0,n.X)(t.colors[r][Math.max(0,s-2)],.2),["--mantine-color-".concat(l,"-light-color")]:"var(--mantine-color-".concat(l,"-").concat(Math.max(s-5,0),")"),["--mantine-color-".concat(l,"-outline")]:"var(--mantine-color-".concat(l,"-").concat(Math.max(s-4,0),")"),["--mantine-color-".concat(l,"-outline-hover")]:(0,n.X)(t.colors[r][Math.max(s-4,0)],.05)};return i?{["--mantine-color-".concat(l,"-0")]:t.colors[r][0],["--mantine-color-".concat(l,"-1")]:t.colors[r][1],["--mantine-color-".concat(l,"-2")]:t.colors[r][2],["--mantine-color-".concat(l,"-3")]:t.colors[r][3],["--mantine-color-".concat(l,"-4")]:t.colors[r][4],["--mantine-color-".concat(l,"-5")]:t.colors[r][5],["--mantine-color-".concat(l,"-6")]:t.colors[r][6],["--mantine-color-".concat(l,"-7")]:t.colors[r][7],["--mantine-color-".concat(l,"-8")]:t.colors[r][8],["--mantine-color-".concat(l,"-9")]:t.colors[r][9],...c}:c}},2585:(e,t,r)=>{"use strict";r.d(t,{useInterval:()=>n});var o=r(12115);function n(e,t){let{autoInvoke:r=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[n,a]=(0,o.useState)(!1),l=(0,o.useRef)(null),i=(0,o.useRef)(null),s=(0,o.useCallback)(()=>{a(e=>(e||l.current&&-1!==l.current||(l.current=window.setInterval(i.current,t)),!0))},[]),c=(0,o.useCallback)(()=>{a(!1),window.clearInterval(l.current||-1),l.current=-1},[]),u=(0,o.useCallback)(()=>{n?c():s()},[n]);return(0,o.useEffect)(()=>(i.current=e,n&&s(),c),[e,n,t]),(0,o.useEffect)(()=>{r&&s()},[]),{start:s,stop:c,toggle:u,active:n}}},2639:(e,t,r)=>{"use strict";r.d(t,{FileButton:()=>s});var o=r(95155),n=r(12115),a=r(88551),l=r(43664);let i={multiple:!1},s=(0,n.forwardRef)((e,t)=>{let{onChange:r,children:s,multiple:c,accept:u,name:d,form:p,resetRef:m,disabled:v,capture:f,inputProps:h,...g}=(0,l.useProps)("FileButton",i,e),b=(0,n.useRef)(null);return(0,a.assignRef)(m,()=>{b.current&&(b.current.value="")}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("input",{style:{display:"none"},type:"file",accept:u,multiple:c,onChange:e=>{if(null===e.currentTarget.files)return r(c?[]:null);c?r(Array.from(e.currentTarget.files)):r(e.currentTarget.files[0]||null)},ref:(0,a.useMergedRef)(t,b),name:d,form:p,capture:f,...h}),s({onClick:()=>{var e;v||null==(e=b.current)||e.click()},...g})]})});s.displayName="@mantine/core/FileButton"},2872:(e,t,r)=>{"use strict";r.d(t,{FLEX_STYLE_PROPS_DATA:()=>o});let o={gap:{type:"spacing",property:"gap"},rowGap:{type:"spacing",property:"rowGap"},columnGap:{type:"spacing",property:"columnGap"},align:{type:"identity",property:"alignItems"},justify:{type:"identity",property:"justifyContent"},wrap:{type:"identity",property:"flexWrap"},direction:{type:"identity",property:"flexDirection"}}},3115:(e,t,r)=>{"use strict";function o(e){return"string"!=typeof e?"":e.charAt(0).toUpperCase()+e.slice(1)}r.d(t,{upperFirst:()=>o})},3500:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={dropdown:"m_38a85659",arrow:"m_a31dc6c1",overlay:"m_3d7bc908"}},3826:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,v:()=>n}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Combobox component was not found in tree")},4463:(e,t,r)=>{"use strict";r.d(t,{convertHsvaTo:()=>u});var o=r(39186);function n(e){let{h:t,s:r,v:n,a}=e,l=t/360*6,i=r/100,s=n/100,c=Math.floor(l),u=s*(1-i),d=s*(1-(l-c)*i),p=s*(1-(1-l+c)*i),m=c%6;return{r:(0,o.LI)(255*[s,d,u,u,p,s][m]),g:(0,o.LI)(255*[p,s,s,d,u,u][m]),b:(0,o.LI)(255*[u,u,p,s,s,d][m]),a:(0,o.LI)(a,2)}}function a(e,t){let{r,g:a,b:l,a:i}=n(e);return t?"rgba(".concat(r,", ").concat(a,", ").concat(l,", ").concat((0,o.LI)(i,2),")"):"rgb(".concat(r,", ").concat(a,", ").concat(l,")")}function l(e,t){let{h:r,s:n,v:a,a:l}=e,i=(200-n)*a/100,s={h:Math.round(r),s:Math.round(i>0&&i<200?n*a/100/(i<=100?i:200-i)*100:0),l:Math.round(i/2)};return t?"hsla(".concat(s.h,", ").concat(s.s,"%, ").concat(s.l,"%, ").concat((0,o.LI)(l,2),")"):"hsl(".concat(s.h,", ").concat(s.s,"%, ").concat(s.l,"%)")}function i(e){let t=e.toString(16);return t.length<2?"0".concat(t):t}function s(e){let{r:t,g:r,b:o}=n(e);return"#".concat(i(t)).concat(i(r)).concat(i(o))}let c={hex:s,hexa:e=>(function(e){let t=Math.round(255*e.a);return"".concat(s(e)).concat(i(t))})(e),rgb:e=>a(e,!1),rgba:e=>a(e,!0),hsl:e=>l(e,!1),hsla:e=>l(e,!0)};function u(e,t){return t?e in c?c[e](t):c.hex(t):"#000000"}},4626:(e,t,r)=>{"use strict";function o(e){return Object.entries(e).map(e=>{let[t,r]=e;return"".concat(t,": ").concat(r,";")}).join("")}function n(e,t){return(Array.isArray(e)?e:[e]).reduce((e,t)=>"".concat(t,"{").concat(e,"}"),t)}function a(e,t){let r=o(e.variables),a=r?n(t,r):"",l=o(e.dark),i=o(e.light),s=l?":host"===t?n("".concat(t,'([data-mantine-color-scheme="dark"])'),l):n("".concat(t,'[data-mantine-color-scheme="dark"]'),l):"",c=i?":host"===t?n("".concat(t,'([data-mantine-color-scheme="light"])'),i):n("".concat(t,'[data-mantine-color-scheme="light"]'),i):"";return"".concat(a).concat(s).concat(c)}r.d(t,{convertCssVariables:()=>a})},5192:(e,t,r)=>{"use strict";r.d(t,{Mark:()=>v});var o=r(95155);r(12115);var n=r(68918),a=r(43664),l=r(53791),i=r(69604),s=r(36960),c=r(98271);function u(e){let{color:t,theme:r,defaultShade:o}=e,n=(0,c.parseThemeColor)({color:t,theme:r});return n.isThemeColor?void 0===n.shade?"var(--mantine-color-".concat(n.color,"-").concat(o,")"):"var(".concat(n.variable,")"):t}var d={root:"m_bcb3f3c2"};let p={color:"yellow"},m=(0,n.createVarsResolver)((e,t)=>{let{color:r}=t;return{root:{"--mark-bg-dark":u({color:r,theme:e,defaultShade:5}),"--mark-bg-light":u({color:r,theme:e,defaultShade:2})}}}),v=(0,s.factory)((e,t)=>{let r=(0,a.useProps)("Mark",p,e),{classNames:n,className:s,style:c,styles:u,unstyled:v,vars:f,color:h,variant:g,...b}=r,y=(0,l.useStyles)({name:"Mark",props:r,className:s,style:c,classes:d,classNames:n,styles:u,unstyled:v,vars:f,varsResolver:m});return(0,o.jsx)(i.Box,{component:"mark",ref:t,variant:g,...y("root"),...b})});v.classes=d,v.displayName="@mantine/core/Mark"},5194:(e,t,r)=>{"use strict";r.d(t,{PaginationItems:()=>i});var o=r(95155),n=r(80864),a=r(43702),l=r(13060);function i(e){let{dotsIcon:t}=e,r=(0,n.b)(),i=r.range.map((e,n)=>{var i,s,c,u;return"dots"===e?(0,o.jsx)(l.PaginationDots,{icon:t},n):(0,o.jsx)(a.PaginationControl,{active:e===r.active,"aria-current":e===r.active?"page":void 0,onClick:()=>r.onChange(e),disabled:r.disabled,...null==(i=r.getItemProps)?void 0:i.call(r,e),children:null!=(u=null==(c=r.getItemProps)||null==(s=c.call(r,e))?void 0:s.children)?u:e},n)});return(0,o.jsx)(o.Fragment,{children:i})}i.displayName="@mantine/core/PaginationItems"},5802:(e,t,r)=>{"use strict";r.d(t,{RangeSlider:()=>I});var o=r(95155),n=r(12115),a=r(57613),l=r(96963),i=r(43461),s=r(88551),c=r(5903),u=r(56204),d=r(57130),p=r(68918),m=r(71180),v=r(43664),f=r(53791),h=r(36960),g=r(53304),b=r(97446),y=r(21369),x=r(44229),w=r(9822),C=r(66143),S=r(30404),k=r(17077),E=r(80159),P=r(41707),j=r(55508);let R=(0,p.createVarsResolver)((e,t)=>{let{size:r,color:o,thumbSize:n,radius:a}=t;return{root:{"--slider-size":(0,u.getSize)(r,"slider-size"),"--slider-color":o?(0,m.getThemeColor)(o,e):void 0,"--slider-radius":void 0===a?void 0:(0,u.getRadius)(a),"--slider-thumb-size":void 0!==n?(0,c.D)(n):"calc(var(--slider-size) * 2)"}}}),D={min:0,max:100,minRange:10,step:1,marks:[],label:e=>e,labelTransitionProps:{transition:"fade",duration:0},labelAlwaysOn:!1,showLabelOnHover:!0,disabled:!1,pushOnOverlap:!0,scale:e=>e,size:"md",maxRange:1/0},I=(0,h.factory)((e,t)=>{let r=(0,v.useProps)("RangeSlider",D,e),{classNames:c,styles:u,value:p,onChange:m,onChangeEnd:h,size:I,min:A,max:T,domain:_,minRange:M,maxRange:N,step:z,precision:O,defaultValue:L,name:B,marks:F,label:V,labelTransitionProps:H,labelAlwaysOn:U,thumbFromLabel:G,thumbToLabel:W,showLabelOnHover:q,thumbChildren:K,disabled:X,unstyled:Z,scale:Y,inverted:Q,className:$,style:J,vars:ee,hiddenInputProps:et,restrictToMarks:er,thumbProps:eo,pushOnOverlap:en,...ea}=r,el=(0,f.useStyles)({name:"Slider",props:r,classes:j.A,classNames:c,className:$,styles:u,style:J,vars:ee,varsResolver:R,unstyled:Z}),ei=(0,n.useRef)(null),{dir:es}=(0,g.useDirection)(),[ec,eu]=(0,n.useState)(-1),[ed,ep]=(0,n.useState)(!1),[em,ev]=(0,a.useUncontrolled)({value:p,defaultValue:L,finalValue:[A,T],onChange:m}),ef=(0,n.useRef)(em),eh=(0,n.useRef)([]),eg=(0,n.useRef)(null),eb=(0,n.useRef)(void 0),[ey,ex]=_||[A,T],ew=[(0,k.E)({value:em[0],min:ey,max:ex}),(0,k.E)({value:em[1],min:ey,max:ex})],eC=null!=O?O:(0,E.X)(z),eS=e=>{ev(e),ef.current=e};(0,n.useEffect)(()=>{Array.isArray(p)&&(ef.current=p)},Array.isArray(p)?[p[0],p[1]]:[null,null]);let ek=(e,t,r)=>{if(-1===t)return;let o=[...ef.current];if(er&&F){let r=(0,d.findClosestNumber)(e,F.map(e=>e.value)),n=o[t];o[t]=r;let a=+(0===t),l=(0,P.rq)(F),i=(0,P.HE)(F);r===l&&o[a]===l||r===i&&o[a]===i?o[t]=n:r===o[a]&&(n>o[a]?o[a]=(0,P.Mh)(r,F):o[a]=(0,P.C8)(r,F))}else{let r=(0,l.clamp)(e,A,T);o[t]=r,0===t&&(r>o[1]-(M-1e-9)&&(en?o[1]=Math.min(e+M,T):o[t]=ef.current[t]),r>(T-(M-1e-9)||A)&&(o[t]=ef.current[t]),o[1]-e>N&&(en?o[1]=e+N:o[t]=ef.current[t])),1===t&&(r<o[0]+M&&(en?o[0]=Math.max(e-M,A):o[t]=ef.current[t]),r<o[0]+M&&(o[t]=ef.current[t]),r-o[0]>N&&(en?o[0]=e-N:o[t]=ef.current[t]))}if(o[0]=(0,S.q)(o[0],eC),o[1]=(0,S.q)(o[1],eC),o[0]>o[1]){let e=o[0];o[0]=o[1],o[1]=e}eS(o),r&&(null==h||h(ef.current))},eE=e=>{X||void 0===eb.current||ek((0,C.c)({value:e,min:ey,max:ex,step:z,precision:eC}),eb.current,!1)},{ref:eP,active:ej}=(0,i.useMove)(e=>{let{x:t}=e;return eE(t)},{onScrubEnd:()=>!X&&(null==h?void 0:h(ef.current))},es),eR=e=>{if(ei.current){ei.current.focus();let t=ei.current.getBoundingClientRect(),r=function(e){return"TouchEvent"in window&&e instanceof window.TouchEvent?e.touches[0].clientX:e.clientX}(e.nativeEvent),o=(0,C.c)({value:r-t.left,max:T,min:A,step:z,containerWidth:t.width}),n=+(Math.abs(em[0]-o)>Math.abs(em[1]-o));eb.current="ltr"===es?n:+(1!==n)}},eD=()=>1!==ec&&0!==ec?(eu(0),0):ec,eI={max:T,min:A,size:I,labelTransitionProps:H,labelAlwaysOn:U,onBlur:()=>eu(-1)},eA=Array.isArray(K);return(0,o.jsx)(b.h,{value:{getStyles:el},children:(0,o.jsxs)(y.g,{...ea,size:I,ref:(0,s.useMergedRef)(t,eg),disabled:X,onMouseDownCapture:()=>{var e;return null==(e=eg.current)?void 0:e.focus()},onKeyDownCapture:()=>{var e,t,r;null!=(t=eh.current[0])&&null!=(e=t.parentElement)&&e.contains(document.activeElement)||null==(r=eh.current[0])||r.focus()},children:[(0,o.jsxs)(w.C,{offset:ew[0],marksOffset:em[0],filled:ew[1]-ew[0],marks:F,inverted:Q,min:ey,max:ex,value:em[1],disabled:X,containerProps:{ref:(0,s.useMergedRef)(ei,eP),onMouseEnter:q?()=>ep(!0):void 0,onMouseLeave:q?()=>ep(!1):void 0,onTouchStartCapture:eR,onTouchEndCapture:()=>{eb.current=-1},onMouseDownCapture:eR,onMouseUpCapture:()=>{eb.current=-1},onKeyDownCapture:e=>{if(!X)switch(e.key){case"ArrowUp":{e.preventDefault();let t=eD();eh.current[t].focus();let r=er&&F?(0,P.C8)(ef.current[t],F):Math.min(Math.max(ef.current[t]+z,A),T);ek((0,S.q)(r,eC),t,!0);break}case"ArrowRight":{e.preventDefault();let t=eD();eh.current[t].focus();let r=er&&F?("rtl"===es?P.Mh:P.C8)(ef.current[t],F):Math.min(Math.max("rtl"===es?ef.current[t]-z:ef.current[t]+z,A),T);ek((0,S.q)(r,eC),t,!0);break}case"ArrowDown":{e.preventDefault();let t=eD();eh.current[t].focus();let r=er&&F?(0,P.Mh)(ef.current[t],F):Math.min(Math.max(ef.current[t]-z,A),T);ek((0,S.q)(r,eC),t,!0);break}case"ArrowLeft":{e.preventDefault();let t=eD();eh.current[t].focus();let r=er&&F?("rtl"===es?P.C8:P.Mh)(ef.current[t],F):Math.min(Math.max("rtl"===es?ef.current[t]+z:ef.current[t]-z,A),T);ek((0,S.q)(r,eC),t,!0)}}}},children:[(0,o.jsx)(x.z,{...eI,value:Y(em[0]),position:ew[0],dragging:ej,label:"function"==typeof V?V((0,S.q)(Y(em[0]),eC)):V,ref:e=>{e&&(eh.current[0]=e)},thumbLabel:G,onMouseDown:()=>void(eb.current=0),onFocus:()=>eu(0),showLabelOnHover:q,isHovered:ed,disabled:X,...null==eo?void 0:eo(0),children:eA?K[0]:K}),(0,o.jsx)(x.z,{...eI,thumbLabel:W,value:Y(em[1]),position:ew[1],dragging:ej,label:"function"==typeof V?V((0,S.q)(Y(em[1]),eC)):V,ref:e=>{e&&(eh.current[1]=e)},onMouseDown:()=>void(eb.current=1),onFocus:()=>eu(1),showLabelOnHover:q,isHovered:ed,disabled:X,...null==eo?void 0:eo(1),children:eA?K[1]:K})]}),(0,o.jsx)("input",{type:"hidden",name:"".concat(B,"_from"),value:em[0],...et}),(0,o.jsx)("input",{type:"hidden",name:"".concat(B,"_to"),value:em[1],...et})]})})});I.classes=j.A,I.displayName="@mantine/core/RangeSlider"},5821:(e,t,r)=>{"use strict";r.d(t,{Accordion:()=>C});var o=r(95155),n=r(64173),a=r(57613),l=r(5903);r(12115);var i=r(25954),s=r(56204),c=r(68918),u=r(43664),d=r(53791),p=r(69604),m=r(36960),v=r(9614),f=r(2020),h=r(97374),g=r(44710),b=r(13120),y=r(54492);let x={multiple:!1,disableChevronRotation:!1,chevronPosition:"right",variant:"default",chevron:(0,o.jsx)(f.AccordionChevron,{})},w=(0,c.createVarsResolver)((e,t)=>{let{transitionDuration:r,chevronSize:o,radius:n}=t;return{root:{"--accordion-transition-duration":void 0===r?void 0:"".concat(r,"ms"),"--accordion-chevron-size":void 0===o?void 0:(0,l.D)(o),"--accordion-radius":void 0===n?void 0:(0,s.getRadius)(n)}}});function C(e){let t=(0,u.useProps)("Accordion",x,e),{classNames:r,className:l,style:s,styles:c,unstyled:m,vars:f,children:h,multiple:g,value:b,defaultValue:C,onChange:S,id:k,loop:E,transitionDuration:P,disableChevronRotation:j,chevronPosition:R,chevronSize:D,order:I,chevron:A,variant:T,radius:_,...M}=t,N=(0,n.useId)(k),[z,O]=(0,a.useUncontrolled)({value:b,defaultValue:C,finalValue:g?[]:null,onChange:S}),L=(0,d.useStyles)({name:"Accordion",classes:y.A,props:t,className:l,style:s,classNames:r,styles:c,unstyled:m,vars:f,varsResolver:w});return(0,o.jsx)(v.I,{value:{isItemActive:e=>Array.isArray(z)?z.includes(e):e===z,onChange:e=>{O(Array.isArray(z)?z.includes(e)?z.filter(t=>t!==e):[...z,e]:e===z?null:e)},getControlId:(0,i.getSafeId)("".concat(N,"-control"),"Accordion.Item component was rendered with invalid value or without value"),getRegionId:(0,i.getSafeId)("".concat(N,"-panel"),"Accordion.Item component was rendered with invalid value or without value"),transitionDuration:P,disableChevronRotation:j,chevronPosition:R,order:I,chevron:A,loop:E,getStyles:L,variant:T,unstyled:m},children:(0,o.jsx)(p.Box,{...L("root"),id:N,...M,variant:T,"data-accordion":!0,children:h})})}C.extend=e=>e,C.withProps=(0,m.getWithProps)(C),C.classes=y.A,C.displayName="@mantine/core/Accordion",C.Item=g.AccordionItem,C.Panel=b.AccordionPanel,C.Control=h.AccordionControl,C.Chevron=f.AccordionChevron},5942:(e,t,r)=>{"use strict";function o(e){let t=new Map;return function(){for(var r=arguments.length,o=Array(r),n=0;n<r;n++)o[n]=arguments[n];let a=JSON.stringify(o);if(t.has(a))return t.get(a);let l=e(...o);return t.set(a,l),l}}r.d(t,{memoize:()=>o})},6083:(e,t,r)=>{"use strict";r.d(t,{ComboboxChevron:()=>v});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(71180),i=r(43664),s=r(53791),c=r(69604),u=r(36960),d=r(19192);let p={error:null},m=(0,a.createVarsResolver)((e,t)=>{let{size:r,color:o}=t;return{chevron:{"--combobox-chevron-size":(0,n.getSize)(r,"combobox-chevron-size"),"--combobox-chevron-color":o?(0,l.getThemeColor)(o,e):void 0}}}),v=(0,u.factory)((e,t)=>{let r=(0,i.useProps)("ComboboxChevron",p,e),{size:n,error:a,style:l,className:u,classNames:v,styles:f,unstyled:h,vars:g,mod:b,...y}=r,x=(0,s.useStyles)({name:"ComboboxChevron",classes:d.A,props:r,style:l,className:u,classNames:v,styles:f,unstyled:h,vars:g,varsResolver:m,rootSelector:"chevron"});return(0,o.jsx)(c.Box,{component:"svg",...y,...x("chevron"),size:n,viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",mod:["combobox-chevron",{error:a},b],ref:t,children:(0,o.jsx)("path",{d:"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})});v.classes=d.A,v.displayName="@mantine/core/ComboboxChevron"},6513:(e,t,r)=>{"use strict";r.d(t,{useEyeDropper:()=>a});var o=r(12115),n=r(73141);function a(){let[e,t]=(0,o.useState)(!1);(0,n.useIsomorphicEffect)(()=>{t("undefined"!=typeof window&&!navigator.userAgent.includes("OPR")&&"EyeDropper"in window)},[]);let r=(0,o.useCallback)(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e?new window.EyeDropper().open(t):Promise.resolve(void 0)},[e]);return{supported:e,open:r}}},8060:(e,t,r)=>{"use strict";r.d(t,{defaultCssVariablesResolver:()=>c});var o=r(19224),n=r(5903);r(12115),r(95155);var a=r(30128),l=r(89200),i=r(2198);function s(e,t,r){(0,o.keys)(t).forEach(o=>Object.assign(e,{["--mantine-".concat(r,"-").concat(o)]:t[o]}))}let c=e=>{let t=(0,a.getPrimaryShade)(e,"light"),r=e.defaultRadius in e.radius?e.radius[e.defaultRadius]:(0,n.D)(e.defaultRadius),c={variables:{"--mantine-scale":e.scale.toString(),"--mantine-cursor-type":e.cursorType,"--mantine-color-scheme":"light dark","--mantine-webkit-font-smoothing":e.fontSmoothing?"antialiased":"unset","--mantine-moz-font-smoothing":e.fontSmoothing?"grayscale":"unset","--mantine-color-white":e.white,"--mantine-color-black":e.black,"--mantine-line-height":e.lineHeights.md,"--mantine-font-family":e.fontFamily,"--mantine-font-family-monospace":e.fontFamilyMonospace,"--mantine-font-family-headings":e.headings.fontFamily,"--mantine-heading-font-weight":e.headings.fontWeight,"--mantine-heading-text-wrap":e.headings.textWrap,"--mantine-radius-default":r,"--mantine-primary-color-filled":"var(--mantine-color-".concat(e.primaryColor,"-filled)"),"--mantine-primary-color-filled-hover":"var(--mantine-color-".concat(e.primaryColor,"-filled-hover)"),"--mantine-primary-color-light":"var(--mantine-color-".concat(e.primaryColor,"-light)"),"--mantine-primary-color-light-hover":"var(--mantine-color-".concat(e.primaryColor,"-light-hover)"),"--mantine-primary-color-light-color":"var(--mantine-color-".concat(e.primaryColor,"-light-color)")},light:{"--mantine-primary-color-contrast":(0,l.getPrimaryContrastColor)(e,"light"),"--mantine-color-bright":"var(--mantine-color-black)","--mantine-color-text":e.black,"--mantine-color-body":e.white,"--mantine-color-error":"var(--mantine-color-red-6)","--mantine-color-placeholder":"var(--mantine-color-gray-5)","--mantine-color-anchor":"var(--mantine-color-".concat(e.primaryColor,"-").concat(t,")"),"--mantine-color-default":"var(--mantine-color-white)","--mantine-color-default-hover":"var(--mantine-color-gray-0)","--mantine-color-default-color":"var(--mantine-color-black)","--mantine-color-default-border":"var(--mantine-color-gray-4)","--mantine-color-dimmed":"var(--mantine-color-gray-6)","--mantine-color-disabled":"var(--mantine-color-gray-2)","--mantine-color-disabled-color":"var(--mantine-color-gray-5)","--mantine-color-disabled-border":"var(--mantine-color-gray-3)"},dark:{"--mantine-primary-color-contrast":(0,l.getPrimaryContrastColor)(e,"dark"),"--mantine-color-bright":"var(--mantine-color-white)","--mantine-color-text":"var(--mantine-color-dark-0)","--mantine-color-body":"var(--mantine-color-dark-7)","--mantine-color-error":"var(--mantine-color-red-8)","--mantine-color-placeholder":"var(--mantine-color-dark-3)","--mantine-color-anchor":"var(--mantine-color-".concat(e.primaryColor,"-4)"),"--mantine-color-default":"var(--mantine-color-dark-6)","--mantine-color-default-hover":"var(--mantine-color-dark-5)","--mantine-color-default-color":"var(--mantine-color-white)","--mantine-color-default-border":"var(--mantine-color-dark-4)","--mantine-color-dimmed":"var(--mantine-color-dark-2)","--mantine-color-disabled":"var(--mantine-color-dark-6)","--mantine-color-disabled-color":"var(--mantine-color-dark-3)","--mantine-color-disabled-border":"var(--mantine-color-gray-6)"}};s(c.variables,e.breakpoints,"breakpoint"),s(c.variables,e.spacing,"spacing"),s(c.variables,e.fontSizes,"font-size"),s(c.variables,e.lineHeights,"line-height"),s(c.variables,e.shadows,"shadow"),s(c.variables,e.radius,"radius"),e.colors[e.primaryColor].forEach((t,r)=>{c.variables["--mantine-primary-color-".concat(r)]="var(--mantine-color-".concat(e.primaryColor,"-").concat(r,")")}),(0,o.keys)(e.colors).forEach(t=>{let r=e.colors[t];if(function(e){return!!e&&"object"==typeof e&&"mantine-virtual-color"in e}(r)){Object.assign(c.light,(0,i.getCSSColorVariables)({theme:e,name:r.name,color:r.light,colorScheme:"light",withColorValues:!0})),Object.assign(c.dark,(0,i.getCSSColorVariables)({theme:e,name:r.name,color:r.dark,colorScheme:"dark",withColorValues:!0}));return}r.forEach((e,r)=>{c.variables["--mantine-color-".concat(t,"-").concat(r)]=e}),Object.assign(c.light,(0,i.getCSSColorVariables)({theme:e,color:t,colorScheme:"light",withColorValues:!1})),Object.assign(c.dark,(0,i.getCSSColorVariables)({theme:e,color:t,colorScheme:"dark",withColorValues:!1}))});let u=e.headings.sizes;return(0,o.keys)(u).forEach(t=>{c.variables["--mantine-".concat(t,"-font-size")]=u[t].fontSize,c.variables["--mantine-".concat(t,"-line-height")]=u[t].lineHeight,c.variables["--mantine-".concat(t,"-font-weight")]=u[t].fontWeight||e.headings.fontWeight}),c}},8347:(e,t,r)=>{"use strict";r.d(t,{useOs:()=>i});var o=r(12115),n=r(73141);function a(e){return/(Macintosh)|(MacIntel)|(MacPPC)|(Mac68K)/i.test(e)}function l(){if("undefined"==typeof window)return"undetermined";let{userAgent:e}=window.navigator;return/(iPhone)|(iPad)|(iPod)/i.test(e)||a(e)&&"ontouchend"in document?"ios":a(e)?"macos":/(Win32)|(Win64)|(Windows)|(WinCE)/i.test(e)?"windows":/Android/i.test(e)?"android":/Linux/i.test(e)?"linux":/CrOS/i.test(e)?"chromeos":"undetermined"}function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{getValueInEffect:!0},[t,r]=(0,o.useState)(e.getValueInEffect?"undetermined":l());return(0,n.useIsomorphicEffect)(()=>{e.getValueInEffect&&r(l)},[]),t}},8548:(e,t,r)=>{"use strict";r.d(t,{Radio:()=>E});var o=r(95155),n=r(64173);r(12115);var a=r(56204),l=r(68918),i=r(98271),s=r(71180),c=r(89200),u=r(98840),d=r(43664),p=r(53791),m=r(99537),v=r(69604),f=r(36960),h=r(44598),g=r(84350),b=r(53051),y=r(37079),x=r(57326),w=r(98631),C={root:"m_f3f1af94",inner:"m_89c4f5e4",icon:"m_f3ed6b2b",radio:"m_8a3dbb89","radio--outline":"m_1bfe9d39"};let S={labelPosition:"right"},k=(0,l.createVarsResolver)((e,t)=>{let{size:r,radius:o,color:n,iconColor:l,variant:d,autoContrast:p}=t,m=(0,i.parseThemeColor)({color:n||e.primaryColor,theme:e}),v=m.isThemeColor&&void 0===m.shade?"var(--mantine-color-".concat(m.color,"-outline)"):m.color;return{root:{"--radio-size":(0,a.getSize)(r,"radio-size"),"--radio-radius":void 0===o?void 0:(0,a.getRadius)(o),"--radio-color":"outline"===d?v:(0,s.getThemeColor)(n,e),"--radio-icon-color":l?(0,s.getThemeColor)(l,e):(0,u.getAutoContrastValue)(p,e)?(0,c.getContrastColor)({color:n,theme:e,autoContrast:p}):void 0,"--radio-icon-size":(0,a.getSize)(r,"radio-icon-size")}}}),E=(0,f.factory)((e,t)=>{var r,a;let l=(0,d.useProps)("Radio",S,e),{classNames:i,className:s,style:c,styles:u,unstyled:f,vars:g,id:y,size:w,label:E,labelPosition:P,description:j,error:R,radius:D,color:I,variant:A,disabled:T,wrapperProps:_,icon:M=x.RadioIcon,rootRef:N,iconColor:z,onChange:O,mod:L,...B}=l,F=(0,p.useStyles)({name:"Radio",classes:C,props:l,className:s,style:c,classNames:i,styles:u,unstyled:f,vars:g,varsResolver:k}),V=(0,b.R)(),H=null!=(r=null==V?void 0:V.size)?r:w,U=l.size?w:H,{styleProps:G,rest:W}=(0,m.extractStyleProps)(B),q=(0,n.useId)(y),K=V?{checked:V.value===W.value,name:null!=(a=W.name)?a:V.name,onChange:e=>{V.onChange(e),null==O||O(e)}}:{};return(0,o.jsx)(h.I,{...F("root"),__staticSelector:"Radio",__stylesApiProps:l,id:q,size:U,labelPosition:P,label:E,description:j,error:R,disabled:T,classNames:i,styles:u,unstyled:f,"data-checked":K.checked||void 0,variant:A,ref:N,mod:L,...G,..._,children:(0,o.jsxs)(v.Box,{...F("inner"),mod:{"label-position":P},children:[(0,o.jsx)(v.Box,{...F("radio",{focusable:!0,variant:A}),onChange:O,...W,...K,component:"input",mod:{error:!!R},ref:t,id:q,disabled:T,type:"radio"}),(0,o.jsx)(M,{...F("icon"),"aria-hidden":!0})]})})});E.classes=C,E.displayName="@mantine/core/Radio",E.Group=y.RadioGroup,E.Card=g.RadioCard,E.Indicator=w.RadioIndicator},8657:(e,t,r)=>{"use strict";r.d(t,{useQueue:()=>n});var o=r(12115);function n(e){let{initialValues:t=[],limit:r}=e,[n,a]=(0,o.useState)({state:t.slice(0,r),queue:t.slice(r)});return{state:n.state,queue:n.queue,add:function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return a(e=>{let o=[...e.state,...e.queue,...t];return{state:o.slice(0,r),queue:o.slice(r)}})},update:e=>a(t=>{let o=e([...t.state,...t.queue]);return{state:o.slice(0,r),queue:o.slice(r)}}),cleanQueue:()=>a(e=>({state:e.state,queue:[]}))}}},9061:(e,t,r)=>{"use strict";r.d(t,{useHotkeys:()=>a});var o=r(12115),n=r(13428);function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["INPUT","TEXTAREA","SELECT"],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];(0,o.useEffect)(()=>{let o=o=>{e.forEach(e=>{let[a,l,i={preventDefault:!0,usePhysicalKeys:!1}]=e;(0,n.v)(a,i.usePhysicalKeys)(o)&&function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return!(e.target instanceof HTMLElement)||(r?!t.includes(e.target.tagName):!e.target.isContentEditable&&!t.includes(e.target.tagName))}(o,t,r)&&(i.preventDefault&&o.preventDefault(),l(o))})};return document.documentElement.addEventListener("keydown",o),()=>document.documentElement.removeEventListener("keydown",o)},[e])}},9541:(e,t,r)=>{"use strict";r.d(t,{useIntersection:()=>n});var o=r(12115);function n(e){let[t,r]=(0,o.useState)(null),n=(0,o.useRef)(null);return{ref:(0,o.useCallback)(t=>{if(n.current&&(n.current.disconnect(),n.current=null),null===t)return void r(null);n.current=new IntersectionObserver(e=>{let[t]=e;r(t)},e),n.current.observe(t)},[null==e?void 0:e.rootMargin,null==e?void 0:e.root,null==e?void 0:e.threshold]),entry:t}}},9614:(e,t,r)=>{"use strict";r.d(t,{D:()=>a,I:()=>n}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Accordion component was not found in the tree")},9822:(e,t,r)=>{"use strict";r.d(t,{C:()=>c});var o=r(95155),n=r(12115),a=r(69604),l=r(97446),i=r(17077);function s(e){let{marks:t,min:r,max:s,disabled:c,value:u,offset:d,inverted:p}=e,{getStyles:m}=(0,l.s)();if(!t)return null;let v=t.map((e,t)=>(0,n.createElement)(a.Box,{...m("markWrapper"),__vars:{"--mark-offset":"".concat((0,i.E)({value:e.value,min:r,max:s}),"%")},key:t},(0,o.jsx)(a.Box,{...m("mark"),mod:{filled:function(e){let{mark:t,offset:r,value:o,inverted:n=!1}=e;return n?"number"==typeof r&&t.value<=r||t.value>=o:"number"==typeof r?t.value>=r&&t.value<=o:t.value<=o}({mark:e,value:u,offset:d,inverted:p}),disabled:c}}),e.label&&(0,o.jsx)("div",{...m("markLabel"),children:e.label})));return(0,o.jsx)("div",{children:v})}function c(e){let{filled:t,children:r,offset:n,disabled:i,marksOffset:c,inverted:u,containerProps:d,...p}=e,{getStyles:m}=(0,l.s)();return(0,o.jsx)(a.Box,{...m("trackContainer"),mod:{disabled:i},...d,children:(0,o.jsxs)(a.Box,{...m("track"),mod:{inverted:u,disabled:i},children:[(0,o.jsx)(a.Box,{mod:{inverted:u,disabled:i},__vars:{"--slider-bar-width":"calc(".concat(t,"% + 2 * var(--slider-size))"),"--slider-bar-offset":"calc(".concat(n,"% - var(--slider-size))")},...m("bar")}),r,(0,o.jsx)(s,{...p,offset:c,disabled:i,inverted:u})]})})}s.displayName="@mantine/core/SliderMarks",c.displayName="@mantine/core/SliderTrack"},10115:(e,t,r)=>{"use strict";r.d(t,{ComboboxOptions:()=>p});var o=r(95155),n=r(12115),a=r(64173),l=r(43664),i=r(69604),s=r(36960),c=r(3826),u=r(19192);let d={},p=(0,s.factory)((e,t)=>{let{classNames:r,className:s,style:u,styles:p,id:m,onMouseDown:v,labelledBy:f,...h}=(0,l.useProps)("ComboboxOptions",d,e),g=(0,c.A)(),b=(0,a.useId)(m);return(0,n.useEffect)(()=>{g.store.setListId(b)},[b]),(0,o.jsx)(i.Box,{ref:t,...g.getStyles("options",{className:s,style:u,classNames:r,styles:p}),...h,id:b,role:"listbox","aria-labelledby":f,onMouseDown:e=>{e.preventDefault(),null==v||v(e)}})});p.classes=u.A,p.displayName="@mantine/core/ComboboxOptions"},10537:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var o=r(95155);r(12115);var n=r(21355);function a(e){let{children:t,role:r}=e,a=(0,n.useInputWrapperContext)();return a?(0,o.jsx)("div",{role:r,"aria-labelledby":a.labelId,"aria-describedby":a.describedBy,children:t}):(0,o.jsx)(o.Fragment,{children:t})}},10884:(e,t,r)=>{"use strict";r.d(t,{CheckboxIndicator:()=>y});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(98271),i=r(71180),s=r(89200),c=r(98840),u=r(43664),d=r(53791),p=r(69604),m=r(36960),v=r(12490),f=r(27547),h={indicator:"m_5e5256ee",icon:"m_1b1c543a","indicator--outline":"m_76e20374"};let g={icon:f.R},b=(0,a.createVarsResolver)((e,t)=>{let{radius:r,color:o,size:a,iconColor:u,variant:d,autoContrast:p}=t,m=(0,l.parseThemeColor)({color:o||e.primaryColor,theme:e}),v=m.isThemeColor&&void 0===m.shade?"var(--mantine-color-".concat(m.color,"-outline)"):m.color;return{indicator:{"--checkbox-size":(0,n.getSize)(a,"checkbox-size"),"--checkbox-radius":void 0===r?void 0:(0,n.getRadius)(r),"--checkbox-color":"outline"===d?v:(0,i.getThemeColor)(o,e),"--checkbox-icon-color":u?(0,i.getThemeColor)(u,e):(0,c.getAutoContrastValue)(p,e)?(0,s.getContrastColor)({color:o,theme:e,autoContrast:p}):void 0}}}),y=(0,m.factory)((e,t)=>{let r=(0,u.useProps)("CheckboxIndicator",g,e),{classNames:n,className:a,style:l,styles:i,unstyled:s,vars:c,icon:m,indeterminate:f,radius:y,color:x,iconColor:w,autoContrast:C,checked:S,mod:k,variant:E,disabled:P,...j}=r,R=(0,d.useStyles)({name:"CheckboxIndicator",classes:h,props:r,className:a,style:l,classNames:n,styles:i,unstyled:s,vars:c,varsResolver:b,rootSelector:"indicator"}),D=(0,v.useCheckboxCardContext)(),I="boolean"==typeof S||"boolean"==typeof f?S||f:(null==D?void 0:D.checked)||!1;return(0,o.jsx)(p.Box,{ref:t,...R("indicator",{variant:E}),variant:E,mod:[{checked:I,disabled:P},k],...j,children:(0,o.jsx)(m,{indeterminate:f,...R("icon")})})});y.displayName="@mantine/core/CheckboxIndicator",y.classes=h},11352:(e,t,r)=>{"use strict";r.d(t,{SegmentedControl:()=>k});var o=r(95155),n=r(12115),a=r(95629),l=r(74275),i=r(57613),s=r(64173),c=r(88551),u=r(29791),d=r(56204),p=r(68918),m=r(71180),v=r(89200),f=r(3131),h=r(43664),g=r(53791),b=r(69604),y=r(36960),x=r(39832),w={root:"m_cf365364",indicator:"m_9e182ccd",label:"m_1738fcb2",input:"m_1714d588",control:"m_69686b9b",innerLabel:"m_78882f40"};let C={withItemsBorders:!0},S=(0,p.createVarsResolver)((e,t)=>{let{radius:r,color:o,transitionDuration:n,size:a,transitionTimingFunction:l}=t;return{root:{"--sc-radius":void 0===r?void 0:(0,d.getRadius)(r),"--sc-color":o?(0,m.getThemeColor)(o,e):void 0,"--sc-shadow":o?void 0:"var(--mantine-shadow-xs)","--sc-transition-duration":void 0===n?void 0:"".concat(n,"ms"),"--sc-transition-timing-function":l,"--sc-padding":(0,d.getSize)(a,"sc-padding"),"--sc-font-size":(0,d.getFontSize)(a)}}}),k=(0,y.factory)((e,t)=>{var r,d,p,m;let y=(0,h.useProps)("SegmentedControl",C,e),{classNames:k,className:E,style:P,styles:j,unstyled:R,vars:D,data:I,value:A,defaultValue:T,onChange:_,size:M,name:N,disabled:z,readOnly:O,fullWidth:L,orientation:B,radius:F,color:V,transitionDuration:H,transitionTimingFunction:U,variant:G,autoContrast:W,withItemsBorders:q,mod:K,...X}=y,Z=(0,g.useStyles)({name:"SegmentedControl",props:y,classes:w,className:E,style:P,classNames:k,styles:j,unstyled:R,vars:D,varsResolver:S}),Y=(0,f.useMantineTheme)(),Q=I.map(e=>"string"==typeof e?{label:e,value:e}:e),$=(0,a.useMounted)(),[J,ee]=(0,n.useState)((0,l.randomId)()),[et,er]=(0,n.useState)(null),[eo,en]=(0,n.useState)({}),ea=(e,t)=>{eo[t]=e,en(eo)},[el,ei]=(0,i.useUncontrolled)({value:A,defaultValue:T,finalValue:Array.isArray(I)&&null!=(m=null!=(p=null==(r=Q.find(e=>!e.disabled))?void 0:r.value)?p:null==(d=I[0])?void 0:d.value)?m:null,onChange:_}),es=(0,s.useId)(N),ec=Q.map(e=>(0,n.createElement)(b.Box,{...Z("control"),mod:{active:el===e.value,orientation:B},key:e.value},(0,n.createElement)("input",{...Z("input"),disabled:z||e.disabled,type:"radio",name:es,value:e.value,id:"".concat(es,"-").concat(e.value),checked:el===e.value,onChange:()=>!O&&ei(e.value),"data-focus-ring":Y.focusRing,key:"".concat(e.value,"-input")}),(0,n.createElement)(b.Box,{component:"label",...Z("label"),mod:{active:el===e.value&&!(z||e.disabled),disabled:z||e.disabled,"read-only":O},htmlFor:"".concat(es,"-").concat(e.value),ref:t=>ea(t,e.value),__vars:{"--sc-label-color":void 0!==V?(0,v.getContrastColor)({color:V,theme:Y,autoContrast:W}):void 0},key:"".concat(e.value,"-label")},(0,o.jsx)("span",{...Z("innerLabel"),children:e.label})))),eu=(0,c.useMergedRef)(t,e=>er(e));return((0,u.useShallowEffect)(()=>{ee((0,l.randomId)())},[I.length]),0===I.length)?null:(0,o.jsxs)(b.Box,{...Z("root"),variant:G,size:M,ref:eu,mod:[{"full-width":L,orientation:B,initialized:$,"with-items-borders":q},K],...X,role:"radiogroup","data-disabled":z,children:["string"==typeof el&&(0,o.jsx)(x.FloatingIndicator,{target:eo[el],parent:et,component:"span",transitionDuration:"var(--sc-transition-duration)",...Z("indicator")},J),ec]})});k.classes=w,k.displayName="@mantine/core/SegmentedControl"},12037:(e,t,r)=>{"use strict";r.d(t,{useDebouncedState:()=>n});var o=r(12115);function n(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{leading:!1},[n,a]=(0,o.useState)(e),l=(0,o.useRef)(null),i=(0,o.useRef)(!0),s=()=>window.clearTimeout(l.current);return(0,o.useEffect)(()=>s,[]),[n,(0,o.useCallback)(e=>{s(),i.current&&r.leading?a(e):l.current=window.setTimeout(()=>{i.current=!0,a(e)},t),i.current=!1},[r.leading])]}},12490:(e,t,r)=>{"use strict";r.d(t,{V:()=>o,useCheckboxCardContext:()=>n}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)()},13060:(e,t,r)=>{"use strict";r.d(t,{PaginationDots:()=>d});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(80864),s=r(63499),c=r(68962);let u={icon:s.iu},d=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:s,styles:c,vars:d,icon:p,...m}=(0,n.useProps)("PaginationDots",u,e),v=(0,i.b)();return(0,o.jsx)(a.Box,{ref:t,...v.getStyles("dots",{className:l,style:s,styles:c,classNames:r}),...m,children:(0,o.jsx)(p,{style:{width:"calc(var(--pagination-control-size) / 1.8)",height:"calc(var(--pagination-control-size) / 1.8)"}})})});d.classes=c.A,d.displayName="@mantine/core/PaginationDots"},13120:(e,t,r)=>{"use strict";r.d(t,{AccordionPanel:()=>d});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(9385),i=r(9614),s=r(89535),c=r(54492);let u={},d=(0,a.factory)((e,t)=>{var r;let{classNames:a,className:c,style:d,styles:p,vars:m,children:v,...f}=(0,n.useProps)("AccordionPanel",u,e),{value:h}=(0,s.A)(),g=(0,i.D)();return(0,o.jsx)(l.Collapse,{ref:t,...g.getStyles("panel",{className:c,classNames:a,style:d,styles:p}),...f,in:g.isItemActive(h),transitionDuration:null!=(r=g.transitionDuration)?r:200,role:"region",id:g.getRegionId(h),"aria-labelledby":g.getControlId(h),children:(0,o.jsx)("div",{...g.getStyles("content",{classNames:a,styles:p}),children:v})})});d.displayName="@mantine/core/AccordionPanel",d.classes=c.A},13135:(e,t,r)=>{"use strict";function o(e,t){if(e===t||Number.isNaN(e)&&Number.isNaN(t))return!0;if(!(e instanceof Object)||!(t instanceof Object))return!1;let r=Object.keys(e),{length:o}=r;if(o!==Object.keys(t).length)return!1;for(let n=0;n<o;n+=1){let o=r[n];if(!(o in t)||e[o]!==t[o]&&!(Number.isNaN(e[o])&&Number.isNaN(t[o])))return!1}return!0}r.d(t,{shallowEqual:()=>o})},13428:(e,t,r)=>{"use strict";r.d(t,{getHotkeyHandler:()=>l,v:()=>a});let o={" ":"space",ArrowLeft:"arrowleft",ArrowRight:"arrowright",ArrowUp:"arrowup",ArrowDown:"arrowdown",Escape:"escape",Esc:"escape",esc:"escape",Enter:"enter",Tab:"tab",Backspace:"backspace",Delete:"delete",Insert:"insert",Home:"home",End:"end",PageUp:"pageup",PageDown:"pagedown","+":"plus","-":"minus","*":"asterisk","/":"slash"};function n(e){let t=e.replace("Key","").toLowerCase();return o[e]||t}function a(e,t){return r=>(function(e,t,r){let{alt:o,ctrl:a,meta:l,mod:i,shift:s,key:c}=e,{altKey:u,ctrlKey:d,metaKey:p,shiftKey:m,key:v,code:f}=t;if(o!==u)return!1;if(i){if(!d&&!p)return!1}else if(a!==d||l!==p)return!1;return s===m&&!!c&&(r?n(f)===n(c):n(null!=v?v:f)===n(c))})(function(e){let t=e.toLowerCase().split("+").map(e=>e.trim()),r={alt:t.includes("alt"),ctrl:t.includes("ctrl"),meta:t.includes("meta"),mod:t.includes("mod"),shift:t.includes("shift"),plus:t.includes("[plus]")},o=["alt","ctrl","meta","shift","mod"],n=t.find(e=>!o.includes(e));return{...r,key:"[plus]"===n?"+":n}}(e),r,t)}function l(e){return t=>{let r="nativeEvent"in t?t.nativeEvent:t;e.forEach(e=>{let[o,n,l={preventDefault:!0,usePhysicalKeys:!1}]=e;a(o,l.usePhysicalKeys)(r)&&(l.preventDefault&&t.preventDefault(),n(r))})}}},14327:(e,t,r)=>{"use strict";r.d(t,{ComboboxEmpty:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(3826),s=r(19192);let c={},u=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:s,styles:u,vars:d,...p}=(0,n.useProps)("ComboboxEmpty",c,e),m=(0,i.A)();return(0,o.jsx)(a.Box,{ref:t,...m.getStyles("empty",{className:l,classNames:r,styles:u,style:s}),...p})});u.classes=s.A,u.displayName="@mantine/core/ComboboxEmpty"},14449:(e,t,r)=>{"use strict";r.d(t,{Combobox:()=>D});var o=r(95155),n=r(5903);r(12115);var a=r(56204),l=r(68918),i=r(43664),s=r(53791),c=r(60266),u=r(3826),d=r(6083),p=r(75139),m=r(18349),v=r(68827),f=r(14327),h=r(16403),g=r(56589),b=r(74775),y=r(44541),x=r(15451),w=r(62861),C=r(10115),S=r(48411),k=r(68665),E=r(87160),P=r(19192);let j={keepMounted:!0,withinPortal:!0,resetSelectionOnOptionHover:!1,width:"target",transitionProps:{transition:"fade",duration:0},size:"sm"},R=(0,l.createVarsResolver)((e,t)=>{let{size:r,dropdownPadding:o}=t;return{options:{"--combobox-option-fz":(0,a.getFontSize)(r),"--combobox-option-padding":(0,a.getSize)(r,"combobox-option-padding")},dropdown:{"--combobox-padding":void 0===o?void 0:(0,n.D)(o),"--combobox-option-fz":(0,a.getFontSize)(r),"--combobox-option-padding":(0,a.getSize)(r,"combobox-option-padding")}}});function D(e){let t=(0,i.useProps)("Combobox",j,e),{classNames:r,styles:n,unstyled:a,children:l,store:d,vars:p,onOptionSubmit:m,onClose:v,size:f,dropdownPadding:h,resetSelectionOnOptionHover:g,__staticSelector:b,readOnly:y,...x}=t,w=(0,E.useCombobox)(),C=d||w,S=(0,s.useStyles)({name:b||"Combobox",classes:P.A,props:t,classNames:r,styles:n,unstyled:a,vars:p,varsResolver:R}),k=()=>{null==v||v(),C.closeDropdown()};return(0,o.jsx)(u.v,{value:{getStyles:S,store:C,onOptionSubmit:m,size:f,resetSelectionOnOptionHover:g,readOnly:y},children:(0,o.jsx)(c.Popover,{opened:C.dropdownOpened,preventPositionChangeWhenVisible:!0,...x,onChange:e=>!e&&k(),withRoles:!1,unstyled:a,children:l})})}D.extend=e=>e,D.classes=P.A,D.displayName="@mantine/core/Combobox",D.Target=k.ComboboxTarget,D.Dropdown=m.ComboboxDropdown,D.Options=C.ComboboxOptions,D.Option=w.ComboboxOption,D.Search=S.ComboboxSearch,D.Empty=f.ComboboxEmpty,D.Chevron=d.ComboboxChevron,D.Footer=g.ComboboxFooter,D.Header=y.ComboboxHeader,D.EventsTarget=h.ComboboxEventsTarget,D.DropdownTarget=v.ComboboxDropdownTarget,D.Group=b.ComboboxGroup,D.ClearButton=p.ComboboxClearButton,D.HiddenInput=x.ComboboxHiddenInput},14719:(e,t,r)=>{"use strict";r.d(t,{TooltipGroup:()=>s});var o=r(95155),n=r(45299);r(12115);var a=r(43664),l=r(84528);let i={openDelay:0,closeDelay:0};function s(e){let{openDelay:t,closeDelay:r,children:s}=(0,a.useProps)("TooltipGroup",i,e);return(0,o.jsx)(l.C,{value:!0,children:(0,o.jsx)(n.T3,{delay:{open:t,close:r},children:s})})}s.displayName="@mantine/core/TooltipGroup",s.extend=e=>e},14784:(e,t,r)=>{"use strict";r.d(t,{useMatches:()=>l});var o=r(69445),n=r(3131);let a=["xs","sm","md","lg","xl"];function l(e,t){let r=(0,n.useMantineTheme)(),l=(0,o.useMediaQuery)("(min-width: ".concat(r.breakpoints.xs,")"),!1,t),i=(0,o.useMediaQuery)("(min-width: ".concat(r.breakpoints.sm,")"),!1,t),s=(0,o.useMediaQuery)("(min-width: ".concat(r.breakpoints.md,")"),!1,t),c=(0,o.useMediaQuery)("(min-width: ".concat(r.breakpoints.lg,")"),!1,t);var u=a[[l,i,s,c,(0,o.useMediaQuery)("(min-width: ".concat(r.breakpoints.xl,")"),!1,t)].findLastIndex(e=>e)];if(!u)return e.base;let d=a.indexOf(u);for(;d>=0;){if(a[d]in e)return e[a[d]];d-=1}return e.base}},14951:(e,t,r)=>{"use strict";r.d(t,{HueSlider:()=>s});var o=r(95155),n=r(12115),a=r(5903),l=r(43664),i=r(85639);let s=(0,n.forwardRef)((e,t)=>{let{value:r,onChange:n,onChangeEnd:s,color:c,...u}=(0,l.useProps)("HueSlider",{},e);return(0,o.jsx)(i.t,{...u,ref:t,value:r,onChange:n,onChangeEnd:s,maxValue:360,thumbColor:"hsl(".concat(r,", 100%, 50%)"),round:!0,"data-hue":!0,overlays:[{backgroundImage:"linear-gradient(to right,hsl(0,100%,50%),hsl(60,100%,50%),hsl(120,100%,50%),hsl(170,100%,50%),hsl(240,100%,50%),hsl(300,100%,50%),hsl(360,100%,50%))"},{boxShadow:"rgba(0, 0, 0, .1) 0 0 0 ".concat((0,a.D)(1)," inset, rgb(0, 0, 0, .15) 0 0 ").concat((0,a.D)(4)," inset")}]})});s.displayName="@mantine/core/HueSlider"},15451:(e,t,r)=>{"use strict";r.d(t,{ComboboxHiddenInput:()=>n});var o=r(95155);function n(e){let{value:t,valuesDivider:r=",",...n}=e;return(0,o.jsx)("input",{type:"hidden",value:Array.isArray(t)?t.join(r):t||"",...n})}n.displayName="@mantine/core/ComboboxHiddenInput"},15542:(e,t,r)=>{"use strict";r.d(t,{TabsTab:()=>v});var o=r(95155);r(12115);var n=r(56570),a=r(71180),l=r(3131),i=r(43664),s=r(36960),c=r(53304),u=r(43608),d=r(22856),p=r(51434);let m={},v=(0,s.factory)((e,t)=>{let r=(0,i.useProps)("TabsTab",m,e),{className:s,children:p,rightSection:v,leftSection:f,value:h,onClick:g,onKeyDown:b,disabled:y,color:x,style:w,classNames:C,styles:S,vars:k,mod:E,tabIndex:P,...j}=r,R=(0,l.useMantineTheme)(),{dir:D}=(0,c.useDirection)(),I=(0,d.f)(),A=h===I.value,T={classNames:C,styles:S,props:r};return(0,o.jsxs)(u.UnstyledButton,{...j,...I.getStyles("tab",{className:s,style:w,variant:I.variant,...T}),disabled:y,unstyled:I.unstyled,variant:I.variant,mod:[{active:A,disabled:y,orientation:I.orientation,inverted:I.inverted,placement:"vertical"===I.orientation&&I.placement},E],ref:t,role:"tab",id:I.getTabId(h),"aria-selected":A,tabIndex:void 0!==P?P:A||null===I.value?0:-1,"aria-controls":I.getPanelId(h),onClick:e=>{I.onChange(I.allowTabDeactivation&&h===I.value?null:h),null==g||g(e)},__vars:{"--tabs-color":x?(0,a.getThemeColor)(x,R):void 0},onKeyDown:(0,n.createScopedKeydownHandler)({siblingSelector:'[role="tab"]',parentSelector:'[role="tablist"]',activateOnFocus:I.activateTabWithKeyboard,loop:I.loop,orientation:I.orientation||"horizontal",dir:D,onKeyDown:b}),children:[f&&(0,o.jsx)("span",{...I.getStyles("tabSection",T),"data-position":"left",children:f}),p&&(0,o.jsx)("span",{...I.getStyles("tabLabel",T),children:p}),v&&(0,o.jsx)("span",{...I.getStyles("tabSection",T),"data-position":"right",children:v})]})});v.classes=p.A,v.displayName="@mantine/core/TabsTab"},15711:(e,t,r)=>{"use strict";r.d(t,{MenuDivider:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(65054),s=r(69324);let c={},u=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:s,styles:u,vars:d,...p}=(0,n.useProps)("MenuDivider",c,e),m=(0,i.K)();return(0,o.jsx)(a.Box,{ref:t,...m.getStyles("divider",{className:l,style:s,styles:u,classNames:r}),...p})});u.classes=s.A,u.displayName="@mantine/core/MenuDivider"},15763:(e,t,r)=>{"use strict";r.d(t,{Select:()=>b});var o=r(95155),n=r(12115),a=r(64173),l=r(57613),i=r(85229),s=r(86028),c=r(43664),u=r(36960),d=r(79483),p=r(48509),m=r(14449),v=r(75843),f=r(87160),h=r(24225);let g={withCheckIcon:!0,allowDeselect:!0,checkIconPosition:"left"},b=(0,u.factory)((e,t)=>{let r=(0,c.useProps)("Select",g,e),{classNames:u,styles:b,unstyled:y,vars:x,dropdownOpened:w,defaultDropdownOpened:C,onDropdownClose:S,onDropdownOpen:k,onFocus:E,onBlur:P,onClick:j,onChange:R,data:D,value:I,defaultValue:A,selectFirstOptionOnChange:T,onOptionSubmit:_,comboboxProps:M,readOnly:N,disabled:z,filter:O,limit:L,withScrollArea:B,maxDropdownHeight:F,size:V,searchable:H,rightSection:U,checkIconPosition:G,withCheckIcon:W,nothingFoundMessage:q,name:K,form:X,searchValue:Z,defaultSearchValue:Y,onSearchChange:Q,allowDeselect:$,error:J,rightSectionPointerEvents:ee,id:et,clearable:er,clearButtonProps:eo,hiddenInputProps:en,renderOption:ea,onClear:el,autoComplete:ei,scrollAreaProps:es,__defaultRightSection:ec,__clearSection:eu,__clearable:ed,chevronColor:ep,...em}=r,ev=(0,n.useMemo)(()=>(0,d.getParsedComboboxData)(D),[D]),ef=(0,n.useMemo)(()=>(0,p.getOptionsLockup)(ev),[ev]),eh=(0,a.useId)(et),[eg,eb,ey]=(0,l.useUncontrolled)({value:I,defaultValue:A,finalValue:null,onChange:R}),ex="string"==typeof eg?ef[eg]:void 0,ew=(0,i.usePrevious)(ex),[eC,eS,ek]=(0,l.useUncontrolled)({value:Z,defaultValue:Y,finalValue:ex?ex.label:"",onChange:Q}),eE=(0,f.useCombobox)({opened:w,defaultOpened:C,onDropdownOpen:()=>{null==k||k(),eE.updateSelectedOptionIndex("active",{scrollIntoView:!0})},onDropdownClose:()=>{null==S||S(),eE.resetSelectedOption()}}),eP=e=>{eS(e),eE.resetSelectedOption()},{resolvedClassNames:ej,resolvedStyles:eR}=(0,s.useResolvedStylesApi)({props:r,styles:b,classNames:u});(0,n.useEffect)(()=>{T&&eE.selectFirstOption()},[T,eC]),(0,n.useEffect)(()=>{null===I&&eP(""),"string"==typeof I&&ex&&((null==ew?void 0:ew.value)!==ex.value||(null==ew?void 0:ew.label)!==ex.label)&&eP(ex.label)},[I,ex]),(0,n.useEffect)(()=>{if(!ey&&!ek){var e;eP("string"==typeof eg&&(null==(e=ef[eg])?void 0:e.label)||"")}},[D,eg]);let eD=(0,o.jsx)(m.Combobox.ClearButton,{...eo,onClear:()=>{eb(null,null),eP(""),null==el||el()}}),eI=er&&!!eg&&!z&&!N;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(m.Combobox,{store:eE,__staticSelector:"Select",classNames:ej,styles:eR,unstyled:y,readOnly:N,onOptionSubmit:e=>{null==_||_(e);let t=$&&ef[e].value===eg?null:ef[e],r=t?t.value:null;r!==eg&&eb(r,t),ey||eP("string"==typeof r&&(null==t?void 0:t.label)||""),eE.closeDropdown()},size:V,...M,children:[(0,o.jsx)(m.Combobox.Target,{targetType:H?"input":"button",autoComplete:ei,children:(0,o.jsx)(h.InputBase,{id:eh,ref:t,__defaultRightSection:(0,o.jsx)(m.Combobox.Chevron,{size:V,error:J,unstyled:y,color:ep}),__clearSection:eD,__clearable:eI,rightSection:U,rightSectionPointerEvents:ee||(eI?"all":"none"),...em,size:V,__staticSelector:"Select",disabled:z,readOnly:N||!H,value:eC,onChange:e=>{eP(e.currentTarget.value),eE.openDropdown(),T&&eE.selectFirstOption()},onFocus:e=>{H&&eE.openDropdown(),null==E||E(e)},onBlur:e=>{var t;H&&eE.closeDropdown(),eP(null!=eg&&(null==(t=ef[eg])?void 0:t.label)||""),null==P||P(e)},onClick:e=>{H?eE.openDropdown():eE.toggleDropdown(),null==j||j(e)},classNames:ej,styles:eR,unstyled:y,pointer:!H,error:J})}),(0,o.jsx)(v.OptionsDropdown,{data:ev,hidden:N||z,filter:O,search:eC,limit:L,hiddenWhenEmpty:!q,withScrollArea:B,maxDropdownHeight:F,filterOptions:!!H&&(null==ex?void 0:ex.label)!==eC,value:eg,checkIconPosition:G,withCheckIcon:W,nothingFoundMessage:q,unstyled:y,labelId:em.label?"".concat(eh,"-label"):void 0,"aria-label":em.label?void 0:em["aria-label"],renderOption:ea,scrollAreaProps:es})]}),(0,o.jsx)(m.Combobox.HiddenInput,{value:eg,name:K,form:X,disabled:z,...en})]})});b.classes={...h.InputBase.classes,...m.Combobox.classes},b.displayName="@mantine/core/Select"},16173:(e,t,r)=>{"use strict";r.d(t,{TimelineItem:()=>m});var o=r(95155);r(12115);var n=r(56204),a=r(71180),l=r(3131),i=r(43664),s=r(69604),c=r(36960),u=r(27886),d=r(31196);let p={},m=(0,c.factory)((e,t)=>{let{classNames:r,className:c,style:d,styles:m,vars:v,__active:f,__align:h,__lineActive:g,__vars:b,bullet:y,radius:x,color:w,lineVariant:C,children:S,title:k,mod:E,...P}=(0,i.useProps)("TimelineItem",p,e),j=(0,u._)(),R=(0,l.useMantineTheme)(),D={classNames:r,styles:m};return(0,o.jsxs)(s.Box,{...j.getStyles("item",{...D,className:c,style:d}),mod:[{"line-active":g,active:f},E],ref:t,__vars:{"--tli-radius":x?(0,n.getRadius)(x):void 0,"--tli-color":w?(0,a.getThemeColor)(w,R):void 0,"--tli-border-style":C||void 0},...P,children:[(0,o.jsx)(s.Box,{...j.getStyles("itemBullet",D),mod:{"with-child":!!y,align:h,active:f},children:y}),(0,o.jsxs)("div",{...j.getStyles("itemBody",D),children:[k&&(0,o.jsx)("div",{...j.getStyles("itemTitle",D),children:k}),(0,o.jsx)("div",{...j.getStyles("itemContent",D),children:S})]})]})});m.classes=d.A,m.displayName="@mantine/core/TimelineItem"},16188:(e,t,r)=>{"use strict";r.d(t,{HeadlessMantineProvider:()=>f,MantineProvider:()=>v});var o=r(95155),n=r(47703),a=r(13656),l=r(19224),i=r(11187),s=r(5903);r(12115);var c=r(3131);function u(){let e=(0,c.useMantineTheme)(),t=(0,a.useMantineStyleNonce)(),r=(0,l.keys)(e.breakpoints).reduce((t,r)=>{let o=e.breakpoints[r].includes("px"),n=(0,i.px)(e.breakpoints[r]),a=o?"".concat(n-.1,"px"):(0,s.em)(n-.1),l=o?"".concat(n,"px"):(0,s.em)(n);return"".concat(t,"@media (max-width: ").concat(a,") {.mantine-visible-from-").concat(r," {display: none !important;}}@media (min-width: ").concat(l,") {.mantine-hidden-from-").concat(r," {display: none !important;}}")},"");return(0,o.jsx)("style",{"data-mantine-styles":"classes",nonce:null==t?void 0:t(),dangerouslySetInnerHTML:{__html:r}})}var d=r(31510),p=r(52223),m=r(73141);function v(e){let{theme:t,children:r,getStyleNonce:l,withStaticClasses:i=!0,withGlobalClasses:s=!0,deduplicateCssVariables:v=!0,withCssVariables:f=!0,cssVariablesSelector:h=":root",classNamesPrefix:g="mantine",colorSchemeManager:b=(0,n.localStorageColorSchemeManager)(),defaultColorScheme:y="light",getRootElement:x=()=>document.documentElement,cssVariablesResolver:w,forceColorScheme:C,stylesTransform:S,env:k}=e,{colorScheme:E,setColorScheme:P,clearColorScheme:j}=(0,p.useProviderColorScheme)({defaultColorScheme:y,forceColorScheme:C,manager:b,getRootElement:x});return!function(e){let{respectReducedMotion:t,getRootElement:r}=e;(0,m.useIsomorphicEffect)(()=>{if(t){var e;null==(e=r())||e.setAttribute("data-respect-reduced-motion","true")}},[t])}({respectReducedMotion:(null==t?void 0:t.respectReducedMotion)||!1,getRootElement:x}),(0,o.jsx)(a.MantineContext.Provider,{value:{colorScheme:E,setColorScheme:P,clearColorScheme:j,getRootElement:x,classNamesPrefix:g,getStyleNonce:l,cssVariablesResolver:w,cssVariablesSelector:h,withStaticClasses:i,stylesTransform:S,env:k},children:(0,o.jsxs)(c.MantineThemeProvider,{theme:t,children:[f&&(0,o.jsx)(d.MantineCssVariables,{cssVariablesSelector:h,deduplicateCssVariables:v}),s&&(0,o.jsx)(u,{}),r]})})}function f(e){let{children:t,theme:r}=e;return(0,o.jsx)(a.MantineContext.Provider,{value:{colorScheme:"auto",setColorScheme:()=>{},clearColorScheme:()=>{},getRootElement:()=>document.documentElement,classNamesPrefix:"mantine",cssVariablesSelector:":root",withStaticClasses:!1,headless:!0},children:(0,o.jsx)(c.MantineThemeProvider,{theme:r,children:t})})}v.displayName="@mantine/core/MantineProvider",f.displayName="@mantine/core/HeadlessMantineProvider"},16403:(e,t,r)=>{"use strict";r.d(t,{ComboboxEventsTarget:()=>p});var o=r(12115),n=r(88551),a=r(10866);r(95155);var l=r(72200),i=r(43664),s=r(36960),c=r(3826),u=r(85351);let d={refProp:"ref",targetType:"input",withKeyboardNavigation:!0,withAriaAttributes:!0,withExpandedAttribute:!1,autoComplete:"off"},p=(0,s.factory)((e,t)=>{let{children:r,refProp:s,withKeyboardNavigation:p,withAriaAttributes:m,withExpandedAttribute:v,targetType:f,autoComplete:h,...g}=(0,i.useProps)("ComboboxEventsTarget",d,e);if(!(0,a.isElement)(r))throw Error("Combobox.EventsTarget component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");let b=(0,c.A)(),y=(0,u.useComboboxTargetProps)({targetType:f,withAriaAttributes:m,withKeyboardNavigation:p,withExpandedAttribute:v,onKeyDown:r.props.onKeyDown,autoComplete:h});return(0,o.cloneElement)(r,{...y,...g,[s]:(0,n.useMergedRef)(t,b.store.targetRef,(0,l.getRefProp)(r))})});p.displayName="@mantine/core/ComboboxEventsTarget"},16505:(e,t,r)=>{"use strict";r.d(t,{MultiSelect:()=>C});var o=r(95155),n=r(12115),a=r(64173),l=r(57613),i=r(86028),s=r(53791),c=r(43664),u=r(99537),d=r(36960),p=r(79483),m=r(48509),v=r(14449),f=r(75843),h=r(87160),g=r(24225),b=r(68001),y=r(28843),x=r(83731);let w={maxValues:1/0,withCheckIcon:!0,checkIconPosition:"left",hiddenInputValuesDivider:","},C=(0,d.factory)((e,t)=>{let r=(0,c.useProps)("MultiSelect",w,e),{classNames:d,className:g,style:C,styles:S,unstyled:k,vars:E,size:P,value:j,defaultValue:R,onChange:D,onKeyDown:I,variant:A,data:T,dropdownOpened:_,defaultDropdownOpened:M,onDropdownOpen:N,onDropdownClose:z,selectFirstOptionOnChange:O,onOptionSubmit:L,comboboxProps:B,filter:F,limit:V,withScrollArea:H,maxDropdownHeight:U,searchValue:G,defaultSearchValue:W,onSearchChange:q,readOnly:K,disabled:X,onFocus:Z,onBlur:Y,radius:Q,rightSection:$,rightSectionWidth:J,rightSectionPointerEvents:ee,rightSectionProps:et,leftSection:er,leftSectionWidth:eo,leftSectionPointerEvents:en,leftSectionProps:ea,inputContainer:el,inputWrapperOrder:ei,withAsterisk:es,labelProps:ec,descriptionProps:eu,errorProps:ed,wrapperProps:ep,description:em,label:ev,error:ef,maxValues:eh,searchable:eg,nothingFoundMessage:eb,withCheckIcon:ey,checkIconPosition:ex,hidePickedOptions:ew,withErrorStyles:eC,name:eS,form:ek,id:eE,clearable:eP,clearButtonProps:ej,hiddenInputProps:eR,placeholder:eD,hiddenInputValuesDivider:eI,required:eA,mod:eT,renderOption:e_,onRemove:eM,onClear:eN,scrollAreaProps:ez,chevronColor:eO,...eL}=r,eB=(0,a.useId)(eE),eF=(0,p.getParsedComboboxData)(T),eV=(0,m.getOptionsLockup)(eF),eH=(0,h.useCombobox)({opened:_,defaultOpened:M,onDropdownOpen:N,onDropdownClose:()=>{null==z||z(),eH.resetSelectedOption()}}),{styleProps:eU,rest:{type:eG,autoComplete:eW,...eq}}=(0,u.extractStyleProps)(eL),[eK,eX]=(0,l.useUncontrolled)({value:j,defaultValue:R,finalValue:[],onChange:D}),[eZ,eY]=(0,l.useUncontrolled)({value:G,defaultValue:W,finalValue:"",onChange:q}),eQ=e=>{eY(e),eH.resetSelectedOption()},e$=(0,s.useStyles)({name:"MultiSelect",classes:{},props:r,classNames:d,styles:S,unstyled:k}),{resolvedClassNames:eJ,resolvedStyles:e0}=(0,i.useResolvedStylesApi)({props:r,styles:S,classNames:d}),e1=eK.map((e,t)=>{var r,n;return(0,o.jsx)(b.Pill,{withRemoveButton:!K&&!(null==(r=eV[e])?void 0:r.disabled),onRemove:()=>{eX(eK.filter(t=>e!==t)),null==eM||eM(e)},unstyled:k,disabled:X,...e$("pill"),children:(null==(n=eV[e])?void 0:n.label)||e},"".concat(e,"-").concat(t))});(0,n.useEffect)(()=>{O&&eH.selectFirstOption()},[O,eZ]);let e5=(0,o.jsx)(v.Combobox.ClearButton,{...ej,onClear:()=>{null==eN||eN(),eX([]),eQ("")}}),e6=function(e){let{data:t,value:r}=e,o=r.map(e=>e.trim().toLowerCase());return t.reduce((e,t)=>((0,x.isOptionsGroup)(t)?e.push({group:t.group,items:t.items.filter(e=>-1===o.indexOf(e.value.toLowerCase().trim()))}):-1===o.indexOf(t.value.toLowerCase().trim())&&e.push(t),e),[])}({data:eF,value:eK}),e9=eP&&eK.length>0&&!X&&!K;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(v.Combobox,{store:eH,classNames:eJ,styles:e0,unstyled:k,size:P,readOnly:K,__staticSelector:"MultiSelect",onOptionSubmit:e=>{null==L||L(e),eQ(""),eH.updateSelectedOptionIndex("selected"),eK.includes(eV[e].value)?(eX(eK.filter(t=>t!==eV[e].value)),null==eM||eM(eV[e].value)):eK.length<eh&&eX([...eK,eV[e].value])},...B,children:[(0,o.jsx)(v.Combobox.DropdownTarget,{children:(0,o.jsx)(y.PillsInput,{...eU,__staticSelector:"MultiSelect",classNames:eJ,styles:e0,unstyled:k,size:P,className:g,style:C,variant:A,disabled:X,radius:Q,__defaultRightSection:(0,o.jsx)(v.Combobox.Chevron,{size:P,error:ef,unstyled:k,color:eO}),__clearSection:e5,__clearable:e9,rightSection:$,rightSectionPointerEvents:ee||(e5?"all":"none"),rightSectionWidth:J,rightSectionProps:et,leftSection:er,leftSectionWidth:eo,leftSectionPointerEvents:en,leftSectionProps:ea,inputContainer:el,inputWrapperOrder:ei,withAsterisk:es,labelProps:ec,descriptionProps:eu,errorProps:ed,wrapperProps:ep,description:em,label:ev,error:ef,withErrorStyles:eC,__stylesApiProps:{...r,rightSectionPointerEvents:ee||(e9?"all":"none"),multiline:!0},pointer:!eg,onClick:()=>eg?eH.openDropdown():eH.toggleDropdown(),"data-expanded":eH.dropdownOpened||void 0,id:eB,required:eA,mod:eT,children:(0,o.jsxs)(b.Pill.Group,{disabled:X,unstyled:k,...e$("pillsList"),children:[e1,(0,o.jsx)(v.Combobox.EventsTarget,{autoComplete:eW,children:(0,o.jsx)(y.PillsInput.Field,{...eq,ref:t,id:eB,placeholder:eD,type:eg||eD?"visible":"hidden",...e$("inputField"),unstyled:k,onFocus:e=>{null==Z||Z(e),eg&&eH.openDropdown()},onBlur:e=>{null==Y||Y(e),eH.closeDropdown(),eQ("")},onKeyDown:e=>{null==I||I(e)," "!==e.key||eg||(e.preventDefault(),eH.toggleDropdown()),"Backspace"===e.key&&0===eZ.length&&eK.length>0&&(null==eM||eM(eK[eK.length-1]),eX(eK.slice(0,eK.length-1)))},value:eZ,onChange:e=>{eQ(e.currentTarget.value),eg&&eH.openDropdown(),O&&eH.selectFirstOption()},disabled:X,readOnly:K||!eg,pointer:!eg})})]})})}),(0,o.jsx)(f.OptionsDropdown,{data:ew?e6:eF,hidden:K||X,filter:F,search:eZ,limit:V,hiddenWhenEmpty:!eb,withScrollArea:H,maxDropdownHeight:U,filterOptions:eg,value:eK,checkIconPosition:ex,withCheckIcon:ey,nothingFoundMessage:eb,unstyled:k,labelId:ev?"".concat(eB,"-label"):void 0,"aria-label":ev?void 0:eL["aria-label"],renderOption:e_,scrollAreaProps:ez})]}),(0,o.jsx)(v.Combobox.HiddenInput,{name:eS,valuesDivider:eI,value:eK,form:ek,disabled:X,...eR})]})});C.classes={...g.InputBase.classes,...v.Combobox.classes},C.displayName="@mantine/core/MultiSelect"},17077:(e,t,r)=>{"use strict";function o(e){let{value:t,min:r,max:o}=e;return Math.min(Math.max((t-r)/(o-r)*100,0),100)}r.d(t,{E:()=>o})},18229:(e,t,r)=>{"use strict";r.d(t,{SimpleGrid:()=>C});var o=r(95155);r(12115);var n=r(43664),a=r(53791),l=r(46390),i=r(69604),s=r(36960),c=r(19224),u=r(11187),d=r(1526),p=r(56204),m=r(61758),v=r(53288),f=r(3131),h=r(58976);function g(e){var t;let{spacing:r,verticalSpacing:n,cols:a,selector:l}=e,i=(0,f.useMantineTheme)(),s=void 0===n?r:n,u=(0,d.filterProps)({"--sg-spacing-x":(0,p.getSpacing)((0,v.getBaseValue)(r)),"--sg-spacing-y":(0,p.getSpacing)((0,v.getBaseValue)(s)),"--sg-cols":null==(t=(0,v.getBaseValue)(a))?void 0:t.toString()}),g=(0,c.keys)(i.breakpoints).reduce((e,t)=>(e[t]||(e[t]={}),"object"==typeof r&&void 0!==r[t]&&(e[t]["--sg-spacing-x"]=(0,p.getSpacing)(r[t])),"object"==typeof s&&void 0!==s[t]&&(e[t]["--sg-spacing-y"]=(0,p.getSpacing)(s[t])),"object"==typeof a&&void 0!==a[t]&&(e[t]["--sg-cols"]=a[t]),e),{}),b=(0,m.getSortedBreakpoints)((0,c.keys)(g),i.breakpoints).filter(e=>(0,c.keys)(g[e.value]).length>0).map(e=>({query:"(min-width: ".concat(i.breakpoints[e.value],")"),styles:g[e.value]}));return(0,o.jsx)(h.InlineStyles,{styles:u,media:b,selector:l})}function b(e){return"object"==typeof e&&null!==e?(0,c.keys)(e):[]}function y(e){var t;let{spacing:r,verticalSpacing:n,cols:a,selector:l}=e,i=void 0===n?r:n,s=(0,d.filterProps)({"--sg-spacing-x":(0,p.getSpacing)((0,v.getBaseValue)(r)),"--sg-spacing-y":(0,p.getSpacing)((0,v.getBaseValue)(i)),"--sg-cols":null==(t=(0,v.getBaseValue)(a))?void 0:t.toString()}),c=function(e){let{spacing:t,verticalSpacing:r,cols:o}=e;return Array.from(new Set([...b(t),...b(r),...b(o)])).sort((e,t)=>(0,u.px)(e)-(0,u.px)(t))}({spacing:r,verticalSpacing:n,cols:a}),m=c.reduce((e,t)=>(e[t]||(e[t]={}),"object"==typeof r&&void 0!==r[t]&&(e[t]["--sg-spacing-x"]=(0,p.getSpacing)(r[t])),"object"==typeof i&&void 0!==i[t]&&(e[t]["--sg-spacing-y"]=(0,p.getSpacing)(i[t])),"object"==typeof a&&void 0!==a[t]&&(e[t]["--sg-cols"]=a[t]),e),{}),f=c.map(e=>({query:"simple-grid (min-width: ".concat(e,")"),styles:m[e]}));return(0,o.jsx)(h.InlineStyles,{styles:s,container:f,selector:l})}var x={container:"m_925c2d2c",root:"m_2415a157"};let w={cols:1,spacing:"md",type:"media"},C=(0,s.factory)((e,t)=>{let r=(0,n.useProps)("SimpleGrid",w,e),{classNames:s,className:c,style:u,styles:d,unstyled:p,vars:m,cols:v,verticalSpacing:f,spacing:h,type:b,...C}=r,S=(0,a.useStyles)({name:"SimpleGrid",classes:x,props:r,className:c,style:u,classNames:s,styles:d,unstyled:p,vars:m}),k=(0,l.useRandomClassName)();return"container"===b?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(y,{...r,selector:".".concat(k)}),(0,o.jsx)("div",{...S("container"),children:(0,o.jsx)(i.Box,{ref:t,...S("root",{className:k}),...C})})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(g,{...r,selector:".".concat(k)}),(0,o.jsx)(i.Box,{ref:t,...S("root",{className:k}),...C})]})});C.classes=x,C.displayName="@mantine/core/SimpleGrid"},18349:(e,t,r)=>{"use strict";r.d(t,{ComboboxDropdown:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(60266),i=r(3826),s=r(19192);let c={},u=(0,a.factory)((e,t)=>{let{classNames:r,styles:a,className:s,style:u,hidden:d,...p}=(0,n.useProps)("ComboboxDropdown",c,e),m=(0,i.A)();return(0,o.jsx)(l.Popover.Dropdown,{...p,ref:t,role:"presentation","data-hidden":d||void 0,...m.getStyles("dropdown",{className:s,style:u,classNames:r,styles:a})})});u.classes=s.A,u.displayName="@mantine/core/ComboboxDropdown"},18489:(e,t,r)=>{"use strict";r.d(t,{useStateHistory:()=>n});var o=r(12115);function n(e){let[t,r]=(0,o.useState)({history:[e],current:0}),n=(0,o.useCallback)(e=>r(t=>{let r=[...t.history.slice(0,t.current+1),e];return{history:r,current:r.length-1}}),[]),a=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return r(t=>({history:t.history,current:Math.max(0,t.current-e)}))},[]),l=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return r(t=>({history:t.history,current:Math.min(t.history.length-1,t.current+e)}))},[]),i=(0,o.useCallback)(()=>{r({history:[e],current:0})},[e]),s=(0,o.useMemo)(()=>({back:a,forward:l,reset:i,set:n}),[a,l,i,n]);return[t.history[t.current],s,t]}},19192:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={dropdown:"m_88b62a41",search:"m_985517d8",options:"m_b2821a6e",option:"m_92253aa5",empty:"m_2530cd1d",header:"m_858f94bd",footer:"m_82b967cb",group:"m_254f3e4f",groupLabel:"m_2bb2e9e5",chevron:"m_2943220b",optionsDropdownOption:"m_390b5f4",optionsDropdownCheckIcon:"m_8ee53fc2"}},19606:(e,t,r)=>{"use strict";r.d(t,{ColorPicker:()=>P});var o=r(95155),n=r(12115),a=r(57613),l=r(84237),i=r(56204),s=r(68918),c=r(43664),u=r(53791),d=r(69604),p=r(36960),m=r(34666),v=r(33263),f=r(70130),h=r(4463),g=r(39186),b=r(14951),y=r(43461),x=r(87323);function w(e){let{className:t,onChange:r,onChangeEnd:a,value:l,saturationLabel:i,focusable:s=!0,size:c,color:u,onScrubStart:p,onScrubEnd:m,...v}=e,{getStyles:g}=(0,f.W)(),[b,w]=(0,n.useState)({x:l.s/100,y:1-l.v/100}),C=(0,n.useRef)(b),{ref:S}=(0,y.useMove)(e=>{let{x:t,y:o}=e;C.current={x:t,y:o},r({s:Math.round(100*t),v:Math.round((1-o)*100)})},{onScrubEnd:()=>{let{x:e,y:t}=C.current;a({s:Math.round(100*e),v:Math.round((1-t)*100)}),null==m||m()},onScrubStart:p});(0,n.useEffect)(()=>{w({x:l.s/100,y:1-l.v/100})},[l.s,l.v]);let k=(e,t)=>{e.preventDefault();let o=(0,y.clampUseMovePosition)(t);r({s:Math.round(100*o.x),v:Math.round((1-o.y)*100)}),a({s:Math.round(100*o.x),v:Math.round((1-o.y)*100)})};return(0,o.jsxs)(d.Box,{...g("saturation"),ref:S,...v,role:"slider","aria-label":i,"aria-valuenow":b.x,"aria-valuetext":(0,h.convertHsvaTo)("rgba",l),tabIndex:s?0:-1,onKeyDown:e=>{switch(e.key){case"ArrowUp":k(e,{y:b.y-.05,x:b.x});break;case"ArrowDown":k(e,{y:b.y+.05,x:b.x});break;case"ArrowRight":k(e,{x:b.x+.05,y:b.y});break;case"ArrowLeft":k(e,{x:b.x-.05,y:b.y})}},children:[(0,o.jsx)("div",{...g("saturationOverlay",{style:{backgroundColor:"hsl(".concat(l.h,", 100%, 50%)")}})}),(0,o.jsx)("div",{...g("saturationOverlay",{style:{backgroundImage:"linear-gradient(90deg, #fff, transparent)"}})}),(0,o.jsx)("div",{...g("saturationOverlay",{style:{backgroundImage:"linear-gradient(0deg, #000, transparent)"}})}),(0,o.jsx)(x.z,{position:b,...g("thumb",{style:{backgroundColor:u}})})]})}w.displayName="@mantine/core/Saturation";let C=(0,n.forwardRef)((e,t)=>{let{className:r,datatype:a,setValue:l,onChangeEnd:i,size:s,focusable:c,data:u,swatchesPerRow:p,...v}=e,h=(0,f.W)(),g=u.map((e,t)=>(0,n.createElement)(m.ColorSwatch,{...h.getStyles("swatch"),unstyled:h.unstyled,component:"button",type:"button",color:e,key:t,radius:"sm",onClick:()=>{l(e),null==i||i(e)},"aria-label":e,tabIndex:c?0:-1,"data-swatch":!0}));return(0,o.jsx)(d.Box,{...h.getStyles("swatches"),ref:t,...v,children:g})});C.displayName="@mantine/core/Swatches";var S=r(90296);let k={swatchesPerRow:7,withPicker:!0,focusable:!0,size:"md",__staticSelector:"ColorPicker"},E=(0,s.createVarsResolver)((e,t)=>{let{size:r,swatchesPerRow:o}=t;return{wrapper:{"--cp-preview-size":(0,i.getSize)(r,"cp-preview-size"),"--cp-width":(0,i.getSize)(r,"cp-width"),"--cp-body-spacing":(0,i.getSpacing)(r),"--cp-swatch-size":"".concat(100/o,"%"),"--cp-thumb-size":(0,i.getSize)(r,"cp-thumb-size"),"--cp-saturation-height":(0,i.getSize)(r,"cp-saturation-height")}}}),P=(0,p.factory)((e,t)=>{let r=(0,c.useProps)("ColorPicker",k,e),{classNames:i,className:s,style:p,styles:y,unstyled:x,vars:P,format:j="hex",value:R,defaultValue:D,onChange:I,onChangeEnd:A,withPicker:T,size:_,saturationLabel:M,hueLabel:N,alphaLabel:z,focusable:O,swatches:L,swatchesPerRow:B,fullWidth:F,onColorSwatchClick:V,__staticSelector:H,mod:U,...G}=r,W=(0,u.useStyles)({name:H,props:r,classes:S.A,className:s,style:p,classNames:i,styles:y,unstyled:x,rootSelector:"wrapper",vars:P,varsResolver:E}),q=(0,n.useRef)(j||"hex"),K=(0,n.useRef)(""),X=(0,n.useRef)(-1),Z=(0,n.useRef)(!1),Y="hexa"===j||"rgba"===j||"hsla"===j,[Q,$,J]=(0,a.useUncontrolled)({value:R,defaultValue:D,finalValue:"#FFFFFF",onChange:I}),[ee,et]=(0,n.useState)((0,g.parseColor)(Q)),er=()=>{window.clearTimeout(X.current),Z.current=!0},eo=()=>{window.clearTimeout(X.current),X.current=window.setTimeout(()=>{Z.current=!1},200)},en=e=>{et(t=>{let r={...t,...e};return K.current=(0,h.convertHsvaTo)(q.current,r),r}),$(K.current)};return(0,l.useDidUpdate)(()=>{"string"==typeof R&&(0,g.isColorValid)(R)&&!Z.current&&et((0,g.parseColor)(R))},[R]),(0,l.useDidUpdate)(()=>{q.current=j||"hex",$((0,h.convertHsvaTo)(q.current,ee))},[j]),(0,o.jsx)(f.v,{value:{getStyles:W,unstyled:x},children:(0,o.jsxs)(d.Box,{ref:t,...W("wrapper"),size:_,mod:[{"full-width":F},U],...G,children:[T&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(w,{value:ee,onChange:en,onChangeEnd:e=>{let{s:t,v:r}=e;return null==A?void 0:A((0,h.convertHsvaTo)(q.current,{...ee,s:t,v:r}))},color:Q,size:_,focusable:O,saturationLabel:M,onScrubStart:er,onScrubEnd:eo}),(0,o.jsxs)("div",{...W("body"),children:[(0,o.jsxs)("div",{...W("sliders"),children:[(0,o.jsx)(b.HueSlider,{value:ee.h,onChange:e=>en({h:e}),onChangeEnd:e=>null==A?void 0:A((0,h.convertHsvaTo)(q.current,{...ee,h:e})),size:_,focusable:O,"aria-label":N,onScrubStart:er,onScrubEnd:eo}),Y&&(0,o.jsx)(v.AlphaSlider,{value:ee.a,onChange:e=>en({a:e}),onChangeEnd:e=>{null==A||A((0,h.convertHsvaTo)(q.current,{...ee,a:e}))},size:_,color:(0,h.convertHsvaTo)("hex",ee),focusable:O,"aria-label":z,onScrubStart:er,onScrubEnd:eo})]}),Y&&(0,o.jsx)(m.ColorSwatch,{color:Q,radius:"sm",size:"var(--cp-preview-size)",...W("preview")})]})]}),Array.isArray(L)&&(0,o.jsx)(C,{data:L,swatchesPerRow:B,focusable:O,setValue:$,onChangeEnd:e=>{let t=(0,h.convertHsvaTo)(j,(0,g.parseColor)(e));null==V||V(t),null==A||A(t),J||et((0,g.parseColor)(e))}})]})})});P.classes=S.A,P.displayName="@mantine/core/ColorPicker"},19997:(e,t,r)=>{"use strict";r.d(t,{normalizeRadialValue:()=>a,useRadialMove:()=>l});var o=r(12115),n=r(96963);function a(e,t){var r,o,a;let l=(0,n.clamp)(e,0,360),i=Math.ceil(l/t),s=Math.round(l/t);return o=i>=l/t?i*t==360?0:i*t:s*t,a=(null==(r=t.toString().split(".")[1])?void 0:r.length)||0,parseFloat(o.toFixed(a))}function l(e){let{step:t=.01,onChangeEnd:r,onScrubStart:n,onScrubEnd:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=(0,o.useRef)(!1),[s,c]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{i.current=!0},[]),{ref:(0,o.useCallback)(o=>{let i=function(n){let l=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(o){o.style.userSelect="none";let i=a(function(e,t){let r=function(e){let t=e.getBoundingClientRect();return[t.left+t.width/2,t.top+t.height/2]}(t);return 360-(180/Math.PI*Math.atan2(e[0]-r[0],e[1]-r[1])+180)}([n.clientX,n.clientY],o),t||1);e(i),l&&(null==r||r(i))}},s=()=>{null==n||n(),c(!0),document.addEventListener("mousemove",p,!1),document.addEventListener("mouseup",m,!1),document.addEventListener("touchmove",v,{passive:!1}),document.addEventListener("touchend",f,!1)},u=()=>{null==l||l(),c(!1),document.removeEventListener("mousemove",p,!1),document.removeEventListener("mouseup",m,!1),document.removeEventListener("touchmove",v,!1),document.removeEventListener("touchend",f,!1)},d=e=>{s(),i(e)},p=e=>{i(e)},m=e=>{i(e,!0),u()},v=e=>{e.preventDefault(),i(e.touches[0])},f=e=>{i(e.changedTouches[0],!0),u()},h=e=>{e.preventDefault(),s(),i(e.touches[0])};return null==o||o.addEventListener("mousedown",d),null==o||o.addEventListener("touchstart",h,{passive:!1}),()=>{o&&(o.removeEventListener("mousedown",d),o.removeEventListener("touchstart",h))}},[e]),active:s}}},20950:(e,t,r)=>{"use strict";r.d(t,{T:()=>n,g:()=>a}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Drawer component was not found in tree")},21003:(e,t,r)=>{"use strict";r.d(t,{useDocumentTitle:()=>n});var o=r(73141);function n(e){(0,o.useIsomorphicEffect)(()=>{"string"==typeof e&&e.trim().length>0&&(document.title=e.trim())},[e])}},21145:(e,t,r)=>{"use strict";r.d(t,{useInputState:()=>n});var o=r(12115);function n(e){let[t,r]=(0,o.useState)(e);return[t,e=>{if(e)if("function"==typeof e)r(e);else if("object"==typeof e&&"nativeEvent"in e){let{currentTarget:t}=e;r("checkbox"===t.type?t.checked:t.value)}else r(e);else r(e)}]}},21369:(e,t,r)=>{"use strict";r.d(t,{g:()=>i});var o=r(95155),n=r(12115),a=r(69604),l=r(97446);let i=(0,n.forwardRef)((e,t)=>{let{size:r,disabled:n,variant:i,color:s,thumbSize:c,radius:u,...d}=e,{getStyles:p}=(0,l.s)();return(0,o.jsx)(a.Box,{tabIndex:-1,variant:i,size:r,ref:t,...p("root"),...d})});i.displayName="@mantine/core/SliderRoot"},21481:(e,t,r)=>{"use strict";r.d(t,{AvatarGroup:()=>v});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u=r(54154),d=r(64796);let p={},m=(0,a.createVarsResolver)((e,t)=>{let{spacing:r}=t;return{group:{"--ag-spacing":(0,n.getSpacing)(r)}}}),v=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("AvatarGroup",p,e),{classNames:n,className:a,style:c,styles:v,unstyled:f,vars:h,spacing:g,...b}=r,y=(0,i.useStyles)({name:"AvatarGroup",classes:d.A,props:r,className:a,style:c,classNames:n,styles:v,unstyled:f,vars:h,varsResolver:m,rootSelector:"group"});return(0,o.jsx)(u.G,{value:!0,children:(0,o.jsx)(s.Box,{ref:t,...y("group"),...b})})});v.classes=d.A,v.displayName="@mantine/core/AvatarGroup"},22185:(e,t,r)=>{"use strict";r.d(t,{readLocalStorageValue:()=>a,useLocalStorage:()=>n});var o=r(40160);function n(e){return(0,o.w)("localStorage","use-local-storage")(e)}let a=(0,o.d)("localStorage")},22856:(e,t,r)=>{"use strict";r.d(t,{O:()=>n,f:()=>a}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Tabs component was not found in the tree")},25151:(e,t,r)=>{"use strict";function o(e){return"string"!=typeof e?"":e.charAt(0).toLowerCase()+e.slice(1)}r.d(t,{lowerFirst:()=>o})},25345:(e,t,r)=>{"use strict";r.d(t,{PasswordInput:()=>x});var o=r(95155),n=r(52596),a=r(64173),l=r(57613);r(12115);var i=r(56204),s=r(68918),c=r(86028),u=r(53791),d=r(43664),p=r(99537),m=r(36960),v=r(81001),f=r(54853),h=r(24225),g={root:"m_f61ca620",input:"m_ccf8da4c",innerInput:"m_f2d85dd2",visibilityToggle:"m_b1072d44"};let b={visibilityToggleIcon:e=>{let{reveal:t}=e;return(0,o.jsx)("svg",{viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{width:"var(--psi-icon-size)",height:"var(--psi-icon-size)"},children:(0,o.jsx)("path",{d:t?"M13.3536 2.35355C13.5488 2.15829 13.5488 1.84171 13.3536 1.64645C13.1583 1.45118 12.8417 1.45118 12.6464 1.64645L10.6828 3.61012C9.70652 3.21671 8.63759 3 7.5 3C4.30786 3 1.65639 4.70638 0.0760002 7.23501C-0.0253338 7.39715 -0.0253334 7.60288 0.0760014 7.76501C0.902945 9.08812 2.02314 10.1861 3.36061 10.9323L1.64645 12.6464C1.45118 12.8417 1.45118 13.1583 1.64645 13.3536C1.84171 13.5488 2.15829 13.5488 2.35355 13.3536L4.31723 11.3899C5.29348 11.7833 6.36241 12 7.5 12C10.6921 12 13.3436 10.2936 14.924 7.76501C15.0253 7.60288 15.0253 7.39715 14.924 7.23501C14.0971 5.9119 12.9769 4.81391 11.6394 4.06771L13.3536 2.35355ZM9.90428 4.38861C9.15332 4.1361 8.34759 4 7.5 4C4.80285 4 2.52952 5.37816 1.09622 7.50001C1.87284 8.6497 2.89609 9.58106 4.09974 10.1931L9.90428 4.38861ZM5.09572 10.6114L10.9003 4.80685C12.1039 5.41894 13.1272 6.35031 13.9038 7.50001C12.4705 9.62183 10.1971 11 7.5 11C6.65241 11 5.84668 10.8639 5.09572 10.6114Z":"M7.5 11C4.80285 11 2.52952 9.62184 1.09622 7.50001C2.52952 5.37816 4.80285 4 7.5 4C10.1971 4 12.4705 5.37816 13.9038 7.50001C12.4705 9.62183 10.1971 11 7.5 11ZM7.5 3C4.30786 3 1.65639 4.70638 0.0760002 7.23501C-0.0253338 7.39715 -0.0253334 7.60288 0.0760014 7.76501C1.65639 10.2936 4.30786 12 7.5 12C10.6921 12 13.3436 10.2936 14.924 7.76501C15.0253 7.60288 15.0253 7.39715 14.924 7.23501C13.3436 4.70638 10.6921 3 7.5 3ZM7.5 9.5C8.60457 9.5 9.5 8.60457 9.5 7.5C9.5 6.39543 8.60457 5.5 7.5 5.5C6.39543 5.5 5.5 6.39543 5.5 7.5C5.5 8.60457 6.39543 9.5 7.5 9.5Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})}},y=(0,s.createVarsResolver)((e,t)=>{let{size:r}=t;return{root:{"--psi-icon-size":(0,i.getSize)(r,"psi-icon-size"),"--psi-button-size":(0,i.getSize)(r,"psi-button-size")}}}),x=(0,m.factory)((e,t)=>{var r;let i=(0,d.useProps)("PasswordInput",b,e),{classNames:s,className:m,style:h,styles:x,unstyled:w,vars:C,required:S,error:k,leftSection:E,disabled:P,id:j,variant:R,inputContainer:D,description:I,label:A,size:T,errorProps:_,descriptionProps:M,labelProps:N,withAsterisk:z,inputWrapperOrder:O,wrapperProps:L,radius:B,rightSection:F,rightSectionWidth:V,rightSectionPointerEvents:H,leftSectionWidth:U,visible:G,defaultVisible:W,onVisibilityChange:q,visibilityToggleIcon:K,visibilityToggleButtonProps:X,rightSectionProps:Z,leftSectionProps:Y,leftSectionPointerEvents:Q,withErrorStyles:$,mod:J,...ee}=i,et=(0,a.useId)(j),[er,eo]=(0,l.useUncontrolled)({value:G,defaultValue:W,finalValue:!1,onChange:q}),en=()=>eo(!er),ea=(0,u.useStyles)({name:"PasswordInput",classes:g,props:i,className:m,style:h,classNames:s,styles:x,unstyled:w,vars:C,varsResolver:y}),{resolvedClassNames:el,resolvedStyles:ei}=(0,c.useResolvedStylesApi)({classNames:s,styles:x,props:i}),{styleProps:es,rest:ec}=(0,p.extractStyleProps)(ee),eu=(null==_?void 0:_.id)||"".concat(et,"-error"),ed=(null==M?void 0:M.id)||"".concat(et,"-description"),ep=!!k&&"boolean"!=typeof k,em=!!I,ev="".concat(ep?eu:""," ").concat(em?ed:""),ef=ev.trim().length>0?ev.trim():void 0,eh=(0,o.jsx)(v.ActionIcon,{...ea("visibilityToggle"),disabled:P,radius:B,"aria-hidden":!X,tabIndex:-1,...X,variant:null!=(r=null==X?void 0:X.variant)?r:"subtle",color:"gray",unstyled:w,onTouchEnd:e=>{var t;e.preventDefault(),null==X||null==(t=X.onTouchEnd)||t.call(X,e),en()},onMouseDown:e=>{var t;e.preventDefault(),null==X||null==(t=X.onMouseDown)||t.call(X,e),en()},onKeyDown:e=>{var t;null==X||null==(t=X.onKeyDown)||t.call(X,e)," "===e.key&&(e.preventDefault(),en())},children:(0,o.jsx)(K,{reveal:er})});return(0,o.jsx)(f.Input.Wrapper,{required:S,id:et,label:A,error:k,description:I,size:T,classNames:el,styles:ei,__staticSelector:"PasswordInput",unstyled:w,withAsterisk:z,inputWrapperOrder:O,inputContainer:D,variant:R,labelProps:{...N,htmlFor:et},descriptionProps:{...M,id:ed},errorProps:{..._,id:eu},mod:J,...ea("root"),...es,...L,children:(0,o.jsx)(f.Input,{component:"div",error:k,leftSection:E,size:T,classNames:{...el,input:(0,n.A)(g.input,el.input)},styles:ei,radius:B,disabled:P,__staticSelector:"PasswordInput",rightSectionWidth:V,rightSection:null!=F?F:eh,variant:R,unstyled:w,leftSectionWidth:U,rightSectionPointerEvents:H||"all",rightSectionProps:Z,leftSectionProps:Y,leftSectionPointerEvents:Q,withAria:!1,withErrorStyles:$,children:(0,o.jsx)("input",{required:S,"data-invalid":!!k||void 0,"data-with-left-section":!!E||void 0,...ea("innerInput"),disabled:P,id:et,ref:t,...ec,"aria-describedby":ef,autoComplete:ec.autoComplete||"off",type:er?"text":"password"})})})});x.classes={...h.InputBase.classes,...g},x.displayName="@mantine/core/PasswordInput"},25409:(e,t,r)=>{"use strict";r.d(t,{useWindowScroll:()=>i});var o=r(12115),n=r(28261);function a(){return"undefined"!=typeof window?{x:window.scrollX,y:window.scrollY}:{x:0,y:0}}function l(e){let{x:t,y:r}=e;if("undefined"!=typeof window){let e={behavior:"smooth"};"number"==typeof t&&(e.left=t),"number"==typeof r&&(e.top=r),window.scrollTo(e)}}function i(){let[e,t]=(0,o.useState)({x:0,y:0});return(0,n.useWindowEvent)("scroll",()=>t(a())),(0,n.useWindowEvent)("resize",()=>t(a())),(0,o.useEffect)(()=>{t(a())},[]),[e,l]}},25484:(e,t,r)=>{"use strict";r.d(t,{Chip:()=>y});var o=r(95155),n=r(64173),a=r(57613);r(12115);var l=r(56204),i=r(68918),s=r(43664),c=r(53791),u=r(99537),d=r(69604),p=r(36960),m=r(27547),v=r(65629),f=r(55436),h={root:"m_f59ffda3",label:"m_be049a53","label--outline":"m_3904c1af","label--filled":"m_fa109255","label--light":"m_f7e165c3",iconWrapper:"m_9ac86df9",checkIcon:"m_d6d72580",input:"m_bde07329"};let g={type:"checkbox"},b=(0,i.createVarsResolver)((e,t)=>{let{size:r,radius:o,variant:n,color:a,autoContrast:i}=t,s=e.variantColorResolver({color:a||e.primaryColor,theme:e,variant:n||"filled",autoContrast:i});return{root:{"--chip-fz":(0,l.getFontSize)(r),"--chip-size":(0,l.getSize)(r,"chip-size"),"--chip-radius":void 0===o?void 0:(0,l.getRadius)(o),"--chip-checked-padding":(0,l.getSize)(r,"chip-checked-padding"),"--chip-padding":(0,l.getSize)(r,"chip-padding"),"--chip-icon-size":(0,l.getSize)(r,"chip-icon-size"),"--chip-bg":a||n?s.background:void 0,"--chip-hover":a||n?s.hover:void 0,"--chip-color":a||n?s.color:void 0,"--chip-bd":a||n?s.border:void 0,"--chip-spacing":(0,l.getSize)(r,"chip-spacing")}}}),y=(0,p.factory)((e,t)=>{let r=(0,s.useProps)("Chip",g,e),{classNames:l,className:i,style:p,styles:f,unstyled:y,vars:x,id:w,checked:C,defaultChecked:S,onChange:k,value:E,wrapperProps:P,type:j,disabled:R,children:D,size:I,variant:A,icon:T,rootRef:_,autoContrast:M,mod:N,...z}=r,O=(0,c.useStyles)({name:"Chip",classes:h,props:r,className:i,style:p,classNames:l,styles:f,unstyled:y,vars:x,varsResolver:b}),L=(0,v.E)(),B=(0,n.useId)(w),{styleProps:F,rest:V}=(0,u.extractStyleProps)(z),[H,U]=(0,a.useUncontrolled)({value:C,defaultValue:S,finalValue:!1,onChange:k}),G=L?{checked:L.isChipSelected(E),onChange:e=>{L.onChange(e),null==k||k(e.currentTarget.checked)},type:L.multiple?"checkbox":"radio"}:{},W=G.checked||H;return(0,o.jsxs)(d.Box,{size:I,variant:A,ref:_,mod:N,...O("root"),...F,...P,children:[(0,o.jsx)("input",{type:j,...O("input"),checked:W,onChange:e=>U(e.currentTarget.checked),id:B,disabled:R,ref:t,value:E,...G,...V}),(0,o.jsxs)("label",{htmlFor:B,"data-checked":W||void 0,"data-disabled":R||void 0,...O("label",{variant:A||"filled"}),children:[W&&(0,o.jsx)("span",{...O("iconWrapper"),children:T||(0,o.jsx)(m.CheckIcon,{...O("checkIcon")})}),(0,o.jsx)("span",{children:D})]})]})});y.classes=h,y.displayName="@mantine/core/Chip",y.Group=f.ChipGroup},25954:(e,t,r)=>{"use strict";function o(e,t){return r=>{if("string"!=typeof r||0===r.trim().length)throw Error(t);return"".concat(e,"-").concat(r)}}r.d(t,{getSafeId:()=>o})},26761:(e,t,r)=>{"use strict";r.d(t,{Slider:()=>I});var o=r(95155),n=r(12115),a=r(57613),l=r(96963),i=r(43461),s=r(88551),c=r(5903),u=r(56204),d=r(57130),p=r(68918),m=r(71180),v=r(43664),f=r(53791),h=r(36960),g=r(53304),b=r(97446),y=r(21369),x=r(44229),w=r(9822),C=r(66143),S=r(30404),k=r(17077),E=r(80159),P=r(41707),j=r(55508);let R={radius:"xl",min:0,max:100,step:1,marks:[],label:e=>e,labelTransitionProps:{transition:"fade",duration:0},thumbLabel:"",showLabelOnHover:!0,scale:e=>e,size:"md"},D=(0,p.createVarsResolver)((e,t)=>{let{size:r,color:o,thumbSize:n,radius:a}=t;return{root:{"--slider-size":(0,u.getSize)(r,"slider-size"),"--slider-color":o?(0,m.getThemeColor)(o,e):void 0,"--slider-radius":void 0===a?void 0:(0,u.getRadius)(a),"--slider-thumb-size":void 0!==n?(0,c.D)(n):"calc(var(--slider-size) * 2)"}}}),I=(0,h.factory)((e,t)=>{let r=(0,v.useProps)("Slider",R,e),{classNames:c,styles:u,value:p,onChange:m,onChangeEnd:h,size:I,min:A,max:T,domain:_,step:M,precision:N,defaultValue:z,name:O,marks:L,label:B,labelTransitionProps:F,labelAlwaysOn:V,thumbLabel:H,showLabelOnHover:U,thumbChildren:G,disabled:W,unstyled:q,scale:K,inverted:X,className:Z,style:Y,vars:Q,hiddenInputProps:$,restrictToMarks:J,thumbProps:ee,...et}=r,er=(0,f.useStyles)({name:"Slider",props:r,classes:j.A,classNames:c,className:Z,styles:u,style:Y,vars:Q,varsResolver:D,unstyled:q}),{dir:eo}=(0,g.useDirection)(),[en,ea]=(0,n.useState)(!1),[el,ei]=(0,a.useUncontrolled)({value:"number"==typeof p?(0,l.clamp)(p,A,T):p,defaultValue:"number"==typeof z?(0,l.clamp)(z,A,T):z,finalValue:(0,l.clamp)(0,A,T),onChange:m}),es=(0,n.useRef)(el),ec=(0,n.useRef)(h);(0,n.useEffect)(()=>{ec.current=h},[h]);let eu=(0,n.useRef)(null),ed=(0,n.useRef)(null),[ep,em]=_||[A,T],ev=(0,k.E)({value:el,min:ep,max:em}),ef=K(el),eh="function"==typeof B?B(ef):B,eg=null!=N?N:(0,E.X)(M),eb=(0,n.useCallback)(e=>{let{x:t}=e;if(!W){let e=(0,C.c)({value:t,min:ep,max:em,step:M,precision:eg}),r=(0,l.clamp)(e,A,T);ei(J&&(null==L?void 0:L.length)?(0,d.findClosestNumber)(r,L.map(e=>e.value)):r),es.current=r}},[W,A,T,ep,em,M,eg,ei,L,J]),ey=(0,n.useCallback)(()=>{if(!W&&ec.current){let e=J&&(null==L?void 0:L.length)?(0,d.findClosestNumber)(es.current,L.map(e=>e.value)):es.current;ec.current(e)}},[W,L,J]),{ref:ex,active:ew}=(0,i.useMove)(eb,{onScrubEnd:ey},eo),eC=(0,n.useCallback)(e=>{!W&&ec.current&&ec.current(e)},[W]);return(0,o.jsx)(b.h,{value:{getStyles:er},children:(0,o.jsxs)(y.g,{...et,ref:(0,s.useMergedRef)(t,eu),onKeyDownCapture:e=>{if(!W){var t,r,o,n,a,l;switch(e.key){case"ArrowUp":{if(e.preventDefault(),null==(t=ed.current)||t.focus(),J&&L){let e=(0,P.C8)(el,L);ei(e),eC(e);break}let r=(0,S.q)(Math.min(Math.max(el+M,A),T),eg);ei(r),eC(r);break}case"ArrowRight":{if(e.preventDefault(),null==(r=ed.current)||r.focus(),J&&L){let e="rtl"===eo?(0,P.Mh)(el,L):(0,P.C8)(el,L);ei(e),eC(e);break}let t=(0,S.q)(Math.min(Math.max("rtl"===eo?el-M:el+M,A),T),eg);ei(t),eC(t);break}case"ArrowDown":{if(e.preventDefault(),null==(o=ed.current)||o.focus(),J&&L){let e=(0,P.Mh)(el,L);ei(e),eC(e);break}let t=(0,S.q)(Math.min(Math.max(el-M,A),T),eg);ei(t),eC(t);break}case"ArrowLeft":{if(e.preventDefault(),null==(n=ed.current)||n.focus(),J&&L){let e="rtl"===eo?(0,P.C8)(el,L):(0,P.Mh)(el,L);ei(e),eC(e);break}let t=(0,S.q)(Math.min(Math.max("rtl"===eo?el+M:el-M,A),T),eg);ei(t),eC(t);break}case"Home":if(e.preventDefault(),null==(a=ed.current)||a.focus(),J&&L){ei((0,P.HE)(L)),eC((0,P.HE)(L));break}ei(A),eC(A);break;case"End":if(e.preventDefault(),null==(l=ed.current)||l.focus(),J&&L){ei((0,P.rq)(L)),eC((0,P.rq)(L));break}ei(T),eC(T)}}},onMouseDownCapture:()=>{var e;return null==(e=eu.current)?void 0:e.focus()},size:I,disabled:W,children:[(0,o.jsx)(w.C,{inverted:X,offset:0,filled:ev,marks:L,min:ep,max:em,value:ef,disabled:W,containerProps:{ref:ex,onMouseEnter:U?()=>ea(!0):void 0,onMouseLeave:U?()=>ea(!1):void 0},children:(0,o.jsx)(x.z,{max:em,min:ep,value:ef,position:ev,dragging:ew,label:eh,ref:ed,labelTransitionProps:F,labelAlwaysOn:V,thumbLabel:H,showLabelOnHover:U,isHovered:en,disabled:W,...ee,children:G})}),(0,o.jsx)("input",{type:"hidden",name:O,value:ef,...$})]})})});I.classes=j.A,I.displayName="@mantine/core/Slider"},27045:(e,t,r)=>{"use strict";r.d(t,{MenuDropdown:()=>m});var o=r(95155),n=r(12115),a=r(88551),l=r(75240),i=r(43664),s=r(36960),c=r(60266),u=r(65054),d=r(69324);let p={},m=(0,s.factory)((e,t)=>{let{classNames:r,className:s,style:d,styles:m,vars:v,onMouseEnter:f,onMouseLeave:h,onKeyDown:g,children:b,...y}=(0,i.useProps)("MenuDropdown",p,e),x=(0,n.useRef)(null),w=(0,u.K)(),C=(0,l.createEventHandler)(g,e=>{if("ArrowUp"===e.key||"ArrowDown"===e.key){var t,r;e.preventDefault(),null==(r=x.current)||null==(t=r.querySelectorAll("[data-menu-item]:not(:disabled)")[0])||t.focus()}}),S=(0,l.createEventHandler)(f,()=>("hover"===w.trigger||"click-hover"===w.trigger)&&w.openDropdown()),k=(0,l.createEventHandler)(h,()=>("hover"===w.trigger||"click-hover"===w.trigger)&&w.closeDropdown());return(0,o.jsxs)(c.Popover.Dropdown,{...y,onMouseEnter:S,onMouseLeave:k,role:"menu","aria-orientation":"vertical",ref:(0,a.useMergedRef)(t,x),...w.getStyles("dropdown",{className:s,style:d,styles:m,classNames:r,withStaticClass:!1}),tabIndex:-1,"data-menu-dropdown":!0,onKeyDown:C,children:[w.withInitialFocusPlaceholder&&(0,o.jsx)("div",{tabIndex:-1,"data-autofocus":!0,"data-mantine-stop-propagation":!0,style:{outline:0}}),b]})});m.classes=d.A,m.displayName="@mantine/core/MenuDropdown"},27361:(e,t,r)=>{"use strict";r.d(t,{Tooltip:()=>_});var o=r(95155),n=r(12115),a=r(52596),l=r(88551),i=r(10866),s=r(58750),c=r(56204),u=r(72200),d=r(68918),p=r(43664),m=r(53791),v=r(69604),f=r(36960),h=r(53304),g=r(89691),b=r(180),y=r(62143),x=r(60384),w=r(79827),C=r(50937),S=r(14719),k=r(84945),E=r(45299),P=r(76492),j=r(64173),R=r(84237),D=r(84528),I=r(572);let A={position:"top",refProp:"ref",withinPortal:!0,arrowSize:4,arrowOffset:5,arrowRadius:0,arrowPosition:"side",offset:5,transitionProps:{duration:100,transition:"fade"},events:{hover:!0,focus:!1,touch:!1},zIndex:(0,s.getDefaultZIndex)("popover"),positionDependencies:[],middlewares:{flip:!0,shift:!0,inline:!1}},T=(0,d.createVarsResolver)((e,t)=>{let{radius:r,color:o,variant:n,autoContrast:a}=t,l=e.variantColorResolver({theme:e,color:o||e.primaryColor,autoContrast:a,variant:n||"filled"});return{tooltip:{"--tooltip-radius":void 0===r?void 0:(0,c.getRadius)(r),"--tooltip-bg":o?l.background:void 0,"--tooltip-color":o?l.color:void 0}}}),_=(0,f.factory)((e,t)=>{let r=(0,p.useProps)("Tooltip",A,e),{children:s,position:c,refProp:d,label:f,openDelay:C,closeDelay:S,onPositionChange:_,opened:M,defaultOpened:N,withinPortal:z,radius:O,color:L,classNames:B,styles:F,unstyled:V,style:H,className:U,withArrow:G,arrowSize:W,arrowOffset:q,arrowRadius:K,arrowPosition:X,offset:Z,transitionProps:Y,multiline:Q,events:$,zIndex:J,disabled:ee,positionDependencies:et,onClick:er,onMouseEnter:eo,onMouseLeave:en,inline:ea,variant:el,keepMounted:ei,vars:es,portalProps:ec,mod:eu,floatingStrategy:ed,middlewares:ep,autoContrast:em,...ev}=(0,p.useProps)("Tooltip",A,r),{dir:ef}=(0,h.useDirection)(),eh=(0,n.useRef)(null),eg=function(e){var t,r,o;let[a,l]=(0,n.useState)(e.defaultOpened),i="boolean"==typeof e.opened?e.opened:a,s=(0,D.r)(),c=(0,j.useId)(),u=(0,n.useCallback)(e=>{l(e),e&&x(c)},[c]),{x:d,y:p,context:m,refs:v,placement:f,middlewareData:{arrow:{x:h,y:g}={}}}=(0,E.we)({strategy:e.strategy,placement:e.position,open:i,onOpenChange:u,middleware:function(e){let t=function(e){if(void 0===e)return{shift:!0,flip:!0};let t={...e};return void 0===e.shift&&(t.shift=!0),void 0===e.flip&&(t.flip=!0),t}(e.middlewares),r=[(0,k.cY)(e.offset)];return t.shift&&r.push((0,k.BN)("boolean"==typeof t.shift?{padding:8}:{padding:8,...t.shift})),t.flip&&r.push("boolean"==typeof t.flip?(0,k.UU)():(0,k.UU)(t.flip)),r.push((0,k.UE)({element:e.arrowRef,padding:e.arrowOffset})),t.inline?r.push("boolean"==typeof t.inline?(0,k.mG)():(0,k.mG)(t.inline)):e.inline&&r.push((0,k.mG)()),r}(e),whileElementsMounted:P.ll}),{delay:b,currentId:y,setCurrentId:x}=(0,E.ck)(m,{id:c}),{getReferenceProps:w,getFloatingProps:C}=(0,E.bv)([(0,E.Mk)(m,{enabled:null==(t=e.events)?void 0:t.hover,delay:s?b:{open:e.openDelay,close:e.closeDelay},mouseOnly:!(null==(r=e.events)?void 0:r.touch)}),(0,E.iQ)(m,{enabled:null==(o=e.events)?void 0:o.focus,visibleOnly:!0}),(0,E.It)(m,{role:"tooltip"}),(0,E.s9)(m,{enabled:void 0===e.opened})]);(0,R.useDidUpdate)(()=>{var t;null==(t=e.onPositionChange)||t.call(e,f)},[f]);let S=i&&y&&y!==c;return{x:d,y:p,arrowX:h,arrowY:g,reference:v.setReference,floating:v.setFloating,getFloatingProps:C,getReferenceProps:w,isGroupPhase:S,opened:i,placement:f}}({position:(0,b.getFloatingPosition)(ef,c),closeDelay:S,openDelay:C,onPositionChange:_,opened:M,defaultOpened:N,events:$,arrowRef:eh,arrowOffset:q,offset:"number"==typeof Z?Z+(G?W/2:0):Z,positionDependencies:[...et,s],inline:ea,strategy:ed,middlewares:ep}),eb=(0,m.useStyles)({name:"Tooltip",props:r,classes:I.A,className:U,style:H,classNames:B,styles:F,unstyled:V,rootSelector:"tooltip",vars:es,varsResolver:T});if(!(0,i.isElement)(s))throw Error("[@mantine/core] Tooltip component children should be an element or a component that accepts ref, fragments, strings, numbers and other primitive values are not supported");let ey=(0,l.useMergedRef)(eg.reference,(0,u.getRefProp)(s),t),ex=(0,w.getTransitionProps)(Y,{duration:100,transition:"fade"}),ew=s.props;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(y.OptionalPortal,{...ec,withinPortal:z,children:(0,o.jsx)(x.Transition,{...ex,keepMounted:ei,mounted:!ee&&!!eg.opened,duration:eg.isGroupPhase?10:ex.duration,children:e=>{var t,r;return(0,o.jsxs)(v.Box,{...ev,"data-fixed":"fixed"===ed||void 0,variant:el,mod:[{multiline:Q},eu],...eg.getFloatingProps({ref:eg.floating,className:eb("tooltip").className,style:{...eb("tooltip").style,...e,zIndex:J,top:null!=(t=eg.y)?t:0,left:null!=(r=eg.x)?r:0}}),children:[f,(0,o.jsx)(g.FloatingArrow,{ref:eh,arrowX:eg.arrowX,arrowY:eg.arrowY,visible:G,position:eg.placement,arrowSize:W,arrowOffset:q,arrowRadius:K,arrowPosition:X,...eb("arrow")})]})}})}),(0,n.cloneElement)(s,eg.getReferenceProps({onClick:er,onMouseEnter:eo,onMouseLeave:en,onMouseMove:r.onMouseMove,onPointerDown:r.onPointerDown,onPointerEnter:r.onPointerEnter,className:(0,a.A)(U,ew.className),...ew,[d]:ey}))]})});_.classes=I.A,_.displayName="@mantine/core/Tooltip",_.Floating=C.TooltipFloating,_.Group=S.TooltipGroup},27547:(e,t,r)=>{"use strict";r.d(t,{CheckIcon:()=>a,R:()=>l});var o=r(95155),n=r(5903);function a(e){let{size:t,style:r,...a}=e,l=void 0!==t?{width:(0,n.D)(t),height:(0,n.D)(t),...r}:r;return(0,o.jsx)("svg",{viewBox:"0 0 10 7",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:l,"aria-hidden":!0,...a,children:(0,o.jsx)("path",{d:"M4 4.586L1.707 2.293A1 1 0 1 0 .293 3.707l3 3a.997.997 0 0 0 1.414 0l5-5A1 1 0 1 0 8.293.293L4 4.586z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})}function l(e){let{indeterminate:t,...r}=e;return t?(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 32 6","aria-hidden":!0,...r,children:(0,o.jsx)("rect",{width:"32",height:"6",fill:"currentColor",rx:"3"})}):(0,o.jsx)(a,{...r})}r(12115)},27733:(e,t,r)=>{"use strict";r.d(t,{DrawerStack:()=>c,useDrawerStackContext:()=>s});var o=r(95155),n=r(12115),a=r(49830),l=r(58750);let[i,s]=(0,a.createOptionalContext)();function c(e){let{children:t}=e,[r,a]=(0,n.useState)([]),[s,c]=(0,n.useState)((0,l.getDefaultZIndex)("modal"));return(0,o.jsx)(i,{value:{stack:r,addModal:(e,t)=>{a(t=>[...new Set([...t,e])]),c(e=>"number"==typeof t&&"number"==typeof e?Math.max(e,t):e)},removeModal:e=>a(t=>t.filter(t=>t!==e)),getZIndex:e=>"calc(".concat(s," + ").concat(r.indexOf(e)," + 1)"),currentId:r[r.length-1],maxZIndex:s},children:t})}c.displayName="@mantine/core/DrawerStack"},27735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},27886:(e,t,r)=>{"use strict";r.d(t,{V:()=>n,_:()=>a}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Timeline component was not found in tree")},28387:(e,t,r)=>{"use strict";r.d(t,{AspectRatio:()=>p});var o=r(95155);r(12115);var n=r(68918),a=r(43664),l=r(53791),i=r(69604),s=r(36960),c={root:"m_71ac47fc"};let u={},d=(0,n.createVarsResolver)((e,t)=>{let{ratio:r}=t;return{root:{"--ar-ratio":null==r?void 0:r.toString()}}}),p=(0,s.factory)((e,t)=>{let r=(0,a.useProps)("AspectRatio",u,e),{classNames:n,className:s,style:p,styles:m,unstyled:v,vars:f,ratio:h,...g}=r,b=(0,l.useStyles)({name:"AspectRatio",classes:c,props:r,className:s,style:p,classNames:n,styles:m,unstyled:v,vars:f,varsResolver:d});return(0,o.jsx)(i.Box,{ref:t,...b("root"),...g})});p.classes=c,p.displayName="@mantine/core/AspectRatio"},28843:(e,t,r)=>{"use strict";r.d(t,{PillsInput:()=>d});var o=r(95155),n=r(12115),a=r(43664),l=r(36960),i=r(24225),s=r(73548),c=r(81180);let u={size:"sm"},d=(0,l.factory)((e,t)=>{let{children:r,onMouseDown:l,onClick:c,size:d,disabled:p,__staticSelector:m,error:v,variant:f,...h}=(0,a.useProps)("PillsInput",u,e),g=(0,n.useRef)(null);return(0,o.jsx)(s.q,{value:{fieldRef:g,size:d,disabled:p,hasError:!!v,variant:f},children:(0,o.jsx)(i.InputBase,{size:d,error:v,variant:f,component:"div",ref:t,onMouseDown:e=>{var t;e.preventDefault(),null==l||l(e),null==(t=g.current)||t.focus()},onClick:e=>{e.preventDefault();let t=e.currentTarget.closest("fieldset");if(!(null==t?void 0:t.disabled)){var r;null==(r=g.current)||r.focus(),null==c||c(e)}},...h,multiline:!0,disabled:p,__staticSelector:m||"PillsInput",withAria:!1,children:r})})});d.displayName="@mantine/core/PillsInput",d.Field=c.PillsInputField},28892:(e,t,r)=>{"use strict";r.d(t,{useDelayedHover:()=>n});var o=r(12115);function n(e){let{open:t,close:r,openDelay:n,closeDelay:a}=e,l=(0,o.useRef)(-1),i=(0,o.useRef)(-1),s=()=>{window.clearTimeout(l.current),window.clearTimeout(i.current)};return(0,o.useEffect)(()=>s,[]),{openDropdown:()=>{s(),0===n||void 0===n?t():l.current=window.setTimeout(t,n)},closeDropdown:()=>{s(),0===a||void 0===a?r():i.current=window.setTimeout(r,a)}}}},29791:(e,t,r)=>{"use strict";r.d(t,{useShallowEffect:()=>a});var o=r(12115),n=r(13135);function a(e,t){(0,o.useEffect)(e,function(e){let t=(0,o.useRef)([]),r=(0,o.useRef)(0);return!function(e,t){if(!e||!t)return!1;if(e===t)return!0;if(e.length!==t.length)return!1;for(let r=0;r<e.length;r+=1)if(!(0,n.shallowEqual)(e[r],t[r]))return!1;return!0}(t.current,e)&&(t.current=e,r.current+=1),[r.current]}(t))}},30210:(e,t,r)=>{"use strict";r.d(t,{DrawerContent:()=>d});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(29586),i=r(69112),s=r(20950),c=r(56196);let u={},d=(0,a.factory)((e,t)=>{let{classNames:r,className:a,style:c,styles:d,vars:p,children:m,radius:v,__hidden:f,...h}=(0,n.useProps)("DrawerContent",u,e),g=(0,s.g)(),b=g.scrollAreaComponent||i.NativeScrollArea;return(0,o.jsx)(l.ModalBaseContent,{...g.getStyles("content",{className:a,style:c,styles:d,classNames:r}),innerProps:g.getStyles("inner",{className:a,style:c,styles:d,classNames:r}),ref:t,...h,radius:v||g.radius||0,"data-hidden":f||void 0,children:(0,o.jsx)(b,{style:{height:"calc(100vh - var(--drawer-offset) * 2)"},children:m})})});d.classes=c.A,d.displayName="@mantine/core/DrawerContent"},30404:(e,t,r)=>{"use strict";function o(e,t){return parseFloat(e.toFixed(t))}r.d(t,{q:()=>o})},30804:(e,t,r)=>{"use strict";r.d(t,{Checkbox:()=>P});var o=r(95155),n=r(12115),a=r(64173),l=r(56204),i=r(68918),s=r(98271),c=r(71180),u=r(89200),d=r(98840),p=r(43664),m=r(53791),v=r(99537),f=r(69604),h=r(36960),g=r(44598),b=r(63459),y=r(43171),x=r(38765),w=r(10884),C=r(27547),S={root:"m_bf2d988c",inner:"m_26062bec",input:"m_26063560",icon:"m_bf295423","input--outline":"m_215c4542"};let k={labelPosition:"right",icon:C.R},E=(0,i.createVarsResolver)((e,t)=>{let{radius:r,color:o,size:n,iconColor:a,variant:i,autoContrast:p}=t,m=(0,s.parseThemeColor)({color:o||e.primaryColor,theme:e}),v=m.isThemeColor&&void 0===m.shade?"var(--mantine-color-".concat(m.color,"-outline)"):m.color;return{root:{"--checkbox-size":(0,l.getSize)(n,"checkbox-size"),"--checkbox-radius":void 0===r?void 0:(0,l.getRadius)(r),"--checkbox-color":"outline"===i?v:(0,c.getThemeColor)(o,e),"--checkbox-icon-color":a?(0,c.getThemeColor)(a,e):(0,d.getAutoContrastValue)(p,e)?(0,u.getContrastColor)({color:o,theme:e,autoContrast:p}):void 0}}}),P=(0,h.factory)((e,t)=>{let r=(0,p.useProps)("Checkbox",k,e),{classNames:l,className:i,style:s,styles:c,unstyled:u,vars:d,color:h,label:b,id:x,size:w,radius:C,wrapperProps:P,checked:j,labelPosition:R,description:D,error:I,disabled:A,variant:T,indeterminate:_,icon:M,rootRef:N,iconColor:z,onChange:O,autoContrast:L,mod:B,...F}=r,V=(0,y.useCheckboxGroupContext)(),H=w||(null==V?void 0:V.size),U=(0,m.useStyles)({name:"Checkbox",props:r,classes:S,className:i,style:s,classNames:l,styles:c,unstyled:u,vars:d,varsResolver:E}),{styleProps:G,rest:W}=(0,v.extractStyleProps)(F),q=(0,a.useId)(x),K=V?{checked:V.value.includes(W.value),onChange:e=>{V.onChange(e),null==O||O(e)}}:{},X=(0,n.useRef)(null),Z=t||X;return(0,n.useEffect)(()=>{Z&&"current"in Z&&Z.current&&(Z.current.indeterminate=_||!1)},[_,Z]),(0,o.jsx)(g.I,{...U("root"),__staticSelector:"Checkbox",__stylesApiProps:r,id:q,size:H,labelPosition:R,label:b,description:D,error:I,disabled:A,classNames:l,styles:c,unstyled:u,"data-checked":K.checked||j||void 0,variant:T,ref:N,mod:B,...G,...P,children:(0,o.jsxs)(f.Box,{...U("inner"),mod:{"data-label-position":R},children:[(0,o.jsx)(f.Box,{component:"input",id:q,ref:Z,checked:j,disabled:A,mod:{error:!!I,indeterminate:_},...U("input",{focusable:!0,variant:T}),onChange:O,...W,...K,type:"checkbox"}),(0,o.jsx)(M,{indeterminate:_,...U("icon")})]})})});P.classes={...S,...g.M},P.displayName="@mantine/core/Checkbox",P.Group=x.CheckboxGroup,P.Indicator=w.CheckboxIndicator,P.Card=b.CheckboxCard},31196:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_43657ece",itemTitle:"m_2ebe8099",item:"m_436178ff",itemBullet:"m_8affcee1",itemBody:"m_540e8f41"}},31339:(e,t,r)=>{"use strict";r.d(t,{Affix:()=>f});var o=r(95155);r(12115);var n=r(58750),a=r(56204),l=r(68918),i=r(43664),s=r(53791),c=r(69604),u=r(36960),d=r(62143),p={root:"m_7f854edf"};let m={position:{bottom:0,right:0},zIndex:(0,n.getDefaultZIndex)("modal"),withinPortal:!0},v=(0,l.createVarsResolver)((e,t)=>{let{zIndex:r,position:o}=t;return{root:{"--affix-z-index":null==r?void 0:r.toString(),"--affix-top":(0,a.getSpacing)(null==o?void 0:o.top),"--affix-left":(0,a.getSpacing)(null==o?void 0:o.left),"--affix-bottom":(0,a.getSpacing)(null==o?void 0:o.bottom),"--affix-right":(0,a.getSpacing)(null==o?void 0:o.right)}}}),f=(0,u.factory)((e,t)=>{let r=(0,i.useProps)("Affix",m,e),{classNames:n,className:a,style:l,styles:u,unstyled:f,vars:h,portalProps:g,zIndex:b,withinPortal:y,position:x,...w}=r,C=(0,s.useStyles)({name:"Affix",classes:p,props:r,className:a,style:l,classNames:n,styles:u,unstyled:f,vars:h,varsResolver:v});return(0,o.jsx)(d.OptionalPortal,{...g,withinPortal:y,children:(0,o.jsx)(c.Box,{ref:t,...C("root"),...w})})});f.classes=p,f.displayName="@mantine/core/Affix"},31510:(e,t,r)=>{"use strict";r.r(t),r.d(t,{MantineCssVariables:()=>p});var o=r(95155),n=r(4626),a=r(13656),l=r(3131),i=r(41750);r(12115);var s=r(8060),c=r(19224),u=r(67118);let d=(0,s.defaultCssVariablesResolver)(u.S);function p(e){let{cssVariablesSelector:t,deduplicateCssVariables:r}=e,u=(0,l.useMantineTheme)(),p=(0,a.useMantineStyleNonce)(),m=function(e){let{theme:t,generator:r}=e,o=(0,s.defaultCssVariablesResolver)(t),n=null==r?void 0:r(t);return n?(0,i.$)(o,n):o}({theme:u,generator:(0,a.useMantineCssVariablesResolver)()}),v=":root"===t&&r,f=v?function(e){let t={variables:{},light:{},dark:{}};return(0,c.keys)(e.variables).forEach(r=>{d.variables[r]!==e.variables[r]&&(t.variables[r]=e.variables[r])}),(0,c.keys)(e.light).forEach(r=>{d.light[r]!==e.light[r]&&(t.light[r]=e.light[r])}),(0,c.keys)(e.dark).forEach(r=>{d.dark[r]!==e.dark[r]&&(t.dark[r]=e.dark[r])}),t}(m):m,h=(0,n.convertCssVariables)(f,t);if(h)return(0,o.jsx)("style",{"data-mantine-styles":!0,nonce:null==p?void 0:p(),dangerouslySetInnerHTML:{__html:"".concat(h).concat(v?"":"\n  ".concat(t,'[data-mantine-color-scheme="dark"] { --mantine-color-scheme: dark; }\n  ').concat(t,'[data-mantine-color-scheme="light"] { --mantine-color-scheme: light; }\n'))}});return null}p.displayName="@mantine/CssVariables"},32153:(e,t,r)=>{"use strict";r.d(t,{useThrottledState:()=>a});var o=r(12115),n=r(1259);function a(e,t){let[r,a]=(0,o.useState)(e),[l,i]=(0,n.Z)(a,t);return(0,o.useEffect)(()=>i,[]),[r,l]}},32987:(e,t,r)=>{"use strict";r.d(t,{Indicator:()=>g});var o=r(95155),n=r(5903);r(12115);var a=r(56204),l=r(68918),i=r(71180),s=r(89200),c=r(98840),u=r(43664),d=r(53791),p=r(69604),m=r(36960),v={root:"m_e5262200",indicator:"m_760d1fb1",processing:"m_885901b1"};let f={position:"top-end",offset:0},h=(0,l.createVarsResolver)((e,t)=>{let{color:r,position:o,offset:l,size:u,radius:d,zIndex:p,autoContrast:m}=t;return{root:{"--indicator-color":r?(0,i.getThemeColor)(r,e):void 0,"--indicator-text-color":(0,c.getAutoContrastValue)(m,e)?(0,s.getContrastColor)({color:r,theme:e,autoContrast:m}):void 0,"--indicator-size":(0,n.D)(u),"--indicator-radius":void 0===d?void 0:(0,a.getRadius)(d),"--indicator-z-index":null==p?void 0:p.toString(),...function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"top-end",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r={"--indicator-top":void 0,"--indicator-bottom":void 0,"--indicator-left":void 0,"--indicator-right":void 0,"--indicator-translate-x":void 0,"--indicator-translate-y":void 0},o=(0,n.D)(t),[a,l]=e.split("-");return"top"===a&&(r["--indicator-top"]=o,r["--indicator-translate-y"]="-50%"),"middle"===a&&(r["--indicator-top"]="50%",r["--indicator-translate-y"]="-50%"),"bottom"===a&&(r["--indicator-bottom"]=o,r["--indicator-translate-y"]="50%"),"start"===l&&(r["--indicator-left"]=o,r["--indicator-translate-x"]="-50%"),"center"===l&&(r["--indicator-left"]="50%",r["--indicator-translate-x"]="-50%"),"end"===l&&(r["--indicator-right"]=o,r["--indicator-translate-x"]="50%"),r}(o,l)}}}),g=(0,m.factory)((e,t)=>{let r=(0,u.useProps)("Indicator",f,e),{classNames:n,className:a,style:l,styles:i,unstyled:s,vars:c,children:m,position:g,offset:b,inline:y,label:x,radius:w,color:C,withBorder:S,disabled:k,processing:E,zIndex:P,autoContrast:j,mod:R,...D}=r,I=(0,d.useStyles)({name:"Indicator",classes:v,props:r,className:a,style:l,classNames:n,styles:i,unstyled:s,vars:c,varsResolver:h});return(0,o.jsxs)(p.Box,{ref:t,...I("root"),mod:[{inline:y},R],...D,children:[!k&&(0,o.jsx)(p.Box,{mod:{"with-label":!!x,"with-border":S,processing:E},...I("indicator"),children:x}),m]})});g.classes=v,g.displayName="@mantine/core/Indicator"},33263:(e,t,r)=>{"use strict";r.d(t,{AlphaSlider:()=>u});var o=r(95155),n=r(12115),a=r(5903),l=r(43664),i=r(85639),s=r(39186);let c={},u=(0,n.forwardRef)((e,t)=>{let{value:r,onChange:n,onChangeEnd:u,color:d,...p}=(0,l.useProps)("AlphaSlider",c,e);return(0,o.jsx)(i.t,{...p,ref:t,value:r,onChange:e=>null==n?void 0:n((0,s.LI)(e,2)),onChangeEnd:e=>null==u?void 0:u((0,s.LI)(e,2)),maxValue:1,round:!1,"data-alpha":!0,overlays:[{backgroundImage:"linear-gradient(45deg, var(--slider-checkers) 25%, transparent 25%), linear-gradient(-45deg, var(--slider-checkers) 25%, transparent 25%), linear-gradient(45deg, transparent 75%, var(--slider-checkers) 75%), linear-gradient(-45deg, var(--mantine-color-body) 75%, var(--slider-checkers) 75%)",backgroundSize:"".concat((0,a.D)(8)," ").concat((0,a.D)(8)),backgroundPosition:"0 0, 0 ".concat((0,a.D)(4),", ").concat((0,a.D)(4)," ").concat((0,a.D)(-4),", ").concat((0,a.D)(-4)," 0")},{backgroundImage:"linear-gradient(90deg, transparent, ".concat(d,")")},{boxShadow:"rgba(0, 0, 0, .1) 0 0 0 ".concat((0,a.D)(1)," inset, rgb(0, 0, 0, .15) 0 0 ").concat((0,a.D)(4)," inset")}]})});u.displayName="@mantine/core/AlphaSlider"},33597:(e,t,r)=>{"use strict";r.d(t,{FileInput:()=>h});var o=r(95155),n=r(12115),a=r(57613),l=r(88551),i=r(86028),s=r(43664),c=r(36960),u=r(95642),d=r(2639),p=r(54853),m=r(24225);let v={valueComponent:e=>{let{value:t}=e;return(0,o.jsx)("div",{style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:Array.isArray(t)?t.map(e=>e.name).join(", "):null==t?void 0:t.name})}},f=(0,c.factory)((e,t)=>{let r=(0,s.useProps)("FileInput",v,e),{unstyled:c,vars:f,onChange:h,value:g,defaultValue:b,multiple:y,accept:x,name:w,form:C,valueComponent:S,clearable:k,clearButtonProps:E,readOnly:P,capture:j,fileInputProps:R,rightSection:D,size:I,placeholder:A,component:T,resetRef:_,classNames:M,styles:N,...z}=r,O=(0,n.useRef)(null),{resolvedClassNames:L,resolvedStyles:B}=(0,i.useResolvedStylesApi)({classNames:M,styles:N,props:r}),[F,V]=(0,a.useUncontrolled)({value:g,defaultValue:b,onChange:h,finalValue:y?[]:null}),H=Array.isArray(F)?0!==F.length:null!==F,U=D||(k&&H&&!P?(0,o.jsx)(u.CloseButton,{...E,variant:"subtle",onClick:()=>V(y?[]:null),size:I,unstyled:c}):null);return(0,n.useEffect)(()=>{if(Array.isArray(F)&&0===F.length||null===F){var e;null==(e=O.current)||e.call(O)}},[F]),(0,o.jsx)(d.FileButton,{onChange:V,multiple:y,accept:x,name:w,form:C,resetRef:(0,l.useMergedRef)(O,_),disabled:P,capture:j,inputProps:R,children:e=>(0,o.jsx)(m.InputBase,{component:T||"button",ref:t,rightSection:U,...e,...z,__staticSelector:"FileInput",multiline:!0,type:"button",pointer:!0,__stylesApiProps:r,unstyled:c,size:I,classNames:M,styles:N,children:H?(0,o.jsx)(S,{value:F}):(0,o.jsx)(p.Input.Placeholder,{__staticSelector:"FileInput",classNames:L,styles:B,children:A})})})});f.classes=m.InputBase.classes,f.displayName="@mantine/core/FileInput";let h=f},33935:(e,t,r)=>{"use strict";function o(e,t){let r=Math.abs(t-e)+1;return e>t?Array.from({length:r},(t,r)=>e-r):Array.from({length:r},(t,r)=>r+e)}r.d(t,{range:()=>o})},33981:(e,t,r)=>{"use strict";r.d(t,{useDebouncedValue:()=>n});var o=r(12115);function n(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{leading:!1},[n,a]=(0,o.useState)(e),l=(0,o.useRef)(!1),i=(0,o.useRef)(null),s=(0,o.useRef)(!1),c=()=>window.clearTimeout(i.current);return(0,o.useEffect)(()=>{l.current&&(!s.current&&r.leading?(s.current=!0,a(e)):(c(),i.current=window.setTimeout(()=>{s.current=!1,a(e)},t)))},[e,r.leading,t]),(0,o.useEffect)(()=>(l.current=!0,c),[]),[n,c]}},34666:(e,t,r)=>{"use strict";r.d(t,{ColorSwatch:()=>v});var o=r(95155),n=r(5903);r(12115);var a=r(56204),l=r(68918),i=r(43664),s=r(53791),c=r(69604),u=r(64511),d={root:"m_de3d2490",colorOverlay:"m_862f3d1b",shadowOverlay:"m_98ae7f22",alphaOverlay:"m_95709ac0",childrenOverlay:"m_93e74e3"};let p={withShadow:!0},m=(0,l.createVarsResolver)((e,t)=>{let{radius:r,size:o}=t;return{root:{"--cs-radius":void 0===r?void 0:(0,a.getRadius)(r),"--cs-size":(0,n.D)(o)}}}),v=(0,u.polymorphicFactory)((e,t)=>{let r=(0,i.useProps)("ColorSwatch",p,e),{classNames:n,className:a,style:l,styles:u,unstyled:v,vars:f,color:h,size:g,radius:b,withShadow:y,children:x,variant:w,...C}=(0,i.useProps)("ColorSwatch",p,r),S=(0,s.useStyles)({name:"ColorSwatch",props:r,classes:d,className:a,style:l,classNames:n,styles:u,unstyled:v,vars:f,varsResolver:m});return(0,o.jsxs)(c.Box,{ref:t,variant:w,size:g,...S("root",{focusable:!0}),...C,children:[(0,o.jsx)("span",{...S("alphaOverlay")}),y&&(0,o.jsx)("span",{...S("shadowOverlay")}),(0,o.jsx)("span",{...S("colorOverlay",{style:{backgroundColor:h}})}),(0,o.jsx)("span",{...S("childrenOverlay"),children:x})]})});v.classes=d,v.displayName="@mantine/core/ColorSwatch"},35049:(e,t,r)=>{"use strict";r.d(t,{ThemeIcon:()=>m});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u={root:"m_7341320d"};let d={},p=(0,a.createVarsResolver)((e,t)=>{let{size:r,radius:o,variant:a,gradient:l,color:i,autoContrast:s}=t,c=e.variantColorResolver({color:i||e.primaryColor,theme:e,gradient:l,variant:a||"filled",autoContrast:s});return{root:{"--ti-size":(0,n.getSize)(r,"ti-size"),"--ti-radius":void 0===o?void 0:(0,n.getRadius)(o),"--ti-bg":i||a?c.background:void 0,"--ti-color":i||a?c.color:void 0,"--ti-bd":i||a?c.border:void 0}}}),m=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("ThemeIcon",d,e),{classNames:n,className:a,style:c,styles:m,unstyled:v,vars:f,autoContrast:h,...g}=r,b=(0,i.useStyles)({name:"ThemeIcon",classes:u,props:r,className:a,style:c,classNames:n,styles:m,unstyled:v,vars:f,varsResolver:p});return(0,o.jsx)(s.Box,{ref:t,...b("root"),...g})});m.classes=u,m.displayName="@mantine/core/ThemeIcon"},36119:(e,t,r)=>{"use strict";r.d(t,{useScrollIntoView:()=>l});var o=r(12115),n=r(43589),a=r(28261);function l(){let{duration:e=1250,axis:t="y",onScrollFinish:r,easing:l=i,offset:s=0,cancelable:c=!0,isList:u=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},d=(0,o.useRef)(0),p=(0,o.useRef)(0),m=(0,o.useRef)(!1),v=(0,o.useRef)(null),f=(0,o.useRef)(null),h=(0,n.useReducedMotion)(),g=()=>{d.current&&cancelAnimationFrame(d.current)},b=(0,o.useCallback)(function(){var o;let{alignment:n="start"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};m.current=!1,d.current&&g();let a=null!=(o=function(e){let{axis:t,parent:r}=e;if(!r&&"undefined"==typeof document)return 0;let o="y"===t?"scrollTop":"scrollLeft";if(r)return r[o];let{body:n,documentElement:a}=document;return n[o]+a[o]}({parent:v.current,axis:t}))?o:0,i=function(e){let{axis:t,target:r,parent:o,alignment:n,offset:a,isList:l}=e;if(!r||!o&&"undefined"==typeof document)return 0;let i=!!o,s=(o||document.body).getBoundingClientRect(),c=r.getBoundingClientRect(),u=e=>c[e]-s[e];if("y"===t){let e=u("top");if(0===e)return 0;if("start"===n){let t=e-a;return t<=!l*c.height||!l?t:0}let t=i?s.height:window.innerHeight;if("end"===n){let r=e+a-t+c.height;return r>=-c.height*!l||!l?r:0}return"center"===n?e-t/2+c.height/2:0}if("x"===t){let e=u("left");if(0===e)return 0;if("start"===n){let t=e-a;return t<=c.width||!l?t:0}let t=i?s.width:window.innerWidth;if("end"===n){let r=e+a-t+c.width;return r>=-c.width||!l?r:0}if("center"===n)return e-t/2+c.width/2}return 0}({parent:v.current,target:f.current,axis:t,alignment:n,offset:s,isList:u})-(v.current?0:a);!function o(){0===p.current&&(p.current=performance.now());let n=performance.now()-p.current,s=h||0===e?1:n/e,c=a+i*l(s);(function(e){let{axis:t,parent:r,distance:o}=e;if(!r&&"undefined"==typeof document)return;let n="y"===t?"scrollTop":"scrollLeft";if(r)r[n]=o;else{let{body:e,documentElement:t}=document;e[n]=o,t[n]=o}})({parent:v.current,axis:t,distance:c}),!m.current&&s<1?d.current=requestAnimationFrame(o):("function"==typeof r&&r(),p.current=0,d.current=0,g())}()},[t,e,l,u,s,r,h]),y=()=>{c&&(m.current=!0)};return(0,a.useWindowEvent)("wheel",y,{passive:!0}),(0,a.useWindowEvent)("touchmove",y,{passive:!0}),(0,o.useEffect)(()=>g,[]),{scrollableRef:v,targetRef:f,scrollIntoView:b,cancel:g}}function i(e){return e<.5?2*e*e:-1+(4-2*e)*e}},36663:(e,t,r)=>{"use strict";r.d(t,{B:()=>n,_:()=>o}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)()},37079:(e,t,r)=>{"use strict";r.d(t,{RadioGroup:()=>p});var o=r(95155),n=r(64173),a=r(57613);r(12115);var l=r(43664),i=r(36960),s=r(54853),c=r(10537),u=r(53051);let d={},p=(0,i.factory)((e,t)=>{let{value:r,defaultValue:i,onChange:p,size:m,wrapperProps:v,children:f,name:h,readOnly:g,...b}=(0,l.useProps)("RadioGroup",d,e),y=(0,n.useId)(h),[x,w]=(0,a.useUncontrolled)({value:r,defaultValue:i,finalValue:"",onChange:p});return(0,o.jsx)(u.M,{value:{value:x,onChange:e=>!g&&w("string"==typeof e?e:e.currentTarget.value),size:m,name:y},children:(0,o.jsx)(s.Input.Wrapper,{size:m,ref:t,...v,...b,labelElement:"div",__staticSelector:"RadioGroup",children:(0,o.jsx)(c.F,{role:"radiogroup",children:f})})})});p.classes=s.Input.Wrapper.classes,p.displayName="@mantine/core/RadioGroup"},37575:(e,t,r)=>{"use strict";r.d(t,{usePagination:()=>i});var o=r(12115),n=r(57613);function a(e,t){return Array.from({length:t-e+1},(t,r)=>r+e)}let l="dots";function i(e){let{total:t,siblings:r=1,boundaries:i=1,page:s,initialPage:c=1,onChange:u}=e,d=Math.max(Math.trunc(t),0),[p,m]=(0,n.useUncontrolled)({value:s,onChange:u,defaultValue:c,finalValue:c}),v=e=>{e<=0?m(1):e>d?m(d):m(e)};return{range:(0,o.useMemo)(()=>{if(2*r+3+2*i>=d)return a(1,d);let e=Math.max(p-r,i),t=Math.min(p+r,d-i),o=e>i+2,n=t<d-(i+1);if(!o&&n)return[...a(1,2*r+i+2),l,...a(d-(i-1),d)];if(o&&!n){let e=i+1+2*r;return[...a(1,i),l,...a(d-e,d)]}return[...a(1,i),l,...a(e,t),l,...a(d-i+1,d)]},[d,r,p]),active:p,setPage:v,next:()=>v(p+1),previous:()=>v(p-1),first:()=>v(1),last:()=>v(d)}}},37701:(e,t,r)=>{"use strict";r.d(t,{NativeSelect:()=>d});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(79483),i=r(6083),s=r(24225);function c(e){let{data:t}=e;if("group"in t){let e=t.items.map(e=>(0,o.jsx)(c,{data:e},e.value));return(0,o.jsx)("optgroup",{label:t.group,children:e})}let{value:r,label:n,...a}=t;return(0,o.jsx)("option",{value:t.value,...a,children:t.label},t.value)}c.displayName="@mantine/core/NativeSelectOption";let u={rightSectionPointerEvents:"none"},d=(0,a.factory)((e,t)=>{let{data:r,children:a,size:d,error:p,rightSection:m,unstyled:v,...f}=(0,n.useProps)("NativeSelect",u,e),h=(0,l.getParsedComboboxData)(r).map((e,t)=>(0,o.jsx)(c,{data:e},t));return(0,o.jsx)(s.InputBase,{component:"select",ref:t,...f,__staticSelector:"NativeSelect",size:d,pointer:!0,error:p,unstyled:v,rightSection:m||(0,o.jsx)(i.ComboboxChevron,{size:d,error:p,unstyled:v}),children:a||h})});d.classes=s.InputBase.classes,d.displayName="@mantine/core/NativeSelect"},37892:(e,t,r)=>{"use strict";r.d(t,{TableOfContents:()=>b});var o=r(95155),n=r(64173),a=r(75451),l=r(88551),i=r(5903);r(12115);var s=r(56204),c=r(68918),u=r(43664),d=r(53791),p=r(69604),m=r(36960),v=r(43608),f={root:"m_bcaa9990",control:"m_375a65ef"};let h={getControlProps:e=>{let{data:t}=e;return{children:t.value}}},g=(0,c.createVarsResolver)((e,t)=>{let{color:r,size:o,variant:n,autoContrast:a,depthOffset:l,radius:c}=t,u=e.variantColorResolver({color:r||e.primaryColor,theme:e,variant:n||"filled",autoContrast:a});return{root:{"--toc-bg":"none"!==n?u.background:void 0,"--toc-color":"none"!==n?u.color:void 0,"--toc-size":(0,s.getFontSize)(o),"--toc-depth-offset":(0,i.D)(l),"--toc-radius":(0,s.getRadius)(c)}}}),b=(0,m.factory)((e,t)=>{let r=(0,u.useProps)("TableOfContents",h,e),{classNames:i,className:s,style:c,styles:m,unstyled:b,vars:y,color:x,autoContrast:w,scrollSpyOptions:C,initialData:S,getControlProps:k,minDepthToOffset:E,depthOffset:P,variant:j,radius:R,reinitializeRef:D,...I}=r,A=(0,d.useStyles)({name:"TableOfContents",classes:f,props:r,className:s,style:c,classNames:i,styles:m,unstyled:b,vars:y,varsResolver:g}),T=(0,n.useId)(),_=(0,a.useScrollSpy)(C);(0,l.assignRef)(D,_.reinitialize);let M=(_.initialized?_.data:S||[]).map((e,t)=>{let r=null==k?void 0:k({active:t===_.active,data:{...e,getNode:e.getNode||(()=>{})}});return(0,o.jsx)(v.UnstyledButton,{__vars:{"--depth-offset":"".concat(e.depth-(E||1))},"data-active":t===_.active||void 0,variant:j,...r,...A("control",{className:null==r?void 0:r.className,style:null==r?void 0:r.style})},e.id||"".concat(T,"-").concat(t))});return(0,o.jsx)(p.Box,{ref:t,variant:j,...A("root"),...I,children:M})});b.displayName="@mantine/core/TableOfContents",b.classes=f},38085:(e,t,r)=>{"use strict";r.d(t,{Drawer:()=>b});var o=r(95155),n=r(12115),a=r(58750),l=r(43664),i=r(36960),s=r(80593),c=r(93961),u=r(30210),d=r(83874),p=r(69829),m=r(77345),v=r(27733),f=r(77823),h=r(56196);let g={closeOnClickOutside:!0,withinPortal:!0,lockScroll:!0,trapFocus:!0,returnFocus:!0,closeOnEscape:!0,keepMounted:!1,zIndex:(0,a.getDefaultZIndex)("modal"),withOverlay:!0,withCloseButton:!0},b=(0,i.factory)((e,t)=>{let{title:r,withOverlay:i,overlayProps:h,withCloseButton:b,closeButtonProps:y,children:x,opened:w,stackId:C,zIndex:S,...k}=(0,l.useProps)("Drawer",g,e),E=(0,v.useDrawerStackContext)(),P=!!r||b,j=E&&C?{closeOnEscape:E.currentId===C,trapFocus:E.currentId===C,zIndex:E.getZIndex(C)}:{},R=!1!==i&&(C&&E?E.currentId===C:w);return(0,n.useEffect)(()=>{E&&C&&(w?E.addModal(C,S||(0,a.getDefaultZIndex)("modal")):E.removeModal(C))},[w,C,S]),(0,o.jsxs)(m.DrawerRoot,{ref:t,opened:w,zIndex:E&&C?E.getZIndex(C):S,...k,...j,children:[i&&(0,o.jsx)(p.DrawerOverlay,{visible:R,transitionProps:E&&C?{duration:0}:void 0,...h}),(0,o.jsxs)(u.DrawerContent,{__hidden:!!E&&!!C&&!!w&&C!==E.currentId,children:[P&&(0,o.jsxs)(d.DrawerHeader,{children:[r&&(0,o.jsx)(f.DrawerTitle,{children:r}),b&&(0,o.jsx)(c.DrawerCloseButton,{...y})]}),(0,o.jsx)(s.DrawerBody,{children:x})]})]})});b.classes=h.A,b.displayName="@mantine/core/Drawer",b.Root=m.DrawerRoot,b.Overlay=p.DrawerOverlay,b.Content=u.DrawerContent,b.Body=s.DrawerBody,b.Header=d.DrawerHeader,b.Title=f.DrawerTitle,b.CloseButton=c.DrawerCloseButton,b.Stack=v.DrawerStack},38765:(e,t,r)=>{"use strict";r.d(t,{CheckboxGroup:()=>d});var o=r(95155),n=r(57613);r(12115);var a=r(43664),l=r(36960),i=r(54853),s=r(10537),c=r(43171);let u={},d=(0,l.factory)((e,t)=>{let{value:r,defaultValue:l,onChange:d,size:p,wrapperProps:m,children:v,readOnly:f,...h}=(0,a.useProps)("CheckboxGroup",u,e),[g,b]=(0,n.useUncontrolled)({value:r,defaultValue:l,finalValue:[],onChange:d});return(0,o.jsx)(c.a,{value:{value:g,onChange:e=>{let t="string"==typeof e?e:e.currentTarget.value;f||b(g.includes(t)?g.filter(e=>e!==t):[...g,t])},size:p},children:(0,o.jsx)(i.Input.Wrapper,{size:p,ref:t,...m,...h,labelElement:"div",__staticSelector:"CheckboxGroup",children:(0,o.jsx)(s.F,{role:"group",children:v})})})});d.classes=i.Input.Wrapper.classes,d.displayName="@mantine/core/CheckboxGroup"},39186:(e,t,r)=>{"use strict";function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10**t;return Math.round(r*e)/r}r.d(t,{LI:()=>o,isColorValid:()=>m,parseColor:()=>v});let n={grad:.9,turn:360,rad:360/(2*Math.PI)},a=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i;function l(e){let t=a.exec(e);return t?function(e){let{h:t,s:r,l:o,a:n}=e,a=(o<50?o:100-o)/100*r;return{h:t,s:a>0?2*a/(o+a)*100:0,v:o+a,a:n}}({h:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"deg";return Number(e)*(n[t]||1)}(t[1],t[2]),s:Number(t[3]),l:Number(t[4]),a:void 0===t[5]?1:Number(t[5])/(t[6]?100:1)}):{h:0,s:0,v:0,a:1}}function i(e){let{r:t,g:r,b:n,a}=e,l=Math.max(t,r,n),i=l-Math.min(t,r,n),s=i?l===t?(r-n)/i:l===r?2+(n-t)/i:4+(t-r)/i:0;return{h:o(60*(s<0?s+6:s),3),s:o(l?i/l*100:0,3),v:o(l/255*100,3),a}}function s(e){let t="#"===e[0]?e.slice(1):e;return 3===t.length?i({r:parseInt(t[0]+t[0],16),g:parseInt(t[1]+t[1],16),b:parseInt(t[2]+t[2],16),a:1}):i({r:parseInt(t.slice(0,2),16),g:parseInt(t.slice(2,4),16),b:parseInt(t.slice(4,6),16),a:1})}let c=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i;function u(e){let t=c.exec(e);return t?i({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:void 0===t[7]?1:Number(t[7])/(t[8]?100:1)}):{h:0,s:0,v:0,a:1}}let d={hex:/^#?([0-9A-F]{3}){1,2}$/i,hexa:/^#?([0-9A-F]{4}){1,2}$/i,rgb:/^rgb\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/i,rgba:/^rgba\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/i,hsl:/hsl\(\s*(\d+)\s*,\s*(\d+(?:\.\d+)?%)\s*,\s*(\d+(?:\.\d+)?%)\)/i,hsla:/^hsla\((\d+),\s*([\d.]+)%,\s*([\d.]+)%,\s*(\d*(?:\.\d+)?)\)$/i},p={hex:s,hexa:function(e){let t="#"===e[0]?e.slice(1):e,r=e=>o(parseInt(e,16)/255,3);if(4===t.length){let e=t.slice(0,3),o=r(t[3]+t[3]);return{...s(e),a:o}}let n=t.slice(0,6),a=r(t.slice(6,8));return{...s(n),a}},rgb:u,rgba:u,hsl:l,hsla:l};function m(e){for(let[,t]of Object.entries(d))if(t.test(e))return!0;return!1}function v(e){if("string"!=typeof e)return{h:0,s:0,v:0,a:1};if("transparent"===e)return{h:0,s:0,v:0,a:0};let t=e.trim();for(let[e,r]of Object.entries(d))if(r.test(t))return p[e](t);return{h:0,s:0,v:0,a:1}}},39287:(e,t,r)=>{"use strict";r.d(t,{StepperStep:()=>h});var o=r(95155);r(12115);var n=r(71180),a=r(3131),l=r(43664),i=r(36960),s=r(27547),c=r(83347),u=r(60384),d=r(43608),p=r(51246),m=r(90812);let v=(e,t)=>"function"==typeof e?(0,o.jsx)(e,{step:t||0}):e,f={withIcon:!0,allowStepClick:!0,iconPosition:"left"},h=(0,i.factory)((e,t)=>{let{classNames:r,className:i,style:m,styles:h,vars:g,step:b,state:y,color:x,icon:w,completedIcon:C,progressIcon:S,label:k,description:E,withIcon:P,iconSize:j,loading:R,allowStepClick:D,allowStepSelect:I,iconPosition:A,orientation:T,mod:_,...M}=(0,l.useProps)("StepperStep",f,e),N=(0,p.s)(),z=(0,a.useMantineTheme)(),O={classNames:r,styles:h},L="stepCompleted"===y?null:"stepProgress"===y?S:w,B={"data-progress":"stepProgress"===y||void 0,"data-completed":"stepCompleted"===y||void 0};return(0,o.jsxs)(d.UnstyledButton,{...N.getStyles("step",{className:i,style:m,variant:N.orientation,...O}),mod:[{"icon-position":A||N.iconPosition,"allow-click":D},_],ref:t,...B,...M,__vars:{"--step-color":x?(0,n.getThemeColor)(x,z):void 0},tabIndex:D?0:-1,children:[P&&(0,o.jsxs)("span",{...N.getStyles("stepWrapper",O),children:[(0,o.jsxs)("span",{...N.getStyles("stepIcon",O),...B,children:[(0,o.jsx)(u.Transition,{mounted:"stepCompleted"===y,transition:"pop",duration:200,children:e=>(0,o.jsx)("span",{...N.getStyles("stepCompletedIcon",{style:e,...O}),children:R?(0,o.jsx)(c.Loader,{color:"var(--mantine-color-white)",size:"calc(var(--stepper-icon-size) / 2)",...N.getStyles("stepLoader",O)}):v(C,b)||(0,o.jsx)(s.CheckIcon,{size:"60%"})})}),"stepCompleted"!==y?R?(0,o.jsx)(c.Loader,{...N.getStyles("stepLoader",O),size:"calc(var(--stepper-icon-size) / 2)",color:x}):v(L||w,b):null]}),"vertical"===T&&(0,o.jsx)("span",{...N.getStyles("verticalSeparator",O),"data-active":"stepCompleted"===y||void 0})]}),(k||E)&&(0,o.jsxs)("span",{...N.getStyles("stepBody",O),"data-orientation":N.orientation,"data-icon-position":A||N.iconPosition,children:[k&&(0,o.jsx)("span",{...N.getStyles("stepLabel",O),children:v(k,b)}),E&&(0,o.jsx)("span",{...N.getStyles("stepDescription",O),children:v(E,b)})]})]})});h.classes=m.A,h.displayName="@mantine/core/StepperStep"},39832:(e,t,r)=>{"use strict";r.d(t,{FloatingIndicator:()=>b});var o=r(95155),n=r(12115),a=r(88551),l=r(68918),i=r(43664),s=r(53791),c=r(69604),u=r(36960),d=r(70181),p=r(90693),m=r(67414),v=r(20678),f={root:"m_96b553a6"};let h={},g=(0,l.createVarsResolver)((e,t)=>{let{transitionDuration:r}=t;return{root:{"--transition-duration":"number"==typeof r?"".concat(r,"ms"):r}}}),b=(0,u.factory)((e,t)=>{let r=(0,i.useProps)("FloatingIndicator",h,e),{classNames:l,className:u,style:b,styles:y,unstyled:x,vars:w,target:C,parent:S,transitionDuration:k,mod:E,displayAfterTransitionEnd:P,...j}=r,R=(0,s.useStyles)({name:"FloatingIndicator",classes:f,props:r,className:u,style:b,classNames:l,styles:y,unstyled:x,vars:w,varsResolver:g}),D=(0,n.useRef)(null),{initialized:I,hidden:A}=function(e){let{target:t,parent:r,ref:o,displayAfterTransitionEnd:a}=e,l=(0,n.useRef)(-1),[i,s]=(0,n.useState)(!1),[c,u]=(0,n.useState)("boolean"==typeof a&&a),f=()=>{if(!t||!r||!o.current)return;let e=t.getBoundingClientRect(),n=r.getBoundingClientRect(),a=window.getComputedStyle(t),l=window.getComputedStyle(r),i=(0,v.R)(a.borderTopWidth)+(0,v.R)(l.borderTopWidth),s=(0,v.R)(a.borderLeftWidth)+(0,v.R)(l.borderLeftWidth),c={top:e.top-n.top-i,left:e.left-n.left-s,width:e.width,height:e.height};o.current.style.transform="translateY(".concat(c.top,"px) translateX(").concat(c.left,"px)"),o.current.style.width="".concat(c.width,"px"),o.current.style.height="".concat(c.height,"px")},h=()=>{window.clearTimeout(l.current),o.current&&(o.current.style.transitionDuration="0ms"),f(),l.current=window.setTimeout(()=>{o.current&&(o.current.style.transitionDuration="")},30)},g=(0,n.useRef)(null),b=(0,n.useRef)(null);return(0,n.useEffect)(()=>{if(f(),t)return g.current=new ResizeObserver(h),g.current.observe(t),r&&(b.current=new ResizeObserver(h),b.current.observe(r)),()=>{var e,t;null==(e=g.current)||e.disconnect(),null==(t=b.current)||t.disconnect()}},[r,t]),(0,n.useEffect)(()=>{if(r){let e=e=>{(function(e,t){if(!t||!e)return!1;let r=t.parentNode;for(;null!=r;){if(r===e)return!0;r=r.parentNode}return!1})(e.target,r)&&(h(),u(!1))};return r.addEventListener("transitionend",e),()=>{r.removeEventListener("transitionend",e)}}},[r]),(0,d.useTimeout)(()=>{"test"!==(0,m.getEnv)()&&s(!0)},20,{autoInvoke:!0}),(0,p.useMutationObserver)(e=>{e.forEach(e=>{"attributes"===e.type&&"dir"===e.attributeName&&h()})},{attributes:!0,attributeFilter:["dir"]},()=>document.documentElement),{initialized:i,hidden:c}}({target:C,parent:S,ref:D,displayAfterTransitionEnd:P}),T=(0,a.useMergedRef)(t,D);return C&&S?(0,o.jsx)(c.Box,{ref:T,mod:[{initialized:I,hidden:A},E],...R("root"),...j}):null});b.displayName="@mantine/core/FloatingIndicator",b.classes=f},39926:(e,t,r)=>{"use strict";r.d(t,{Avatar:()=>b});var o=r(95155),n=r(12115),a=r(56204),l=r(68918),i=r(43664),s=r(53791),c=r(69604),u=r(64511),d=r(21481),p=r(54154);function m(e){return(0,o.jsx)("svg",{...e,"data-avatar-placeholder-icon":!0,viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{d:"M0.877014 7.49988C0.877014 3.84219 3.84216 0.877045 7.49985 0.877045C11.1575 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1575 14.1227 7.49985 14.1227C3.84216 14.1227 0.877014 11.1575 0.877014 7.49988ZM7.49985 1.82704C4.36683 1.82704 1.82701 4.36686 1.82701 7.49988C1.82701 8.97196 2.38774 10.3131 3.30727 11.3213C4.19074 9.94119 5.73818 9.02499 7.50023 9.02499C9.26206 9.02499 10.8093 9.94097 11.6929 11.3208C12.6121 10.3127 13.1727 8.97172 13.1727 7.49988C13.1727 4.36686 10.6328 1.82704 7.49985 1.82704ZM10.9818 11.9787C10.2839 10.7795 8.9857 9.97499 7.50023 9.97499C6.01458 9.97499 4.71624 10.7797 4.01845 11.9791C4.97952 12.7272 6.18765 13.1727 7.49985 13.1727C8.81227 13.1727 10.0206 12.727 10.9818 11.9787ZM5.14999 6.50487C5.14999 5.207 6.20212 4.15487 7.49999 4.15487C8.79786 4.15487 9.84999 5.207 9.84999 6.50487C9.84999 7.80274 8.79786 8.85487 7.49999 8.85487C6.20212 8.85487 5.14999 7.80274 5.14999 6.50487ZM7.49999 5.10487C6.72679 5.10487 6.09999 5.73167 6.09999 6.50487C6.09999 7.27807 6.72679 7.90487 7.49999 7.90487C8.27319 7.90487 8.89999 7.27807 8.89999 6.50487C8.89999 5.73167 8.27319 5.10487 7.49999 5.10487Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})}let v=["blue","cyan","grape","green","indigo","lime","orange","pink","red","teal","violet"];var f=r(64796);let h={},g=(0,l.createVarsResolver)((e,t)=>{let{size:r,radius:o,variant:n,gradient:l,color:i,autoContrast:s,name:c,allowedInitialsColors:u}=t,d="initials"===i&&"string"==typeof c?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:v,r=Math.abs(function(e){let t=0;for(let r=0;r<e.length;r+=1)t=(t<<5)-t+e.charCodeAt(r)|0;return t}(e))%t.length;return t[r]}(c,u):i,p=e.variantColorResolver({color:d||"gray",theme:e,gradient:l,variant:n||"light",autoContrast:s});return{root:{"--avatar-size":(0,a.getSize)(r,"avatar-size"),"--avatar-radius":void 0===o?void 0:(0,a.getRadius)(o),"--avatar-bg":d||n?p.background:void 0,"--avatar-color":d||n?p.color:void 0,"--avatar-bd":d||n?p.border:void 0}}}),b=(0,u.polymorphicFactory)((e,t)=>{let r=(0,i.useProps)("Avatar",h,e),{classNames:a,className:l,style:u,styles:d,unstyled:v,vars:b,src:y,alt:x,radius:w,color:C,gradient:S,imageProps:k,children:E,autoContrast:P,mod:j,name:R,allowedInitialsColors:D,...I}=r,A=(0,p.D)(),[T,_]=(0,n.useState)(!y),M=(0,s.useStyles)({name:"Avatar",props:r,classes:f.A,className:l,style:u,classNames:a,styles:d,unstyled:v,vars:b,varsResolver:g});return(0,n.useEffect)(()=>_(!y),[y]),(0,o.jsx)(c.Box,{...M("root"),mod:[{"within-group":A.withinGroup},j],ref:t,...I,children:T||!y?(0,o.jsx)("span",{...M("placeholder"),title:x,children:E||"string"==typeof R&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,r=e.split(" ");return 1===r.length?e.slice(0,t).toUpperCase():r.map(e=>e[0]).slice(0,t).join("").toUpperCase()}(R)||(0,o.jsx)(m,{})}):(0,o.jsx)("img",{...k,...M("image"),src:y,alt:x,onError:e=>{var t;_(!0),null==k||null==(t=k.onError)||t.call(k,e)}})})});b.classes=f.A,b.displayName="@mantine/core/Avatar",b.Group=d.AvatarGroup},39974:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_abbac491",item:"m_abb6bec2",itemWrapper:"m_75cd9f71",itemIcon:"m_60f83e5b"}},40135:(e,t,r)=>{"use strict";r.d(t,{useIdle:()=>a});var o=r(12115);let n={events:["keydown","mousemove","touchmove","click","scroll","wheel"],initialState:!0};function a(e,t){let{events:r,initialState:a}={...n,...t},[l,i]=(0,o.useState)(a),s=(0,o.useRef)(-1);return(0,o.useEffect)(()=>{let t=()=>{i(!1),s.current&&window.clearTimeout(s.current),s.current=window.setTimeout(()=>{i(!0)},e)};return r.forEach(e=>document.addEventListener(e,t)),s.current=window.setTimeout(()=>{i(!0)},e),()=>{r.forEach(e=>document.removeEventListener(e,t)),window.clearTimeout(s.current),s.current=-1}},[e]),l}},40160:(e,t,r)=>{"use strict";r.d(t,{d:()=>s,w:()=>i});var o=r(12115),n=r(28261);function a(e){try{return e&&JSON.parse(e)}catch(t){return e}}function l(e){return{getItem:t=>{try{return window[e].getItem(t)}catch(e){return console.warn("use-local-storage: Failed to get value from storage, localStorage is blocked"),null}},setItem:(t,r)=>{try{window[e].setItem(t,r)}catch(e){console.warn("use-local-storage: Failed to set value to storage, localStorage is blocked")}},removeItem:t=>{try{window[e].removeItem(t)}catch(e){console.warn("use-local-storage: Failed to remove value from storage, localStorage is blocked")}}}}function i(e,t){let r="localStorage"===e?"mantine-local-storage":"mantine-session-storage",{getItem:i,setItem:s,removeItem:c}=l(e);return function(l){let{key:u,defaultValue:d,getInitialValueInEffect:p=!0,sync:m=!0,deserialize:v=a,serialize:f=e=>(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"use-local-storage";try{return JSON.stringify(e)}catch(e){throw Error("@mantine/hooks ".concat(t,": Failed to serialize the value"))}})(e,t)}=l,h=(0,o.useCallback)(t=>{let r;try{r="undefined"==typeof window||!(e in window)||null===window[e]||!!t}catch(e){r=!0}if(r)return d;let o=i(u);return null!==o?v(o):d},[u,d]),[g,b]=(0,o.useState)(h(p)),y=(0,o.useCallback)(e=>{e instanceof Function?b(t=>{let o=e(t);return s(u,f(o)),queueMicrotask(()=>{window.dispatchEvent(new CustomEvent(r,{detail:{key:u,value:e(t)}}))}),o}):(s(u,f(e)),window.dispatchEvent(new CustomEvent(r,{detail:{key:u,value:e}})),b(e))},[u]),x=(0,o.useCallback)(()=>{c(u),window.dispatchEvent(new CustomEvent(r,{detail:{key:u,value:d}}))},[]);return(0,n.useWindowEvent)("storage",t=>{if(m&&t.storageArea===window[e]&&t.key===u){var r;b(v(null!=(r=t.newValue)?r:void 0))}}),(0,n.useWindowEvent)(r,e=>{m&&e.detail.key===u&&b(e.detail.value)}),(0,o.useEffect)(()=>{void 0!==d&&void 0===g&&y(d)},[d,g,y]),(0,o.useEffect)(()=>{let e=h();void 0!==e&&y(e)},[u]),[void 0===g?d:g,y,x]}}function s(e){let{getItem:t}=l(e);return function(r){let o,{key:n,defaultValue:l,deserialize:i=a}=r;try{o="undefined"==typeof window||!(e in window)||null===window[e]}catch(e){o=!0}if(o)return l;let s=t(n);return null!==s?i(s):l}}},40543:(e,t,r)=>{"use strict";r.d(t,{Kbd:()=>m});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u={root:"m_dc6f14e2"};let d={},p=(0,a.createVarsResolver)((e,t)=>{let{size:r}=t;return{root:{"--kbd-fz":(0,n.getSize)(r,"kbd-fz")}}}),m=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("Kbd",d,e),{classNames:n,className:a,style:c,styles:m,unstyled:v,vars:f,...h}=r,g=(0,i.useStyles)({name:"Kbd",classes:u,props:r,className:a,style:c,classNames:n,styles:m,unstyled:v,vars:f,varsResolver:p});return(0,o.jsx)(s.Box,{component:"kbd",ref:t,...g("root"),...h})});m.classes=u,m.displayName="@mantine/core/Kbd"},40673:(e,t,r)=>{"use strict";r.d(t,{useFetch:()=>n});var o=r(12115);function n(e){let{autoInvoke:t=!0,...r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},[n,a]=(0,o.useState)(null),[l,i]=(0,o.useState)(!1),[s,c]=(0,o.useState)(null),u=(0,o.useRef)(null),d=(0,o.useCallback)(()=>(u.current&&u.current.abort(),u.current=new AbortController,i(!0),fetch(e,{signal:u.current.signal,...r}).then(e=>e.json()).then(e=>(a(e),i(!1),e)).catch(e=>(i(!1),"AbortError"!==e.name&&c(e),e))),[e]),p=(0,o.useCallback)(()=>{if(u.current){var e;null==(e=u.current)||e.abort("")}},[]);return(0,o.useEffect)(()=>(t&&d(),()=>{u.current&&u.current.abort("")}),[d,t]),{data:n,loading:l,error:s,refetch:d,abort:p}}},40837:(e,t,r)=>{"use strict";r.d(t,{U:()=>l,x:()=>a});var o=r(12115);let n=(0,o.createContext)(null),a=n.Provider,l=()=>(0,o.useContext)(n)},41294:(e,t,r)=>{"use strict";r.d(t,{ListItem:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(71532),s=r(39974);let c={},u=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:s,styles:u,vars:d,icon:p,children:m,mod:v,...f}=(0,n.useProps)("ListItem",c,e),h=(0,i.T)(),g=p||h.icon,b={classNames:r,styles:u};return(0,o.jsx)(a.Box,{...h.getStyles("item",{...b,className:l,style:s}),component:"li",mod:[{"with-icon":!!g,centered:h.center},v],ref:t,...f,children:(0,o.jsxs)("div",{...h.getStyles("itemWrapper",b),children:[g&&(0,o.jsx)("span",{...h.getStyles("itemIcon",b),children:g}),(0,o.jsx)("span",{...h.getStyles("itemLabel",b),children:m})]})})});u.classes=s.A,u.displayName="@mantine/core/ListItem"},41471:(e,t,r)=>{"use strict";r.d(t,{PillGroup:()=>f});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u=r(73548),d=r(36663),p=r(74088);let m={},v=(0,a.createVarsResolver)((e,t,r)=>{let{gap:o}=t,{size:a}=r;return{group:{"--pg-gap":void 0!==o?(0,n.getSize)(o):(0,n.getSize)(a,"pg-gap")}}}),f=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("PillGroup",m,e),{classNames:n,className:a,style:c,styles:f,unstyled:h,vars:g,size:b,disabled:y,...x}=r,w=(0,u.n)(),C=(null==w?void 0:w.size)||b||void 0,S=(0,i.useStyles)({name:"PillGroup",classes:p.A,props:r,className:a,style:c,classNames:n,styles:f,unstyled:h,vars:g,varsResolver:v,stylesCtx:{size:C},rootSelector:"group"});return(0,o.jsx)(d._,{value:{size:C,disabled:y},children:(0,o.jsx)(s.Box,{ref:t,size:C,...S("group"),...x})})});f.classes=p.A,f.displayName="@mantine/core/PillGroup"},41701:(e,t,r)=>{"use strict";r.d(t,{HoverCard:()=>p});var o=r(95155),n=r(11681);r(12115);var a=r(43664),l=r(28892),i=r(60266),s=r(54806),c=r(43400),u=r(63580);let d={openDelay:0,closeDelay:150,initiallyOpened:!1};function p(e){let{children:t,onOpen:r,onClose:c,openDelay:u,closeDelay:p,initiallyOpened:m,...v}=(0,a.useProps)("HoverCard",d,e),[f,{open:h,close:g}]=(0,n.useDisclosure)(m,{onClose:c,onOpen:r}),{openDropdown:b,closeDropdown:y}=(0,l.useDelayedHover)({open:h,close:g,openDelay:u,closeDelay:p});return(0,o.jsx)(s.H,{value:{openDropdown:b,closeDropdown:y},children:(0,o.jsx)(i.Popover,{...v,opened:f,__staticSelector:"HoverCard",children:t})})}p.displayName="@mantine/core/HoverCard",p.Target=u.HoverCardTarget,p.Dropdown=c.HoverCardDropdown,p.extend=e=>e},41707:(e,t,r)=>{"use strict";function o(e,t){let r=[...t].sort((e,t)=>e.value-t.value).find(t=>t.value>e);return r?r.value:e}function n(e,t){let r=[...t].sort((e,t)=>t.value-e.value).find(t=>t.value<e);return r?r.value:e}function a(e){let t=[...e].sort((e,t)=>e.value-t.value);return t.length>0?t[0].value:0}function l(e){let t=[...e].sort((e,t)=>e.value-t.value);return t.length>0?t[t.length-1].value:100}r.d(t,{C8:()=>o,HE:()=>a,Mh:()=>n,rq:()=>l})},42445:(e,t,r)=>{"use strict";r.d(t,{CopyButton:()=>i});var o=r(95155),n=r(77213);r(12115);var a=r(43664);let l={timeout:1e3};function i(e){let{children:t,timeout:r,value:i,...s}=(0,a.useProps)("CopyButton",l,e),c=(0,n.useClipboard)({timeout:r});return(0,o.jsx)(o.Fragment,{children:t({copy:()=>c.copy(i),copied:c.copied,...s})})}i.displayName="@mantine/core/CopyButton"},42846:()=>{},43007:(e,t,r)=>{"use strict";r.d(t,{useTextSelection:()=>a});var o=r(12115),n=r(66437);function a(){let e=(0,n.useForceUpdate)(),[t,r]=(0,o.useState)(null),a=()=>{r(document.getSelection()),e()};return(0,o.useEffect)(()=>(r(document.getSelection()),document.addEventListener("selectionchange",a),()=>document.removeEventListener("selectionchange",a)),[]),t}},43171:(e,t,r)=>{"use strict";r.d(t,{a:()=>a,useCheckboxGroupContext:()=>l});var o=r(12115);let n=(0,o.createContext)(null),a=n.Provider,l=()=>(0,o.useContext)(n)},43400:(e,t,r)=>{"use strict";r.d(t,{HoverCardDropdown:()=>c});var o=r(95155);r(12115);var n=r(75240),a=r(43664),l=r(60266),i=r(54806);let s={};function c(e){let{children:t,onMouseEnter:r,onMouseLeave:c,...u}=(0,a.useProps)("HoverCardDropdown",s,e),d=(0,i.f)(),p=(0,n.createEventHandler)(r,d.openDropdown),m=(0,n.createEventHandler)(c,d.closeDropdown);return(0,o.jsx)(l.Popover.Dropdown,{onMouseEnter:p,onMouseLeave:m,...u,children:t})}c.displayName="@mantine/core/HoverCardDropdown"},43461:(e,t,r)=>{"use strict";r.d(t,{clampUseMovePosition:()=>a,useMove:()=>l});var o=r(12115),n=r(96963);function a(e){return{x:(0,n.clamp)(e.x,0,1),y:(0,n.clamp)(e.y,0,1)}}function l(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",a=(0,o.useRef)(!1),l=(0,o.useRef)(!1),i=(0,o.useRef)(0),[s,c]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{a.current=!0},[]),{ref:(0,o.useCallback)(o=>{let s=t=>{let{x:l,y:s}=t;cancelAnimationFrame(i.current),i.current=requestAnimationFrame(()=>{if(a.current&&o){o.style.userSelect="none";let t=o.getBoundingClientRect();if(t.width&&t.height){let o=(0,n.clamp)((l-t.left)/t.width,0,1);e({x:"ltr"===r?o:1-o,y:(0,n.clamp)((s-t.top)/t.height,0,1)})}}})},u=()=>{document.addEventListener("mousemove",f),document.addEventListener("mouseup",m),document.addEventListener("touchmove",g),document.addEventListener("touchend",m)},d=()=>{document.removeEventListener("mousemove",f),document.removeEventListener("mouseup",m),document.removeEventListener("touchmove",g),document.removeEventListener("touchend",m)},p=()=>{!l.current&&a.current&&(l.current=!0,"function"==typeof(null==t?void 0:t.onScrubStart)&&t.onScrubStart(),c(!0),u())},m=()=>{l.current&&a.current&&(l.current=!1,c(!1),d(),setTimeout(()=>{"function"==typeof(null==t?void 0:t.onScrubEnd)&&t.onScrubEnd()},0))},v=e=>{p(),e.preventDefault(),f(e)},f=e=>s({x:e.clientX,y:e.clientY}),h=e=>{e.cancelable&&e.preventDefault(),p(),g(e)},g=e=>{e.cancelable&&e.preventDefault(),s({x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY})};return null==o||o.addEventListener("mousedown",v),null==o||o.addEventListener("touchstart",h,{passive:!1}),()=>{o&&(o.removeEventListener("mousedown",v),o.removeEventListener("touchstart",h))}},[r,e]),active:s}}},43702:(e,t,r)=>{"use strict";r.d(t,{PaginationControl:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(43608),i=r(80864),s=r(68962);let c={withPadding:!0},u=(0,a.factory)((e,t)=>{let{classNames:r,className:a,style:s,styles:u,vars:d,active:p,disabled:m,withPadding:v,mod:f,...h}=(0,n.useProps)("PaginationControl",c,e),g=(0,i.b)(),b=m||g.disabled;return(0,o.jsx)(l.UnstyledButton,{ref:t,disabled:b,mod:[{active:p,disabled:b,"with-padding":v},f],...g.getStyles("control",{className:a,style:s,classNames:r,styles:u,active:!b}),...h})});u.classes=s.A,u.displayName="@mantine/core/PaginationControl"},44229:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var o=r(95155),n=r(12115),a=r(69604),l=r(60384),i=r(97446);let s=(0,n.forwardRef)((e,t)=>{let{max:r,min:s,value:c,position:u,label:d,dragging:p,onMouseDown:m,onKeyDownCapture:v,labelTransitionProps:f,labelAlwaysOn:h,thumbLabel:g,onFocus:b,onBlur:y,showLabelOnHover:x,isHovered:w,children:C=null,disabled:S}=e,{getStyles:k}=(0,i.s)(),[E,P]=(0,n.useState)(!1),j=h||p||E||x&&w;return(0,o.jsxs)(a.Box,{tabIndex:0,role:"slider","aria-label":g,"aria-valuemax":r,"aria-valuemin":s,"aria-valuenow":c,ref:t,__vars:{"--slider-thumb-offset":"".concat(u,"%")},...k("thumb",{focusable:!0}),mod:{dragging:p,disabled:S},onFocus:e=>{P(!0),"function"==typeof b&&b(e)},onBlur:e=>{P(!1),"function"==typeof y&&y(e)},onTouchStart:m,onMouseDown:m,onKeyDownCapture:v,onClick:e=>e.stopPropagation(),children:[C,(0,o.jsx)(l.Transition,{mounted:null!=d&&!!j,transition:"fade",duration:0,...f,children:e=>(0,o.jsx)("div",{...k("label",{style:e}),children:d})})]})});s.displayName="@mantine/core/SliderThumb"},44230:(e,t,r)=>{"use strict";r.d(t,{LoadingOverlay:()=>g});var o=r(95155);r(12115);var n=r(58750),a=r(68918),l=r(3131),i=r(43664),s=r(53791),c=r(69604),u=r(36960),d=r(83347),p=r(56231),m=r(60384),v={root:"m_6e45937b",loader:"m_e8eb006c",overlay:"m_df587f17"};let f={transitionProps:{transition:"fade",duration:0},overlayProps:{backgroundOpacity:.75},zIndex:(0,n.getDefaultZIndex)("overlay")},h=(0,a.createVarsResolver)((e,t)=>{let{zIndex:r}=t;return{root:{"--lo-z-index":null==r?void 0:r.toString()}}}),g=(0,u.factory)((e,t)=>{let r=(0,i.useProps)("LoadingOverlay",f,e),{classNames:n,className:a,style:u,styles:g,unstyled:b,vars:y,transitionProps:x,loaderProps:w,overlayProps:C,visible:S,zIndex:k,...E}=r,P=(0,l.useMantineTheme)(),j=(0,s.useStyles)({name:"LoadingOverlay",classes:v,props:r,className:a,style:u,classNames:n,styles:g,unstyled:b,vars:y,varsResolver:h}),R={...f.overlayProps,...C};return(0,o.jsx)(m.Transition,{transition:"fade",...x,mounted:!!S,children:e=>(0,o.jsxs)(c.Box,{...j("root",{style:e}),ref:t,...E,children:[(0,o.jsx)(d.Loader,{...j("loader"),unstyled:b,...w}),(0,o.jsx)(p.Overlay,{...R,...j("overlay"),darkHidden:!0,unstyled:b,color:(null==C?void 0:C.color)||P.white}),(0,o.jsx)(p.Overlay,{...R,...j("overlay"),lightHidden:!0,unstyled:b,color:(null==C?void 0:C.color)||P.colors.dark[5]})]})})});g.classes=v,g.displayName="@mantine/core/LoadingOverlay"},44541:(e,t,r)=>{"use strict";r.d(t,{ComboboxHeader:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(3826),s=r(19192);let c={},u=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:s,styles:u,vars:d,...p}=(0,n.useProps)("ComboboxHeader",c,e),m=(0,i.A)();return(0,o.jsx)(a.Box,{ref:t,...m.getStyles("header",{className:l,classNames:r,style:s,styles:u}),...p,onMouseDown:e=>{e.preventDefault()}})});u.classes=s.A,u.displayName="@mantine/core/ComboboxHeader"},44598:(e,t,r)=>{"use strict";r.d(t,{I:()=>d,M:()=>u});var o=r(95155),n=r(12115),a=r(56204),l=r(53791),i=r(69604),s=r(54853),c={root:"m_5f75b09e",body:"m_5f6e695e",labelWrapper:"m_d3ea56bb",label:"m_8ee546b8",description:"m_328f68c0",error:"m_8e8a99cc"};let u=c,d=(0,n.forwardRef)((e,t)=>{let{__staticSelector:r,__stylesApiProps:n,className:u,classNames:d,styles:p,unstyled:m,children:v,label:f,description:h,id:g,disabled:b,error:y,size:x,labelPosition:w="left",bodyElement:C="div",labelElement:S="label",variant:k,style:E,vars:P,mod:j,...R}=e,D=(0,l.useStyles)({name:r,props:n,className:u,style:E,classes:c,classNames:d,styles:p,unstyled:m});return(0,o.jsx)(i.Box,{...D("root"),ref:t,__vars:{"--label-fz":(0,a.getFontSize)(x),"--label-lh":(0,a.getSize)(x,"label-lh")},mod:[{"label-position":w},j],variant:k,size:x,...R,children:(0,o.jsxs)(i.Box,{component:C,htmlFor:"label"===C?g:void 0,...D("body"),children:[v,(0,o.jsxs)("div",{...D("labelWrapper"),"data-disabled":b||void 0,children:[f&&(0,o.jsx)(i.Box,{component:S,htmlFor:"label"===S?g:void 0,...D("label"),"data-disabled":b||void 0,children:f}),h&&(0,o.jsx)(s.Input.Description,{size:x,__inheritStyles:!1,...D("description"),children:h}),y&&"boolean"!=typeof y&&(0,o.jsx)(s.Input.Error,{size:x,__inheritStyles:!1,...D("error"),children:y})]})]})})});d.displayName="@mantine/core/InlineInput"},44655:(e,t,r)=>{"use strict";r.d(t,{useLogger:()=>a});var o=r(12115),n=r(84237);function a(e,t){return(0,o.useEffect)(()=>(console.log("".concat(e," mounted"),...t),()=>console.log("".concat(e," unmounted"))),[]),(0,n.useDidUpdate)(()=>{console.log("".concat(e," updated"),...t)},t),null}},44710:(e,t,r)=>{"use strict";r.d(t,{AccordionItem:()=>d});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(9614),s=r(89535),c=r(54492);let u={},d=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:c,styles:d,vars:p,value:m,mod:v,...f}=(0,n.useProps)("AccordionItem",u,e),h=(0,i.D)();return(0,o.jsx)(s.T,{value:{value:m},children:(0,o.jsx)(a.Box,{ref:t,mod:[{active:h.isItemActive(m)},v],...h.getStyles("item",{className:l,classNames:r,styles:d,style:c,variant:h.variant}),...f})})});d.displayName="@mantine/core/AccordionItem",d.classes=c.A},45510:(e,t,r)=>{"use strict";r.d(t,{TabsPanel:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(22856),s=r(51434);let c={},u=(0,l.factory)((e,t)=>{let r=(0,n.useProps)("TabsPanel",c,e),{children:l,className:s,value:u,classNames:d,styles:p,style:m,mod:v,keepMounted:f,...h}=r,g=(0,i.f)(),b=g.value===u,y=g.keepMounted||f||b?l:null;return(0,o.jsx)(a.Box,{...h,...g.getStyles("panel",{className:s,classNames:d,styles:p,style:[m,b?void 0:{display:"none"}],props:r}),ref:t,mod:[{orientation:g.orientation},v],role:"tabpanel",id:g.getPanelId(u),"aria-labelledby":g.getTabId(u),children:y})});u.classes=s.A,u.displayName="@mantine/core/TabsPanel"},45949:(e,t,r)=>{"use strict";r.d(t,{Timeline:()=>x});var o=r(95155),n=r(12115),a=r(5903),l=r(56204),i=r(68918),s=r(71180),c=r(89200),u=r(98840),d=r(43664),p=r(53791),m=r(69604),v=r(36960),f=r(27886),h=r(16173),g=r(31196);let b={active:-1,align:"left"},y=(0,i.createVarsResolver)((e,t)=>{let{bulletSize:r,lineWidth:o,radius:n,color:i,autoContrast:d}=t;return{root:{"--tl-bullet-size":(0,a.D)(r),"--tl-line-width":(0,a.D)(o),"--tl-radius":void 0===n?void 0:(0,l.getRadius)(n),"--tl-color":i?(0,s.getThemeColor)(i,e):void 0,"--tl-icon-color":(0,u.getAutoContrastValue)(d,e)?(0,c.getContrastColor)({color:i,theme:e,autoContrast:d}):void 0}}}),x=(0,v.factory)((e,t)=>{let r=(0,d.useProps)("Timeline",b,e),{classNames:a,className:l,style:i,styles:s,unstyled:c,vars:u,children:v,active:h,color:x,radius:w,bulletSize:C,align:S,lineWidth:k,reverseActive:E,mod:P,autoContrast:j,...R}=r,D=(0,p.useStyles)({name:"Timeline",classes:g.A,props:r,className:l,style:i,classNames:a,styles:s,unstyled:c,vars:u,varsResolver:y}),I=n.Children.toArray(v),A=I.map((e,t)=>{var r,o;return(0,n.cloneElement)(e,{unstyled:c,__align:S,__active:(null==(r=e.props)?void 0:r.active)||(E?h>=I.length-t-1:h>=t),__lineActive:(null==(o=e.props)?void 0:o.lineActive)||(E?h>=I.length-t-1:h-1>=t)})});return(0,o.jsx)(f.V,{value:{getStyles:D},children:(0,o.jsx)(m.Box,{...D("root"),mod:[{align:S},P],ref:t,...R,children:A})})});x.classes=g.A,x.displayName="@mantine/core/Timeline",x.Item=h.TimelineItem},46647:(e,t,r)=>{"use strict";r.d(t,{Tabs:()=>k});var o=r(95155),n=r(64173),a=r(57613);r(12115);var l=r(25954),i=r(56204),s=r(68918),c=r(71180),u=r(89200),d=r(98840),p=r(43664),m=r(53791),v=r(69604),f=r(36960),h=r(22856),g=r(52668),b=r(45510),y=r(15542),x=r(51434);let w="Tabs.Tab or Tabs.Panel component was rendered with invalid value or without value",C={keepMounted:!0,orientation:"horizontal",loop:!0,activateTabWithKeyboard:!0,variant:"default",placement:"left"},S=(0,s.createVarsResolver)((e,t)=>{let{radius:r,color:o,autoContrast:n}=t;return{root:{"--tabs-radius":(0,i.getRadius)(r),"--tabs-color":(0,c.getThemeColor)(o,e),"--tabs-text-color":(0,d.getAutoContrastValue)(n,e)?(0,u.getContrastColor)({color:o,theme:e,autoContrast:n}):void 0}}}),k=(0,f.factory)((e,t)=>{let r=(0,p.useProps)("Tabs",C,e),{defaultValue:i,value:s,onChange:c,orientation:u,children:d,loop:f,id:g,activateTabWithKeyboard:b,allowTabDeactivation:y,variant:k,color:E,radius:P,inverted:j,placement:R,keepMounted:D,classNames:I,styles:A,unstyled:T,className:_,style:M,vars:N,autoContrast:z,mod:O,...L}=r,B=(0,n.useId)(g),[F,V]=(0,a.useUncontrolled)({value:s,defaultValue:i,finalValue:null,onChange:c}),H=(0,m.useStyles)({name:"Tabs",props:r,classes:x.A,className:_,style:M,classNames:I,styles:A,unstyled:T,vars:N,varsResolver:S});return(0,o.jsx)(h.O,{value:{placement:R,value:F,orientation:u,id:B,loop:f,activateTabWithKeyboard:b,getTabId:(0,l.getSafeId)("".concat(B,"-tab"),w),getPanelId:(0,l.getSafeId)("".concat(B,"-panel"),w),onChange:V,allowTabDeactivation:y,variant:k,color:E,radius:P,inverted:j,keepMounted:D,unstyled:T,getStyles:H},children:(0,o.jsx)(v.Box,{ref:t,id:B,variant:k,mod:[{orientation:u,inverted:"horizontal"===u&&j,placement:"vertical"===u&&R},O],...H("root"),...L,children:d})})});k.classes=x.A,k.displayName="@mantine/core/Tabs",k.Tab=y.TabsTab,k.Panel=b.TabsPanel,k.List=g.TabsList},47703:(e,t,r)=>{"use strict";r.d(t,{localStorageColorSchemeManager:()=>n});var o=r(82685);function n(){let e,{key:t="mantine-color-scheme-value"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{get:e=>{if("undefined"==typeof window)return e;try{let r=window.localStorage.getItem(t);return(0,o.isMantineColorScheme)(r)?r:e}catch(t){return e}},set:e=>{try{window.localStorage.setItem(t,e)}catch(e){console.warn("[@mantine/core] Local storage color scheme manager was unable to save color scheme.",e)}},subscribe:r=>{e=e=>{e.storageArea===window.localStorage&&e.key===t&&(0,o.isMantineColorScheme)(e.newValue)&&r(e.newValue)},window.addEventListener("storage",e)},unsubscribe:()=>{window.removeEventListener("storage",e)},clear:()=>{window.localStorage.removeItem(t)}}}},47780:(e,t,r)=>{"use strict";r.d(t,{RingProgress:()=>b});var o=r(95155),n=r(12115),a=r(5903),l=r(68918),i=r(43664),s=r(53791),c=r(69604),u=r(36960),d=r(71180),p=r(3131),m=r(27361);function v(e){let{size:t,value:r,offset:n,sum:a,thickness:l,root:i,color:s,lineRoundCaps:u,tooltip:v,getStyles:f,display:h,...g}=e,b=(0,p.useMantineTheme)();return(0,o.jsx)(m.Tooltip.Floating,{disabled:!v,label:v,children:(0,o.jsx)(c.Box,{component:"circle",...g,...f("curve"),__vars:{"--curve-color":s?(0,d.getThemeColor)(s,b):void 0},fill:"none",strokeLinecap:u?"round":"butt",...function(e){let{size:t,thickness:r,sum:o,value:n,root:a,offset:l}=e,i=(.9*t-2*r)/2,s=Math.PI*i*2/100,c=a||void 0===n?"".concat((100-o)*s,", ").concat(o*s):"".concat(n*s,", ").concat((100-n)*s);return{strokeWidth:Number.isNaN(r)?12:r,cx:t/2||0,cy:t/2||0,r:i||0,transform:a?"scale(1, -1) translate(0, -".concat(t,")"):void 0,strokeDasharray:c,strokeDashoffset:a?0:l||0}}({sum:a,size:t,thickness:l,value:r,offset:n,root:i})})})}v.displayName="@mantine/core/Curve";var f={root:"m_b32e4812",svg:"m_d43b5134",curve:"m_b1ca1fbf",label:"m_b23f9dc4"};let h={size:120,thickness:12},g=(0,l.createVarsResolver)((e,t)=>{let{size:r,thickness:o,transitionDuration:n}=t;return{root:{"--rp-size":(0,a.D)(r),"--rp-label-offset":(0,a.D)(2*o),"--rp-transition-duration":n?"".concat(n,"ms"):void 0}}}),b=(0,u.factory)((e,t)=>{let r=(0,i.useProps)("RingProgress",h,e),{classNames:a,className:l,style:u,styles:d,unstyled:p,vars:m,label:b,sections:y,size:x,thickness:w,roundCaps:C,rootColor:S,transitionDuration:k,...E}=r,P=(0,s.useStyles)({name:"RingProgress",classes:f,props:r,className:l,style:u,classNames:a,styles:d,unstyled:p,vars:m,varsResolver:g}),j=Math.min(w||12,(x||120)/4),R=(function(e){let{size:t,thickness:r,sections:o,renderRoundedLineCaps:n,rootColor:a}=e,l=o.reduce((e,t)=>e+t.value,0),i=(.9*t-2*r)/2*Math.PI*2,s=i,c=[],u=[];for(let e=0;e<o.length;e+=1)c.push({sum:l,offset:s,data:o[e],root:!1}),s-=o[e].value/100*i;if(c.push({sum:l,offset:s,data:{color:a},root:!0}),u.push({...c[c.length-1],lineRoundCaps:!1}),c.length>2){u.push({...c[0],lineRoundCaps:n}),u.push({...c[c.length-2],lineRoundCaps:n});for(let e=1;e<=c.length-3;e+=1)u.push({...c[e],lineRoundCaps:!1})}else u.push({...c[0],lineRoundCaps:n});return u})({size:x,thickness:j,sections:y,renderRoundedLineCaps:C,rootColor:S}).map((e,t)=>{let{data:r,sum:o,root:a,lineRoundCaps:l,offset:i}=e;return(0,n.createElement)(v,{...r,key:t,size:x,thickness:j,sum:o,offset:i,color:null==r?void 0:r.color,root:a,lineRoundCaps:l,getStyles:P})});return(0,o.jsxs)(c.Box,{...P("root"),size:x,ref:t,...E,children:[(0,o.jsx)("svg",{...P("svg"),children:R}),b&&(0,o.jsx)("div",{...P("label"),children:b})]})});b.classes=f,b.displayName="@mantine/core/RingProgress"},48411:(e,t,r)=>{"use strict";r.d(t,{ComboboxSearch:()=>p});var o=r(95155),n=r(88551);r(12115);var a=r(43664),l=r(36960),i=r(54853),s=r(3826),c=r(85351),u=r(19192);let d={withAriaAttributes:!0,withKeyboardNavigation:!0},p=(0,l.factory)((e,t)=>{let{classNames:r,styles:l,unstyled:u,vars:p,withAriaAttributes:m,onKeyDown:v,withKeyboardNavigation:f,size:h,...g}=(0,a.useProps)("ComboboxSearch",d,e),b=(0,s.A)(),y=b.getStyles("search"),x=(0,c.useComboboxTargetProps)({targetType:"input",withAriaAttributes:m,withKeyboardNavigation:f,withExpandedAttribute:!1,onKeyDown:v,autoComplete:"off"});return(0,o.jsx)(i.Input,{ref:(0,n.useMergedRef)(t,b.store.searchRef),classNames:[{input:y.className},r],styles:[{input:y.style},l],size:h||b.size,...x,...g,__staticSelector:"Combobox"})});p.classes=u.A,p.displayName="@mantine/core/ComboboxSearch"},48509:(e,t,r)=>{"use strict";r.d(t,{getLabelsLockup:()=>function e(t){return t.reduce((t,r)=>"group"in r?{...t,...e(r.items)}:(t[r.label]=r,t),{})},getOptionsLockup:()=>function e(t){return t.reduce((t,r)=>"group"in r?{...t,...e(r.items)}:(t[r.value]=r,t),{})}})},48827:(e,t,r)=>{"use strict";r.d(t,{MenuLabel:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(65054),s=r(69324);let c={},u=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:s,styles:u,vars:d,...p}=(0,n.useProps)("MenuLabel",c,e),m=(0,i.K)();return(0,o.jsx)(a.Box,{ref:t,...m.getStyles("label",{className:l,style:s,styles:u,classNames:r}),...p})});u.classes=s.A,u.displayName="@mantine/core/MenuLabel"},49077:(e,t,r)=>{"use strict";r.d(t,{Image:()=>v});var o=r(95155),n=r(12115),a=r(56204),l=r(68918),i=r(43664),s=r(53791),c=r(69604),u=r(64511),d={root:"m_9e117634"};let p={},m=(0,l.createVarsResolver)((e,t)=>{let{radius:r,fit:o}=t;return{root:{"--image-radius":void 0===r?void 0:(0,a.getRadius)(r),"--image-object-fit":o}}}),v=(0,u.polymorphicFactory)((e,t)=>{let r=(0,i.useProps)("Image",p,e),{classNames:a,className:l,style:u,styles:v,unstyled:f,vars:h,onError:g,src:b,radius:y,fit:x,fallbackSrc:w,mod:C,...S}=r,[k,E]=(0,n.useState)(!b);(0,n.useEffect)(()=>E(!b),[b]);let P=(0,s.useStyles)({name:"Image",classes:d,props:r,className:l,style:u,classNames:a,styles:v,unstyled:f,vars:h,varsResolver:m});return k&&w?(0,o.jsx)(c.Box,{component:"img",ref:t,src:w,...P("root"),onError:g,mod:["fallback",C],...S}):(0,o.jsx)(c.Box,{component:"img",ref:t,...P("root"),src:b,onError:e=>{null==g||g(e),E(!0)},mod:C,...S})});v.classes=d,v.displayName="@mantine/core/Image"},50238:(e,t,r)=>{"use strict";r.d(t,{getContextItemIndex:()=>n});var o=r(91834);function n(e,t,r){var n;return r?Array.from((null==(n=(0,o.findElementAncestor)(r,t))?void 0:n.querySelectorAll(e))||[]).findIndex(e=>e===r):null}},50937:(e,t,r)=>{"use strict";r.d(t,{TooltipFloating:()=>E});var o=r(95155),n=r(12115),a=r(88551),l=r(10866),i=r(58750),s=r(56204),c=r(72200),u=r(68918),d=r(71180),p=r(3131),m=r(43664),v=r(53791),f=r(52736),h=r(69604),g=r(36960),b=r(62143),y=r(45299),x=r(84945),w=r(86301),C=r(572);let S={refProp:"ref",withinPortal:!0,offset:10,position:"right",zIndex:(0,i.getDefaultZIndex)("popover")},k=(0,u.createVarsResolver)((e,t)=>{let{radius:r,color:o}=t;return{tooltip:{"--tooltip-radius":void 0===r?void 0:(0,s.getRadius)(r),"--tooltip-bg":o?(0,d.getThemeColor)(o,e):void 0,"--tooltip-color":o?"var(--mantine-color-white)":void 0}}}),E=(0,g.factory)((e,t)=>{var r,i;let s=(0,m.useProps)("TooltipFloating",S,e),{children:u,refProp:d,withinPortal:g,style:E,className:P,classNames:j,styles:R,unstyled:D,radius:I,color:A,label:T,offset:_,position:M,multiline:N,zIndex:z,disabled:O,defaultOpened:L,variant:B,vars:F,portalProps:V,...H}=s,U=(0,p.useMantineTheme)(),G=(0,v.useStyles)({name:"TooltipFloating",props:s,classes:C.A,className:P,style:E,classNames:j,styles:R,unstyled:D,rootSelector:"tooltip",vars:F,varsResolver:k}),{handleMouseMove:W,x:q,y:K,opened:X,boundaryRef:Z,floating:Y,setOpened:Q}=function(e){let{offset:t,position:r,defaultOpened:o}=e,[a,l]=(0,n.useState)(o),i=(0,n.useRef)(null),{x:s,y:c,elements:u,refs:d,update:p,placement:m}=(0,y.we)({placement:r,middleware:[(0,x.BN)({crossAxis:!0,padding:5,rootBoundary:"document"})]}),v=m.includes("right")?t:r.includes("left")?-1*t:0,f=m.includes("bottom")?t:r.includes("top")?-1*t:0,h=(0,n.useCallback)(e=>{let{clientX:t,clientY:r}=e;d.setPositionReference({getBoundingClientRect:()=>({width:0,height:0,x:t,y:r,left:t+v,top:r+f,right:t,bottom:r})})},[u.reference]);return(0,n.useEffect)(()=>{if(d.floating.current){let e=i.current;e.addEventListener("mousemove",h);let t=(0,w.v9)(d.floating.current);return t.forEach(e=>{e.addEventListener("scroll",p)}),()=>{e.removeEventListener("mousemove",h),t.forEach(e=>{e.removeEventListener("scroll",p)})}}},[u.reference,d.floating.current,p,h,a]),{handleMouseMove:h,x:s,y:c,opened:a,setOpened:l,boundaryRef:i,floating:d.setFloating}}({offset:_,position:M,defaultOpened:L});if(!(0,l.isElement)(u))throw Error("[@mantine/core] Tooltip.Floating component children should be an element or a component that accepts ref, fragments, strings, numbers and other primitive values are not supported");let $=(0,a.useMergedRef)(Z,(0,c.getRefProp)(u),t),J=u.props;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(b.OptionalPortal,{...V,withinPortal:g,children:(0,o.jsx)(h.Box,{...H,...G("tooltip",{style:{...(0,f.getStyleObject)(E,U),zIndex:z,display:!O&&X?"block":"none",top:null!=(r=K&&Math.round(K))?r:"",left:null!=(i=q&&Math.round(q))?i:""}}),variant:B,ref:Y,mod:{multiline:N},children:T})}),(0,n.cloneElement)(u,{...J,[d]:$,onMouseEnter:e=>{var t;null==(t=J.onMouseEnter)||t.call(J,e),W(e),Q(!0)},onMouseLeave:e=>{var t;null==(t=J.onMouseLeave)||t.call(J,e),Q(!1)}})]})});E.classes=C.A,E.displayName="@mantine/core/TooltipFloating"},51133:(e,t,r)=>{"use strict";r.d(t,{usePageLeave:()=>n});var o=r(12115);function n(e){(0,o.useEffect)(()=>(document.documentElement.addEventListener("mouseleave",e),()=>document.documentElement.removeEventListener("mouseleave",e)),[])}},51246:(e,t,r)=>{"use strict";r.d(t,{P:()=>n,s:()=>a}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Stepper component was not found in tree")},51423:(e,t,r)=>{"use strict";r.d(t,{PopoverDropdown:()=>b});var o=r(95155),n=r(60497),a=r(88551),l=r(5903);r(12115);var i=r(83742),s=r(43664),c=r(69604),u=r(36960),d=r(89691),p=r(49781),m=r(62143),v=r(60384),f=r(66142),h=r(3500);let g={},b=(0,u.factory)((e,t)=>{var r,u,h,b,y;let x=(0,s.useProps)("PopoverDropdown",g,e),{className:w,style:C,vars:S,children:k,onKeyDownCapture:E,variant:P,classNames:j,styles:R,...D}=x,I=(0,f.C)(),A=(0,n.useFocusReturn)({opened:I.opened,shouldReturnFocus:I.returnFocus}),T=I.withRoles?{"aria-labelledby":I.getTargetId(),id:I.getDropdownId(),role:"dialog",tabIndex:-1}:{},_=(0,a.useMergedRef)(t,I.floating);return I.disabled?null:(0,o.jsx)(m.OptionalPortal,{...I.portalProps,withinPortal:I.withinPortal,children:(0,o.jsx)(v.Transition,{mounted:I.opened,...I.transitionProps,transition:(null==(r=I.transitionProps)?void 0:r.transition)||"fade",duration:null!=(y=null==(u=I.transitionProps)?void 0:u.duration)?y:150,keepMounted:I.keepMounted,exitDuration:"number"==typeof(null==(h=I.transitionProps)?void 0:h.exitDuration)?I.transitionProps.exitDuration:null==(b=I.transitionProps)?void 0:b.duration,children:e=>{var t,r;return(0,o.jsx)(p.FocusTrap,{active:I.trapFocus&&I.opened,innerRef:_,children:(0,o.jsxs)(c.Box,{...T,...D,variant:P,onKeyDownCapture:(0,i.closeOnEscape)(()=>{var e,t;null==(e=I.onClose)||e.call(I),null==(t=I.onDismiss)||t.call(I)},{active:I.closeOnEscape,onTrigger:A,onKeyDown:E}),"data-position":I.placement,"data-fixed":"fixed"===I.floatingStrategy||void 0,...I.getStyles("dropdown",{className:w,props:x,classNames:j,styles:R,style:[{...e,zIndex:I.zIndex,top:null!=(t=I.y)?t:0,left:null!=(r=I.x)?r:0,width:"target"===I.width?void 0:(0,l.D)(I.width),...I.referenceHidden?{display:"none"}:null},I.resolvedStyles.dropdown,null==R?void 0:R.dropdown,C]}),children:[k,(0,o.jsx)(d.FloatingArrow,{ref:I.arrowRef,arrowX:I.arrowX,arrowY:I.arrowY,visible:I.withArrow,position:I.placement,arrowSize:I.arrowSize,arrowRadius:I.arrowRadius,arrowOffset:I.arrowOffset,arrowPosition:I.arrowPosition,...I.getStyles("arrow",{props:x,classNames:j,styles:R})})]})})}})})});b.classes=h.A,b.displayName="@mantine/core/PopoverDropdown"},51433:(e,t,r)=>{"use strict";r.d(t,{useFavicon:()=>l});var o=r(12115),n=r(73141);let a={ico:"image/x-icon",png:"image/png",svg:"image/svg+xml",gif:"image/gif"};function l(e){let t=(0,o.useRef)(null);(0,n.useIsomorphicEffect)(()=>{if(!e)return;if(!t.current){document.querySelectorAll('link[rel*="icon"]').forEach(e=>document.head.removeChild(e));let e=document.createElement("link");e.rel="shortcut icon",t.current=e,document.querySelector("head").appendChild(e)}let r=e.split(".");t.current.setAttribute("type",a[r[r.length-1].toLowerCase()]),t.current.setAttribute("href",e)},[e])}},51434:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_89d60db1","list--default":"m_576c9d4",list:"m_89d33d6d",tab:"m_4ec4dce6",panel:"m_b0c91715",tabSection:"m_fc420b1f",tabLabel:"m_42bbd1ae","tab--default":"m_539e827b","list--outline":"m_6772fbd5","tab--outline":"m_b59ab47c","tab--pills":"m_c3381914"}},52045:(e,t,r)=>{"use strict";r.d(t,{useEventListener:()=>n});var o=r(12115);function n(e,t,r){let n=(0,o.useRef)(null),a=(0,o.useRef)(null),l=(0,o.useCallback)(o=>{o&&(a.current&&n.current&&a.current.removeEventListener(e,n.current,r),o.addEventListener(e,t,r),a.current=o,n.current=t)},[e,t,r]);return(0,o.useEffect)(()=>()=>{a.current&&n.current&&a.current.removeEventListener(e,n.current,r)},[e,r]),l}},52223:(e,t,r)=>{"use strict";r.d(t,{useProviderColorScheme:()=>l});var o=r(12115),n=r(73141);function a(e,t){var r,o;let n="undefined"!=typeof window&&"matchMedia"in window&&(null==(r=window.matchMedia("(prefers-color-scheme: dark)"))?void 0:r.matches);null==(o=t())||o.setAttribute("data-mantine-color-scheme","auto"!==e?e:n?"dark":"light")}function l(e){let{manager:t,defaultColorScheme:r,getRootElement:l,forceColorScheme:i}=e,s=(0,o.useRef)(null),[c,u]=(0,o.useState)(()=>t.get(r)),d=i||c,p=(0,o.useCallback)(e=>{i||(a(e,l),u(e),t.set(e))},[t.set,d,i]),m=(0,o.useCallback)(()=>{u(r),a(r,l),t.clear()},[t.clear,r]);return(0,o.useEffect)(()=>(t.subscribe(p),t.unsubscribe),[t.subscribe,t.unsubscribe]),(0,n.useIsomorphicEffect)(()=>{a(t.get(r),l)},[]),(0,o.useEffect)(()=>{var e;if(i)return a(i,l),()=>{};void 0===i&&a(c,l),"undefined"!=typeof window&&"matchMedia"in window&&(s.current=window.matchMedia("(prefers-color-scheme: dark)"));let t=e=>{"auto"===c&&a(e.matches?"dark":"light",l)};return null==(e=s.current)||e.addEventListener("change",t),()=>{var e;return null==(e=s.current)?void 0:e.removeEventListener("change",t)}},[c,i]),{colorScheme:d,setColorScheme:p,clearColorScheme:m}}},52547:(e,t,r)=>{"use strict";r.d(t,{useDrawersStack:()=>a,useModalsStack:()=>n});var o=r(12115);function n(e){let t=e.reduce((e,t)=>({...e,[t]:!1}),{}),[r,n]=(0,o.useState)(t),a=(0,o.useCallback)(e=>{n(t=>({...t,[e]:!0}))},[]),l=(0,o.useCallback)(e=>n(t=>({...t,[e]:!1})),[]),i=(0,o.useCallback)(e=>n(t=>({...t,[e]:!t[e]})),[]),s=(0,o.useCallback)(()=>n(t),[]),c=(0,o.useCallback)(e=>({opened:r[e],onClose:()=>l(e),stackId:e}),[r]);return{state:r,open:a,close:l,closeAll:s,toggle:i,register:c}}let a=n},52668:(e,t,r)=>{"use strict";r.d(t,{TabsList:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(22856),s=r(51434);let c={},u=(0,l.factory)((e,t)=>{let r=(0,n.useProps)("TabsList",c,e),{children:l,className:s,grow:u,justify:d,classNames:p,styles:m,style:v,mod:f,...h}=r,g=(0,i.f)();return(0,o.jsx)(a.Box,{...h,...g.getStyles("list",{className:s,style:v,classNames:p,styles:m,props:r,variant:g.variant}),ref:t,role:"tablist",variant:g.variant,mod:[{grow:u,orientation:g.orientation,placement:"vertical"===g.orientation&&g.placement,inverted:g.inverted},f],"aria-orientation":g.orientation,__vars:{"--tabs-justify":d},children:l})});u.classes=s.A,u.displayName="@mantine/core/TabsList"},53051:(e,t,r)=>{"use strict";r.d(t,{M:()=>o,R:()=>n}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)()},53291:(e,t,r)=>{"use strict";r.d(t,{List:()=>f});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u=r(71532),d=r(41294),p=r(39974);let m={type:"unordered"},v=(0,a.createVarsResolver)((e,t)=>{let{size:r,spacing:o}=t;return{root:{"--list-fz":(0,n.getFontSize)(r),"--list-lh":(0,n.getLineHeight)(r),"--list-spacing":(0,n.getSpacing)(o)}}}),f=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("List",m,e),{classNames:n,className:a,style:c,styles:d,unstyled:f,vars:h,children:g,type:b,withPadding:y,icon:x,spacing:w,center:C,listStyleType:S,mod:k,...E}=r,P=(0,i.useStyles)({name:"List",classes:p.A,props:r,className:a,style:c,classNames:n,styles:d,unstyled:f,vars:h,varsResolver:v});return(0,o.jsx)(u.C,{value:{center:C,icon:x,getStyles:P},children:(0,o.jsx)(s.Box,{...P("root",{style:{listStyleType:S}}),component:"unordered"===b?"ul":"ol",mod:[{"with-padding":y},k],ref:t,...E,children:g})})});f.classes=p.A,f.displayName="@mantine/core/List",f.Item=d.ListItem},53527:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>h});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(71180),i=r(43664),s=r(53791),c=r(69604),u=r(36960),d=r(95642),p=r(83347),m={root:"m_a513464",icon:"m_a4ceffb",loader:"m_b0920b15",body:"m_a49ed24",title:"m_3feedf16",description:"m_3d733a3a",closeButton:"m_919a4d88"};let v={withCloseButton:!0},f=(0,a.createVarsResolver)((e,t)=>{let{radius:r,color:o}=t;return{root:{"--notification-radius":void 0===r?void 0:(0,n.getRadius)(r),"--notification-color":o?(0,l.getThemeColor)(o,e):void 0}}}),h=(0,u.factory)((e,t)=>{let r=(0,i.useProps)("Notification",v,e),{className:n,color:a,radius:l,loading:u,withCloseButton:h,withBorder:g,title:b,icon:y,children:x,onClose:w,closeButtonProps:C,classNames:S,style:k,styles:E,unstyled:P,variant:j,vars:R,mod:D,loaderProps:I,role:A,...T}=r,_=(0,s.useStyles)({name:"Notification",classes:m,props:r,className:n,style:k,classNames:S,styles:E,unstyled:P,vars:R,varsResolver:f});return(0,o.jsxs)(c.Box,{..._("root"),mod:[{"data-with-icon":!!y||u,"data-with-border":g},D],ref:t,variant:j,role:A||"alert",...T,children:[y&&!u&&(0,o.jsx)("div",{..._("icon"),children:y}),u&&(0,o.jsx)(p.Loader,{size:28,color:a,...I,..._("loader")}),(0,o.jsxs)("div",{..._("body"),children:[b&&(0,o.jsx)("div",{..._("title"),children:b}),(0,o.jsx)(c.Box,{..._("description"),mod:{"data-with-title":!!b},children:x})]}),h&&(0,o.jsx)(d.CloseButton,{iconSize:16,color:"gray",...C,unstyled:P,onClick:w,..._("closeButton")})]})});h.classes=m,h.displayName="@mantine/core/Notification"},53721:(e,t,r)=>{"use strict";r.d(t,{useThrottledValue:()=>a});var o=r(12115),n=r(1259);function a(e,t){let[r,a]=(0,o.useState)(e),l=(0,o.useRef)(e),[i,s]=(0,n.Z)(a,t);return(0,o.useEffect)(()=>{e!==l.current&&(l.current=e,i(e))},[i,e]),(0,o.useEffect)(()=>s,[]),r}},53776:(e,t,r)=>{"use strict";r.d(t,{Divider:()=>v});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(71180),i=r(43664),s=r(53791),c=r(69604),u=r(36960),d={root:"m_3eebeb36",label:"m_9e365f20"};let p={orientation:"horizontal"},m=(0,a.createVarsResolver)((e,t)=>{let{color:r,variant:o,size:a}=t;return{root:{"--divider-color":r?(0,l.getThemeColor)(r,e):void 0,"--divider-border-style":o,"--divider-size":(0,n.getSize)(a,"divider-size")}}}),v=(0,u.factory)((e,t)=>{let r=(0,i.useProps)("Divider",p,e),{classNames:n,className:a,style:l,styles:u,unstyled:v,vars:f,color:h,orientation:g,label:b,labelPosition:y,mod:x,...w}=r,C=(0,s.useStyles)({name:"Divider",classes:d,props:r,className:a,style:l,classNames:n,styles:u,unstyled:v,vars:f,varsResolver:m});return(0,o.jsx)(c.Box,{ref:t,mod:[{orientation:g,"with-label":!!b},x],...C("root"),...w,role:"separator",children:b&&(0,o.jsx)(c.Box,{component:"span",mod:{position:y},...C("label"),children:b})})});v.classes=d,v.displayName="@mantine/core/Divider"},54154:(e,t,r)=>{"use strict";r.d(t,{D:()=>l,G:()=>a});var o=r(12115);let n=(0,o.createContext)(null),a=n.Provider;function l(){return{withinGroup:!!(0,o.useContext)(n)}}},54380:(e,t,r)=>{"use strict";r.d(t,{PaginationRoot:()=>y});var o=r(95155),n=r(37575);r(12115);var a=r(56204),l=r(75240),i=r(68918),s=r(71180),c=r(89200),u=r(98840),d=r(43664),p=r(53791),m=r(69604),v=r(36960),f=r(80864),h=r(68962);let g={siblings:1,boundaries:1},b=(0,i.createVarsResolver)((e,t)=>{let{size:r,radius:o,color:n,autoContrast:l}=t;return{root:{"--pagination-control-radius":void 0===o?void 0:(0,a.getRadius)(o),"--pagination-control-size":(0,a.getSize)(r,"pagination-control-size"),"--pagination-control-fz":(0,a.getFontSize)(r),"--pagination-active-bg":n?(0,s.getThemeColor)(n,e):void 0,"--pagination-active-color":(0,u.getAutoContrastValue)(l,e)?(0,c.getContrastColor)({color:n,theme:e,autoContrast:l}):void 0}}}),y=(0,v.factory)((e,t)=>{let r=(0,d.useProps)("PaginationRoot",g,e),{classNames:a,className:i,style:s,styles:c,unstyled:u,vars:v,total:y,value:x,defaultValue:w,onChange:C,disabled:S,siblings:k,boundaries:E,color:P,radius:j,onNextPage:R,onPreviousPage:D,onFirstPage:I,onLastPage:A,getItemProps:T,autoContrast:_,...M}=r,N=(0,p.useStyles)({name:"Pagination",classes:h.A,props:r,className:i,style:s,classNames:a,styles:c,unstyled:u,vars:v,varsResolver:b}),{range:z,setPage:O,next:L,previous:B,active:F,first:V,last:H}=(0,n.usePagination)({page:x,initialPage:w,onChange:C,total:y,siblings:k,boundaries:E}),U=(0,l.createEventHandler)(R,L),G=(0,l.createEventHandler)(D,B),W=(0,l.createEventHandler)(I,V),q=(0,l.createEventHandler)(A,H);return(0,o.jsx)(f.g,{value:{total:y,range:z,active:F,disabled:S,getItemProps:T,onChange:O,onNext:U,onPrevious:G,onFirst:W,onLast:q,getStyles:N},children:(0,o.jsx)(m.Box,{ref:t,...N("root"),...M})})});y.classes=h.A,y.displayName="@mantine/core/PaginationRoot"},54430:(e,t,r)=>{"use strict";r.d(t,{useRadioCardContext:()=>n,v:()=>o}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)()},54492:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_9bdbb667",panel:"m_df78851f",content:"m_4ba554d4",itemTitle:"m_8fa820a0",control:"m_4ba585b8","control--default":"m_6939a5e9","control--contained":"m_4271d21b",label:"m_df3ffa0f",chevron:"m_3f35ae96",icon:"m_9bd771fe",item:"m_9bd7b098","item--default":"m_fe19b709","item--contained":"m_1f921b3b","item--filled":"m_2cdf939a","item--separated":"m_9f59b069"}},54632:(e,t,r)=>{"use strict";r.d(t,{useVirtualizedCombobox:()=>a});var o=r(12115),n=r(57613);function a(){let{defaultOpened:e,opened:t,onOpenedChange:r,onDropdownClose:a,onDropdownOpen:l,loop:i=!0,totalOptionsCount:s,isOptionDisabled:c=()=>!1,getOptionId:u,selectedOptionIndex:d,setSelectedOptionIndex:p,activeOptionIndex:m,onSelectedOptionSubmit:v}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{totalOptionsCount:0,getOptionId:()=>null,selectedOptionIndex:1,setSelectedOptionIndex:()=>{},onSelectedOptionSubmit:()=>{}},[f,h]=(0,n.useUncontrolled)({value:t,defaultValue:e,finalValue:!1,onChange:r}),g=(0,o.useRef)(null),b=(0,o.useRef)(null),y=(0,o.useRef)(null),x=(0,o.useRef)(-1),w=(0,o.useRef)(-1),C=()=>{f||(h(!0),null==l||l())},S=()=>{f&&(h(!1),null==a||a())},k=e=>{let t=e>=s?0:e<0?s-1:e;return p(t),u(t)};(0,o.useEffect)(()=>()=>{window.clearTimeout(x.current),window.clearTimeout(w.current)},[]);let E=(0,o.useCallback)(()=>d,[]);return{dropdownOpened:f,openDropdown:C,closeDropdown:S,toggleDropdown:()=>{f?S():C()},selectedOptionIndex:d,getSelectedOptionIndex:E,selectOption:k,selectFirstOption:()=>k(function(e){let{totalOptionsCount:t,isOptionDisabled:r}=e;for(let e=0;e<t;e+=1)if(!r(e))return e;return -1}({isOptionDisabled:c,totalOptionsCount:s})),selectActiveOption:()=>k(null!=m?m:0),selectNextOption:()=>k(function(e){let{currentIndex:t,isOptionDisabled:r,totalOptionsCount:o,loop:n}=e;for(let e=t+1;e<o;e+=1)if(!r(e))return e;if(n){for(let e=0;e<o;e+=1)if(!r(e))return e}return t}({currentIndex:d,isOptionDisabled:c,totalOptionsCount:s,loop:i})),selectPreviousOption:()=>k(function(e){let{currentIndex:t,isOptionDisabled:r,totalOptionsCount:o,loop:n}=e;for(let e=t-1;e>=0;e-=1)if(!r(e))return e;if(n){for(let e=o-1;e>-1;e-=1)if(!r(e))return e}return t}({currentIndex:d,isOptionDisabled:c,totalOptionsCount:s,loop:i})),resetSelectedOption:()=>{p(-1)},updateSelectedOptionIndex:()=>{},listId:g.current,setListId:e=>{g.current=e},clickSelectedOption:()=>{null==v||v(d)},searchRef:b,focusSearchInput:()=>{x.current=window.setTimeout(()=>b.current.focus(),0)},targetRef:y,focusTarget:()=>{w.current=window.setTimeout(()=>y.current.focus(),0)}}}},54806:(e,t,r)=>{"use strict";r.d(t,{H:()=>n,f:()=>a}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("HoverCard component was not found in the tree")},55369:(e,t,r)=>{"use strict";r.d(t,{NumberFormatter:()=>i});var o=r(95155),n=r(91555);r(12115);var a=r(43664);let l={};function i(e){let{value:t,defaultValue:r,...i}=(0,a.useProps)("NumberFormatter",l,e);return void 0===t?null:(0,o.jsx)(n.HG,{displayType:"text",value:t,...i})}i.extend=e=>e,i.displayName="@mantine/core/NumberFormatter"},55436:(e,t,r)=>{"use strict";r.d(t,{ChipGroup:()=>s});var o=r(95155),n=r(57613);r(12115);var a=r(43664),l=r(65629);let i={};function s(e){let{value:t,defaultValue:r,onChange:s,multiple:c,children:u}=(0,a.useProps)("ChipGroup",i,e),[d,p]=(0,n.useUncontrolled)({value:t,defaultValue:r,finalValue:c?[]:null,onChange:s});return(0,o.jsx)(l.r,{value:{isChipSelected:e=>Array.isArray(d)?d.includes(e):e===d,onChange:e=>{let t=e.currentTarget.value;Array.isArray(d)?p(d.includes(t)?d.filter(e=>e!==t):[...d,t]):p(t)},multiple:c},children:u})}s.displayName="@mantine/core/ChipGroup"},55508:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_dd36362e",label:"m_c9357328",thumb:"m_c9a9a60a",trackContainer:"m_a8645c2",track:"m_c9ade57f",bar:"m_38aeed47",markWrapper:"m_b7b0423a",mark:"m_dd33bc19",markLabel:"m_68c77a5b"}},55568:(e,t,r)=>{"use strict";r.d(t,{ColorSchemeScript:()=>a});var o=r(95155);let n=e=>{let{defaultColorScheme:t,localStorageKey:r,forceColorScheme:o}=e;return o?'document.documentElement.setAttribute("data-mantine-color-scheme", \''.concat(o,"');"):'try {\n  var _colorScheme = window.localStorage.getItem("'.concat(r,'");\n  var colorScheme = _colorScheme === "light" || _colorScheme === "dark" || _colorScheme === "auto" ? _colorScheme : "').concat(t,'";\n  var computedColorScheme = colorScheme !== "auto" ? colorScheme : window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";\n  document.documentElement.setAttribute("data-mantine-color-scheme", computedColorScheme);\n} catch (e) {}\n')};function a(e){let{defaultColorScheme:t="light",localStorageKey:r="mantine-color-scheme-value",forceColorScheme:a,...l}=e,i=["light","dark","auto"].includes(t)?t:"light";return(0,o.jsx)("script",{...l,"data-mantine-script":!0,dangerouslySetInnerHTML:{__html:n({defaultColorScheme:i,localStorageKey:r,forceColorScheme:a})}})}},56185:(e,t,r)=>{"use strict";r.d(t,{Skeleton:()=>v});var o=r(95155),n=r(5903);r(12115);var a=r(56204),l=r(68918),i=r(43664),s=r(53791),c=r(69604),u=r(36960),d={root:"m_18320242","skeleton-fade":"m_299c329c"};let p={visible:!0,animate:!0},m=(0,l.createVarsResolver)((e,t)=>{let{width:r,height:o,radius:l,circle:i}=t;return{root:{"--skeleton-height":(0,n.D)(o),"--skeleton-width":i?(0,n.D)(o):(0,n.D)(r),"--skeleton-radius":i?"1000px":void 0===l?void 0:(0,a.getRadius)(l)}}}),v=(0,u.factory)((e,t)=>{let r=(0,i.useProps)("Skeleton",p,e),{classNames:n,className:a,style:l,styles:u,unstyled:v,vars:f,width:h,height:g,circle:b,visible:y,radius:x,animate:w,mod:C,...S}=r,k=(0,s.useStyles)({name:"Skeleton",classes:d,props:r,className:a,style:l,classNames:n,styles:u,unstyled:v,vars:f,varsResolver:m});return(0,o.jsx)(c.Box,{ref:t,...k("root"),mod:[{visible:y,animate:w},C],...S})});v.classes=d,v.displayName="@mantine/core/Skeleton"},56196:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_f11b401e",header:"m_5a7c2c9",content:"m_b8a05bbd",inner:"m_31cd769a"}},56470:(e,t,r)=>{"use strict";r.d(t,{SwitchGroup:()=>d});var o=r(95155),n=r(57613);r(12115);var a=r(43664),l=r(36960),i=r(54853),s=r(10537),c=r(40837);let u={},d=(0,l.factory)((e,t)=>{let{value:r,defaultValue:l,onChange:d,size:p,wrapperProps:m,children:v,readOnly:f,...h}=(0,a.useProps)("SwitchGroup",u,e),[g,b]=(0,n.useUncontrolled)({value:r,defaultValue:l,finalValue:[],onChange:d});return(0,o.jsx)(c.x,{value:{value:g,onChange:e=>{let t=e.currentTarget.value;f||b(g.includes(t)?g.filter(e=>e!==t):[...g,t])},size:p},children:(0,o.jsx)(i.Input.Wrapper,{size:p,ref:t,...m,...h,labelElement:"div",__staticSelector:"SwitchGroup",children:(0,o.jsx)(s.F,{role:"group",children:v})})})});d.classes=i.Input.Wrapper.classes,d.displayName="@mantine/core/SwitchGroup"},56570:(e,t,r)=>{"use strict";r.d(t,{createScopedKeydownHandler:()=>n});var o=r(91834);function n(e){let{parentSelector:t,siblingSelector:r,onKeyDown:n,loop:a=!0,activateOnFocus:l=!1,dir:i="rtl",orientation:s}=e;return e=>{var c;null==n||n(e);let u=Array.from((null==(c=(0,o.findElementAncestor)(e.currentTarget,t))?void 0:c.querySelectorAll(r))||[]).filter(r=>{var n;return n=e.currentTarget,(0,o.findElementAncestor)(n,t)===(0,o.findElementAncestor)(r,t)}),d=u.findIndex(t=>e.currentTarget===t),p=function(e,t,r){for(let r=e+1;r<t.length;r+=1)if(!t[r].disabled)return r;if(r){for(let e=0;e<t.length;e+=1)if(!t[e].disabled)return e}return e}(d,u,a),m=function(e,t,r){for(let r=e-1;r>=0;r-=1)if(!t[r].disabled)return r;if(r){for(let e=t.length-1;e>-1;e-=1)if(!t[e].disabled)return e}return e}(d,u,a),v="rtl"===i?m:p,f="rtl"===i?p:m;switch(e.key){case"ArrowRight":"horizontal"===s&&(e.stopPropagation(),e.preventDefault(),u[v].focus(),l&&u[v].click());break;case"ArrowLeft":"horizontal"===s&&(e.stopPropagation(),e.preventDefault(),u[f].focus(),l&&u[f].click());break;case"ArrowUp":"vertical"===s&&(e.stopPropagation(),e.preventDefault(),u[m].focus(),l&&u[m].click());break;case"ArrowDown":"vertical"===s&&(e.stopPropagation(),e.preventDefault(),u[p].focus(),l&&u[p].click());break;case"Home":e.stopPropagation(),e.preventDefault(),u[0].disabled||u[0].focus();break;case"End":{e.stopPropagation(),e.preventDefault();let t=u.length-1;u[t].disabled||u[t].focus()}}}}},56581:(e,t,r)=>{"use strict";r.d(t,{useValidatedState:()=>n});var o=r(12115);function n(e,t,r){let[n,a]=(0,o.useState)(e),[l,i]=(0,o.useState)(t(e)?e:void 0),[s,c]=(0,o.useState)("boolean"==typeof r?r:t(e));return[{value:n,lastValidValue:l,valid:s},e=>{t(e)?(i(e),c(!0)):c(!1),a(e)}]}},56589:(e,t,r)=>{"use strict";r.d(t,{ComboboxFooter:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(3826),s=r(19192);let c={},u=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:s,styles:u,vars:d,...p}=(0,n.useProps)("ComboboxFooter",c,e),m=(0,i.A)();return(0,o.jsx)(a.Box,{ref:t,...m.getStyles("footer",{className:l,classNames:r,style:s,styles:u}),...p,onMouseDown:e=>{e.preventDefault()}})});u.classes=s.A,u.displayName="@mantine/core/ComboboxFooter"},57130:(e,t,r)=>{"use strict";function o(e,t){return 0===t.length?e:t.reduce((t,r)=>Math.abs(r-e)<Math.abs(t-e)?r:t)}r.d(t,{findClosestNumber:()=>o})},57305:(e,t,r)=>{"use strict";r.d(t,{StepperCompleted:()=>o});let o=()=>null;o.displayName="@mantine/core/StepperCompleted"},57326:(e,t,r)=>{"use strict";r.d(t,{RadioIcon:()=>a});var o=r(95155),n=r(5903);function a(e){let{size:t,style:r,...a}=e;return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 5 5",style:{width:(0,n.D)(t),height:(0,n.D)(t),...r},"aria-hidden":!0,...a,children:(0,o.jsx)("circle",{cx:"2.5",cy:"2.5",r:"2.5",fill:"currentColor"})})}r(12115)},57681:(e,t,r)=>{"use strict";r.d(t,{useOrientation:()=>a});var o=r(12115),n=r(73141);function a(){let{defaultAngle:e=0,defaultType:t="landscape-primary",getInitialValueInEffect:r=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[a,l]=(0,o.useState)(function(e,t){if(t)return e;if("undefined"!=typeof window&&"screen"in window){var r,o,n,a;return{angle:null!=(n=null==(r=window.screen.orientation)?void 0:r.angle)?n:e.angle,type:null!=(a=null==(o=window.screen.orientation)?void 0:o.type)?a:e.type}}return e}({angle:e,type:t},r)),i=e=>{let t=e.currentTarget;l({angle:(null==t?void 0:t.angle)||0,type:(null==t?void 0:t.type)||"landscape-primary"})};return(0,n.useIsomorphicEffect)(()=>{var e;return null==(e=window.screen.orientation)||e.addEventListener("change",i),()=>{var e;return null==(e=window.screen.orientation)?void 0:e.removeEventListener("change",i)}},[]),a}},58344:(e,t,r)=>{"use strict";r.d(t,{AngleSlider:()=>b});var o=r(95155),n=r(12115),a=r(57613),l=r(19997),i=r(88551),s=r(5903),c=r(57130),u=r(68918),d=r(43664),p=r(53791),m=r(69604),v=r(36960),f={root:"m_48204f9b",marks:"m_bb9cdbad",mark:"m_481dd586",thumb:"m_bc02ba3d",label:"m_bb8e875b"};let h={step:1,withLabel:!0},g=(0,u.createVarsResolver)((e,t)=>{let{size:r,thumbSize:o}=t;return{root:{"--slider-size":(0,s.D)(r),"--thumb-size":(0,s.D)(o)}}}),b=(0,v.factory)((e,t)=>{let r=(0,d.useProps)("AngleSlider",h,e),{classNames:s,className:u,style:v,styles:b,unstyled:y,vars:x,step:w,value:C,defaultValue:S,onChange:k,onMouseDown:E,withLabel:P,marks:j,thumbSize:R,restrictToMarks:D,formatLabel:I,onChangeEnd:A,disabled:T,onTouchStart:_,name:M,hiddenInputProps:N,"aria-label":z,tabIndex:O,onScrubStart:L,onScrubEnd:B,...F}=r,V=(0,n.useRef)(null),[H,U]=(0,a.useUncontrolled)({value:C,defaultValue:S,finalValue:0,onChange:k}),{ref:G}=(0,l.useRadialMove)(e=>{V.current&&U(D&&Array.isArray(j)?(0,c.findClosestNumber)(e,j.map(e=>e.value)):e)},{step:w,onChangeEnd:A,onScrubStart:L,onScrubEnd:B}),W=(0,p.useStyles)({name:"AngleSlider",classes:f,props:r,className:u,style:v,classNames:s,styles:b,unstyled:y,vars:x,varsResolver:g}),q=null==j?void 0:j.map((e,t)=>(0,n.createElement)("div",{...W("mark",{style:{"--angle":"".concat(e.value,"deg")}}),"data-label":e.label||void 0,key:t}));return(0,o.jsxs)(m.Box,{ref:(0,i.useMergedRef)(t,V,G),...W("root",{focusable:!0}),...F,children:[q&&q.length>0&&(0,o.jsx)("div",{...W("marks"),children:q}),P&&(0,o.jsx)("div",{...W("label"),children:"function"==typeof I?I(H):H}),(0,o.jsx)("div",{tabIndex:null!=O?O:T?-1:0,role:"slider","aria-valuemax":360,"aria-valuemin":0,"aria-valuenow":H,onKeyDown:e=>{if(T)return;let t=H;if(("ArrowLeft"===e.key||"ArrowDown"===e.key)&&(t=(0,l.normalizeRadialValue)(H-w,w)),("ArrowRight"===e.key||"ArrowUp"===e.key)&&(t=(0,l.normalizeRadialValue)(H+w,w)),"Home"===e.key&&(t=0),"End"===e.key&&(t=359),D&&Array.isArray(j)){let r=j.map(e=>e.value),o=r.indexOf(H);t=-1!==o?"ArrowLeft"===e.key||"ArrowDown"===e.key?r[Math.max(0,o-1)]:"ArrowRight"===e.key||"ArrowUp"===e.key?r[Math.min(r.length-1,o+1)]:(0,c.findClosestNumber)(t,r):(0,c.findClosestNumber)(t,r)}U(t),null==A||A(t)},"aria-label":z,...W("thumb",{style:{transform:"rotate(".concat(H,"deg)")}})}),(0,o.jsx)("input",{type:"hidden",name:M,value:H,...N})]})});b.displayName="@mantine/core/AngleSlider",b.classes=f},58453:(e,t,r)=>{"use strict";r.d(t,{useCounter:()=>l});var o=r(12115),n=r(96963);let a={min:-1/0,max:1/0};function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0,{min:r,max:l}={...a,...t},[i,s]=(0,o.useState)((0,n.clamp)(e,r,l)),c=(0,o.useCallback)(()=>s(e=>(0,n.clamp)(e+1,r,l)),[r,l]),u=(0,o.useCallback)(()=>s(e=>(0,n.clamp)(e-1,r,l)),[r,l]);return[i,{increment:c,decrement:u,set:(0,o.useCallback)(e=>s((0,n.clamp)(e,r,l)),[r,l]),reset:(0,o.useCallback)(()=>s((0,n.clamp)(e,r,l)),[e,r,l])}]}},59089:(e,t,r)=>{"use strict";r.d(t,{useMouse:()=>n});var o=r(12115);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{resetOnExit:!1},[t,r]=(0,o.useState)({x:0,y:0}),n=(0,o.useRef)(null),a=e=>{if(n.current){let t=e.currentTarget.getBoundingClientRect();r({x:Math.max(0,Math.round(e.pageX-t.left-(window.scrollX||window.scrollX))),y:Math.max(0,Math.round(e.pageY-t.top-(window.scrollY||window.scrollY)))})}else r({x:e.clientX,y:e.clientY})},l=()=>r({x:0,y:0});return(0,o.useEffect)(()=>{let t=(null==n?void 0:n.current)?n.current:document;return t.addEventListener("mousemove",a),e.resetOnExit&&t.addEventListener("mouseleave",l),()=>{t.removeEventListener("mousemove",a),e.resetOnExit&&t.removeEventListener("mouseleave",l)}},[n.current]),{ref:n,...t}}},59741:(e,t,r)=>{"use strict";r.d(t,{Menu:()=>V});var o=r(95155),n=r(12115),a=r(57613),l=r(50238),i=r(86028),s=r(53791),c=r(43664),u=r(36960),d=r(28892),p=r(60266),m=r(65054),v=r(15711),f=r(27045),h=r(60185),g=r(48827),b=r(64173),y=r(11681),x=r(88551),w=r(75240),C=r(88316),S=r(69324);let k={},E=(0,u.factory)((e,t)=>{let{classNames:r,className:a,style:l,styles:i,vars:s,onMouseEnter:u,onMouseLeave:d,onKeyDown:v,children:f,...h}=(0,c.useProps)("MenuSubDropdown",k,e),g=(0,n.useRef)(null),b=(0,m.K)(),y=(0,C.m)(),S=(0,w.createEventHandler)(u,null==y?void 0:y.open),E=(0,w.createEventHandler)(d,null==y?void 0:y.close);return(0,o.jsx)(p.Popover.Dropdown,{...h,onMouseEnter:S,onMouseLeave:E,role:"menu","aria-orientation":"vertical",ref:(0,x.useMergedRef)(t,g),...b.getStyles("dropdown",{className:a,style:l,styles:i,classNames:r,withStaticClass:!1}),tabIndex:-1,"data-menu-dropdown":!0,children:f})});E.classes=S.A,E.displayName="@mantine/core/MenuSubDropdown";var P=r(56570),j=r(98271),R=r(3131),D=r(64511),I=r(53304),A=r(2020),T=r(43608);let _={},M=(0,D.polymorphicFactory)((e,t)=>{let{classNames:r,className:a,style:l,styles:i,vars:s,color:u,leftSection:d,rightSection:p,children:v,disabled:f,"data-disabled":h,closeMenuOnClick:g,...b}=(0,c.useProps)("MenuSubItem",_,e),y=(0,m.K)(),S=(0,C.m)(),k=(0,R.useMantineTheme)(),{dir:E}=(0,I.useDirection)(),D=(0,n.useRef)(null),M=u?k.variantColorResolver({color:u,theme:k,variant:"light"}):void 0,N=u?(0,j.parseThemeColor)({color:u,theme:k}):null,z=(0,w.createEventHandler)(b.onKeyDown,e=>{"ArrowRight"===e.key&&(null==S||S.open(),null==S||S.focusFirstItem()),"ArrowLeft"===e.key&&(null==S?void 0:S.parentContext)&&(S.parentContext.close(),S.parentContext.focusParentItem())}),O=(0,w.createEventHandler)(b.onClick,()=>{!h&&g&&y.closeDropdownImmediately()}),L=(0,w.createEventHandler)(b.onMouseEnter,null==S?void 0:S.open),B=(0,w.createEventHandler)(b.onMouseLeave,null==S?void 0:S.close);return(0,o.jsxs)(T.UnstyledButton,{onMouseDown:e=>e.preventDefault(),...b,unstyled:y.unstyled,tabIndex:y.menuItemTabIndex,...y.getStyles("item",{className:a,style:l,styles:i,classNames:r}),ref:(0,x.useMergedRef)(D,t),role:"menuitem",disabled:f,"data-menu-item":!0,"data-sub-menu-item":!0,"data-disabled":f||h||void 0,"data-mantine-stop-propagation":!0,onMouseEnter:L,onMouseLeave:B,onClick:O,onKeyDown:(0,P.createScopedKeydownHandler)({siblingSelector:"[data-menu-item]:not([data-disabled])",parentSelector:"[data-menu-dropdown]",activateOnFocus:!1,loop:y.loop,dir:E,orientation:"vertical",onKeyDown:z}),__vars:{"--menu-item-color":(null==N?void 0:N.isThemeColor)&&(null==N?void 0:N.shade)===void 0?"var(--mantine-color-".concat(N.color,"-6)"):null==M?void 0:M.color,"--menu-item-hover":null==M?void 0:M.hover},children:[d&&(0,o.jsx)("div",{...y.getStyles("itemSection",{styles:i,classNames:r}),"data-position":"left",children:d}),v&&(0,o.jsx)("div",{...y.getStyles("itemLabel",{styles:i,classNames:r}),children:v}),(0,o.jsx)("div",{...y.getStyles("itemSection",{styles:i,classNames:r}),"data-position":"right",children:p||(0,o.jsx)(A.AccordionChevron,{...y.getStyles("chevron"),size:14})})]})});M.classes=S.A,M.displayName="@mantine/core/MenuSubItem";var N=r(10866);function z(e){let{children:t,refProp:r}=e;if(!(0,N.isElement)(t))throw Error("Menu.Sub.Target component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");return(0,m.K)(),(0,o.jsx)(p.Popover.Target,{refProp:r,popupType:"menu",children:t})}z.displayName="@mantine/core/MenuSubTarget";let O={offset:0,position:"right-start",transitionProps:{duration:0}};function L(e){let{children:t,closeDelay:r,...n}=(0,c.useProps)("MenuSub",O,e),a=(0,b.useId)(),[l,{open:i,close:s}]=(0,y.useDisclosure)(!1),u=(0,C.m)(),{openDropdown:m,closeDropdown:v}=(0,d.useDelayedHover)({open:i,close:s,closeDelay:r,openDelay:0});return(0,o.jsx)(C.Z,{value:{opened:l,close:v,open:m,focusFirstItem:()=>window.setTimeout(()=>{var e,t;null==(t=document.getElementById("".concat(a,"-dropdown")))||null==(e=t.querySelectorAll("[data-menu-item]:not([data-disabled])")[0])||e.focus()},16),focusParentItem:()=>window.setTimeout(()=>{var e;null==(e=document.getElementById("".concat(a,"-target")))||e.focus()},16),parentContext:u},children:(0,o.jsx)(p.Popover,{opened:l,...n,withinPortal:!1,id:a,children:t})})}L.extend=e=>e,L.displayName="@mantine/core/MenuSub",L.Target=z,L.Dropdown=E,L.Item=M;var B=r(94193);let F={trapFocus:!0,closeOnItemClick:!0,withInitialFocusPlaceholder:!0,clickOutsideEvents:["mousedown","touchstart","keydown"],loop:!0,trigger:"click",openDelay:0,closeDelay:100,menuItemTabIndex:-1};function V(e){let t=(0,c.useProps)("Menu",F,e),{children:r,onOpen:u,onClose:v,opened:f,defaultOpened:h,trapFocus:g,onChange:b,closeOnItemClick:y,loop:x,closeOnEscape:w,trigger:C,openDelay:k,closeDelay:E,classNames:P,styles:j,unstyled:R,variant:D,vars:I,menuItemTabIndex:A,keepMounted:T,withInitialFocusPlaceholder:_,...M}=t,N=(0,s.useStyles)({name:"Menu",classes:S.A,props:t,classNames:P,styles:j,unstyled:R}),[z,O]=(0,a.useUncontrolled)({value:f,defaultValue:h,finalValue:!1,onChange:b}),[L,B]=(0,n.useState)(!1),V=()=>{O(!1),B(!1),z&&(null==v||v())},H=()=>{O(!0),z||null==u||u()},U=()=>{z?V():H()},{openDropdown:G,closeDropdown:W}=(0,d.useDelayedHover)({open:H,close:V,closeDelay:E,openDelay:k}),{resolvedClassNames:q,resolvedStyles:K}=(0,i.useResolvedStylesApi)({classNames:P,styles:j,props:t});return(0,o.jsx)(m.S,{value:{getStyles:N,opened:z,toggleDropdown:U,getItemIndex:e=>(0,l.getContextItemIndex)("[data-menu-item]","[data-menu-dropdown]",e),openedViaClick:L,setOpenedViaClick:B,closeOnItemClick:y,closeDropdown:"click"===C?V:W,openDropdown:"click"===C?H:G,closeDropdownImmediately:V,loop:x,trigger:C,unstyled:R,menuItemTabIndex:A,withInitialFocusPlaceholder:_},children:(0,o.jsx)(p.Popover,{...M,opened:z,onChange:U,defaultOpened:h,trapFocus:!T&&g,closeOnEscape:w,__staticSelector:"Menu",classNames:q,styles:K,unstyled:R,variant:D,keepMounted:T,children:r})})}V.extend=e=>e,V.withProps=(0,u.getWithProps)(V),V.classes=S.A,V.displayName="@mantine/core/Menu",V.Item=h.MenuItem,V.Label=g.MenuLabel,V.Dropdown=f.MenuDropdown,V.Target=B.MenuTarget,V.Divider=v.MenuDivider,V.Sub=L},59832:(e,t,r)=>{"use strict";r.d(t,{mergeThemeOverrides:()=>n});var o=r(41750);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.reduce((e,t)=>(0,o.$)(e,t),{})}r(12115),r(95155)},60185:(e,t,r)=>{"use strict";r.d(t,{MenuItem:()=>b});var o=r(95155),n=r(12115),a=r(88551),l=r(56570),i=r(75240),s=r(98271),c=r(3131),u=r(43664),d=r(64511),p=r(53304),m=r(43608),v=r(65054),f=r(88316),h=r(69324);let g={},b=(0,d.polymorphicFactory)((e,t)=>{let{classNames:r,className:d,style:h,styles:b,vars:y,color:x,closeMenuOnClick:w,leftSection:C,rightSection:S,children:k,disabled:E,"data-disabled":P,...j}=(0,u.useProps)("MenuItem",g,e),R=(0,v.K)(),D=(0,f.m)(),I=(0,c.useMantineTheme)(),{dir:A}=(0,p.useDirection)(),T=(0,n.useRef)(null),_=(0,i.createEventHandler)(j.onClick,()=>{!P&&("boolean"==typeof w?w&&R.closeDropdownImmediately():R.closeOnItemClick&&R.closeDropdownImmediately())}),M=x?I.variantColorResolver({color:x,theme:I,variant:"light"}):void 0,N=x?(0,s.parseThemeColor)({color:x,theme:I}):null,z=(0,i.createEventHandler)(j.onKeyDown,e=>{"ArrowLeft"===e.key&&D&&(D.close(),D.focusParentItem())});return(0,o.jsxs)(m.UnstyledButton,{onMouseDown:e=>e.preventDefault(),...j,unstyled:R.unstyled,tabIndex:R.menuItemTabIndex,...R.getStyles("item",{className:d,style:h,styles:b,classNames:r}),ref:(0,a.useMergedRef)(T,t),role:"menuitem",disabled:E,"data-menu-item":!0,"data-disabled":E||P||void 0,"data-mantine-stop-propagation":!0,onClick:_,onKeyDown:(0,l.createScopedKeydownHandler)({siblingSelector:"[data-menu-item]:not([data-disabled])",parentSelector:"[data-menu-dropdown]",activateOnFocus:!1,loop:R.loop,dir:A,orientation:"vertical",onKeyDown:z}),__vars:{"--menu-item-color":(null==N?void 0:N.isThemeColor)&&(null==N?void 0:N.shade)===void 0?"var(--mantine-color-".concat(N.color,"-6)"):null==M?void 0:M.color,"--menu-item-hover":null==M?void 0:M.hover},children:[C&&(0,o.jsx)("div",{...R.getStyles("itemSection",{styles:b,classNames:r}),"data-position":"left",children:C}),k&&(0,o.jsx)("div",{...R.getStyles("itemLabel",{styles:b,classNames:r}),children:k}),S&&(0,o.jsx)("div",{...R.getStyles("itemSection",{styles:b,classNames:r}),"data-position":"right",children:S})]})});b.classes=h.A,b.displayName="@mantine/core/MenuItem"},60266:(e,t,r)=>{"use strict";r.d(t,{Popover:()=>I});var o=r(95155),n=r(12115),a=r(64173),l=r(67385),i=r(58750),s=r(56204),c=r(68918),u=r(86028),d=r(53791),p=r(13656),m=r(43664),v=r(53304),f=r(180),h=r(56231),g=r(62143),b=r(60384),y=r(66142),x=r(51423),w=r(80975),C=r(84945),S=r(45299),k=r(76492),E=r(57613),P=r(84237),j=r(3500);let R={position:"bottom",offset:8,positionDependencies:[],transitionProps:{transition:"fade",duration:150},middlewares:{flip:!0,shift:!0,inline:!1},arrowSize:7,arrowOffset:5,arrowRadius:0,arrowPosition:"side",closeOnClickOutside:!0,withinPortal:!0,closeOnEscape:!0,trapFocus:!1,withRoles:!0,returnFocus:!1,withOverlay:!1,hideDetached:!0,clickOutsideEvents:["mousedown","touchstart"],zIndex:(0,i.getDefaultZIndex)("popover"),__staticSelector:"Popover",width:"max-content"},D=(0,c.createVarsResolver)((e,t)=>{let{radius:r,shadow:o}=t;return{dropdown:{"--popover-radius":void 0===r?void 0:(0,s.getRadius)(r),"--popover-shadow":(0,s.getShadow)(o)}}});function I(e){var t,r,i,s,c,x,w,I;let A=(0,m.useProps)("Popover",R,e),{children:T,position:_,offset:M,onPositionChange:N,positionDependencies:z,opened:O,transitionProps:L,onExitTransitionEnd:B,onEnterTransitionEnd:F,width:V,middlewares:H,withArrow:U,arrowSize:G,arrowOffset:W,arrowRadius:q,arrowPosition:K,unstyled:X,classNames:Z,styles:Y,closeOnClickOutside:Q,withinPortal:$,portalProps:J,closeOnEscape:ee,clickOutsideEvents:et,trapFocus:er,onClose:eo,onDismiss:en,onOpen:ea,onChange:el,zIndex:ei,radius:es,shadow:ec,id:eu,defaultOpened:ed,__staticSelector:ep,withRoles:em,disabled:ev,returnFocus:ef,variant:eh,keepMounted:eg,vars:eb,floatingStrategy:ey,withOverlay:ex,overlayProps:ew,hideDetached:eC,preventPositionChangeWhenVisible:eS,...ek}=A,eE=(0,d.useStyles)({name:ep,props:A,classes:j.A,classNames:Z,styles:Y,unstyled:X,rootSelector:"dropdown",vars:eb,varsResolver:D}),{resolvedStyles:eP}=(0,u.useResolvedStylesApi)({classNames:Z,styles:Y,props:A}),[ej,eR]=(0,n.useState)(null!=(I=null!=O?O:ed)&&I),eD=(0,n.useRef)(_),eI=(0,n.useRef)(null),[eA,eT]=(0,n.useState)(null),[e_,eM]=(0,n.useState)(null),{dir:eN}=(0,v.useDirection)(),ez=(0,p.useMantineEnv)(),eO=(0,a.useId)(eu),eL=function(e){let t=(0,p.useMantineEnv)(),[r,o]=(0,E.useUncontrolled)({value:e.opened,defaultValue:e.defaultOpened,finalValue:!1,onChange:e.onChange}),a=(0,n.useRef)(r),l=(0,S.we)({strategy:e.strategy,placement:e.preventPositionChangeWhenVisible?e.positionRef.current:e.position,middleware:function(e,t,r){let o=function(e){if(void 0===e)return{shift:!0,flip:!0};let t={...e};return void 0===e.shift&&(t.shift=!0),void 0===e.flip&&(t.flip=!0),t}(e.middlewares),n=[(0,C.cY)(e.offset),(0,C.jD)()];return e.dropdownVisible&&"test"!==r&&e.preventPositionChangeWhenVisible&&(o.flip=!1,o.shift=!1),o.shift&&n.push((0,C.BN)("boolean"==typeof o.shift?{limiter:(0,C.ER)(),padding:5}:{limiter:(0,C.ER)(),padding:5,...o.shift})),o.flip&&n.push("boolean"==typeof o.flip?(0,C.UU)():(0,C.UU)(o.flip)),o.inline&&n.push("boolean"==typeof o.inline?(0,C.mG)():(0,C.mG)(o.inline)),n.push((0,C.UE)({element:e.arrowRef,padding:e.arrowOffset})),(o.size||"target"===e.width)&&n.push((0,C.Ej)({..."boolean"==typeof o.size?{}:o.size,apply(r){var n,a;let{rects:l,availableWidth:i,availableHeight:s,...c}=r,u=null!=(a=null==(n=t().refs.floating.current)?void 0:n.style)?a:{};o.size&&("object"==typeof o.size&&o.size.apply?o.size.apply({rects:l,availableWidth:i,availableHeight:s,...c}):Object.assign(u,{maxWidth:"".concat(i,"px"),maxHeight:"".concat(s,"px")})),"target"===e.width&&Object.assign(u,{width:"".concat(l.reference.width,"px")})}})),n}(e,()=>l,t),whileElementsMounted:k.ll});return(0,P.useDidUpdate)(()=>{var t;null==(t=e.onPositionChange)||t.call(e,l.placement),e.positionRef.current=l.placement},[l.placement]),(0,P.useDidUpdate)(()=>{if(r!==a.current){var t,o;r?null==(o=e.onOpen)||o.call(e):null==(t=e.onClose)||t.call(e)}a.current=r},[r,e.onClose,e.onOpen]),(0,P.useDidUpdate)(()=>{let t=-1;return r&&(t=window.setTimeout(()=>e.setDropdownVisible(!0),4)),()=>{window.clearTimeout(t)}},[r,e.position]),{floating:l,controlled:"boolean"==typeof e.opened,opened:r,onClose:()=>{r&&!e.disabled&&o(!1)},onToggle:()=>{e.disabled||o(!r)}}}({middlewares:H,width:V,position:(0,f.getFloatingPosition)(eN,_),offset:"number"==typeof M?M+(U?G/2:0):M,arrowRef:eI,arrowOffset:W,onPositionChange:N,positionDependencies:z,opened:O,defaultOpened:ed,onChange:el,onOpen:ea,onClose:eo,onDismiss:en,strategy:ey,dropdownVisible:ej,setDropdownVisible:eR,positionRef:eD,disabled:ev,preventPositionChangeWhenVisible:eS});(0,l.useClickOutside)(()=>{Q&&(eL.onClose(),null==en||en())},et,[eA,e_]);let eB=(0,n.useCallback)(e=>{eT(e),eL.floating.refs.setReference(e)},[eL.floating.refs.setReference]),eF=(0,n.useCallback)(e=>{eM(e),eL.floating.refs.setFloating(e)},[eL.floating.refs.setFloating]),eV=(0,n.useCallback)(()=>{var e;null==L||null==(e=L.onExited)||e.call(L),null==B||B(),eR(!1),eD.current=_},[null==L?void 0:L.onExited,B]),eH=(0,n.useCallback)(()=>{var e;null==L||null==(e=L.onEntered)||e.call(L),null==F||F()},[null==L?void 0:L.onEntered,F]);return(0,o.jsxs)(y.w,{value:{returnFocus:ef,disabled:ev,controlled:eL.controlled,reference:eB,floating:eF,x:eL.floating.x,y:eL.floating.y,arrowX:null==(i=eL.floating)||null==(r=i.middlewareData)||null==(t=r.arrow)?void 0:t.x,arrowY:null==(x=eL.floating)||null==(c=x.middlewareData)||null==(s=c.arrow)?void 0:s.y,opened:eL.opened,arrowRef:eI,transitionProps:{...L,onExited:eV,onEntered:eH},width:V,withArrow:U,arrowSize:G,arrowOffset:W,arrowRadius:q,arrowPosition:K,placement:eL.floating.placement,trapFocus:er,withinPortal:$,portalProps:J,zIndex:ei,radius:es,shadow:ec,closeOnEscape:ee,onDismiss:en,onClose:eL.onClose,onToggle:eL.onToggle,getTargetId:()=>"".concat(eO,"-target"),getDropdownId:()=>"".concat(eO,"-dropdown"),withRoles:em,targetProps:ek,__staticSelector:ep,classNames:Z,styles:Y,unstyled:X,variant:eh,keepMounted:eg,getStyles:eE,resolvedStyles:eP,floatingStrategy:ey,referenceHidden:!!eC&&"test"!==ez&&(null==(w=eL.floating.middlewareData.hide)?void 0:w.referenceHidden)},children:[T,ex&&(0,o.jsx)(b.Transition,{transition:"fade",mounted:eL.opened,duration:(null==L?void 0:L.duration)||250,exitDuration:(null==L?void 0:L.exitDuration)||250,children:e=>(0,o.jsx)(g.OptionalPortal,{withinPortal:$,children:(0,o.jsx)(h.Overlay,{...ew,...eE("overlay",{className:null==ew?void 0:ew.className,style:[e,null==ew?void 0:ew.style]})})})})]})}I.Target=w.PopoverTarget,I.Dropdown=x.PopoverDropdown,I.displayName="@mantine/core/Popover",I.extend=e=>e},62093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},62500:()=>{},62861:(e,t,r)=>{"use strict";r.d(t,{ComboboxOption:()=>d});var o=r(95155),n=r(12115),a=r(43664),l=r(69604),i=r(36960),s=r(3826),c=r(19192);let u={},d=(0,i.factory)((e,t)=>{let r=(0,a.useProps)("ComboboxOption",u,e),{classNames:i,className:c,style:d,styles:p,vars:m,onClick:v,id:f,active:h,onMouseDown:g,onMouseOver:b,disabled:y,selected:x,mod:w,...C}=r,S=(0,s.A)(),k=(0,n.useId)();return(0,o.jsx)(l.Box,{...S.getStyles("option",{className:c,classNames:i,styles:p,style:d}),...C,ref:t,id:f||k,mod:["combobox-option",{"combobox-active":h,"combobox-disabled":y,"combobox-selected":x},w],role:"option",onClick:e=>{if(y)e.preventDefault();else{var t;null==(t=S.onOptionSubmit)||t.call(S,r.value,r),null==v||v(e)}},onMouseDown:e=>{e.preventDefault(),null==g||g(e)},onMouseOver:e=>{S.resetSelectionOnOptionHover&&S.store.resetSelectedOption(),null==b||b(e)}})});d.classes=c.A,d.displayName="@mantine/core/ComboboxOption"},62948:()=>{},63459:(e,t,r)=>{"use strict";r.d(t,{CheckboxCard:()=>h});var o=r(95155),n=r(57613);r(12115);var a=r(56204),l=r(68918),i=r(43664),s=r(53791),c=r(36960),u=r(43608),d=r(43171),p=r(12490),m={card:"m_26775b0a"};let v={withBorder:!0},f=(0,l.createVarsResolver)((e,t)=>{let{radius:r}=t;return{card:{"--card-radius":(0,a.getRadius)(r)}}}),h=(0,c.factory)((e,t)=>{let r=(0,i.useProps)("CheckboxCard",v,e),{classNames:a,className:l,style:c,styles:h,unstyled:g,vars:b,checked:y,mod:x,withBorder:w,value:C,onClick:S,defaultChecked:k,onChange:E,...P}=r,j=(0,s.useStyles)({name:"CheckboxCard",classes:m,props:r,className:l,style:c,classNames:a,styles:h,unstyled:g,vars:b,varsResolver:f,rootSelector:"card"}),R=(0,d.useCheckboxGroupContext)(),D="boolean"==typeof y?y:R?R.value.includes(C||""):void 0,[I,A]=(0,n.useUncontrolled)({value:D,defaultValue:k,finalValue:!1,onChange:E});return(0,o.jsx)(p.V,{value:{checked:I},children:(0,o.jsx)(u.UnstyledButton,{ref:t,mod:[{"with-border":w,checked:I},x],...j("card"),...P,role:"checkbox","aria-checked":I,onClick:e=>{null==S||S(e),null==R||R.onChange(C||""),A(!I)}})})});h.displayName="@mantine/core/CheckboxCard",h.classes=m},63499:(e,t,r)=>{"use strict";r.d(t,{An:()=>i,SQ:()=>s,bX:()=>l,iu:()=>c,jH:()=>a});var o=r(95155);function n(e){let{style:t,children:r,path:n,...a}=e;return(0,o.jsx)("svg",{viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg",style:{width:"calc(var(--pagination-control-size) / 1.8)",height:"calc(var(--pagination-control-size) / 1.8)",...t},...a,children:(0,o.jsx)("path",{d:n,fill:"currentColor"})})}let a=e=>(0,o.jsx)(n,{...e,path:"M8.781 8l-3.3-3.3.943-.943L10.667 8l-4.243 4.243-.943-.943 3.3-3.3z"}),l=e=>(0,o.jsx)(n,{...e,path:"M7.219 8l3.3 3.3-.943.943L5.333 8l4.243-4.243.943.943-3.3 3.3z"}),i=e=>(0,o.jsx)(n,{...e,path:"M6.85355 3.85355C7.04882 3.65829 7.04882 3.34171 6.85355 3.14645C6.65829 2.95118 6.34171 2.95118 6.14645 3.14645L2.14645 7.14645C1.95118 7.34171 1.95118 7.65829 2.14645 7.85355L6.14645 11.8536C6.34171 12.0488 6.65829 12.0488 6.85355 11.8536C7.04882 11.6583 7.04882 11.3417 6.85355 11.1464L3.20711 7.5L6.85355 3.85355ZM12.8536 3.85355C13.0488 3.65829 13.0488 3.34171 12.8536 3.14645C12.6583 2.95118 12.3417 2.95118 12.1464 3.14645L8.14645 7.14645C7.95118 7.34171 7.95118 7.65829 8.14645 7.85355L12.1464 11.8536C12.3417 12.0488 12.6583 12.0488 12.8536 11.8536C13.0488 11.6583 13.0488 11.3417 12.8536 11.1464L9.20711 7.5L12.8536 3.85355Z"}),s=e=>(0,o.jsx)(n,{...e,path:"M2.14645 11.1464C1.95118 11.3417 1.95118 11.6583 2.14645 11.8536C2.34171 12.0488 2.65829 12.0488 2.85355 11.8536L6.85355 7.85355C7.04882 7.65829 7.04882 7.34171 6.85355 7.14645L2.85355 3.14645C2.65829 2.95118 2.34171 2.95118 2.14645 3.14645C1.95118 3.34171 1.95118 3.65829 2.14645 3.85355L5.79289 7.5L2.14645 11.1464ZM8.14645 11.1464C7.95118 11.3417 7.95118 11.6583 8.14645 11.8536C8.34171 12.0488 8.65829 12.0488 8.85355 11.8536L12.8536 7.85355C13.0488 7.65829 13.0488 7.34171 12.8536 7.14645L8.85355 3.14645C8.65829 2.95118 8.34171 2.95118 8.14645 3.14645C7.95118 3.34171 7.95118 3.65829 8.14645 3.85355L11.7929 7.5L8.14645 11.1464Z"}),c=e=>(0,o.jsx)(n,{...e,path:"M2 8c0-.733.6-1.333 1.333-1.333.734 0 1.334.6 1.334 1.333s-.6 1.333-1.334 1.333C2.6 9.333 2 8.733 2 8zm9.333 0c0-.733.6-1.333 1.334-1.333C13.4 6.667 14 7.267 14 8s-.6 1.333-1.333 1.333c-.734 0-1.334-.6-1.334-1.333zM6.667 8c0-.733.6-1.333 1.333-1.333s1.333.6 1.333 1.333S8.733 9.333 8 9.333 6.667 8.733 6.667 8z"})},63549:(e,t,r)=>{"use strict";r.d(t,{readSessionStorageValue:()=>a,useSessionStorage:()=>n});var o=r(40160);function n(e){return(0,o.w)("sessionStorage","use-session-storage")(e)}let a=(0,o.d)("sessionStorage")},63580:(e,t,r)=>{"use strict";r.d(t,{HoverCardTarget:()=>d});var o=r(95155),n=r(12115),a=r(10866),l=r(75240),i=r(43664),s=r(60266),c=r(54806);let u={refProp:"ref"},d=(0,n.forwardRef)((e,t)=>{let{children:r,refProp:d,eventPropsWrapperName:p,...m}=(0,i.useProps)("HoverCardTarget",u,e);if(!(0,a.isElement)(r))throw Error("HoverCard.Target component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");let v=(0,c.f)(),f={onMouseEnter:(0,l.createEventHandler)(r.props.onMouseEnter,v.openDropdown),onMouseLeave:(0,l.createEventHandler)(r.props.onMouseLeave,v.closeDropdown)};return(0,o.jsx)(s.Popover.Target,{refProp:d,ref:t,...m,children:(0,n.cloneElement)(r,p?{[p]:f}:f)})});d.displayName="@mantine/core/HoverCardTarget"},63983:(e,t,r)=>{"use strict";r.d(t,{useListState:()=>n});var o=r(12115);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,o.useState)(e);return[t,{setState:r,append:function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return r(e=>[...e,...t])},prepend:function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return r(e=>[...t,...e])},insert:function(e){for(var t=arguments.length,o=Array(t>1?t-1:0),n=1;n<t;n++)o[n-1]=arguments[n];return r(t=>[...t.slice(0,e),...o,...t.slice(e)])},pop:()=>r(e=>{let t=[...e];return t.pop(),t}),shift:()=>r(e=>{let t=[...e];return t.shift(),t}),apply:e=>r(t=>t.map((t,r)=>e(t,r))),applyWhere:(e,t)=>r(r=>r.map((r,o)=>e(r,o)?t(r,o):r)),remove:function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return r(e=>e.filter((e,r)=>!t.includes(r)))},reorder:e=>{let{from:t,to:o}=e;return r(e=>{let r=[...e],n=e[t];return r.splice(t,1),r.splice(o,0,n),r})},swap:e=>{let{from:t,to:o}=e;return r(e=>{let r=[...e],n=r[t],a=r[o];return r.splice(o,1,n),r.splice(t,1,a),r})},setItem:(e,t)=>r(r=>{let o=[...r];return o[e]=t,o}),setItemProp:(e,t,o)=>r(r=>{let n=[...r];return n[e]={...n[e],[t]:o},n}),filter:e=>{r(t=>t.filter(e))}}]}},64516:(e,t,r)=>{"use strict";r.d(t,{PinInput:()=>C});var o=r(95155),n=r(12115),a=r(64173),l=r(57613),i=r(88551),s=r(56204),c=r(68918),u=r(86028),d=r(53791),p=r(43664),m=r(36960),v=r(70112),f=r(54853),h=r(24225);function g(e,t){if(e<1)return[];let r=Array(e).fill("");if(t){let o=t.trim().split("");for(let t=0;t<Math.min(e,o.length);t+=1)r[t]=" "===o[t]?"":o[t]}return r}var b={root:"m_f1cb205a",pinInput:"m_cb288ead"};let y={number:/^[0-9]+$/,alphanumeric:/^[a-zA-Z0-9]+$/i},x={gap:"sm",length:4,manageFocus:!0,oneTimeCode:!0,placeholder:"○",type:"alphanumeric",ariaLabel:"PinInput"},w=(0,c.createVarsResolver)((e,t)=>{let{size:r}=t;return{root:{"--pin-input-size":(0,s.getSize)(null!=r?r:"md","pin-input-size")}}}),C=(0,m.factory)((e,t)=>{let{name:r,form:s,className:c,value:m,defaultValue:h,variant:C,gap:S,style:k,size:E,classNames:P,styles:j,unstyled:R,length:D,onChange:I,onComplete:A,manageFocus:T,autoFocus:_,error:M,radius:N,disabled:z,oneTimeCode:O,placeholder:L,type:B,mask:F,readOnly:V,inputType:H,inputMode:U,ariaLabel:G,vars:W,id:q,hiddenInputProps:K,rootRef:X,getInputProps:Z,...Y}=(0,p.useProps)("PinInput",x,e),Q=(0,a.useId)(q),$=(0,d.useStyles)({name:"PinInput",classes:b,props:e,className:c,style:k,classNames:P,styles:j,unstyled:R,vars:W,varsResolver:w}),{resolvedClassNames:J,resolvedStyles:ee}=(0,u.useResolvedStylesApi)({classNames:P,styles:j,props:e}),[et,er]=(0,n.useState)(-1),[eo,en]=(0,l.useUncontrolled)({value:m?g(null!=D?D:0,m):void 0,defaultValue:null==h?void 0:h.split("").slice(0,null!=D?D:0),finalValue:g(null!=D?D:0,""),onChange:"function"==typeof I?e=>{I(e.join("").trim())}:void 0}),ea=eo.join("").trim(),el=(0,n.useRef)([]),ei=e=>{let t=B instanceof RegExp?B:B&&B in y?y[B]:null;return null==t?void 0:t.test(e)},es=(e,t,r)=>{if(!T){null==r||r.preventDefault();return}if("next"===e){let e=t+1;e<(null!=D?D:0)&&(null==r||r.preventDefault(),el.current[e].focus())}if("prev"===e){let e=t-1;e>-1&&(null==r||r.preventDefault(),el.current[e].focus())}},ec=(e,t)=>{let r=[...eo];r[t]=e,en(r)},eu=(e,t)=>{let r=e.target.value,o=2===r.length?r.split("")[r.length-1]:r,n=ei(o);o.length<2?n?(ec(o,t),es("next",t)):ec("",t):n&&en(g(null!=D?D:0,r))},ed=(e,t)=>{let{ctrlKey:r,metaKey:o,key:n,shiftKey:a,target:l}=e,i=l.value;"numeric"===U&&("Backspace"===n||"Tab"===n||"Control"===n||"Delete"===n||r&&"v"===n||o&&"v"===n||!Number.isNaN(Number(n))||e.preventDefault()),"ArrowLeft"===n||a&&"Tab"===n?es("prev",t,e):"ArrowRight"===n||"Tab"===n||" "===n?es("next",t,e):"Delete"===n?ec("",t):"Backspace"===n?0!==t&&(ec("",t),D===t+1?""===e.target.value&&es("prev",t,e):es("prev",t,e)):i.length>0&&n===eo[t]&&es("next",t,e)},ep=(e,t)=>{e.target.select(),er(t)},em=()=>{er(-1)},ev=e=>{e.preventDefault();let t=e.clipboardData.getData("text/plain").replace(/[\n\r\s]+/g,"");if(ei(t.trim())){let e=g(null!=D?D:0,t);en(e),es("next",e.length-2)}};return(0,n.useEffect)(()=>{ea.length===D&&(null==A||A(ea))},[D,ea]),(0,n.useEffect)(()=>{D!==eo.length&&en(g(null!=D?D:0,eo.join("")))},[D,eo]),(0,n.useEffect)(()=>{""===m&&en(g(null!=D?D:0,m))},[m]),(0,n.useEffect)(()=>{z&&er(-1)},[z]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(v.Group,{...Y,...$("root"),ref:X,role:"group",id:Q,gap:S,unstyled:R,wrap:"nowrap",variant:C,__size:E,dir:"ltr",children:eo.map((e,r)=>(0,n.createElement)(f.Input,{component:"input",...$("pinInput",{style:{"--input-padding":"0","--input-text-align":"center"}}),classNames:J,styles:ee,size:E,__staticSelector:"PinInput",id:"".concat(Q,"-").concat(r+1),key:"".concat(Q,"-").concat(r),inputMode:U||("number"===B?"numeric":"text"),onChange:e=>eu(e,r),onKeyDown:e=>ed(e,r),onFocus:e=>ep(e,r),onBlur:em,onPaste:ev,type:H||(F?"password":"number"===B?"tel":"text"),radius:N,error:M,variant:C,disabled:z,ref:e=>{e&&(0===r&&(0,i.assignRef)(t,e),el.current[r]=e)},autoComplete:O?"one-time-code":"off",placeholder:et===r?"":L,value:e,autoFocus:_&&0===r,unstyled:R,"aria-label":G,readOnly:V,...null==Z?void 0:Z(r)}))}),(0,o.jsx)("input",{type:"hidden",name:r,form:s,value:ea,...K})]})});C.classes={...b,...h.InputBase.classes},C.displayName="@mantine/core/PinInput"},64693:(e,t,r)=>{"use strict";r.d(t,{useModals:()=>a});var o=r(12115),n=r(97975);function a(){let e=(0,o.useContext)(n.w);if(!e)throw Error("[@mantine/modals] useModals hook was called outside of context, wrap your app with ModalsProvider component");return e}},64745:(e,t,r)=>{"use strict";r.d(t,{useComputedColorScheme:()=>a});var o=r(76197),n=r(83370);function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{getInitialValueInEffect:!0},r=(0,o.useColorScheme)(e,t),{colorScheme:a}=(0,n.useMantineColorScheme)();return"auto"===a?r:a}},64796:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={group:"m_11def92b",root:"m_f85678b6",image:"m_11f8ac07",placeholder:"m_104cd71f"}},65054:(e,t,r)=>{"use strict";r.d(t,{K:()=>a,S:()=>n}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Menu component was not found in the tree")},65402:(e,t,r)=>{"use strict";r.d(t,{PaginationFirst:()=>m,PaginationLast:()=>v,PaginationNext:()=>d,PaginationPrevious:()=>p});var o=r(95155),n=r(12115),a=r(43664),l=r(3097),i=r(80864),s=r(63499),c=r(43702);function u(e){let{icon:t,name:r,action:s,type:u}=e,d={icon:t},p=(0,n.forwardRef)((e,t)=>{let{icon:n,...l}=(0,a.useProps)(r,d,e),p=(0,i.b)(),m="next"===u?p.active===p.total:1===p.active;return(0,o.jsx)(c.PaginationControl,{disabled:p.disabled||m,ref:t,onClick:p[s],withPadding:!1,...l,children:(0,o.jsx)(n,{className:"mantine-rotate-rtl",style:{width:"calc(var(--pagination-control-size) / 1.8)",height:"calc(var(--pagination-control-size) / 1.8)"}})})});return p.displayName="@mantine/core/".concat(r),(0,l.K)(p)}let d=u({icon:s.jH,name:"PaginationNext",action:"onNext",type:"next"}),p=u({icon:s.bX,name:"PaginationPrevious",action:"onPrevious",type:"previous"}),m=u({icon:s.An,name:"PaginationFirst",action:"onFirst",type:"previous"}),v=u({icon:s.SQ,name:"PaginationLast",action:"onLast",type:"next"})},65629:(e,t,r)=>{"use strict";r.d(t,{E:()=>n,r:()=>o}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)()},65703:(e,t,r)=>{"use strict";r.d(t,{useHash:()=>a});var o=r(12115),n=r(28261);function a(){let{getInitialValueInEffect:e=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,r]=(0,o.useState)(e?"":window.location.hash||"");return(0,n.useWindowEvent)("hashchange",()=>{let e=window.location.hash;t!==e&&r(e)}),(0,o.useEffect)(()=>{e&&r(window.location.hash)},[]),[t,e=>{let t=e.startsWith("#")?e:"#".concat(e);window.location.hash=t,r(t)}]}},66121:(e,t,r)=>{"use strict";r.d(t,{Switch:()=>x});var o=r(95155),n=r(64173),a=r(57613);r(12115);var l=r(56204),i=r(68918),s=r(71180),c=r(43664),u=r(53791),d=r(99537),p=r(69604),m=r(36960),v=r(44598),f=r(40837),h=r(56470),g={root:"m_5f93f3bb",input:"m_926b4011",track:"m_9307d992",thumb:"m_93039a1d",trackLabel:"m_8277e082"};let b={labelPosition:"right",withThumbIndicator:!0},y=(0,i.createVarsResolver)((e,t)=>{let{radius:r,color:o,size:n}=t;return{root:{"--switch-radius":void 0===r?void 0:(0,l.getRadius)(r),"--switch-height":(0,l.getSize)(n,"switch-height"),"--switch-width":(0,l.getSize)(n,"switch-width"),"--switch-thumb-size":(0,l.getSize)(n,"switch-thumb-size"),"--switch-label-font-size":(0,l.getSize)(n,"switch-label-font-size"),"--switch-track-label-padding":(0,l.getSize)(n,"switch-track-label-padding"),"--switch-color":o?(0,s.getThemeColor)(o,e):void 0}}}),x=(0,m.factory)((e,t)=>{var r;let l=(0,c.useProps)("Switch",b,e),{classNames:i,className:s,style:m,styles:h,unstyled:x,vars:w,color:C,label:S,offLabel:k,onLabel:E,id:P,size:j,radius:R,wrapperProps:D,thumbIcon:I,checked:A,defaultChecked:T,onChange:_,labelPosition:M,description:N,error:z,disabled:O,variant:L,rootRef:B,mod:F,withThumbIndicator:V,...H}=l,U=(0,f.U)(),G=j||(null==U?void 0:U.size),W=(0,u.useStyles)({name:"Switch",props:l,classes:g,className:s,style:m,classNames:i,styles:h,unstyled:x,vars:w,varsResolver:y}),{styleProps:q,rest:K}=(0,d.extractStyleProps)(H),X=(0,n.useId)(P),Z=U?{checked:U.value.includes(K.value),onChange:U.onChange}:{},[Y,Q]=(0,a.useUncontrolled)({value:null!=(r=Z.checked)?r:A,defaultValue:T,finalValue:!1});return(0,o.jsxs)(v.I,{...W("root"),__staticSelector:"Switch",__stylesApiProps:l,id:X,size:G,labelPosition:M,label:S,description:N,error:z,disabled:O,bodyElement:"label",labelElement:"span",classNames:i,styles:h,unstyled:x,"data-checked":Z.checked||A||void 0,variant:L,ref:B,mod:F,...q,...D,children:[(0,o.jsx)("input",{...K,disabled:O,checked:Y,"data-checked":Z.checked||A||void 0,onChange:e=>{var t;U?null==(t=Z.onChange)||t.call(Z,e):null==_||_(e),Q(e.currentTarget.checked)},id:X,ref:t,type:"checkbox",role:"switch",...W("input")}),(0,o.jsxs)(p.Box,{"aria-hidden":"true",component:"span",mod:{error:z,"label-position":M,"without-labels":!E&&!k},...W("track"),children:[(0,o.jsx)(p.Box,{component:"span",mod:{"reduce-motion":!0,"with-thumb-indicator":V&&!I},...W("thumb"),children:I}),(0,o.jsx)("span",{...W("trackLabel"),children:Y?E:k})]})]})});x.classes={...g,...v.M},x.displayName="@mantine/core/Switch",x.Group=h.SwitchGroup},66142:(e,t,r)=>{"use strict";r.d(t,{C:()=>a,w:()=>n}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Popover component was not found in the tree")},66143:(e,t,r)=>{"use strict";function o(e){let{value:t,containerWidth:r,min:o,max:n,step:a,precision:l}=e,i=(r?Math.min(Math.max(t,0),r)/r:t)*(n-o),s=Math.max((0!==i?Math.round(i/a)*a:0)+o,o);return void 0!==l?Number(s.toFixed(l)):s}r.d(t,{c:()=>o})},66437:(e,t,r)=>{"use strict";r.d(t,{useForceUpdate:()=>a});var o=r(12115);let n=e=>(e+1)%1e6;function a(){let[,e]=(0,o.useReducer)(n,0);return e}},66610:(e,t,r)=>{"use strict";r.d(t,{useHover:()=>n});var o=r(12115);function n(){let[e,t]=(0,o.useState)(!1),r=(0,o.useRef)(null),n=(0,o.useCallback)(()=>{t(!0)},[]),a=(0,o.useCallback)(()=>{t(!1)},[]);return{ref:(0,o.useCallback)(e=>{r.current&&(r.current.removeEventListener("mouseenter",n),r.current.removeEventListener("mouseleave",a)),e&&(e.addEventListener("mouseenter",n),e.addEventListener("mouseleave",a)),r.current=e},[n,a]),hovered:e}}},66783:(e,t,r)=>{"use strict";r.d(t,{Dialog:()=>h});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(36960),c=r(31339),u=r(95642),d=r(97287),p=r(60384),m={root:"m_e2125a27",closeButton:"m_5abab665"};let v={shadow:"md",p:"md",withBorder:!1,transitionProps:{transition:"pop-top-right",duration:200},position:{bottom:30,right:30}},f=(0,a.createVarsResolver)((e,t)=>{let{size:r}=t;return{root:{"--dialog-size":(0,n.getSize)(r,"dialog-size")}}}),h=(0,s.factory)((e,t)=>{let r=(0,l.useProps)("Dialog",v,e),{classNames:n,className:a,style:s,styles:h,unstyled:g,vars:b,zIndex:y,position:x,keepMounted:w,opened:C,transitionProps:S,withCloseButton:k,withinPortal:E,children:P,onClose:j,portalProps:R,...D}=r,I=(0,i.useStyles)({name:"Dialog",classes:m,props:r,className:a,style:s,classNames:n,styles:h,unstyled:g,vars:b,varsResolver:f});return(0,o.jsx)(c.Affix,{zIndex:y,position:x,ref:t,withinPortal:E,portalProps:R,unstyled:g,children:(0,o.jsx)(p.Transition,{keepMounted:w,mounted:C,...S,children:e=>(0,o.jsxs)(d.Paper,{unstyled:g,...I("root",{style:e}),...D,children:[k&&(0,o.jsx)(u.CloseButton,{onClick:j,unstyled:g,...I("closeButton")}),P]})})})});h.classes=m,h.displayName="@mantine/core/Dialog"},67385:(e,t,r)=>{"use strict";r.d(t,{useClickOutside:()=>a});var o=r(12115);let n=["mousedown","touchstart"];function a(e,t,r){let a=(0,o.useRef)(null);return(0,o.useEffect)(()=>{let o=t=>{let{target:o}=null!=t?t:{};if(Array.isArray(r)){let n=(null==o?void 0:o.hasAttribute("data-ignore-outside-clicks"))||!document.body.contains(o)&&"HTML"!==o.tagName;r.every(e=>!!e&&!t.composedPath().includes(e))&&!n&&e()}else a.current&&!a.current.contains(o)&&e()};return(t||n).forEach(e=>document.addEventListener(e,o)),()=>{(t||n).forEach(e=>document.removeEventListener(e,o))}},[a,e,r]),a}},67913:(e,t,r)=>{"use strict";r.d(t,{defaultOptionsFilter:()=>function e(t){let{options:r,search:n,limit:a}=t,l=n.trim().toLowerCase(),i=[];for(let t=0;t<r.length;t+=1){let s=r[t];if(i.length===a)break;(0,o.isOptionsGroup)(s)&&i.push({group:s.group,items:e({options:s.items,search:n,limit:a-i.length})}),!(0,o.isOptionsGroup)(s)&&s.label.toLowerCase().includes(l)&&i.push(s)}return i}});var o=r(83731)},68001:(e,t,r)=>{"use strict";r.d(t,{Pill:()=>g});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u=r(95642),d=r(73548),p=r(36663),m=r(41471),v=r(74088);let f={variant:"default"},h=(0,a.createVarsResolver)((e,t,r)=>{let{radius:o}=t,{size:a}=r;return{root:{"--pill-fz":(0,n.getSize)(a,"pill-fz"),"--pill-height":(0,n.getSize)(a,"pill-height"),"--pill-radius":void 0===o?void 0:(0,n.getRadius)(o)}}}),g=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("Pill",f,e),{classNames:n,className:a,style:c,styles:m,unstyled:g,vars:b,variant:y,children:x,withRemoveButton:w,onRemove:C,removeButtonProps:S,radius:k,size:E,disabled:P,mod:j,...R}=r,D=(0,p.B)(),I=(0,d.n)(),A=E||(null==D?void 0:D.size)||void 0,T=(null==I?void 0:I.variant)==="filled"?"contrast":y||"default",_=(0,i.useStyles)({name:"Pill",classes:v.A,props:r,className:a,style:c,classNames:n,styles:m,unstyled:g,vars:b,varsResolver:h,stylesCtx:{size:A}});return(0,o.jsxs)(s.Box,{component:"span",ref:t,variant:T,size:A,..._("root",{variant:T}),mod:[{"with-remove":w&&!P,disabled:P||(null==D?void 0:D.disabled)},j],...R,children:[(0,o.jsx)("span",{..._("label"),children:x}),w&&(0,o.jsx)(u.CloseButton,{variant:"transparent",radius:k,tabIndex:-1,"aria-hidden":!0,unstyled:g,...S,..._("remove",{className:null==S?void 0:S.className,style:null==S?void 0:S.style}),onMouseDown:e=>{var t;e.preventDefault(),e.stopPropagation(),null==S||null==(t=S.onMouseDown)||t.call(S,e)},onClick:e=>{var t;e.stopPropagation(),null==C||C(),null==S||null==(t=S.onClick)||t.call(S,e)}})]})});g.classes=v.A,g.displayName="@mantine/core/Pill",g.Group=m.PillGroup},68665:(e,t,r)=>{"use strict";r.d(t,{ComboboxTarget:()=>m});var o=r(95155),n=r(12115),a=r(88551),l=r(10866),i=r(43664),s=r(36960),c=r(60266),u=r(3826),d=r(85351);let p={refProp:"ref",targetType:"input",withKeyboardNavigation:!0,withAriaAttributes:!0,withExpandedAttribute:!1,autoComplete:"off"},m=(0,s.factory)((e,t)=>{let{children:r,refProp:s,withKeyboardNavigation:m,withAriaAttributes:v,withExpandedAttribute:f,targetType:h,autoComplete:g,...b}=(0,i.useProps)("ComboboxTarget",p,e);if(!(0,l.isElement)(r))throw Error("Combobox.Target component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");let y=(0,u.A)(),x=(0,d.useComboboxTargetProps)({targetType:h,withAriaAttributes:v,withKeyboardNavigation:m,withExpandedAttribute:f,onKeyDown:r.props.onKeyDown,autoComplete:g}),w=(0,n.cloneElement)(r,{...x,...b});return(0,o.jsx)(c.Popover.Target,{ref:(0,a.useMergedRef)(t,y.store.targetRef),children:w})});m.displayName="@mantine/core/ComboboxTarget"},68827:(e,t,r)=>{"use strict";r.d(t,{ComboboxDropdownTarget:()=>u});var o=r(95155),n=r(10866);r(12115);var a=r(43664),l=r(36960),i=r(60266),s=r(3826);let c={refProp:"ref"},u=(0,l.factory)((e,t)=>{let{children:r,refProp:l}=(0,a.useProps)("ComboboxDropdownTarget",c,e);if((0,s.A)(),!(0,n.isElement)(r))throw Error("Combobox.DropdownTarget component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");return(0,o.jsx)(i.Popover.Target,{ref:t,refProp:l,children:r})});u.displayName="@mantine/core/ComboboxDropdownTarget"},68962:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_4addd315",control:"m_326d024a",dots:"m_4ad7767d"}},69324:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={dropdown:"m_dc9b7c9f",label:"m_9bfac126",divider:"m_efdf90cb",item:"m_99ac2aa1",itemLabel:"m_5476e0d3",itemSection:"m_8b75e504",chevron:"m_b85b0bed"}},69569:(e,t,r)=>{"use strict";r.d(t,{Notifications:()=>W});var o=r(95155),n=r(12115),a=r(93495);function l(e,t){return(l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function i(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,l(e,t)}var s=r(47650);let c={disabled:!1},u=n.createContext(null);var d="unmounted",p="exited",m="entering",v="entered",f="exiting",h=function(e){function t(t,r){var o,n=e.call(this,t,r)||this,a=r&&!r.isMounting?t.enter:t.appear;return n.appearStatus=null,t.in?a?(o=p,n.appearStatus=m):o=v:o=t.unmountOnExit||t.mountOnEnter?d:p,n.state={status:o},n.nextCallback=null,n}i(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===d?{status:p}:null};var r=t.prototype;return r.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},r.componentDidUpdate=function(e){var t=null;if(e!==this.props){var r=this.state.status;this.props.in?r!==m&&r!==v&&(t=m):(r===m||r===v)&&(t=f)}this.updateStatus(!1,t)},r.componentWillUnmount=function(){this.cancelNextCallback()},r.getTimeouts=function(){var e,t,r,o=this.props.timeout;return e=t=r=o,null!=o&&"number"!=typeof o&&(e=o.exit,t=o.enter,r=void 0!==o.appear?o.appear:t),{exit:e,enter:t,appear:r}},r.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===m){if(this.props.unmountOnExit||this.props.mountOnEnter){var r=this.props.nodeRef?this.props.nodeRef.current:s.findDOMNode(this);r&&r.scrollTop}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===p&&this.setState({status:d})},r.performEnter=function(e){var t=this,r=this.props.enter,o=this.context?this.context.isMounting:e,n=this.props.nodeRef?[o]:[s.findDOMNode(this),o],a=n[0],l=n[1],i=this.getTimeouts(),u=o?i.appear:i.enter;if(!e&&!r||c.disabled)return void this.safeSetState({status:v},function(){t.props.onEntered(a)});this.props.onEnter(a,l),this.safeSetState({status:m},function(){t.props.onEntering(a,l),t.onTransitionEnd(u,function(){t.safeSetState({status:v},function(){t.props.onEntered(a,l)})})})},r.performExit=function(){var e=this,t=this.props.exit,r=this.getTimeouts(),o=this.props.nodeRef?void 0:s.findDOMNode(this);if(!t||c.disabled)return void this.safeSetState({status:p},function(){e.props.onExited(o)});this.props.onExit(o),this.safeSetState({status:f},function(){e.props.onExiting(o),e.onTransitionEnd(r.exit,function(){e.safeSetState({status:p},function(){e.props.onExited(o)})})})},r.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},r.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},r.setNextCallback=function(e){var t=this,r=!0;return this.nextCallback=function(o){r&&(r=!1,t.nextCallback=null,e(o))},this.nextCallback.cancel=function(){r=!1},this.nextCallback},r.onTransitionEnd=function(e,t){this.setNextCallback(t);var r=this.props.nodeRef?this.props.nodeRef.current:s.findDOMNode(this),o=null==e&&!this.props.addEndListener;if(!r||o)return void setTimeout(this.nextCallback,0);if(this.props.addEndListener){var n=this.props.nodeRef?[this.nextCallback]:[r,this.nextCallback],a=n[0],l=n[1];this.props.addEndListener(a,l)}null!=e&&setTimeout(this.nextCallback,e)},r.render=function(){var e=this.state.status;if(e===d)return null;var t=this.props,r=t.children,o=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,(0,a.A)(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return n.createElement(u.Provider,{value:null},"function"==typeof r?r(e,o):n.cloneElement(n.Children.only(r),o))},t}(n.Component);function g(){}h.contextType=u,h.propTypes={},h.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:g,onEntering:g,onEntered:g,onExit:g,onExiting:g,onExited:g},h.UNMOUNTED=d,h.EXITED=p,h.ENTERING=m,h.ENTERED=v,h.EXITING=f;var b=r(79630);function y(e,t){var r=Object.create(null);return e&&n.Children.map(e,function(e){return e}).forEach(function(e){r[e.key]=t&&(0,n.isValidElement)(e)?t(e):e}),r}function x(e,t,r){return null!=r[t]?r[t]:e.props[t]}var w=Object.values||function(e){return Object.keys(e).map(function(t){return e[t]})},C=function(e){function t(t,r){var o=e.call(this,t,r)||this,n=o.handleExited.bind(function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(o));return o.state={contextValue:{isMounting:!0},handleExited:n,firstRender:!0},o}i(t,e);var r=t.prototype;return r.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},r.componentWillUnmount=function(){this.mounted=!1},t.getDerivedStateFromProps=function(e,t){var r,o,a=t.children,l=t.handleExited;return{children:t.firstRender?y(e.children,function(t){return(0,n.cloneElement)(t,{onExited:l.bind(null,t),in:!0,appear:x(t,"appear",e),enter:x(t,"enter",e),exit:x(t,"exit",e)})}):(Object.keys(o=function(e,t){function r(r){return r in t?t[r]:e[r]}e=e||{},t=t||{};var o,n=Object.create(null),a=[];for(var l in e)l in t?a.length&&(n[l]=a,a=[]):a.push(l);var i={};for(var s in t){if(n[s])for(o=0;o<n[s].length;o++){var c=n[s][o];i[n[s][o]]=r(c)}i[s]=r(s)}for(o=0;o<a.length;o++)i[a[o]]=r(a[o]);return i}(a,r=y(e.children))).forEach(function(t){var i=o[t];if((0,n.isValidElement)(i)){var s=t in a,c=t in r,u=a[t],d=(0,n.isValidElement)(u)&&!u.props.in;c&&(!s||d)?o[t]=(0,n.cloneElement)(i,{onExited:l.bind(null,i),in:!0,exit:x(i,"exit",e),enter:x(i,"enter",e)}):c||!s||d?c&&s&&(0,n.isValidElement)(u)&&(o[t]=(0,n.cloneElement)(i,{onExited:l.bind(null,i),in:u.props.in,exit:x(i,"exit",e),enter:x(i,"enter",e)})):o[t]=(0,n.cloneElement)(i,{in:!1})}}),o),firstRender:!1}},r.handleExited=function(e,t){var r=y(this.props.children);e.key in r||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState(function(t){var r=(0,b.A)({},t.children);return delete r[e.key],{children:r}}))},r.render=function(){var e=this.props,t=e.component,r=e.childFactory,o=(0,a.A)(e,["component","childFactory"]),l=this.state.contextValue,i=w(this.state.children).map(r);return(delete o.appear,delete o.enter,delete o.exit,null===t)?n.createElement(u.Provider,{value:l},i):n.createElement(u.Provider,{value:l},n.createElement(t,o,i))},t}(n.Component);C.propTypes={},C.defaultProps={component:"div",childFactory:function(e){return e}};var S=r(58750),k=r(68918),E=r(5903),P=r(36960),j=r(43664),R=r(3131),D=r(53791),I=r(62143),A=r(69604),T=r(93795),_=r(66437),M=r(43589),N=r(84237);let z=["bottom-center","bottom-left","bottom-right","top-center","top-left","top-right"],O={left:"translateX(-100%)",right:"translateX(100%)","top-center":"translateY(-100%)","bottom-center":"translateY(100%)"},L={left:"translateX(0)",right:"translateX(0)","top-center":"translateY(0)","bottom-center":"translateY(0)"};var B=r(53527);let F=(0,n.forwardRef)((e,t)=>{let{data:r,onHide:a,autoClose:l,...i}=e,{autoClose:s,message:c,...u}=r,d=function(e,t){return"number"==typeof t?t:!1!==t&&!1!==e&&e}(l,r.autoClose),p=(0,n.useRef)(-1),m=()=>window.clearTimeout(p.current),v=()=>{a(r.id),m()},f=()=>{"number"==typeof d&&(p.current=window.setTimeout(v,d))};return(0,n.useEffect)(()=>{var e;null==(e=r.onOpen)||e.call(r,r)},[]),(0,n.useEffect)(()=>(f(),m),[d]),(0,o.jsx)(B.Notification,{...i,...u,onClose:v,ref:t,onMouseEnter:m,onMouseLeave:f,children:c})});F.displayName="@mantine/notifications/NotificationContainer";var V=r(6942),H={root:"m_b37d9ac7",notification:"m_5ed0edd0"};let U={position:"bottom-right",autoClose:4e3,transitionDuration:250,containerWidth:440,notificationMaxHeight:200,limit:5,zIndex:(0,S.getDefaultZIndex)("overlay"),store:V.notificationsStore,withinPortal:!0},G=(0,k.createVarsResolver)((e,t)=>{let{zIndex:r,containerWidth:o}=t;return{root:{"--notifications-z-index":null==r?void 0:r.toString(),"--notifications-container-width":(0,E.D)(o)}}}),W=(0,P.factory)((e,t)=>{var r;let a=(0,j.useProps)("Notifications",U,e),{classNames:l,className:i,style:s,styles:c,unstyled:u,vars:d,position:p,autoClose:m,transitionDuration:v,containerWidth:f,notificationMaxHeight:g,limit:b,zIndex:y,store:x,portalProps:w,withinPortal:S,...k}=a,E=(0,R.useMantineTheme)(),P=(0,V.useNotifications)(x),B=(0,_.useForceUpdate)(),W=(0,M.useReducedMotion)(),q=(0,n.useRef)({}),K=(0,n.useRef)(0),X=E.respectReducedMotion&&W?1:v,Z=(0,D.useStyles)({name:"Notifications",classes:H,props:a,className:i,style:s,classNames:l,styles:c,unstyled:u,vars:d,varsResolver:G});(0,n.useEffect)(()=>{null==x||x.updateState(e=>({...e,limit:b||5,defaultPosition:p}))},[b,p]),(0,N.useDidUpdate)(()=>{P.notifications.length>K.current&&setTimeout(()=>B(),0),K.current=P.notifications.length},[P.notifications]);let Y=(r=P.notifications,r.reduce((e,t)=>(e[t.position||p].push(t),e),z.reduce((e,t)=>(e[t]=[],e),{}))),Q=z.reduce((e,t)=>(e[t]=Y[t].map(e=>{let{style:r,...n}=e;return(0,o.jsx)(h,{timeout:X,onEnter:()=>q.current[n.id].offsetHeight,nodeRef:{current:q.current[n.id]},children:e=>(0,o.jsx)(F,{ref:e=>{e&&(q.current[n.id]=e)},data:n,onHide:e=>(0,V.hideNotification)(e,x),autoClose:m,...Z("notification",{style:{...function(e){let{state:t,maxHeight:r,position:o,transitionDuration:n}=e,[a,l]=o.split("-"),i="center"===l?"".concat(a,"-center"):l,s={opacity:0,maxHeight:r,transform:O[i],transitionDuration:"".concat(n,"ms, ").concat(n,"ms, ").concat(n,"ms"),transitionTimingFunction:"cubic-bezier(.51,.3,0,1.21), cubic-bezier(.51,.3,0,1.21), linear",transitionProperty:"opacity, transform, max-height"},c={opacity:1,transform:L[i]},u={opacity:0,maxHeight:0,transform:O[i]};return{...s,...{entering:c,entered:c,exiting:u,exited:u}[t]}}({state:e,position:t,transitionDuration:X,maxHeight:g}),...r}})})},n.id)}),e),{});return(0,o.jsxs)(I.OptionalPortal,{withinPortal:S,...w,children:[(0,o.jsx)(A.Box,{...Z("root"),"data-position":"top-center",ref:t,...k,children:(0,o.jsx)(C,{children:Q["top-center"]})}),(0,o.jsx)(A.Box,{...Z("root"),"data-position":"top-left",...k,children:(0,o.jsx)(C,{children:Q["top-left"]})}),(0,o.jsx)(A.Box,{...Z("root",{className:T.A.classNames.fullWidth}),"data-position":"top-right",...k,children:(0,o.jsx)(C,{children:Q["top-right"]})}),(0,o.jsx)(A.Box,{...Z("root",{className:T.A.classNames.fullWidth}),"data-position":"bottom-right",...k,children:(0,o.jsx)(C,{children:Q["bottom-right"]})}),(0,o.jsx)(A.Box,{...Z("root"),"data-position":"bottom-left",...k,children:(0,o.jsx)(C,{children:Q["bottom-left"]})}),(0,o.jsx)(A.Box,{...Z("root"),"data-position":"bottom-center",...k,children:(0,o.jsx)(C,{children:Q["bottom-center"]})})]})});W.classes=H,W.displayName="@mantine/notifications/Notifications",W.show=V.notifications.show,W.hide=V.notifications.hide,W.update=V.notifications.update,W.clean=V.notifications.clean,W.cleanQueue=V.notifications.cleanQueue,W.updateState=V.notifications.updateState},69829:(e,t,r)=>{"use strict";r.d(t,{DrawerOverlay:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(12151),i=r(20950),s=r(56196);let c={},u=(0,a.factory)((e,t)=>{let{classNames:r,className:a,style:s,styles:u,vars:d,...p}=(0,n.useProps)("DrawerOverlay",c,e),m=(0,i.g)();return(0,o.jsx)(l.ModalBaseOverlay,{ref:t,...m.getStyles("overlay",{classNames:r,style:s,styles:u,className:a}),...p})});u.classes=s.A,u.displayName="@mantine/core/DrawerOverlay"},70058:(e,t,r)=>{"use strict";r.d(t,{Flex:()=>h});var o=r(95155),n=r(1526);r(12115);var a=r(3131),l=r(43664),i=r(53791),s=r(58976),c=r(29235),u=r(46390),d=r(69604),p=r(64511),m=r(2872),v={root:"m_8bffd616"};let f={},h=(0,p.polymorphicFactory)((e,t)=>{let r=(0,l.useProps)("Flex",f,e),{classNames:p,className:h,style:g,styles:b,unstyled:y,vars:x,gap:w,rowGap:C,columnGap:S,align:k,justify:E,wrap:P,direction:j,...R}=r,D=(0,i.useStyles)({name:"Flex",classes:v,props:r,className:h,style:g,classNames:p,styles:b,unstyled:y,vars:x}),I=(0,a.useMantineTheme)(),A=(0,u.useRandomClassName)(),T=(0,c.parseStyleProps)({styleProps:{gap:w,rowGap:C,columnGap:S,align:k,justify:E,wrap:P,direction:j},theme:I,data:m.FLEX_STYLE_PROPS_DATA});return(0,o.jsxs)(o.Fragment,{children:[T.hasResponsiveStyles&&(0,o.jsx)(s.InlineStyles,{selector:".".concat(A),styles:T.styles,media:T.media}),(0,o.jsx)(d.Box,{ref:t,...D("root",{className:A,style:(0,n.filterProps)(T.inlineStyles)}),...R})]})});h.classes=v,h.displayName="@mantine/core/Flex"},70130:(e,t,r)=>{"use strict";r.d(t,{W:()=>n,v:()=>o}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)(null)},70181:(e,t,r)=>{"use strict";r.d(t,{useTimeout:()=>n});var o=r(12115);function n(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{autoInvoke:!1},n=(0,o.useRef)(null),a=(0,o.useCallback)(function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];n.current||(n.current=window.setTimeout(()=>{e(o),n.current=null},t))},[t]),l=(0,o.useCallback)(()=>{n.current&&(window.clearTimeout(n.current),n.current=null)},[]);return(0,o.useEffect)(()=>(r.autoInvoke&&a(),l),[l,a]),{start:a,clear:l}}},70881:(e,t,r)=>{"use strict";r.d(t,{useHeadroom:()=>c});var o=r(12115),n=r(73141),a=r(25409);let l=(e,t)=>e<=t,i=(e,t,r,o,n,a)=>{let i=l(e,t);i&&!r.current?(r.current=!0,null==n||n()):i||!o||r.current?!i&&r.current&&(r.current=!1,null==a||a()):(r.current=!0,null==n||n())},s=()=>{let[e,t]=(0,o.useState)(0),[r,n]=(0,o.useState)(!1),[a,l]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{let r,o=()=>{l(!0),clearTimeout(r),r=setTimeout(()=>{l(!1)},300)},i=()=>{if(a)return;let r=window.scrollY||document.documentElement.scrollTop;n(r<e),t(r)};return window.addEventListener("scroll",i),window.addEventListener("resize",o),()=>{window.removeEventListener("scroll",i),window.removeEventListener("resize",o)}},[e,a]),r};function c(){let{fixedAt:e=0,onPin:t,onFix:r,onRelease:c}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},u=(0,o.useRef)(!1),d=s(),[{y:p}]=(0,a.useWindowScroll)();return(0,n.useIsomorphicEffect)(()=>{i(p,e,u,d,t,c)},[p]),(0,n.useIsomorphicEffect)(()=>{l(p,e)&&(null==r||r())},[p,e,r]),!!l(p,e)||!!d}},70885:(e,t,r)=>{"use strict";r.d(t,{useElementSize:()=>l,useResizeObserver:()=>a});var o=r(12115);let n={x:0,y:0,width:0,height:0,top:0,left:0,bottom:0,right:0};function a(e){let t=(0,o.useRef)(0),r=(0,o.useRef)(null),[a,l]=(0,o.useState)(n),i=(0,o.useMemo)(()=>"undefined"!=typeof window?new ResizeObserver(e=>{let o=e[0];o&&(cancelAnimationFrame(t.current),t.current=requestAnimationFrame(()=>{if(r.current){var e,t;let r=(null==(e=o.borderBoxSize)?void 0:e[0])||(null==(t=o.contentBoxSize)?void 0:t[0]);r?l({width:r.inlineSize,height:r.blockSize,x:o.contentRect.x,y:o.contentRect.y,top:o.contentRect.top,left:o.contentRect.left,bottom:o.contentRect.bottom,right:o.contentRect.right}):l(o.contentRect)}}))}):null,[]);return(0,o.useEffect)(()=>(r.current&&(null==i||i.observe(r.current,e)),()=>{null==i||i.disconnect(),t.current&&cancelAnimationFrame(t.current)}),[r.current]),[r,a]}function l(e){let[t,{width:r,height:o}]=a(e);return{ref:t,width:r,height:o}}},71532:(e,t,r)=>{"use strict";r.d(t,{C:()=>n,T:()=>a}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("List component was not found in tree")},72200:(e,t,r)=>{"use strict";r.d(t,{getRefProp:()=>n});var o=r(12115);function n(e){var t;let r=o.version;return"string"!=typeof o.version||r.startsWith("18.")?null==e?void 0:e.ref:null==e||null==(t=e.props)?void 0:t.ref}},72368:(e,t,r)=>{"use strict";r.d(t,{BackgroundImage:()=>m});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(64511),u={root:"m_2ce0de02"};let d={},p=(0,a.createVarsResolver)((e,t)=>{let{radius:r}=t;return{root:{"--bi-radius":void 0===r?void 0:(0,n.getRadius)(r)}}}),m=(0,c.polymorphicFactory)((e,t)=>{let r=(0,l.useProps)("BackgroundImage",d,e),{classNames:n,className:a,style:c,styles:m,unstyled:v,vars:f,radius:h,src:g,variant:b,...y}=r,x=(0,i.useStyles)({name:"BackgroundImage",props:r,classes:u,className:a,style:c,classNames:n,styles:m,unstyled:v,vars:f,varsResolver:p});return(0,o.jsx)(s.Box,{ref:t,variant:b,...x("root",{style:{backgroundImage:"url(".concat(g,")")}}),...y})});m.classes=u,m.displayName="@mantine/core/BackgroundImage"},72477:(e,t,r)=>{"use strict";r.d(t,{useViewportSize:()=>l});var o=r(12115),n=r(28261);let a={passive:!0};function l(){let[e,t]=(0,o.useState)({width:0,height:0}),r=(0,o.useCallback)(()=>{t({width:window.innerWidth||0,height:window.innerHeight||0})},[]);return(0,n.useWindowEvent)("resize",r,a),(0,n.useWindowEvent)("orientationchange",r,a),(0,o.useEffect)(r,[]),e}},72770:(e,t,r)=>{"use strict";r.d(t,{getTreeExpandedState:()=>u,useTree:()=>d});var o=r(12115);function n(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=[];for(let a of e)if(Array.isArray(a.children)&&a.children.length>0){let e=n(a.children,t,r);if(e.currentTreeChecked.length===a.children.length){let t=e.currentTreeChecked.every(e=>e.checked),n={checked:t,indeterminate:!t,value:a.value,hasChildren:!0};o.push(n),r.push(n)}else if(e.currentTreeChecked.length>0){let e={checked:!1,indeterminate:!0,value:a.value,hasChildren:!0};o.push(e),r.push(e)}}else if(t.includes(a.value)){let e={checked:!0,indeterminate:!1,value:a.value,hasChildren:!1};o.push(e),r.push(e)}return{result:r,currentTreeChecked:o}}function a(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=function e(t,r){for(let o of r){if(o.value===t)return o;if(Array.isArray(o.children)){let r=e(t,o.children);if(r)return r}}return null}(e,t);return o?Array.isArray(o.children)&&0!==o.children.length?(o.children.forEach(e=>{Array.isArray(e.children)&&e.children.length>0?a(e.value,t,r):r.push(e.value)}),r):[o.value]:r}r(95155);var l=r(5942);let i=(0,l.memoize)(function(e,t,r){return 0!==r.length&&(!!r.includes(e)||n(t,r).result.some(t=>t.value===e&&t.checked))}),s=(0,l.memoize)(function(e,t,r){return 0!==r.length&&n(t,r).result.some(t=>t.value===e&&t.indeterminate)});function c(e,t,r){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return t.forEach(t=>{o[t.value]=t.value in e?e[t.value]:t.value===r,Array.isArray(t.children)&&c(e,t.children,r,o)}),o}function u(e,t){let r=c({},e,[]);return"*"===t?Object.keys(r).reduce((e,t)=>({...e,[t]:!0}),{}):(t.forEach(e=>{r[e]=!0}),r)}function d(){let{initialSelectedState:e=[],initialCheckedState:t=[],initialExpandedState:r={},multiple:l=!1,onNodeCollapse:u,onNodeExpand:d}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[p,m]=(0,o.useState)([]),[v,f]=(0,o.useState)(r),[h,g]=(0,o.useState)(e),[b,y]=(0,o.useState)(t),[x,w]=(0,o.useState)(null),[C,S]=(0,o.useState)(null),k=(0,o.useCallback)(e=>{f(t=>c(t,e,h)),y(t=>(function(e,t){let r=[];return e.forEach(e=>r.push(...a(e,t))),Array.from(new Set(r))})(t,e)),m(e)},[h,b]),E=(0,o.useCallback)(e=>{f(t=>{let r={...t,[e]:!t[e]};return r[e]?null==d||d(e):null==u||u(e),r})},[u,d]),P=(0,o.useCallback)(e=>{f(t=>(!1!==t[e]&&(null==u||u(e)),{...t,[e]:!1}))},[u]),j=(0,o.useCallback)(e=>{f(t=>(!0!==t[e]&&(null==d||d(e)),{...t,[e]:!0}))},[d]),R=(0,o.useCallback)(()=>{f(e=>{let t={...e};return Object.keys(t).forEach(e=>{t[e]=!0}),t})},[]),D=(0,o.useCallback)(()=>{f(e=>{let t={...e};return Object.keys(t).forEach(e=>{t[e]=!1}),t})},[]),I=(0,o.useCallback)(e=>g(t=>l?t.includes(e)?(w(null),t.filter(t=>t!==e)):(w(e),[...t,e]):t.includes(e)?(w(null),[]):(w(e),[e])),[]),A=(0,o.useCallback)(e=>{w(e),g(t=>l?t.includes(e)?t:[...t,e]:[e])},[]),T=(0,o.useCallback)(e=>{x===e&&w(null),g(t=>t.filter(t=>t!==e))},[]),_=(0,o.useCallback)(()=>{g([]),w(null)},[]),M=(0,o.useCallback)(e=>{let t=a(e,p);y(e=>Array.from(new Set([...e,...t])))},[p]),N=(0,o.useCallback)(e=>{let t=a(e,p);y(e=>e.filter(e=>!t.includes(e)))},[p]),z=(0,o.useCallback)(()=>{y(()=>(function e(t){return t.reduce((t,r)=>(Array.isArray(r.children)&&r.children.length>0?t.push(...e(r.children)):t.push(r.value),t),[])})(p))},[p]);return{multiple:l,expandedState:v,selectedState:h,checkedState:b,anchorNode:x,initialize:k,toggleExpanded:E,collapse:P,expand:j,expandAllNodes:R,collapseAllNodes:D,setExpandedState:f,checkNode:M,uncheckNode:N,checkAllNodes:z,uncheckAllNodes:(0,o.useCallback)(()=>{y([])},[]),setCheckedState:y,toggleSelected:I,select:A,deselect:T,clearSelected:_,setSelectedState:g,hoveredNode:C,setHoveredNode:S,getCheckedNodes:()=>n(p,b).result,isNodeChecked:e=>i(e,p,b),isNodeIndeterminate:e=>s(e,p,b)}}},73548:(e,t,r)=>{"use strict";r.d(t,{n:()=>n,q:()=>o}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)()},73985:(e,t,r)=>{"use strict";r.d(t,{useInViewport:()=>n});var o=r(12115);function n(){let e=(0,o.useRef)(null),[t,r]=(0,o.useState)(!1);return{ref:(0,o.useCallback)(t=>{if("undefined"!=typeof IntersectionObserver){var o,n;t&&!e.current?e.current=new IntersectionObserver(e=>r(e.some(e=>e.isIntersecting))):null==(o=e.current)||o.disconnect(),t?null==(n=e.current)||n.observe(t):r(!1)}},[]),inViewport:t}}},74088:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_7cda1cd6","root--default":"m_44da308b","root--contrast":"m_e3a01f8",label:"m_1e0e6180",remove:"m_ae386778",group:"m_1dcfd90b"}},74775:(e,t,r)=>{"use strict";r.d(t,{ComboboxGroup:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960),i=r(3826),s=r(19192);let c={},u=(0,l.factory)((e,t)=>{let{classNames:r,className:l,style:s,styles:u,vars:d,children:p,label:m,...v}=(0,n.useProps)("ComboboxGroup",c,e),f=(0,i.A)();return(0,o.jsxs)(a.Box,{ref:t,...f.getStyles("group",{className:l,classNames:r,style:s,styles:u}),...v,children:[m&&(0,o.jsx)("div",{...f.getStyles("groupLabel",{classNames:r,styles:u}),children:m}),p]})});u.classes=s.A,u.displayName="@mantine/core/ComboboxGroup"},75139:(e,t,r)=>{"use strict";r.d(t,{ComboboxClearButton:()=>l});var o=r(95155),n=r(12115),a=r(54853);let l=(0,n.forwardRef)((e,t)=>{let{size:r,onMouseDown:n,onClick:l,onClear:i,...s}=e;return(0,o.jsx)(a.Input.ClearButton,{ref:t,tabIndex:-1,"aria-hidden":!0,...s,onMouseDown:e=>{e.preventDefault(),null==n||n(e)},onClick:e=>{i(),null==l||l(e)}})});l.displayName="@mantine/core/ComboboxClearButton"},75205:(e,t,r)=>{"use strict";r.d(t,{useMap:()=>a});var o=r(12115),n=r(66437);function a(e){let t=(0,o.useRef)(new Map(e)),r=(0,n.useForceUpdate)();return t.current.set=function(){for(var e=arguments.length,o=Array(e),n=0;n<e;n++)o[n]=arguments[n];return Map.prototype.set.apply(t.current,o),r(),t.current},t.current.clear=function(){for(var e=arguments.length,o=Array(e),n=0;n<e;n++)o[n]=arguments[n];Map.prototype.clear.apply(t.current,o),r()},t.current.delete=function(){for(var e=arguments.length,o=Array(e),n=0;n<e;n++)o[n]=arguments[n];let a=Map.prototype.delete.apply(t.current,o);return r(),a},t.current}},75240:(e,t,r)=>{"use strict";function o(e,t){return r=>{null==e||e(r),null==t||t(r)}}r.d(t,{createEventHandler:()=>o})},75451:(e,t,r)=>{"use strict";r.d(t,{useScrollSpy:()=>s});var o=r(12115),n=r(74275);function a(e){return 0===e.length?-1:e.reduce((e,t,r)=>Math.abs(e.position)<Math.abs(t.y)?e:{index:r,position:t.y},{index:0,position:e[0].y}).index}function l(e){return Number(e.tagName[1])}function i(e){return e.textContent||""}function s(){let{selector:e="h1, h2, h3, h4, h5, h6",getDepth:t=l,getValue:r=i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[s,c]=(0,o.useState)(-1),[u,d]=(0,o.useState)(!1),[p,m]=(0,o.useState)([]),v=(0,o.useRef)([]),f=()=>{c(a(v.current.map(e=>e.getNode().getBoundingClientRect())))},h=()=>{let o=function(e,t,r){let o=[];for(let a=0;a<e.length;a+=1){let l=e[a];o.push({depth:t(l),value:r(l),id:l.id||(0,n.randomId)(),getNode:()=>l.id?document.getElementById(l.id):l})}return o}(Array.from(document.querySelectorAll(e)),t,r);v.current=o,d(!0),m(o),c(a(o.map(e=>e.getNode().getBoundingClientRect())))};return(0,o.useEffect)(()=>(h(),window.addEventListener("scroll",f),()=>window.removeEventListener("scroll",f)),[]),{reinitialize:h,active:s,initialized:u,data:p}}},75752:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_db6d6462",section:"m_2242eb65","stripes-animation":"m_81a374bd",label:"m_91e40b74"}},75843:(e,t,r)=>{"use strict";r.d(t,{OptionsDropdown:()=>p});var o=r(95155),n=r(52596),a=r(27547),l=r(74271),i=r(14449),s=r(67913),c=r(83731),u=r(19192);function d(e){let{data:t,withCheckIcon:r,value:l,checkIconPosition:s,unstyled:p,renderOption:m}=e;if(!(0,c.isOptionsGroup)(t)){var v;let e=(v=t.value,Array.isArray(l)?l.includes(v):l===v),c=r&&e&&(0,o.jsx)(a.CheckIcon,{className:u.A.optionsDropdownCheckIcon}),d=(0,o.jsxs)(o.Fragment,{children:["left"===s&&c,(0,o.jsx)("span",{children:t.label}),"right"===s&&c]});return(0,o.jsx)(i.Combobox.Option,{value:t.value,disabled:t.disabled,className:(0,n.A)({[u.A.optionsDropdownOption]:!p}),"data-reverse":"right"===s||void 0,"data-checked":e||void 0,"aria-selected":e,active:e,children:"function"==typeof m?m({option:t,checked:e}):d})}let f=t.items.map(e=>(0,o.jsx)(d,{data:e,value:l,unstyled:p,withCheckIcon:r,checkIconPosition:s,renderOption:m},e.value));return(0,o.jsx)(i.Combobox.Group,{label:t.group,children:f})}function p(e){let{data:t,hidden:r,hiddenWhenEmpty:n,filter:a,search:u,limit:p,maxDropdownHeight:m,withScrollArea:v=!0,filterOptions:f=!0,withCheckIcon:h=!1,value:g,checkIconPosition:b,nothingFoundMessage:y,unstyled:x,labelId:w,renderOption:C,scrollAreaProps:S,"aria-label":k}=e;!function e(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;if(Array.isArray(t))for(let o of t)if((0,c.isOptionsGroup)(o))e(o.items,r);else{if(void 0===o.value)throw Error("[@mantine/core] Each option must have value property");if("string"!=typeof o.value)throw Error("[@mantine/core] Option value must be a string, other data formats are not supported, got ".concat(typeof o.value));if(r.has(o.value))throw Error('[@mantine/core] Duplicate options are not supported. Option with value "'.concat(o.value,'" was provided more than once'));r.add(o.value)}}(t);let E="string"==typeof u?(a||s.defaultOptionsFilter)({options:t,search:f?u:"",limit:null!=p?p:1/0}):t,P=function(e){if(0===e.length)return!0;for(let t of e)if(!("group"in t)||t.items.length>0)return!1;return!0}(E),j=E.map(e=>(0,o.jsx)(d,{data:e,withCheckIcon:h,value:g,checkIconPosition:b,unstyled:x,renderOption:C},(0,c.isOptionsGroup)(e)?e.group:e.value));return(0,o.jsx)(i.Combobox.Dropdown,{hidden:r||n&&P,"data-composed":!0,children:(0,o.jsxs)(i.Combobox.Options,{labelledBy:w,"aria-label":k,children:[v?(0,o.jsx)(l.ScrollArea.Autosize,{mah:null!=m?m:220,type:"scroll",scrollbarSize:"var(--combobox-padding)",offsetScrollbars:"y",...S,children:j}):j,P&&y&&(0,o.jsx)(i.Combobox.Empty,{children:y})]})})}},76197:(e,t,r)=>{"use strict";r.d(t,{useColorScheme:()=>n});var o=r(69445);function n(e,t){return(0,o.useMediaQuery)("(prefers-color-scheme: dark)","dark"===e,t)?"dark":"light"}},76259:(e,t,r)=>{"use strict";r.d(t,{ProgressRoot:()=>v});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u=r(85794),d=r(75752);let p={},m=(0,a.createVarsResolver)((e,t)=>{let{size:r,radius:o,transitionDuration:a}=t;return{root:{"--progress-size":(0,n.getSize)(r,"progress-size"),"--progress-radius":void 0===o?void 0:(0,n.getRadius)(o),"--progress-transition-duration":"number"==typeof a?"".concat(a,"ms"):void 0}}}),v=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("ProgressRoot",p,e),{classNames:n,className:a,style:c,styles:v,unstyled:f,vars:h,autoContrast:g,transitionDuration:b,...y}=r,x=(0,i.useStyles)({name:"Progress",classes:d.A,props:r,className:a,style:c,classNames:n,styles:v,unstyled:f,vars:h,varsResolver:m});return(0,o.jsx)(u.V,{value:{getStyles:x,autoContrast:g},children:(0,o.jsx)(s.Box,{ref:t,...x("root"),...y})})});v.classes=d.A,v.displayName="@mantine/core/ProgressRoot"},77213:(e,t,r)=>{"use strict";r.d(t,{useClipboard:()=>n});var o=r(12115);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{timeout:2e3},[t,r]=(0,o.useState)(null),[n,a]=(0,o.useState)(!1),[l,i]=(0,o.useState)(null),s=t=>{window.clearTimeout(l),i(window.setTimeout(()=>a(!1),e.timeout)),a(t)};return{copy:e=>{"clipboard"in navigator?navigator.clipboard.writeText(e).then(()=>s(!0)).catch(e=>r(e)):r(Error("useClipboard: navigator.clipboard is not supported"))},reset:()=>{a(!1),r(null),window.clearTimeout(l)},error:t,copied:n}}},77345:(e,t,r)=>{"use strict";r.d(t,{DrawerRoot:()=>x});var o=r(95155),n=r(5903);r(12115);var a=r(58750),l=r(56204),i=r(68918),s=r(43664),c=r(53791),u=r(36960),d=r(53304),p=r(41407),m=r(74271),v=r(20950),f=r(56196);let h={top:"slide-down",bottom:"slide-up",left:"slide-right",right:"slide-left"},g={top:"slide-down",bottom:"slide-up",right:"slide-right",left:"slide-left"},b={closeOnClickOutside:!0,withinPortal:!0,lockScroll:!0,trapFocus:!0,returnFocus:!0,closeOnEscape:!0,keepMounted:!1,zIndex:(0,a.getDefaultZIndex)("modal"),position:"left"},y=(0,i.createVarsResolver)((e,t)=>{let{position:r,size:o,offset:a}=t;return{root:{"--drawer-size":(0,l.getSize)(o,"drawer-size"),"--drawer-flex":function(e){if("top"===e||"bottom"===e)return"0 0 calc(100% - var(--drawer-offset, 0rem) * 2)"}(r),"--drawer-height":"left"===r||"right"===r?void 0:"var(--drawer-size)","--drawer-align":function(e){switch(e){case"top":return"flex-start";case"bottom":return"flex-end";default:return}}(r),"--drawer-justify":"right"===r?"flex-end":void 0,"--drawer-offset":(0,n.D)(a)}}}),x=(0,u.factory)((e,t)=>{let r=(0,s.useProps)("DrawerRoot",b,e),{classNames:n,className:a,style:l,styles:i,unstyled:u,vars:x,scrollAreaComponent:w,position:C,transitionProps:S,radius:k,...E}=r,{dir:P}=(0,d.useDirection)(),j=(0,c.useStyles)({name:"Drawer",classes:f.A,props:r,className:a,style:l,classNames:n,styles:i,unstyled:u,vars:x,varsResolver:y}),R=("rtl"===P?g:h)[C];return(0,o.jsx)(v.T,{value:{scrollAreaComponent:w,getStyles:j,radius:k},children:(0,o.jsx)(p.ModalBase,{ref:t,...j("root"),transitionProps:{transition:R,...S},"data-offset-scrollbars":w===m.ScrollArea.Autosize||void 0,unstyled:u,...E})})});x.classes=f.A,x.displayName="@mantine/core/DrawerRoot"},77823:(e,t,r)=>{"use strict";r.d(t,{DrawerTitle:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(54578),i=r(20950),s=r(56196);let c={},u=(0,a.factory)((e,t)=>{let{classNames:r,className:a,style:s,styles:u,vars:d,...p}=(0,n.useProps)("DrawerTitle",c,e),m=(0,i.g)();return(0,o.jsx)(l.ModalBaseTitle,{ref:t,...m.getStyles("title",{classNames:r,style:s,styles:u,className:a}),...p})});u.classes=s.A,u.displayName="@mantine/core/DrawerTitle"},79382:(e,t,r)=>{"use strict";r.d(t,{Blockquote:()=>g});var o=r(95155),n=r(5903);r(12115);var a=r(56204),l=r(68918),i=r(98271),s=r(71180),c=r(70714),u=r(43664),d=r(53791),p=r(69604),m=r(36960),v={root:"m_ddec01c0",icon:"m_dde7bd57",cite:"m_dde51a35"};let f={iconSize:48},h=(0,l.createVarsResolver)((e,t)=>{let{color:r,iconSize:o,radius:l}=t,u=(0,i.parseThemeColor)({color:r||e.primaryColor,theme:e,colorScheme:"dark"}),d=(0,i.parseThemeColor)({color:r||e.primaryColor,theme:e,colorScheme:"light"});return{root:{"--bq-bg-light":(0,c.B)(d.value,.07),"--bq-bg-dark":(0,c.B)(u.value,.06),"--bq-bd":(0,s.getThemeColor)(r,e),"--bq-icon-size":(0,n.D)(o),"--bq-radius":(0,a.getRadius)(l)}}}),g=(0,m.factory)((e,t)=>{let r=(0,u.useProps)("Blockquote",f,e),{classNames:n,className:a,style:l,styles:i,unstyled:s,vars:c,children:m,icon:g,iconSize:b,cite:y,...x}=r,w=(0,d.useStyles)({name:"Blockquote",classes:v,props:r,className:a,style:l,classNames:n,styles:i,unstyled:s,vars:c,varsResolver:h});return(0,o.jsxs)(p.Box,{component:"blockquote",ref:t,...w("root"),...x,children:[g&&(0,o.jsx)("span",{...w("icon"),children:g}),m,y&&(0,o.jsx)("cite",{...w("cite"),children:y})]})});g.classes=v,g.displayName="@mantine/core/Blockquote"},79483:(e,t,r)=>{"use strict";function o(e){return e?e.map(e=>(function e(t){return"string"==typeof t?{value:t,label:t}:"value"in t&&!("label"in t)?{value:t.value,label:t.value,disabled:t.disabled}:"number"==typeof t?{value:t.toString(),label:t.toString()}:"group"in t?{group:t.group,items:t.items.map(t=>e(t))}:t})(e)):[]}r.d(t,{getParsedComboboxData:()=>o})},79555:(e,t,r)=>{"use strict";r.d(t,{useFocusWithin:()=>n});var o=r(12115);function n(){let{onBlur:e,onFocus:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[r,n]=(0,o.useState)(!1),a=(0,o.useRef)(!1),l=(0,o.useRef)(null),i=(0,o.useCallback)(e=>{n(e),a.current=e},[]),s=(0,o.useCallback)(e=>{a.current||(i(!0),null==t||t(e))},[t]),c=(0,o.useCallback)(t=>{a.current&&!(t.currentTarget instanceof HTMLElement&&t.relatedTarget instanceof HTMLElement&&t.currentTarget.contains(t.relatedTarget))&&(i(!1),null==e||e(t))},[e]),u=(0,o.useCallback)(e=>{e&&(l.current&&(l.current.removeEventListener("focusin",s),l.current.removeEventListener("focusout",c)),e.addEventListener("focusin",s),e.addEventListener("focusout",c))},[s,c]);return(0,o.useEffect)(()=>()=>{l.current&&(l.current.removeEventListener("focusin",s),l.current.removeEventListener("focusout",c))},[]),{ref:u,focused:r}}},79589:(e,t,r)=>{"use strict";r.d(t,{useSetState:()=>n});var o=r(12115);function n(e){let[t,r]=(0,o.useState)(e);return[t,(0,o.useCallback)(e=>r(t=>({...t,..."function"==typeof e?e(t):e})),[])]}},79827:(e,t,r)=>{"use strict";r.d(t,{getTransitionProps:()=>n});let o={duration:100,transition:"fade"};function n(e,t){return{...o,...t,...e}}},79967:(e,t,r)=>{"use strict";r.d(t,{Burger:()=>h});var o=r(95155),n=r(5903);r(12115);var a=r(56204),l=r(68918),i=r(71180),s=r(43664),c=r(53791),u=r(69604),d=r(36960),p=r(43608),m={root:"m_fea6bf1a",burger:"m_d4fb9cad"};let v={},f=(0,l.createVarsResolver)((e,t)=>{let{color:r,size:o,lineSize:l,transitionDuration:s,transitionTimingFunction:c}=t;return{root:{"--burger-color":r?(0,i.getThemeColor)(r,e):void 0,"--burger-size":(0,a.getSize)(o,"burger-size"),"--burger-line-size":l?(0,n.D)(l):void 0,"--burger-transition-duration":void 0===s?void 0:"".concat(s,"ms"),"--burger-transition-timing-function":c}}}),h=(0,d.factory)((e,t)=>{let r=(0,s.useProps)("Burger",v,e),{classNames:n,className:a,style:l,styles:i,unstyled:d,vars:h,opened:g,children:b,transitionDuration:y,transitionTimingFunction:x,lineSize:w,...C}=r,S=(0,c.useStyles)({name:"Burger",classes:m,props:r,className:a,style:l,classNames:n,styles:i,unstyled:d,vars:h,varsResolver:f});return(0,o.jsxs)(p.UnstyledButton,{...S("root"),ref:t,...C,children:[(0,o.jsx)(u.Box,{mod:["reduce-motion",{opened:g}],...S("burger")}),b]})});h.classes=m,h.displayName="@mantine/core/Burger"},80159:(e,t,r)=>{"use strict";function o(e){if(!e)return 0;let t=e.toString().split(".");return t.length>1?t[1].length:0}r.d(t,{X:()=>o})},80415:(e,t,r)=>{"use strict";r.d(t,{ProgressSection:()=>v});var o=r(95155);r(12115);var n=r(71180),a=r(89200),l=r(98840),i=r(3131),s=r(43664),c=r(69604),u=r(36960),d=r(85794),p=r(75752);let m={withAria:!0},v=(0,u.factory)((e,t)=>{let{classNames:r,className:u,style:p,styles:v,vars:f,value:h,withAria:g,color:b,striped:y,animated:x,mod:w,...C}=(0,s.useProps)("ProgressSection",m,e),S=(0,d.Q)(),k=(0,i.useMantineTheme)();return(0,o.jsx)(c.Box,{ref:t,...S.getStyles("section",{className:u,classNames:r,styles:v,style:p}),...C,...g?{role:"progressbar","aria-valuemax":100,"aria-valuemin":0,"aria-valuenow":h,"aria-valuetext":"".concat(h,"%")}:{},mod:[{striped:y||x,animated:x},w],__vars:{"--progress-section-width":"".concat(h,"%"),"--progress-section-color":(0,n.getThemeColor)(b,k),"--progress-label-color":(0,l.getAutoContrastValue)(S.autoContrast,k)?(0,a.getContrastColor)({color:b,theme:k,autoContrast:S.autoContrast}):void 0}})});v.classes=p.A,v.displayName="@mantine/core/ProgressSection"},80593:(e,t,r)=>{"use strict";r.d(t,{DrawerBody:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(48254),i=r(20950),s=r(56196);let c={},u=(0,a.factory)((e,t)=>{let{classNames:r,className:a,style:s,styles:u,vars:d,...p}=(0,n.useProps)("DrawerBody",c,e),m=(0,i.g)();return(0,o.jsx)(l.ModalBaseBody,{ref:t,...m.getStyles("body",{classNames:r,style:s,styles:u,className:a}),...p})});u.classes=s.A,u.displayName="@mantine/core/DrawerBody"},80864:(e,t,r)=>{"use strict";r.d(t,{b:()=>a,g:()=>n}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Pagination.Root component was not found in tree")},80975:(e,t,r)=>{"use strict";r.d(t,{PopoverTarget:()=>p});var o=r(12115),n=r(52596),a=r(88551),l=r(10866);r(95155);var i=r(72200),s=r(43664),c=r(36960),u=r(66142);let d={refProp:"ref",popupType:"dialog"},p=(0,c.factory)((e,t)=>{let{children:r,refProp:c,popupType:p,...m}=(0,s.useProps)("PopoverTarget",d,e);if(!(0,l.isElement)(r))throw Error("Popover.Target component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");let v=(0,u.C)(),f=(0,a.useMergedRef)(v.reference,(0,i.getRefProp)(r),t),h=v.withRoles?{"aria-haspopup":p,"aria-expanded":v.opened,"aria-controls":v.getDropdownId(),id:v.getTargetId()}:{};return(0,o.cloneElement)(r,{...m,...h,...v.targetProps,className:(0,n.A)(v.targetProps.className,m.className,r.props.className),[c]:f,...!v.controlled?{onClick:v.onToggle}:null})});p.displayName="@mantine/core/PopoverTarget"},81180:(e,t,r)=>{"use strict";r.d(t,{PillsInputField:()=>m});var o=r(95155),n=r(88551);r(12115);var a=r(43664),l=r(53791),i=r(69604),s=r(36960),c=r(21355),u=r(73548),d={field:"m_45c4369d"};let p={type:"visible"},m=(0,s.factory)((e,t)=>{let r=(0,a.useProps)("PillsInputField",p,e),{classNames:s,className:m,style:v,styles:f,unstyled:h,vars:g,type:b,disabled:y,id:x,pointer:w,mod:C,...S}=r,k=(0,u.n)(),E=(0,c.useInputWrapperContext)(),P=(0,l.useStyles)({name:"PillsInputField",classes:d,props:r,className:m,style:v,classNames:s,styles:f,unstyled:h,rootSelector:"field"}),j=y||(null==k?void 0:k.disabled);return(0,o.jsx)(i.Box,{component:"input",ref:(0,n.useMergedRef)(t,null==k?void 0:k.fieldRef),"data-type":b,disabled:j,mod:[{disabled:j,pointer:w},C],...P("field"),...S,id:(null==E?void 0:E.inputId)||x,"aria-invalid":null==k?void 0:k.hasError,"aria-describedby":null==E?void 0:E.describedBy,type:"text",onMouseDown:e=>!w&&e.stopPropagation()})});m.classes=d,m.displayName="@mantine/core/PillsInputField"},82230:(e,t,r)=>{"use strict";r.d(t,{useHovered:()=>n});var o=r(12115);function n(){let[e,t]=(0,o.useState)(-1);return[e,{setHovered:t,resetHovered:()=>t(-1)}]}},82685:(e,t,r)=>{"use strict";function o(e){return"auto"===e||"dark"===e||"light"===e}r.d(t,{isMantineColorScheme:()=>o})},83101:(e,t,r)=>{"use strict";r.d(t,{useFileDialog:()=>l});var o=r(12115),n=r(73141);let a={multiple:!0,accept:"*"};function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={...a,...e},[r,l]=(0,o.useState)(function(e){if(!e)return null;if(e instanceof FileList)return e;let t=new DataTransfer;for(let r of e)t.items.add(r);return t.files}(t.initialFiles)),i=(0,o.useRef)(null),s=(0,o.useCallback)(e=>{let r=e.target;if(null==r?void 0:r.files){var o;l(r.files),null==(o=t.onChange)||o.call(t,r.files)}},[t.onChange]),c=(0,o.useCallback)(()=>{var e;null==(e=i.current)||e.remove(),i.current=function(e){if("undefined"==typeof document)return null;let t=document.createElement("input");return t.type="file",e.accept&&(t.accept=e.accept),e.multiple&&(t.multiple=e.multiple),e.capture&&(t.capture=e.capture),e.directory&&(t.webkitdirectory=e.directory),t.style.display="none",t}(t),i.current&&(i.current.addEventListener("change",s,{once:!0}),document.body.appendChild(i.current))},[t,s]);(0,n.useIsomorphicEffect)(()=>(c(),()=>{var e;return null==(e=i.current)?void 0:e.remove()}),[]);let u=(0,o.useCallback)(()=>{var e;l(null),null==(e=t.onChange)||e.call(t,null)},[t.onChange]);return{files:r,open:(0,o.useCallback)(()=>{var e;t.resetOnOpen&&u(),c(),null==(e=i.current)||e.click()},[t.resetOnOpen,u,c]),reset:u}}},83370:(e,t,r)=>{"use strict";r.d(t,{useMantineColorScheme:()=>s});var o=r(12115),n=r(76197);r(95155);var a=r(83204),l=r(13656);function i(e){let t=document.createElement("style");return t.setAttribute("data-mantine-styles","inline"),t.innerHTML="*, *::before, *::after {transition: none !important;}",t.setAttribute("data-mantine-disable-transition","true"),e&&t.setAttribute("nonce",e),document.head.appendChild(t),()=>document.querySelectorAll("[data-mantine-disable-transition]").forEach(e=>e.remove())}function s(){let{keepTransitions:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,o.useRef)(a.noop),r=(0,o.useRef)(-1),s=(0,o.useContext)(l.MantineContext),c=(0,l.useMantineStyleNonce)(),u=(0,o.useRef)(null==c?void 0:c());if(!s)throw Error("[@mantine/core] MantineProvider was not found in tree");let d=o=>{s.setColorScheme(o),t.current=e?()=>{}:i(u.current),window.clearTimeout(r.current),r.current=window.setTimeout(()=>{var e;null==(e=t.current)||e.call(t)},10)},p=(0,n.useColorScheme)("light",{getInitialValueInEffect:!1}),m="auto"===s.colorScheme?p:s.colorScheme,v=(0,o.useCallback)(()=>d("light"===m?"dark":"light"),[d,m]);return(0,o.useEffect)(()=>()=>{var e;null==(e=t.current)||e.call(t),window.clearTimeout(r.current)},[]),{colorScheme:s.colorScheme,setColorScheme:d,clearColorScheme:()=>{s.clearColorScheme(),t.current=e?()=>{}:i(u.current),window.clearTimeout(r.current),r.current=window.setTimeout(()=>{var e;null==(e=t.current)||e.call(t)},10)},toggleColorScheme:v}}},83549:(e,t,r)=>{"use strict";r.d(t,{Stepper:()=>w});var o=r(95155),n=r(12115),a=r(5903),l=r(56204),i=r(68918),s=r(71180),c=r(89200),u=r(98840),d=r(43664),p=r(53791),m=r(69604),v=r(36960),f=r(51246),h=r(57305),g=r(39287),b=r(90812);let y={orientation:"horizontal",iconPosition:"left",allowNextStepsSelect:!0,wrap:!0},x=(0,i.createVarsResolver)((e,t)=>{let{color:r,iconSize:o,size:n,contentPadding:i,radius:d,autoContrast:p}=t;return{root:{"--stepper-color":r?(0,s.getThemeColor)(r,e):void 0,"--stepper-icon-color":(0,u.getAutoContrastValue)(p,e)?(0,c.getContrastColor)({color:r,theme:e,autoContrast:p}):void 0,"--stepper-icon-size":void 0===o?(0,l.getSize)(n,"stepper-icon-size"):(0,a.D)(o),"--stepper-content-padding":(0,l.getSpacing)(i),"--stepper-radius":void 0===d?void 0:(0,l.getRadius)(d),"--stepper-fz":(0,l.getFontSize)(n),"--stepper-spacing":(0,l.getSpacing)(n)}}}),w=(0,v.factory)((e,t)=>{var r,a,l;let i=(0,d.useProps)("Stepper",y,e),{classNames:s,className:c,style:u,styles:v,unstyled:g,vars:w,children:C,onStepClick:S,active:k,icon:E,completedIcon:P,progressIcon:j,color:R,iconSize:D,contentPadding:I,orientation:A,iconPosition:T,size:_,radius:M,allowNextStepsSelect:N,wrap:z,autoContrast:O,...L}=i,B=(0,p.useStyles)({name:"Stepper",classes:b.A,props:i,className:c,style:u,classNames:s,styles:v,unstyled:g,vars:w,varsResolver:x}),F=n.Children.toArray(C),V=F.filter(e=>e.type!==h.StepperCompleted),H=F.find(e=>e.type===h.StepperCompleted),U=V.reduce((e,t,r)=>{let o=k===r?"stepProgress":k>r?"stepCompleted":"stepInactive",a="function"==typeof S&&("boolean"==typeof t.props.allowStepSelect?t.props.allowStepSelect:"stepCompleted"===o||N);return e.push((0,n.cloneElement)(t,{icon:t.props.icon||E||r+1,key:r,step:r,state:o,onClick:()=>a&&(null==S?void 0:S(r)),allowStepClick:a,completedIcon:t.props.completedIcon||P,progressIcon:t.props.progressIcon||j,color:t.props.color||R,iconSize:D,iconPosition:t.props.iconPosition||T,orientation:A})),"horizontal"===A&&r!==V.length-1&&e.push((0,n.createElement)("div",{...B("separator"),"data-active":r<k||void 0,"data-orientation":A,key:"separator-".concat(r)})),e},[]),G=null==(a=V[k])||null==(r=a.props)?void 0:r.children,W=null==H||null==(l=H.props)?void 0:l.children,q=k>V.length-1?W:G;return(0,o.jsx)(f.P,{value:{getStyles:B,orientation:A,iconPosition:T},children:(0,o.jsxs)(m.Box,{...B("root"),ref:t,size:_,...L,children:[(0,o.jsx)(m.Box,{...B("steps"),mod:{orientation:A,"icon-position":T,wrap:z&&"vertical"!==A},children:U}),q&&(0,o.jsx)("div",{...B("content"),children:q})]})})});w.classes=b.A,w.displayName="@mantine/core/Stepper",w.Completed=h.StepperCompleted,w.Step=g.StepperStep},83731:(e,t,r)=>{"use strict";function o(e){return"group"in e}r.d(t,{isOptionsGroup:()=>o})},83742:(e,t,r)=>{"use strict";r.d(t,{closeOnEscape:()=>n});var o=r(83204);function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{active:!0};return"function"==typeof e&&t.active?r=>{if("Escape"===r.key){var o;e(r),null==(o=t.onTrigger)||o.call(t)}}:t.onKeyDown||o.noop}},83874:(e,t,r)=>{"use strict";r.d(t,{DrawerHeader:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(40978),i=r(20950),s=r(56196);let c={},u=(0,a.factory)((e,t)=>{let{classNames:r,className:a,style:s,styles:u,vars:d,...p}=(0,n.useProps)("DrawerHeader",c,e),m=(0,i.g)();return(0,o.jsx)(l.ModalBaseHeader,{ref:t,...m.getStyles("header",{classNames:r,style:s,styles:u,className:a}),...p})});u.classes=s.A,u.displayName="@mantine/core/DrawerHeader"},84224:(e,t,r)=>{"use strict";r.d(t,{SemiCircleProgress:()=>f});var o=r(95155),n=r(96963),a=r(5903);r(12115);var l=r(68918),i=r(71180),s=r(43664),c=r(53791),u=r(69604),d=r(36960),p={root:"m_fa528724",svg:"m_62e9e7e2",filledSegment:"m_c573fb6f",label:"m_4fa340f2"};let m={size:200,thickness:12,orientation:"up",fillDirection:"left-to-right",labelPosition:"bottom"},v=(0,l.createVarsResolver)((e,t)=>{let{filledSegmentColor:r,emptySegmentColor:o,orientation:n,fillDirection:l,transitionDuration:s,thickness:c}=t;return{root:{"--scp-filled-segment-color":r?(0,i.getThemeColor)(r,e):void 0,"--scp-empty-segment-color":o?(0,i.getThemeColor)(o,e):void 0,"--scp-rotation":function(e){let{orientation:t,fillDirection:r}=e;return"down"===t?"right-to-left"===r?"rotate(180deg) rotateY(180deg)":"rotate(180deg)":"left-to-right"===r?"rotateY(180deg)":void 0}({orientation:n,fillDirection:l}),"--scp-transition-duration":s?"".concat(s,"ms"):void 0,"--scp-thickness":(0,a.D)(c)}}}),f=(0,d.factory)((e,t)=>{let r=(0,s.useProps)("SemiCircleProgress",m,e),{classNames:a,className:l,style:i,styles:d,unstyled:f,vars:h,size:g,thickness:b,value:y,orientation:x,fillDirection:w,filledSegmentColor:C,emptySegmentColor:S,transitionDuration:k,label:E,labelPosition:P,...j}=r,R=(0,c.useStyles)({name:"SemiCircleProgress",classes:p,props:r,className:l,style:i,classNames:a,styles:d,unstyled:f,vars:h,varsResolver:v}),D=g/2,I=(g-2*b)/2,A=Math.PI*I,T=(0,n.clamp)(y,0,100)*(A/100);return(0,o.jsxs)(u.Box,{ref:t,size:g,...R("root"),...j,children:[E&&(0,o.jsx)("p",{...R("label"),"data-position":P,"data-orientation":x,children:E}),(0,o.jsxs)("svg",{width:g,height:g/2,...R("svg"),children:[(0,o.jsx)("circle",{cx:D,cy:D,r:I,fill:"none",stroke:"var(--scp-empty-segment-color)",strokeWidth:b,strokeDasharray:A,...R("emptySegment",{style:{strokeDashoffset:A}})}),(0,o.jsx)("circle",{cx:D,cy:D,r:I,fill:"none",stroke:"var(--scp-filled-segment-color)",strokeWidth:b,strokeDasharray:A,...R("filledSegment",{style:{strokeDashoffset:T}})})]})]})});f.displayName="@mantine/core/SemiCircleProgress",f.classes=p},84350:(e,t,r)=>{"use strict";r.d(t,{RadioCard:()=>h});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(36960),c=r(53304),u=r(43608),d=r(53051),p=r(54430),m={card:"m_9dc8ae12"};let v={withBorder:!0},f=(0,a.createVarsResolver)((e,t)=>{let{radius:r}=t;return{card:{"--card-radius":(0,n.getRadius)(r)}}}),h=(0,s.factory)((e,t)=>{let r=(0,l.useProps)("RadioCard",v,e),{classNames:n,className:a,style:s,styles:h,unstyled:g,vars:b,checked:y,mod:x,withBorder:w,value:C,onClick:S,name:k,onKeyDown:E,...P}=r,j=(0,i.useStyles)({name:"RadioCard",classes:m,props:r,className:a,style:s,classNames:n,styles:h,unstyled:g,vars:b,varsResolver:f,rootSelector:"card"}),{dir:R}=(0,c.useDirection)(),D=(0,d.R)(),I="boolean"==typeof y?y:(null==D?void 0:D.value)===C,A=k||(null==D?void 0:D.name);return(0,o.jsx)(p.v,{value:{checked:I},children:(0,o.jsx)(u.UnstyledButton,{ref:t,mod:[{"with-border":w,checked:I},x],...j("card"),...P,role:"radio","aria-checked":I,name:A,onClick:e=>{null==S||S(e),null==D||D.onChange(C||"")},onKeyDown:e=>{if(null==E||E(e),["ArrowDown","ArrowUp","ArrowLeft","ArrowRight"].includes(e.nativeEvent.code)){e.preventDefault();let t=Array.from(document.querySelectorAll('[role="radio"][name="'.concat(A||"__mantine",'"]'))),r=t.findIndex(t=>t===e.target),o=r+1>=t.length?0:r+1,n=r-1<0?t.length-1:r-1;"ArrowDown"===e.nativeEvent.code&&(t[o].focus(),t[o].click()),"ArrowUp"===e.nativeEvent.code&&(t[n].focus(),t[n].click()),"ArrowLeft"===e.nativeEvent.code&&(t["ltr"===R?n:o].focus(),t["ltr"===R?n:o].click()),"ArrowRight"===e.nativeEvent.code&&(t["ltr"===R?o:n].focus(),t["ltr"===R?o:n].click())}}})})});h.displayName="@mantine/core/RadioCard",h.classes=m},84395:(e,t,r)=>{"use strict";r.d(t,{Autocomplete:()=>g});var o=r(95155),n=r(12115),a=r(64173),l=r(57613),i=r(86028),s=r(43664),c=r(36960),u=r(79483),d=r(48509),p=r(14449),m=r(75843),v=r(87160),f=r(24225);let h={},g=(0,c.factory)((e,t)=>{let r=(0,s.useProps)("Autocomplete",h,e),{classNames:c,styles:g,unstyled:b,vars:y,dropdownOpened:x,defaultDropdownOpened:w,onDropdownClose:C,onDropdownOpen:S,onFocus:k,onBlur:E,onClick:P,onChange:j,data:R,value:D,defaultValue:I,selectFirstOptionOnChange:A,onOptionSubmit:T,comboboxProps:_,readOnly:M,disabled:N,filter:z,limit:O,withScrollArea:L,maxDropdownHeight:B,size:F,id:V,renderOption:H,autoComplete:U,scrollAreaProps:G,onClear:W,clearButtonProps:q,error:K,clearable:X,rightSection:Z,...Y}=r,Q=(0,a.useId)(V),$=(0,u.getParsedComboboxData)(R),J=(0,d.getOptionsLockup)($),[ee,et]=(0,l.useUncontrolled)({value:D,defaultValue:I,finalValue:"",onChange:j}),er=(0,v.useCombobox)({opened:x,defaultOpened:w,onDropdownOpen:S,onDropdownClose:()=>{null==C||C(),er.resetSelectedOption()}}),eo=e=>{et(e),er.resetSelectedOption()},{resolvedClassNames:en,resolvedStyles:ea}=(0,i.useResolvedStylesApi)({props:r,styles:g,classNames:c});(0,n.useEffect)(()=>{A&&er.selectFirstOption()},[A,ee]);let el=(0,o.jsx)(p.Combobox.ClearButton,{...q,onClear:()=>{eo(""),null==W||W()}});return(0,o.jsxs)(p.Combobox,{store:er,__staticSelector:"Autocomplete",classNames:en,styles:ea,unstyled:b,readOnly:M,onOptionSubmit:e=>{null==T||T(e),eo(J[e].label),er.closeDropdown()},size:F,..._,children:[(0,o.jsx)(p.Combobox.Target,{autoComplete:U,children:(0,o.jsx)(f.InputBase,{ref:t,...Y,size:F,__staticSelector:"Autocomplete",__clearSection:el,__clearable:X&&!!ee&&!N&&!M,rightSection:Z,disabled:N,readOnly:M,value:ee,error:K,onChange:e=>{eo(e.currentTarget.value),er.openDropdown(),A&&er.selectFirstOption()},onFocus:e=>{er.openDropdown(),null==k||k(e)},onBlur:e=>{er.closeDropdown(),null==E||E(e)},onClick:e=>{er.openDropdown(),null==P||P(e)},classNames:en,styles:ea,unstyled:b,id:Q})}),(0,o.jsx)(m.OptionsDropdown,{data:$,hidden:M||N,filter:z,search:ee,limit:O,hiddenWhenEmpty:!0,withScrollArea:L,maxDropdownHeight:B,unstyled:b,labelId:Y.label?"".concat(Q,"-label"):void 0,"aria-label":Y.label?void 0:Y["aria-label"],renderOption:H,scrollAreaProps:G})]})});g.classes={...f.InputBase.classes,...p.Combobox.classes},g.displayName="@mantine/core/Autocomplete"},84528:(e,t,r)=>{"use strict";r.d(t,{C:()=>a,r:()=>l});var o=r(12115);let n=(0,o.createContext)(!1),a=n.Provider,l=()=>(0,o.useContext)(n)},85229:(e,t,r)=>{"use strict";r.d(t,{usePrevious:()=>n});var o=r(12115);function n(e){let t=(0,o.useRef)(void 0);return(0,o.useEffect)(()=>{t.current=e},[e]),t.current}},85351:(e,t,r)=>{"use strict";r.d(t,{useComboboxTargetProps:()=>a});var o=r(12115),n=r(3826);function a(e){let{onKeyDown:t,withKeyboardNavigation:r,withAriaAttributes:a,withExpandedAttribute:l,targetType:i,autoComplete:s}=e,c=(0,n.A)(),[u,d]=(0,o.useState)(null);return{...a?{"aria-haspopup":"listbox","aria-expanded":l&&!!(c.store.listId&&c.store.dropdownOpened)||void 0,"aria-controls":c.store.dropdownOpened&&c.store.listId?c.store.listId:void 0,"aria-activedescendant":c.store.dropdownOpened&&u||void 0,autoComplete:s,"data-expanded":c.store.dropdownOpened||void 0,"data-mantine-stop-propagation":c.store.dropdownOpened||void 0}:{},onKeyDown:e=>{if((null==t||t(e),!c.readOnly&&r)&&!e.nativeEvent.isComposing){if("ArrowDown"===e.nativeEvent.code&&(e.preventDefault(),c.store.dropdownOpened?d(c.store.selectNextOption()):(c.store.openDropdown("keyboard"),d(c.store.selectActiveOption()),c.store.updateSelectedOptionIndex("selected",{scrollIntoView:!0}))),"ArrowUp"===e.nativeEvent.code&&(e.preventDefault(),c.store.dropdownOpened?d(c.store.selectPreviousOption()):(c.store.openDropdown("keyboard"),d(c.store.selectActiveOption()),c.store.updateSelectedOptionIndex("selected",{scrollIntoView:!0}))),"Enter"===e.nativeEvent.code||"NumpadEnter"===e.nativeEvent.code){if(229===e.nativeEvent.keyCode)return;let t=c.store.getSelectedOptionIndex();c.store.dropdownOpened&&-1!==t?(e.preventDefault(),c.store.clickSelectedOption()):"button"===i&&(e.preventDefault(),c.store.openDropdown("keyboard"))}"Escape"===e.key&&c.store.closeDropdown("keyboard"),"Space"===e.nativeEvent.code&&"button"===i&&(e.preventDefault(),c.store.toggleDropdown("keyboard"))}}}}},85639:(e,t,r)=>{"use strict";r.d(t,{t:()=>b});var o=r(95155),n=r(12115),a=r(43461),l=r(84237),i=r(88551),s=r(5903),c=r(3131),u=r(43664),d=r(53791),p=r(69604),m=r(36960),v=r(70130),f=r(87323),h=r(90296);let g={},b=(0,m.factory)((e,t)=>{var r;let m=(0,u.useProps)("ColorSlider",g,e),{classNames:b,className:y,style:x,styles:w,unstyled:C,vars:S,onChange:k,onChangeEnd:E,maxValue:P,round:j,size:R="md",focusable:D=!0,value:I,overlays:A,thumbColor:T="transparent",onScrubStart:_,onScrubEnd:M,__staticSelector:N="ColorPicker",...z}=m,O=(0,d.useStyles)({name:N,classes:h.A,props:m,className:y,style:x,classNames:b,styles:w,unstyled:C}),L=(null==(r=(0,v.W)())?void 0:r.getStyles)||O,B=(0,c.useMantineTheme)(),[F,V]=(0,n.useState)({y:0,x:I/P}),H=(0,n.useRef)(F),U=e=>j?Math.round(e*P):e*P,{ref:G}=(0,a.useMove)(e=>{let{x:t,y:r}=e;H.current={x:t,y:r},null==k||k(U(t))},{onScrubEnd:()=>{let{x:e}=H.current;null==E||E(U(e)),null==M||M()},onScrubStart:_});(0,l.useDidUpdate)(()=>{V({y:0,x:I/P})},[I]);let W=(e,t)=>{e.preventDefault();let r=(0,a.clampUseMovePosition)(t);null==k||k(U(r.x)),null==E||E(U(r.x))},q=A.map((e,t)=>(0,n.createElement)("div",{...L("sliderOverlay"),style:e,key:t}));return(0,o.jsxs)(p.Box,{...z,ref:(0,i.useMergedRef)(G,t),...L("slider"),role:"slider","aria-valuenow":I,"aria-valuemax":P,"aria-valuemin":0,tabIndex:D?0:-1,onKeyDown:e=>{switch(e.key){case"ArrowRight":W(e,{x:F.x+.05,y:F.y});break;case"ArrowLeft":W(e,{x:F.x-.05,y:F.y})}},"data-focus-ring":B.focusRing,__vars:{"--cp-thumb-size":"var(--cp-thumb-size-".concat(R,")")},children:[q,(0,o.jsx)(f.z,{position:F,...L("thumb",{style:{top:(0,s.D)(1),background:T}})})]})});b.displayName="@mantine/core/ColorSlider"},85794:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a,V:()=>n}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Progress.Root component was not found in tree")},86583:(e,t,r)=>{"use strict";r.d(t,{useLongPress:()=>n});var o=r(12115);function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{threshold:r=400,onStart:n,onFinish:i,onCancel:s}=t,c=(0,o.useRef)(!1),u=(0,o.useRef)(!1),d=(0,o.useRef)(-1);return(0,o.useEffect)(()=>()=>window.clearTimeout(d.current),[]),(0,o.useMemo)(()=>{if("function"!=typeof e)return{};let t=t=>{(l(t)||a(t))&&(n&&n(t),u.current=!0,d.current=window.setTimeout(()=>{e(t),c.current=!0},r))},o=e=>{(l(e)||a(e))&&(c.current?i&&i(e):u.current&&s&&s(e),c.current=!1,u.current=!1,d.current&&window.clearTimeout(d.current))};return{onMouseDown:t,onMouseUp:o,onMouseLeave:o,onTouchStart:t,onTouchEnd:o}},[e,r,s,i,n])}function a(e){return window.TouchEvent?e.nativeEvent instanceof TouchEvent:"touches"in e.nativeEvent}function l(e){return e.nativeEvent instanceof MouseEvent}},87160:(e,t,r)=>{"use strict";r.d(t,{useCombobox:()=>a});var o=r(12115),n=r(57613);function a(){let{defaultOpened:e,opened:t,onOpenedChange:r,onDropdownClose:a,onDropdownOpen:l,loop:i=!0,scrollBehavior:s="instant"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[c,u]=(0,n.useUncontrolled)({value:t,defaultValue:e,finalValue:!1,onChange:r}),d=(0,o.useRef)(null),p=(0,o.useRef)(-1),m=(0,o.useRef)(null),v=(0,o.useRef)(null),f=(0,o.useRef)(-1),h=(0,o.useRef)(-1),g=(0,o.useRef)(-1),b=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";c||(u(!0),null==l||l(e))},[u,l,c]),y=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";c&&(u(!1),null==a||a(e))},[u,a,c]),x=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"unknown";c?y(e):b(e)},[y,b,c]),w=(0,o.useCallback)(()=>{let e=document.querySelector("#".concat(d.current," [data-combobox-selected]"));null==e||e.removeAttribute("data-combobox-selected"),null==e||e.removeAttribute("aria-selected")},[]),C=(0,o.useCallback)(e=>{let t=document.getElementById(d.current),r=null==t?void 0:t.querySelectorAll("[data-combobox-option]");if(!r)return null;let o=e>=r.length?0:e<0?r.length-1:e;return(p.current=o,(null==r?void 0:r[o])&&!r[o].hasAttribute("data-combobox-disabled"))?(w(),r[o].setAttribute("data-combobox-selected","true"),r[o].setAttribute("aria-selected","true"),r[o].scrollIntoView({block:"nearest",behavior:s}),r[o].id):null},[s,w]),S=(0,o.useCallback)(()=>{let e=document.querySelector("#".concat(d.current," [data-combobox-active]"));return e?C(Array.from(document.querySelectorAll("#".concat(d.current," [data-combobox-option]"))).findIndex(t=>t===e)):C(0)},[C]),k=(0,o.useCallback)(()=>C(function(e,t,r){for(let r=e+1;r<t.length;r+=1)if(!t[r].hasAttribute("data-combobox-disabled"))return r;if(r){for(let e=0;e<t.length;e+=1)if(!t[e].hasAttribute("data-combobox-disabled"))return e}return e}(p.current,document.querySelectorAll("#".concat(d.current," [data-combobox-option]")),i)),[C,i]),E=(0,o.useCallback)(()=>C(function(e,t,r){for(let r=e-1;r>=0;r-=1)if(!t[r].hasAttribute("data-combobox-disabled"))return r;if(r){for(let e=t.length-1;e>-1;e-=1)if(!t[e].hasAttribute("data-combobox-disabled"))return e}return e}(p.current,document.querySelectorAll("#".concat(d.current," [data-combobox-option]")),i)),[C,i]),P=(0,o.useCallback)(()=>C(function(e){for(let t=0;t<e.length;t+=1)if(!e[t].hasAttribute("data-combobox-disabled"))return t;return -1}(document.querySelectorAll("#".concat(d.current," [data-combobox-option]")))),[C]),j=(0,o.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"selected",t=arguments.length>1?arguments[1]:void 0;g.current=window.setTimeout(()=>{let r=document.querySelectorAll("#".concat(d.current," [data-combobox-option]")),o=Array.from(r).findIndex(t=>t.hasAttribute("data-combobox-".concat(e)));if(p.current=o,null==t?void 0:t.scrollIntoView){var n;null==(n=r[o])||n.scrollIntoView({block:"nearest",behavior:s})}},0)},[]),R=(0,o.useCallback)(()=>{p.current=-1,w()},[w]),D=(0,o.useCallback)(()=>{let e=document.querySelectorAll("#".concat(d.current," [data-combobox-option]")),t=null==e?void 0:e[p.current];null==t||t.click()},[]),I=(0,o.useCallback)(e=>{d.current=e},[]),A=(0,o.useCallback)(()=>{f.current=window.setTimeout(()=>m.current.focus(),0)},[]),T=(0,o.useCallback)(()=>{h.current=window.setTimeout(()=>v.current.focus(),0)},[]),_=(0,o.useCallback)(()=>p.current,[]);return(0,o.useEffect)(()=>()=>{window.clearTimeout(f.current),window.clearTimeout(h.current),window.clearTimeout(g.current)},[]),{dropdownOpened:c,openDropdown:b,closeDropdown:y,toggleDropdown:x,selectedOptionIndex:p.current,getSelectedOptionIndex:_,selectOption:C,selectFirstOption:P,selectActiveOption:S,selectNextOption:k,selectPreviousOption:E,resetSelectedOption:R,updateSelectedOptionIndex:j,listId:d.current,setListId:I,clickSelectedOption:D,searchRef:m,focusSearchInput:A,targetRef:v,focusTarget:T}}},87323:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var o=r(95155),n=r(12115),a=r(69604);let l=(0,n.forwardRef)((e,t)=>{let{position:r,...n}=e;return(0,o.jsx)(a.Box,{ref:t,__vars:{"--thumb-y-offset":"".concat(100*r.y,"%"),"--thumb-x-offset":"".concat(100*r.x,"%")},...n})});l.displayName="@mantine/core/ColorPickerThumb"},87872:(e,t,r)=>{"use strict";r.d(t,{Fieldset:()=>m});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u={root:"m_e9408a47","root--default":"m_84c9523a","root--filled":"m_ef274e49","root--unstyled":"m_eda993d3",legend:"m_90794832","legend--unstyled":"m_74ca27fe"};let d={variant:"default"},p=(0,a.createVarsResolver)((e,t)=>{let{radius:r}=t;return{root:{"--fieldset-radius":void 0===r?void 0:(0,n.getRadius)(r)}}}),m=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("Fieldset",d,e),{classNames:n,className:a,style:c,styles:m,unstyled:v,vars:f,legend:h,variant:g,children:b,...y}=r,x=(0,i.useStyles)({name:"Fieldset",classes:u,props:r,className:a,style:c,classNames:n,styles:m,unstyled:v,vars:f,varsResolver:p});return(0,o.jsxs)(s.Box,{component:"fieldset",ref:t,variant:g,...x("root",{variant:g}),...y,children:[h&&(0,o.jsx)("legend",{...x("legend",{variant:g}),children:h}),b]})});m.classes=u,m.displayName="@mantine/core/Fieldset"},87965:(e,t,r)=>{"use strict";r.d(t,{useSet:()=>a});var o=r(12115),n=r(66437);function a(e){let t=(0,o.useRef)(new Set(e)),r=(0,n.useForceUpdate)();return t.current.add=function(){for(var e=arguments.length,o=Array(e),n=0;n<e;n++)o[n]=arguments[n];let a=Set.prototype.add.apply(t.current,o);return r(),a},t.current.clear=function(){for(var e=arguments.length,o=Array(e),n=0;n<e;n++)o[n]=arguments[n];Set.prototype.clear.apply(t.current,o),r()},t.current.delete=function(){for(var e=arguments.length,o=Array(e),n=0;n<e;n++)o[n]=arguments[n];let a=Set.prototype.delete.apply(t.current,o);return r(),a},t.current.union=e=>{let r=new Set(t.current);return(function(e){if(e instanceof Set)return e;let t=new Set;for(let r of e)t.add(r);return t})(e).forEach(e=>r.add(e)),r},t.current}},88316:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o,m:()=>n}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)()},89200:(e,t,r)=>{"use strict";r.d(t,{getContrastColor:()=>a,getPrimaryContrastColor:()=>l});var o=r(30128),n=r(98271);function a(e){let{color:t,theme:r,autoContrast:o}=e;return("boolean"==typeof o?o:r.autoContrast)&&(0,n.parseThemeColor)({color:t||r.primaryColor,theme:r}).isLight?"var(--mantine-color-black)":"var(--mantine-color-white)"}function l(e,t){return a({color:e.colors[e.primaryColor][(0,o.getPrimaryShade)(e,t)],theme:e,autoContrast:null})}},89210:(e,t,r)=>{"use strict";r.d(t,{Breadcrumbs:()=>f});var o=r(95155),n=r(12115),a=r(10866),l=r(56204),i=r(68918),s=r(43664),c=r(53791),u=r(69604),d=r(36960),p={root:"m_8b3717df",breadcrumb:"m_f678d540",separator:"m_3b8f2208"};let m={separator:"/"},v=(0,i.createVarsResolver)((e,t)=>{let{separatorMargin:r}=t;return{root:{"--bc-separator-margin":(0,l.getSpacing)(r)}}}),f=(0,d.factory)((e,t)=>{let r=(0,s.useProps)("Breadcrumbs",m,e),{classNames:l,className:i,style:d,styles:f,unstyled:h,vars:g,children:b,separator:y,separatorMargin:x,...w}=r,C=(0,c.useStyles)({name:"Breadcrumbs",classes:p,props:r,className:i,style:d,classNames:l,styles:f,unstyled:h,vars:g,varsResolver:v}),S=n.Children.toArray(b).reduce((e,t,r,o)=>{var l;let i=(0,a.isElement)(t)?(0,n.cloneElement)(t,{...C("breadcrumb",{className:null==(l=t.props)?void 0:l.className}),key:r}):(0,n.createElement)("div",{...C("breadcrumb"),key:r},t);return e.push(i),r!==o.length-1&&e.push((0,n.createElement)(u.Box,{...C("separator"),key:"separator-".concat(r)},y)),e},[]);return(0,o.jsx)(u.Box,{ref:t,...C("root"),...w,children:S})});f.classes=p,f.displayName="@mantine/core/Breadcrumbs"},89535:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,T:()=>n}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("Accordion.Item component was not found in the tree")},89691:(e,t,r)=>{"use strict";r.d(t,{FloatingArrow:()=>c});var o=r(95155),n=r(12115),a=r(53304);function l(e,t,r,o){return"center"===e||"center"===o?{top:t}:"end"===e?{bottom:r}:"start"===e?{top:r}:{}}function i(e,t,r,o,n){return"center"===e||"center"===o?{left:t}:"end"===e?{["ltr"===n?"right":"left"]:r}:"start"===e?{["ltr"===n?"left":"right"]:r}:{}}let s={bottom:"borderTopLeftRadius",left:"borderTopRightRadius",right:"borderBottomLeftRadius",top:"borderBottomRightRadius"},c=(0,n.forwardRef)((e,t)=>{let{position:r,arrowSize:n,arrowOffset:c,arrowRadius:u,arrowPosition:d,visible:p,arrowX:m,arrowY:v,style:f,...h}=e,{dir:g}=(0,a.useDirection)();return p?(0,o.jsx)("div",{...h,ref:t,style:{...f,...function(e){let{position:t,arrowSize:r,arrowOffset:o,arrowRadius:n,arrowPosition:a,arrowX:c,arrowY:u,dir:d}=e,[p,m="center"]=t.split("-"),v={width:r,height:r,transform:"rotate(45deg)",position:"absolute",[s[p]]:n},f=-r/2;return"left"===p?{...v,...l(m,u,o,a),right:f,borderLeftColor:"transparent",borderBottomColor:"transparent",clipPath:"polygon(100% 0, 0 0, 100% 100%)"}:"right"===p?{...v,...l(m,u,o,a),left:f,borderRightColor:"transparent",borderTopColor:"transparent",clipPath:"polygon(0 100%, 0 0, 100% 100%)"}:"top"===p?{...v,...i(m,c,o,a,d),bottom:f,borderTopColor:"transparent",borderLeftColor:"transparent",clipPath:"polygon(0 100%, 100% 100%, 100% 0)"}:"bottom"===p?{...v,...i(m,c,o,a,d),top:f,borderBottomColor:"transparent",borderRightColor:"transparent",clipPath:"polygon(0 100%, 0 0, 100% 0)"}:{}}({position:r,arrowSize:n,arrowOffset:c,arrowRadius:u,arrowPosition:d,dir:g,arrowX:m,arrowY:v})}}):null});c.displayName="@mantine/core/FloatingArrow"},90296:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={wrapper:"m_fee9c77",preview:"m_9dddfbac",body:"m_bffecc3e",sliders:"m_3283bb96",thumb:"m_40d572ba",swatch:"m_d8ee6fd8",swatches:"m_5711e686",saturation:"m_202a296e",saturationOverlay:"m_11b3db02",slider:"m_d856d47d",sliderOverlay:"m_8f327113"}},90538:(e,t,r)=>{"use strict";r.d(t,{Highlight:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(64511),l=r(5192),i=r(58887);function s(e){return e.replace(/[-[\]{}()*+?.,\\^$|#]/g,"\\$&")}let c={},u=(0,a.polymorphicFactory)((e,t)=>{let{unstyled:r,children:a,highlight:u,highlightStyles:d,color:p,...m}=(0,n.useProps)("Highlight",c,e),v=function(e,t){if(null==t)return[{chunk:e,highlighted:!1}];let r=Array.isArray(t)?t.map(s):s(t);if(!(Array.isArray(r)?r.filter(e=>e.trim().length>0).length>0:""!==r.trim()))return[{chunk:e,highlighted:!1}];let o="string"==typeof r?r.trim():r.filter(e=>0!==e.trim().length).map(e=>e.trim()).sort((e,t)=>t.length-e.length).join("|"),n=RegExp("(".concat(o,")"),"gi");return e.split(n).map(e=>({chunk:e,highlighted:n.test(e)})).filter(e=>{let{chunk:t}=e;return t})}(a,u);return(0,o.jsx)(i.Text,{unstyled:r,ref:t,...m,__staticSelector:"Highlight",children:v.map((e,t)=>{let{chunk:n,highlighted:a}=e;return a?(0,o.jsx)(l.Mark,{unstyled:r,color:p,style:d,"data-highlight":n,children:n},t):(0,o.jsx)("span",{children:n},t)})})});u.classes=i.Text.classes,u.displayName="@mantine/core/Highlight"},90557:(e,t,r)=>{"use strict";r.d(t,{Space:()=>s});var o=r(95155);r(12115);var n=r(43664),a=r(69604),l=r(36960);let i={},s=(0,l.factory)((e,t)=>{let{w:r,h:l,miw:s,mih:c,...u}=(0,n.useProps)("Space",i,e);return(0,o.jsx)(a.Box,{ref:t,...u,w:r,miw:null!=s?s:r,h:l,mih:null!=c?c:l})});s.displayName="@mantine/core/Space"},90635:()=>{},90693:(e,t,r)=>{"use strict";r.d(t,{useMutationObserver:()=>n});var o=r(12115);function n(e,t,r){let n=(0,o.useRef)(null),a=(0,o.useRef)(null);return(0,o.useEffect)(()=>{let o="function"==typeof r?r():r;return(o||a.current)&&(n.current=new MutationObserver(e),n.current.observe(o||a.current,t)),()=>{var e;null==(e=n.current)||e.disconnect()}},[e,t]),a}},90812:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var o={root:"m_cbb4ea7e",steps:"m_aaf89d0b",separator:"m_2a371ac9",content:"m_78da155d",step:"m_cbb57068","step--horizontal":"m_f56b1e2c","step--vertical":"m_833edb7e",verticalSeparator:"m_6496b3f3",stepWrapper:"m_818e70b",stepIcon:"m_1959ad01",stepCompletedIcon:"m_a79331dc",stepBody:"m_1956aa2a",stepLabel:"m_12051f6c",stepDescription:"m_164eea74"}},90892:(e,t,r)=>{"use strict";r.d(t,{Code:()=>m});var o=r(95155);r(12115);var n=r(68918),a=r(71180),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u={root:"m_b183c0a2"};let d={},p=(0,n.createVarsResolver)((e,t)=>{let{color:r}=t;return{root:{"--code-bg":r?(0,a.getThemeColor)(r,e):void 0}}}),m=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("Code",d,e),{classNames:n,className:a,style:c,styles:m,unstyled:v,vars:f,color:h,block:g,variant:b,mod:y,...x}=r,w=(0,i.useStyles)({name:"Code",props:r,classes:u,className:a,style:c,classNames:n,styles:m,unstyled:v,vars:f,varsResolver:p});return(0,o.jsx)(s.Box,{component:g?"pre":"code",variant:b,ref:t,mod:[{block:g},y],...w("root"),...x,dir:"ltr"})});m.classes=u,m.displayName="@mantine/core/Code"},91390:(e,t,r)=>{"use strict";r.d(t,{Tree:()=>x});var o=r(95155),n=r(12115),a=r(67385),l=r(88551),i=r(56204),s=r(68918),c=r(43664),u=r(53791),d=r(69604),p=r(36960),m=r(91834);function v(e,t,r){if(!e||!t)return[];let o=r.indexOf(e),n=r.indexOf(t),a=Math.min(o,n),l=Math.max(o,n);return r.slice(a,l+1)}function f(e){let{node:t,getStyles:r,rootIndex:a,controller:l,expandOnClick:i,selectOnClick:s,isSubtree:c,level:u=1,renderNode:p,flatValues:h,allowRangeSelection:g,expandOnSpace:b,checkOnSpace:y}=e,x=(0,n.useRef)(null),w=(t.children||[]).map(e=>(0,o.jsx)(f,{node:e,flatValues:h,getStyles:r,rootIndex:void 0,level:u+1,controller:l,expandOnClick:i,isSubtree:!0,renderNode:p,selectOnClick:s,allowRangeSelection:g,expandOnSpace:b,checkOnSpace:y},e.value)),C=l.selectedState.includes(t.value),S={...r("label"),onClick:e=>{var r,o;e.stopPropagation(),g&&e.shiftKey&&l.anchorNode?(l.setSelectedState(v(l.anchorNode,t.value,h)),null==(r=x.current)||r.focus()):(i&&l.toggleExpanded(t.value),s&&l.select(t.value),null==(o=x.current)||o.focus())},"data-selected":C||void 0,"data-value":t.value,"data-hovered":l.hoveredNode===t.value||void 0};return(0,o.jsxs)("li",{...r("node",{style:{"--label-offset":"calc(var(--level-offset) * ".concat(u-1,")")}}),role:"treeitem","aria-selected":C,"data-value":t.value,"data-selected":C||void 0,"data-hovered":l.hoveredNode===t.value||void 0,"data-level":u,tabIndex:0===a?0:-1,onKeyDown:e=>{var r,o,n;if("ArrowRight"===e.nativeEvent.code&&(e.stopPropagation(),e.preventDefault(),l.expandedState[t.value]?null==(r=e.currentTarget.querySelector("[role=treeitem]"))||r.focus():l.expand(t.value)),"ArrowLeft"===e.nativeEvent.code&&(e.stopPropagation(),e.preventDefault(),l.expandedState[t.value]&&(t.children||[]).length>0?l.collapse(t.value):c&&(null==(o=(0,m.findElementAncestor)(e.currentTarget,"[role=treeitem]"))||o.focus())),"ArrowDown"===e.nativeEvent.code||"ArrowUp"===e.nativeEvent.code){let t=(0,m.findElementAncestor)(e.currentTarget,"[data-tree-root]");if(!t)return;e.stopPropagation(),e.preventDefault();let r=Array.from(t.querySelectorAll("[role=treeitem]")),o=r.indexOf(e.currentTarget);if(-1===o)return;let a="ArrowDown"===e.nativeEvent.code?o+1:o-1;if(null==(n=r[a])||n.focus(),e.shiftKey){let e=r[a];e&&l.setSelectedState(v(l.anchorNode,e.dataset.value,h))}}"Space"===e.nativeEvent.code&&(b&&(e.stopPropagation(),e.preventDefault(),l.toggleExpanded(t.value)),y&&(e.stopPropagation(),e.preventDefault(),l.isNodeChecked(t.value)?l.uncheckNode(t.value):l.checkNode(t.value)))},ref:x,onMouseOver:e=>{e.stopPropagation(),l.setHoveredNode(t.value)},onMouseLeave:e=>{e.stopPropagation(),l.setHoveredNode(null)},children:["function"==typeof p?p({node:t,level:u,selected:C,tree:l,expanded:l.expandedState[t.value]||!1,hasChildren:Array.isArray(t.children)&&t.children.length>0,elementProps:S}):(0,o.jsx)("div",{...S,children:t.label}),l.expandedState[t.value]&&w.length>0&&(0,o.jsx)(d.Box,{component:"ul",role:"group",...r("subtree"),"data-level":u,children:w})]})}f.displayName="@mantine/core/TreeNode";var h=r(72770),g={root:"m_f698e191",subtree:"m_75f3ecf",node:"m_f6970eb1",label:"m_dc283425"};let b={expandOnClick:!0,allowRangeSelection:!0,expandOnSpace:!0},y=(0,s.createVarsResolver)((e,t)=>{let{levelOffset:r}=t;return{root:{"--level-offset":(0,i.getSpacing)(r)}}}),x=(0,p.factory)((e,t)=>{let r=(0,c.useProps)("Tree",b,e),{classNames:i,className:s,style:p,styles:m,unstyled:v,vars:x,data:w,expandOnClick:C,tree:S,renderNode:k,selectOnClick:E,clearSelectionOnOutsideClick:P,allowRangeSelection:j,expandOnSpace:R,levelOffset:D,checkOnSpace:I,...A}=r,T=(0,h.useTree)(),_=S||T,M=(0,u.useStyles)({name:"Tree",classes:g,props:r,className:s,style:p,classNames:i,styles:m,unstyled:v,vars:x,varsResolver:y}),N=(0,a.useClickOutside)(()=>P&&_.clearSelected()),z=(0,l.useMergedRef)(t,N),O=(0,n.useMemo)(()=>(function e(t){return t.reduce((t,r)=>(t.push(r.value),r.children&&t.push(...e(r.children)),t),[])})(w),[w]);(0,n.useEffect)(()=>{_.initialize(w)},[w]);let L=w.map((e,t)=>(0,o.jsx)(f,{node:e,getStyles:M,rootIndex:t,expandOnClick:C,selectOnClick:E,controller:_,renderNode:k,flatValues:O,allowRangeSelection:j,expandOnSpace:R,checkOnSpace:I},e.value));return(0,o.jsx)(d.Box,{component:"ul",ref:z,...M("root"),...A,role:"tree","aria-multiselectable":_.multiple,"data-tree-root":!0,children:L})});x.displayName="@mantine/core/Tree",x.classes=g},91590:(e,t,r)=>{"use strict";r.d(t,{Container:()=>m});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(43664),i=r(53791),s=r(69604),c=r(36960),u={root:"m_7485cace"};let d={},p=(0,a.createVarsResolver)((e,t)=>{let{size:r,fluid:o}=t;return{root:{"--container-size":o?void 0:(0,n.getSize)(r,"container-size")}}}),m=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("Container",d,e),{classNames:n,className:a,style:c,styles:m,unstyled:v,vars:f,fluid:h,mod:g,...b}=r,y=(0,i.useStyles)({name:"Container",classes:u,props:r,className:a,style:c,classNames:n,styles:m,unstyled:v,vars:f,varsResolver:p});return(0,o.jsx)(s.Box,{ref:t,mod:[{fluid:h},g],...y("root"),...b})});m.classes=u,m.displayName="@mantine/core/Container"},91834:(e,t,r)=>{"use strict";function o(e,t){let r=e;for(;(r=r.parentElement)&&!r.matches(t););return r}r.d(t,{findElementAncestor:()=>o})},93695:(e,t,r)=>{"use strict";r.d(t,{Anchor:()=>u});var o=r(95155),n=r(52596);r(12115);var a=r(43664),l=r(64511),i=r(58887),s={root:"m_849cf0da"};let c={underline:"hover"},u=(0,l.polymorphicFactory)((e,t)=>{let{underline:r,className:l,unstyled:u,mod:d,...p}=(0,a.useProps)("Anchor",c,e);return(0,o.jsx)(i.Text,{component:"a",ref:t,className:(0,n.A)({[s.root]:!u},l),...p,mod:[{underline:r},d],__staticSelector:"Anchor",unstyled:u})});u.classes=s,u.displayName="@mantine/core/Anchor"},93961:(e,t,r)=>{"use strict";r.d(t,{DrawerCloseButton:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(91033),i=r(20950),s=r(56196);let c={},u=(0,a.factory)((e,t)=>{let{classNames:r,className:a,style:s,styles:u,vars:d,...p}=(0,n.useProps)("DrawerCloseButton",c,e),m=(0,i.g)();return(0,o.jsx)(l.ModalBaseCloseButton,{ref:t,...m.getStyles("close",{classNames:r,style:s,styles:u,className:a}),...p})});u.classes=s.A,u.displayName="@mantine/core/DrawerCloseButton"},94193:(e,t,r)=>{"use strict";r.d(t,{MenuTarget:()=>d});var o=r(95155),n=r(12115),a=r(10866),l=r(75240),i=r(43664),s=r(60266),c=r(65054);let u={refProp:"ref"},d=(0,n.forwardRef)((e,t)=>{let{children:r,refProp:d,...p}=(0,i.useProps)("MenuTarget",u,e);if(!(0,a.isElement)(r))throw Error("Menu.Target component children should be an element or a component that accepts ref. Fragments, strings, numbers and other primitive values are not supported");let m=(0,c.K)(),v=r.props,f=(0,l.createEventHandler)(v.onClick,()=>{"click"===m.trigger?m.toggleDropdown():"click-hover"===m.trigger&&(m.setOpenedViaClick(!0),m.opened||m.openDropdown())}),h=(0,l.createEventHandler)(v.onMouseEnter,()=>("hover"===m.trigger||"click-hover"===m.trigger)&&m.openDropdown()),g=(0,l.createEventHandler)(v.onMouseLeave,()=>{"hover"===m.trigger?m.closeDropdown():"click-hover"!==m.trigger||m.openedViaClick||m.closeDropdown()});return(0,o.jsx)(s.Popover.Target,{refProp:d,popupType:"menu",ref:t,...p,children:(0,n.cloneElement)(r,{onClick:f,onMouseEnter:h,onMouseLeave:g,"data-expanded":!!m.opened||void 0})})});d.displayName="@mantine/core/MenuTarget"},94289:(e,t,r)=>{"use strict";r.d(t,{useFullscreen:()=>i});var o=r(12115);function n(){let e=window.document;return e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement}let a=["","webkit","moz","ms"];function l(e,t){let{onFullScreen:r,onError:o}=t;return a.forEach(t=>{e.addEventListener("".concat(t,"fullscreenchange"),r),e.addEventListener("".concat(t,"fullscreenerror"),o)}),()=>{a.forEach(t=>{e.removeEventListener("".concat(t,"fullscreenchange"),r),e.removeEventListener("".concat(t,"fullscreenerror"),o)})}}function i(){let[e,t]=(0,o.useState)(!1),r=(0,o.useRef)(null),a=(0,o.useCallback)(e=>{t(e.target===n())},[t]),i=(0,o.useCallback)(e=>{t(!1),console.error("[@mantine/hooks] use-fullscreen: Error attempting full-screen mode method: ".concat(e," (").concat(e.target,")"))},[t]),s=(0,o.useCallback)(async()=>{if(n())await function(){let e=window.document;return"function"==typeof e.exitFullscreen?e.exitFullscreen():"function"==typeof e.msExitFullscreen?e.msExitFullscreen():"function"==typeof e.webkitExitFullscreen?e.webkitExitFullscreen():"function"==typeof e.mozCancelFullScreen?e.mozCancelFullScreen():null}();else{var e,t,o,a,l,i;await ((null==(t=(e=r.current).requestFullscreen)?void 0:t.call(e))||(null==(o=e.msRequestFullscreen)?void 0:o.call(e))||(null==(a=e.webkitEnterFullscreen)?void 0:a.call(e))||(null==(l=e.webkitRequestFullscreen)?void 0:l.call(e))||(null==(i=e.mozRequestFullscreen)?void 0:i.call(e)))}},[]),c=(0,o.useCallback)(e=>{null===e?r.current=window.document.documentElement:r.current=e},[]);return(0,o.useEffect)(()=>!r.current&&window.document?(r.current=window.document.documentElement,l(r.current,{onFullScreen:a,onError:i})):r.current?l(r.current,{onFullScreen:a,onError:i}):void 0,[r.current]),{ref:c,toggle:s,fullscreen:e}}},94953:(e,t,r)=>{"use strict";r.d(t,{useNetwork:()=>l});var o=r(12115),n=r(28261);function a(){if("undefined"==typeof navigator)return{};let e=navigator,t=e.connection||e.mozConnection||e.webkitConnection;return t?{downlink:null==t?void 0:t.downlink,downlinkMax:null==t?void 0:t.downlinkMax,effectiveType:null==t?void 0:t.effectiveType,rtt:null==t?void 0:t.rtt,saveData:null==t?void 0:t.saveData,type:null==t?void 0:t.type}:{}}function l(){let[e,t]=(0,o.useState)({online:!0}),r=(0,o.useCallback)(()=>t(e=>({...e,...a()})),[]);return(0,n.useWindowEvent)("online",()=>t({online:!0,...a()})),(0,n.useWindowEvent)("offline",()=>t({online:!1,...a()})),(0,o.useEffect)(()=>{let e=navigator;if(e.connection)return t({online:e.onLine,...a()}),e.connection.addEventListener("change",r),()=>e.connection.removeEventListener("change",r);"boolean"==typeof e.onLine&&t(t=>({...t,online:e.onLine}))},[]),e}},95387:(e,t,r)=>{"use strict";r.d(t,{ColorInput:()=>j});var o=r(95155),n=r(12115),a=r(57613),l=r(6513),i=r(84237),s=r(56204),c=r(68918),u=r(86028),d=r(53791),p=r(43664),m=r(36960),v=r(81001),f=r(19606),h=r(4463),g=r(39186),b=r(34666),y=r(54853),x=r(83746),w=r(24225),C=r(60266);function S(e){let{style:t,...r}=e;return(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",style:{width:"var(--ci-eye-dropper-icon-size)",height:"var(--ci-eye-dropper-icon-size)",...t},viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",...r,children:[(0,o.jsx)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.jsx)("path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"}),(0,o.jsx)("path",{d:"M12 3l0 4"}),(0,o.jsx)("path",{d:"M12 21l0 -3"}),(0,o.jsx)("path",{d:"M3 12l4 0"}),(0,o.jsx)("path",{d:"M21 12l-3 0"}),(0,o.jsx)("path",{d:"M12 12l0 .01"})]})}var k={eyeDropperIcon:"m_b077c2bc",colorPreview:"m_c5ccdcab",dropdown:"m_5ece2cd7"};let E={format:"hex",fixOnBlur:!0,withPreview:!0,swatchesPerRow:7,withPicker:!0,popoverProps:{transitionProps:{transition:"fade",duration:0}},withEyeDropper:!0},P=(0,c.createVarsResolver)((e,t)=>{let{size:r}=t;return{eyeDropperIcon:{"--ci-eye-dropper-icon-size":(0,s.getSize)(r,"ci-eye-dropper-icon-size")},colorPreview:{"--ci-preview-size":(0,s.getSize)(r,"ci-preview-size")}}}),j=(0,m.factory)((e,t)=>{let r=(0,p.useProps)("ColorInput",E,e),{classNames:s,styles:c,unstyled:m,disallowInput:w,fixOnBlur:j,popoverProps:R,withPreview:D,withEyeDropper:I,eyeDropperIcon:A,closeOnColorSwatchClick:T,eyeDropperButtonProps:_,value:M,defaultValue:N,onChange:z,onChangeEnd:O,onClick:L,onFocus:B,onBlur:F,inputProps:V,format:H="hex",wrapperProps:U,readOnly:G,withPicker:W,swatches:q,disabled:K,leftSection:X,rightSection:Z,swatchesPerRow:Y,...Q}=(0,x.useInputProps)("ColorInput",E,e),$=(0,d.useStyles)({name:"ColorInput",props:r,classes:k,classNames:s,styles:c,unstyled:m,rootSelector:"wrapper",vars:r.vars,varsResolver:P}),{resolvedClassNames:J,resolvedStyles:ee}=(0,u.useResolvedStylesApi)({classNames:s,styles:c,props:r}),[et,er]=(0,n.useState)(!1),[eo,en]=(0,n.useState)(""),[ea,el]=(0,a.useUncontrolled)({value:M,defaultValue:N,finalValue:"",onChange:z}),{supported:ei,open:es}=(0,l.useEyeDropper)(),ec=(0,o.jsx)(v.ActionIcon,{..._,...$("eyeDropperButton",{className:null==_?void 0:_.className,style:null==_?void 0:_.style}),variant:"subtle",color:"gray",size:V.size,unstyled:m,onClick:()=>es().then(e=>{if(null==e?void 0:e.sRGBHex){let t=(0,h.convertHsvaTo)(H,(0,g.parseColor)(e.sRGBHex));el(t),null==O||O(t)}}).catch(()=>{}),children:A||(0,o.jsx)(S,{...$("eyeDropperIcon")})});return(0,n.useEffect)(()=>{((0,g.isColorValid)(ea)||""===ea.trim())&&en(ea)},[ea]),(0,i.useDidUpdate)(()=>{(0,g.isColorValid)(ea)&&el((0,h.convertHsvaTo)(H,(0,g.parseColor)(ea)))},[H]),(0,o.jsx)(y.Input.Wrapper,{...U,classNames:J,styles:ee,__staticSelector:"ColorInput",children:(0,o.jsxs)(C.Popover,{__staticSelector:"ColorInput",position:"bottom-start",offset:5,opened:et,...R,classNames:J,styles:ee,unstyled:m,withRoles:!1,disabled:G||!1===W&&(!Array.isArray(q)||0===q.length),children:[(0,o.jsx)(C.Popover.Target,{children:(0,o.jsx)(y.Input,{autoComplete:"off",...Q,...V,classNames:J,styles:ee,disabled:K,ref:t,__staticSelector:"ColorInput",onFocus:e=>{null==B||B(e),er(!0)},onBlur:e=>{j&&el(eo),null==F||F(e),er(!1)},onClick:e=>{null==L||L(e),er(!0)},spellCheck:!1,value:ea,onChange:e=>{let t=e.currentTarget.value;el(t),(0,g.isColorValid)(t)&&(null==O||O((0,h.convertHsvaTo)(H,(0,g.parseColor)(t))))},leftSection:X||(D?(0,o.jsx)(b.ColorSwatch,{color:(0,g.isColorValid)(ea)?ea:"#fff",size:"var(--ci-preview-size)",...$("colorPreview")}):null),readOnly:w||G,pointer:w,unstyled:m,rightSection:Z||(I&&!K&&!G&&ei?ec:null)})}),(0,o.jsx)(C.Popover.Dropdown,{onMouseDown:e=>e.preventDefault(),className:k.dropdown,children:(0,o.jsx)(f.ColorPicker,{__staticSelector:"ColorInput",value:ea,onChange:el,onChangeEnd:O,format:H,swatches:q,swatchesPerRow:Y,withPicker:W,size:V.size,focusable:!1,unstyled:m,styles:ee,classNames:J,onColorSwatchClick:()=>T&&er(!1)})})]})})});j.classes=w.InputBase.classes,j.displayName="@mantine/core/ColorInput"},95629:(e,t,r)=>{"use strict";r.d(t,{useMounted:()=>n});var o=r(12115);function n(){let[e,t]=(0,o.useState)(!1);return(0,o.useEffect)(()=>t(!0),[]),e}},95980:(e,t,r)=>{"use strict";r.d(t,{TypographyStylesProvider:()=>u});var o=r(95155);r(12115);var n=r(43664),a=r(53791),l=r(69604),i=r(36960),s={root:"m_d6493fad"};let c={},u=(0,i.factory)((e,t)=>{let r=(0,n.useProps)("TypographyStylesProvider",c,e),{classNames:i,className:u,style:d,styles:p,unstyled:m,...v}=r,f=(0,a.useStyles)({name:"TypographyStylesProvider",classes:s,props:r,className:u,style:d,classNames:i,styles:p,unstyled:m});return(0,o.jsx)(l.Box,{ref:t,...f("root"),...v})});u.classes=s,u.displayName="@mantine/core/TypographyStylesProvider"},97374:(e,t,r)=>{"use strict";r.d(t,{AccordionControl:()=>m});var o=r(95155);r(12115);var n=r(56570),a=r(43664),l=r(69604),i=r(36960),s=r(43608),c=r(9614),u=r(89535),d=r(54492);let p={},m=(0,i.factory)((e,t)=>{let{classNames:r,className:i,style:d,styles:m,vars:v,chevron:f,icon:h,onClick:g,onKeyDown:b,children:y,disabled:x,mod:w,...C}=(0,a.useProps)("AccordionControl",p,e),{value:S}=(0,u.A)(),k=(0,c.D)(),E=k.isItemActive(S),P="number"==typeof k.order,j="h".concat(k.order),R=(0,o.jsxs)(s.UnstyledButton,{...C,...k.getStyles("control",{className:i,classNames:r,style:d,styles:m,variant:k.variant}),unstyled:k.unstyled,mod:["accordion-control",{active:E,"chevron-position":k.chevronPosition,disabled:x},w],ref:t,onClick:e=>{null==g||g(e),k.onChange(S)},type:"button",disabled:x,"aria-expanded":E,"aria-controls":k.getRegionId(S),id:k.getControlId(S),onKeyDown:(0,n.createScopedKeydownHandler)({siblingSelector:"[data-accordion-control]",parentSelector:"[data-accordion]",activateOnFocus:!1,loop:k.loop,orientation:"vertical",onKeyDown:b}),children:[(0,o.jsx)(l.Box,{component:"span",mod:{rotate:!k.disableChevronRotation&&E,position:k.chevronPosition},...k.getStyles("chevron",{classNames:r,styles:m}),children:f||k.chevron}),(0,o.jsx)("span",{...k.getStyles("label",{classNames:r,styles:m}),children:y}),h&&(0,o.jsx)(l.Box,{component:"span",mod:{"chevron-position":k.chevronPosition},...k.getStyles("icon",{classNames:r,styles:m}),children:h})]});return P?(0,o.jsx)(j,{...k.getStyles("itemTitle",{classNames:r,styles:m}),children:R}):R});m.displayName="@mantine/core/AccordionControl",m.classes=d.A},97446:(e,t,r)=>{"use strict";r.d(t,{h:()=>n,s:()=>a}),r(12115);var o=r(56970);r(95155);let[n,a]=(0,o.createSafeContext)("SliderProvider was not found in tree")},97453:(e,t,r)=>{"use strict";r.d(t,{useDocumentVisibility:()=>n});var o=r(12115);function n(){let[e,t]=(0,o.useState)("visible");return(0,o.useEffect)(()=>{let e=()=>t(document.visibilityState);return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[]),e}},97975:(e,t,r)=>{"use strict";r.d(t,{w:()=>o});let o=(0,r(12115).createContext)(null);o.displayName="@mantine/modals/ModalsContext"},98180:(e,t,r)=>{"use strict";r.d(t,{Rating:()=>P});var o=r(95155),n=r(12115),a=r(64173),l=r(57613),i=r(96963),s=r(88551),c=r(56204),u=r(68918),d=r(71180),p=r(43664),m=r(53791),v=r(69604),f=r(36960),h=r(53304);let[g,b]=(0,r(56970).createSafeContext)("Rating was not found in tree");function y(e){let{width:t,height:r,style:n,...a}=e;return(0,o.jsx)("svg",{viewBox:"0 0 24 24",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{width:t,height:r,...n},...a,children:(0,o.jsx)("path",{d:"M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z"})})}function x(e){let{type:t}=e,r=b();return(0,o.jsx)(y,{...r.getStyles("starSymbol"),"data-filled":"full"===t||void 0})}function w(e){var t;let{getSymbolLabel:r,emptyIcon:n,fullIcon:a,full:l,active:i,value:s,readOnly:c,fractionValue:u,color:d,id:p,onBlur:m,onChange:f,onInputChange:g,style:y,...w}=e,C=b(),S="function"==typeof a?a(s):a,k="function"==typeof n?n(s):n,{dir:E}=(0,h.useDirection)();return(0,o.jsxs)(o.Fragment,{children:[!c&&(0,o.jsx)("input",{...C.getStyles("input"),onKeyDown:e=>" "===e.key&&f(s),id:p,type:"radio","data-active":i||void 0,"aria-label":null==r?void 0:r(s),value:s,onBlur:m,onChange:g,...w}),(0,o.jsx)(v.Box,{component:c?"div":"label",...C.getStyles("label"),"data-read-only":c||void 0,htmlFor:p,onClick:()=>f(s),__vars:{"--rating-item-z-index":null==(t=1===u?void 0:2*!!i)?void 0:t.toString()},children:(0,o.jsx)(v.Box,{...C.getStyles("symbolBody"),__vars:{"--rating-symbol-clip-path":1===u?void 0:"ltr"===E?"inset(0 ".concat(i?100-100*u:100,"% 0 0)"):"inset(0 0 0 ".concat(i?100-100*u:100,"% )")},children:l?S||(0,o.jsx)(x,{type:"full"}):k||(0,o.jsx)(x,{type:"empty"})})})]})}y.displayName="@mantine/core/StarIcon",x.displayName="@mantine/core/StarSymbol",w.displayName="@mantine/core/RatingItem";var C={root:"m_f8d312f2",symbolGroup:"m_61734bb7",starSymbol:"m_5662a89a",input:"m_211007ba",label:"m_21342ee4",symbolBody:"m_fae05d6a"};function S(e,t){var r;let o=Math.round(e/t)*t,n=(null==(r="".concat(t).split(".")[1])?void 0:r.length)||0;return Number(o.toFixed(n))}let k={size:"sm",getSymbolLabel:e=>"".concat(e),count:5,fractions:1,color:"yellow"},E=(0,u.createVarsResolver)((e,t)=>{let{size:r,color:o}=t;return{root:{"--rating-size":(0,c.getSize)(r,"rating-size"),"--rating-color":(0,d.getThemeColor)(o,e)}}}),P=(0,f.factory)((e,t)=>{let r=(0,p.useProps)("Rating",k,e),{classNames:c,className:u,style:d,styles:f,unstyled:b,vars:y,name:x,id:P,value:j,defaultValue:R,onChange:D,fractions:I,count:A,onMouseEnter:T,readOnly:_,onMouseMove:M,onHover:N,onMouseLeave:z,onTouchStart:O,onTouchEnd:L,size:B,variant:F,getSymbolLabel:V,color:H,emptySymbol:U,fullSymbol:G,highlightSelectedOnly:W,...q}=r,K=(0,m.useStyles)({name:"Rating",classes:C,props:r,className:u,style:d,classNames:c,styles:f,unstyled:b,vars:y,varsResolver:E}),{dir:X}=(0,h.useDirection)(),Z=(0,a.useId)(x),Y=(0,a.useId)(P),Q=(0,n.useRef)(null),[$,J]=(0,l.useUncontrolled)({value:j,defaultValue:R,finalValue:0,onChange:D}),[ee,et]=(0,n.useState)(-1),[er,eo]=(0,n.useState)(!0),en=Math.floor(I),ea=Math.floor(A),el=1/en,ei=S($,el),es=-1!==ee?ee:ei,ec=e=>{if(!Q.current)return 0;let{left:t,right:r,width:o}=Q.current.getBoundingClientRect(),n=o/ea;return(0,i.clamp)(S(("rtl"===X?r-e:e-t)/n+el/2,el),el,ea)},eu=()=>er&&et(-1),ed=e=>{_||("number"==typeof e?et(e):et(parseFloat(e.target.value)))},ep=e=>{_||("number"==typeof e?J(e):J(parseFloat(e.target.value)))},em=Array(ea).fill(0).map((e,t)=>{let r=t+1,n=Array.from(Array(0===t?en+1:en)),a=!_&&Math.ceil(ee)===r;return(0,o.jsx)("div",{"data-active":a||void 0,...K("symbolGroup"),children:n.map((e,n)=>{let a=el*(0===t?n:n+1),l=S(r-1+a,el);return(0,o.jsx)(w,{getSymbolLabel:V,emptyIcon:U,fullIcon:G,full:W?l===es:l<=es,active:l===es,checked:l===ei,readOnly:_,fractionValue:a,value:l,name:Z,onChange:ep,onBlur:eu,onInputChange:ed,id:"".concat(Y,"-").concat(t,"-").concat(n)},"".concat(r,"-").concat(l))})},r)});return(0,o.jsx)(g,{value:{getStyles:K},children:(0,o.jsx)(v.Box,{ref:(0,s.useMergedRef)(Q,t),...K("root"),onMouseMove:e=>{if(null==M||M(e),_)return;let t=ec(e.clientX);et(t),t!==ee&&(null==N||N(t))},onMouseEnter:e=>{null==T||T(e),_||eo(!1)},onMouseLeave:e=>{null==z||z(e),_||(et(-1),eo(!0),-1!==ee&&(null==N||N(-1)))},onTouchStart:e=>{let{touches:t}=e;1===t.length&&(_||J(ec(t[0].clientX)),null==O||O(e))},onTouchEnd:e=>{e.preventDefault(),null==L||L(e)},variant:F,size:B,id:Y,...q,children:em})})});P.classes=C,P.displayName="@mantine/core/Rating"},98605:(e,t,r)=>{"use strict";r.d(t,{Spoiler:()=>g});var o=r(95155),n=r(64173),a=r(57613),l=r(70885),i=r(5903);r(12115);var s=r(68918),c=r(43664),u=r(53791),d=r(69604),p=r(36960),m=r(93695),v={root:"m_559cce2d",content:"m_b912df4e",control:"m_b9131032"};let f={maxHeight:100,initialState:!1},h=(0,s.createVarsResolver)((e,t)=>{let{transitionDuration:r}=t;return{root:{"--spoiler-transition-duration":void 0!==r?"".concat(r,"ms"):void 0}}}),g=(0,p.factory)((e,t)=>{let r=(0,c.useProps)("Spoiler",f,e),{classNames:s,className:p,style:g,styles:b,unstyled:y,vars:x,initialState:w,maxHeight:C,hideLabel:S,showLabel:k,children:E,controlRef:P,transitionDuration:j,id:R,expanded:D,onExpandedChange:I,...A}=r,T=(0,u.useStyles)({name:"Spoiler",classes:v,props:r,className:p,style:g,classNames:s,styles:b,unstyled:y,vars:x,varsResolver:h}),_=(0,n.useId)(R),M="".concat(_,"-region"),[N,z]=(0,a.useUncontrolled)({value:D,defaultValue:w,finalValue:!1,onChange:I}),{ref:O,height:L}=(0,l.useElementSize)(),B=N?S:k,F=null!==B&&C<L;return(0,o.jsxs)(d.Box,{...T("root"),id:_,ref:t,"data-has-spoiler":F||void 0,...A,children:[F&&(0,o.jsx)(m.Anchor,{component:"button",type:"button",ref:P,onClick:()=>z(!N),"aria-expanded":N,"aria-controls":M,...T("control"),children:B}),(0,o.jsx)("div",{...T("content",{style:{maxHeight:N?L?(0,i.D)(L):void 0:(0,i.D)(C)}}),"data-reduce-motion":!0,role:"region",id:M,children:(0,o.jsx)("div",{ref:O,children:E})})]})});g.classes=v,g.displayName="@mantine/core/Spoiler"},98631:(e,t,r)=>{"use strict";r.d(t,{RadioIndicator:()=>y});var o=r(95155);r(12115);var n=r(56204),a=r(68918),l=r(98271),i=r(71180),s=r(89200),c=r(98840),u=r(43664),d=r(53791),p=r(69604),m=r(36960),v=r(54430),f=r(57326),h={indicator:"m_717d7ff6",icon:"m_3e4da632","indicator--outline":"m_2980836c"};let g={icon:f.RadioIcon},b=(0,a.createVarsResolver)((e,t)=>{let{radius:r,color:o,size:a,iconColor:u,variant:d,autoContrast:p}=t,m=(0,l.parseThemeColor)({color:o||e.primaryColor,theme:e}),v=m.isThemeColor&&void 0===m.shade?"var(--mantine-color-".concat(m.color,"-outline)"):m.color;return{indicator:{"--radio-size":(0,n.getSize)(a,"radio-size"),"--radio-radius":void 0===r?void 0:(0,n.getRadius)(r),"--radio-color":"outline"===d?v:(0,i.getThemeColor)(o,e),"--radio-icon-size":(0,n.getSize)(a,"radio-icon-size"),"--radio-icon-color":u?(0,i.getThemeColor)(u,e):(0,c.getAutoContrastValue)(p,e)?(0,s.getContrastColor)({color:o,theme:e,autoContrast:p}):void 0}}}),y=(0,m.factory)((e,t)=>{let r=(0,u.useProps)("RadioIndicator",g,e),{classNames:n,className:a,style:l,styles:i,unstyled:s,vars:c,icon:m,radius:f,color:y,iconColor:x,autoContrast:w,checked:C,mod:S,variant:k,disabled:E,...P}=r,j=(0,d.useStyles)({name:"RadioIndicator",classes:h,props:r,className:a,style:l,classNames:n,styles:i,unstyled:s,vars:c,varsResolver:b,rootSelector:"indicator"}),R=(0,v.useRadioCardContext)(),D="boolean"==typeof C?C:(null==R?void 0:R.checked)||!1;return(0,o.jsx)(p.Box,{ref:t,...j("indicator",{variant:k}),variant:k,mod:[{checked:D,disabled:E},S],...P,children:(0,o.jsx)(m,{...j("icon")})})});y.displayName="@mantine/core/RadioIndicator",y.classes=h},98703:(e,t,r)=>{"use strict";r.d(t,{Pagination:()=>v});var o=r(95155);r(12115);var n=r(43664),a=r(36960),l=r(70112),i=r(43702),s=r(13060),c=r(65402),u=r(5194),d=r(54380),p=r(68962);let m={withControls:!0,withPages:!0,siblings:1,boundaries:1,gap:8},v=(0,a.factory)((e,t)=>{let{withEdges:r,withControls:a,getControlProps:i,nextIcon:s,previousIcon:p,lastIcon:v,firstIcon:f,dotsIcon:h,total:g,gap:b,hideWithOnePage:y,withPages:x,...w}=(0,n.useProps)("Pagination",m,e);return g<=0||y&&1===g?null:(0,o.jsx)(d.PaginationRoot,{ref:t,total:g,...w,children:(0,o.jsxs)(l.Group,{gap:b,children:[r&&(0,o.jsx)(c.PaginationFirst,{icon:f,...null==i?void 0:i("first")}),a&&(0,o.jsx)(c.PaginationPrevious,{icon:p,...null==i?void 0:i("previous")}),x&&(0,o.jsx)(u.PaginationItems,{dotsIcon:h}),a&&(0,o.jsx)(c.PaginationNext,{icon:s,...null==i?void 0:i("next")}),r&&(0,o.jsx)(c.PaginationLast,{icon:v,...null==i?void 0:i("last")})]})})});v.classes=p.A,v.displayName="@mantine/core/Pagination",v.Root=d.PaginationRoot,v.Control=i.PaginationControl,v.Dots=s.PaginationDots,v.First=c.PaginationFirst,v.Last=c.PaginationLast,v.Next=c.PaginationNext,v.Previous=c.PaginationPrevious,v.Items=u.PaginationItems},98719:(e,t,r)=>{"use strict";r.d(t,{useToggle:()=>n});var o=r(12115);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[!1,!0],[[t],r]=(0,o.useReducer)((e,t)=>{let r=t instanceof Function?t(e[0]):t,o=Math.abs(e.indexOf(r));return e.slice(o).concat(e.slice(0,o))},e);return[t,r]}},98781:(e,t,r)=>{"use strict";r.d(t,{useIsFirstRender:()=>n});var o=r(12115);function n(){let e=(0,o.useRef)(!0);return!0===e.current?(e.current=!1,!0):e.current}},98840:(e,t,r)=>{"use strict";function o(e,t){return"boolean"==typeof e?e:t.autoContrast}r.d(t,{getAutoContrastValue:()=>o})},99313:(e,t,r)=>{"use strict";r.d(t,{ModalsProvider:()=>g});var o=r(95155),n=r(12115),a=r(63617),l=r(58750),i=r(74275),s=r(69604),c=r(70112),u=r(26903),d=r(64693);function p(e){let{id:t,cancelProps:r,confirmProps:n,labels:a={cancel:"",confirm:""},closeOnConfirm:l=!0,closeOnCancel:i=!0,groupProps:p,onCancel:m,onConfirm:v,children:f}=e,{cancel:h,confirm:g}=a,b=(0,d.useModals)();return(0,o.jsxs)(o.Fragment,{children:[f&&(0,o.jsx)(s.Box,{mb:"md",children:f}),(0,o.jsxs)(c.Group,{mt:f?0:"md",justify:"flex-end",...p,children:[(0,o.jsx)(u.Button,{variant:"default",...r,onClick:e=>{"function"==typeof(null==r?void 0:r.onClick)&&(null==r||r.onClick(e)),"function"==typeof m&&m(),i&&b.closeModal(t)},children:(null==r?void 0:r.children)||h}),(0,o.jsx)(u.Button,{...n,onClick:e=>{"function"==typeof(null==n?void 0:n.onClick)&&(null==n||n.onClick(e)),"function"==typeof v&&v(),l&&b.closeModal(t)},children:(null==n?void 0:n.children)||g})]})]})}var m=r(97975),v=r(91423);function f(e,t){var r,o,n,a;t&&"confirm"===e.type&&(null==(n=(a=e.props).onCancel)||n.call(a)),null==(r=(o=e.props).onClose)||r.call(o)}function h(e,t){switch(t.type){case"OPEN":return{current:t.modal,modals:[...e.modals,t.modal]};case"CLOSE":{let r=e.modals.find(e=>e.id===t.modalId);if(!r)return e;f(r,t.canceled);let o=e.modals.filter(e=>e.id!==t.modalId);return{current:o[o.length-1]||e.current,modals:o}}case"CLOSE_ALL":if(!e.modals.length)return e;return e.modals.concat().reverse().forEach(e=>{f(e,t.canceled)}),{current:e.current,modals:[]};case"UPDATE":{var r;let{modalId:o,newProps:n}=t,a=e.modals.map(e=>e.id!==o?e:"content"===e.type||"confirm"===e.type?{...e,props:{...e.props,...n}}:"context"===e.type?{...e,props:{...e.props,...n,innerProps:{...e.props.innerProps,...n.innerProps}}}:e),l=(null==(r=e.current)?void 0:r.id)===o&&a.find(e=>e.id===o)||e.current;return{...e,modals:a,current:l}}default:return e}}function g(e){let{children:t,modalProps:r,labels:s,modals:c}=e,[u,d]=(0,n.useReducer)(h,{modals:[],current:null}),f=(0,n.useRef)(u);f.current=u;let g=(0,n.useCallback)(e=>{d({type:"CLOSE_ALL",canceled:e})},[f,d]),b=(0,n.useCallback)(e=>{let{modalId:t,...r}=e,o=t||(0,i.randomId)();return d({type:"OPEN",modal:{id:o,type:"content",props:r}}),o},[d]),y=(0,n.useCallback)(e=>{let{modalId:t,...r}=e,o=t||(0,i.randomId)();return d({type:"OPEN",modal:{id:o,type:"confirm",props:r}}),o},[d]),x=(0,n.useCallback)((e,t)=>{let{modalId:r,...o}=t,n=r||(0,i.randomId)();return d({type:"OPEN",modal:{id:n,type:"context",props:o,ctx:e}}),n},[d]),w=(0,n.useCallback)((e,t)=>{d({type:"CLOSE",modalId:e,canceled:t})},[f,d]),C=(0,n.useCallback)(e=>{let{modalId:t,...r}=e;d({type:"UPDATE",modalId:t,newProps:r})},[d]),S=(0,n.useCallback)(e=>{let{modalId:t,...r}=e;d({type:"UPDATE",modalId:t,newProps:r})},[d]);(0,v.F)({openModal:b,openConfirmModal:y,openContextModal:e=>{let{modal:t,...r}=e;return x(t,r)},closeModal:w,closeContextModal:w,closeAllModals:g,updateModal:C,updateContextModal:S});let k={modalProps:r||{},modals:u.modals,openModal:b,openConfirmModal:y,openContextModal:x,closeModal:w,closeContextModal:w,closeAll:g,updateModal:C,updateContextModal:S},{modalProps:E,content:P}=(()=>{let e=f.current.current;switch(null==e?void 0:e.type){case"context":{let{innerProps:t,...r}=e.props,n=c[e.ctx];return{modalProps:r,content:(0,o.jsx)(n,{innerProps:t,context:k,id:e.id})}}case"confirm":{let{modalProps:t,confirmProps:r}=function(e){if(!e)return{confirmProps:{},modalProps:{}};let{id:t,children:r,onCancel:o,onConfirm:n,closeOnConfirm:a,closeOnCancel:l,cancelProps:i,confirmProps:s,groupProps:c,labels:u,...d}=e;return{confirmProps:{id:t,children:r,onCancel:o,onConfirm:n,closeOnConfirm:a,closeOnCancel:l,cancelProps:i,confirmProps:s,groupProps:c,labels:u},modalProps:{id:t,...d}}}(e.props);return{modalProps:t,content:(0,o.jsx)(p,{...r,id:e.id,labels:e.props.labels||s})}}case"content":{let{children:t,...r}=e.props;return{modalProps:r,content:t}}default:return{modalProps:{},content:null}}})();return(0,o.jsxs)(m.w.Provider,{value:k,children:[(0,o.jsx)(a.Modal,{zIndex:(0,l.getDefaultZIndex)("modal")+1,...r,...E,opened:u.modals.length>0,onClose:()=>{var e;return w(null==(e=u.current)?void 0:e.id)},children:P}),t]})}}}]);