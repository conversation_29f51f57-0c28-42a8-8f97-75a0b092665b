{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@mantine/core/styles.css"], "sourcesContent": [":root {\n  color-scheme: var(--mantine-color-scheme);\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\ninput,\nbutton,\ntextarea,\nselect {\n  font: inherit;\n}\n\nbutton,\nselect {\n  text-transform: none;\n}\n\nbody {\n  margin: 0;\n  font-family: var(--mantine-font-family);\n  font-size: var(--mantine-font-size-md);\n  line-height: var(--mantine-line-height);\n  background-color: var(--mantine-color-body);\n  color: var(--mantine-color-text);\n\n  -webkit-font-smoothing: var(--mantine-webkit-font-smoothing);\n  -moz-osx-font-smoothing: var(--mantine-moz-font-smoothing);\n}\n\n@media screen and (max-device-width: 31.25em) {\n\nbody {\n    -webkit-text-size-adjust: 100%\n}\n  }\n\n@media (prefers-reduced-motion: reduce) {\n    [data-respect-reduced-motion] [data-reduce-motion] {\n      transition: none;\n      animation: none;\n    }\n  }\n\n[data-mantine-color-scheme='light'] .mantine-light-hidden {\n    display: none;\n}\n\n[data-mantine-color-scheme='dark'] .mantine-dark-hidden {\n    display: none;\n}\n\n.mantine-focus-auto:focus-visible {\n    outline: 2px solid var(--mantine-primary-color-filled);\n    outline-offset: calc(0.125rem * var(--mantine-scale));\n  }\n\n.mantine-focus-always:focus {\n    outline: 2px solid var(--mantine-primary-color-filled);\n    outline-offset: calc(0.125rem * var(--mantine-scale));\n  }\n\n.mantine-focus-never:focus {\n    outline: none;\n  }\n\n.mantine-active:active {\n    transform: translateY(calc(0.0625rem * var(--mantine-scale)));\n  }\n\nfieldset:disabled .mantine-active:active {\n    transform: none;\n  }\n\n:where([dir=\"rtl\"]) .mantine-rotate-rtl {\n    transform: rotate(180deg);\n}\n\n/* stylelint-disable */\n/* This file is automatically generated, do not modify it directly. */\n:root {\n  --mantine-z-index-app: 100;\n  --mantine-z-index-modal: 200;\n  --mantine-z-index-popover: 300;\n  --mantine-z-index-overlay: 400;\n  --mantine-z-index-max: 9999;\n\n  --mantine-scale: 1;\n  --mantine-cursor-type: default;\n  --mantine-webkit-font-smoothing: antialiased;\n  --mantine-moz-font-smoothing: grayscale;\n  --mantine-color-white: #fff;\n  --mantine-color-black: #000;\n  --mantine-line-height: 1.55;\n  --mantine-font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial,\n    sans-serif, Apple Color Emoji, Segoe UI Emoji;\n  --mantine-font-family-monospace: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,\n    Liberation Mono, Courier New, monospace;\n  --mantine-font-family-headings: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica,\n    Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji;\n  --mantine-heading-font-weight: 700;\n  --mantine-heading-text-wrap: wrap;\n  --mantine-radius-default: calc(0.25rem * var(--mantine-scale));\n  --mantine-primary-color-filled: var(--mantine-color-blue-filled);\n  --mantine-primary-color-filled-hover: var(--mantine-color-blue-filled-hover);\n  --mantine-primary-color-light: var(--mantine-color-blue-light);\n  --mantine-primary-color-light-hover: var(--mantine-color-blue-light-hover);\n  --mantine-primary-color-light-color: var(--mantine-color-blue-light-color);\n  --mantine-breakpoint-xs: 36em;\n  --mantine-breakpoint-sm: 48em;\n  --mantine-breakpoint-md: 62em;\n  --mantine-breakpoint-lg: 75em;\n  --mantine-breakpoint-xl: 88em;\n  --mantine-spacing-xs: calc(0.625rem * var(--mantine-scale));\n  --mantine-spacing-sm: calc(0.75rem * var(--mantine-scale));\n  --mantine-spacing-md: calc(1rem * var(--mantine-scale));\n  --mantine-spacing-lg: calc(1.25rem * var(--mantine-scale));\n  --mantine-spacing-xl: calc(2rem * var(--mantine-scale));\n  --mantine-font-size-xs: calc(0.75rem * var(--mantine-scale));\n  --mantine-font-size-sm: calc(0.875rem * var(--mantine-scale));\n  --mantine-font-size-md: calc(1rem * var(--mantine-scale));\n  --mantine-font-size-lg: calc(1.125rem * var(--mantine-scale));\n  --mantine-font-size-xl: calc(1.25rem * var(--mantine-scale));\n  --mantine-line-height-xs: 1.4;\n  --mantine-line-height-sm: 1.45;\n  --mantine-line-height-md: 1.55;\n  --mantine-line-height-lg: 1.6;\n  --mantine-line-height-xl: 1.65;\n  --mantine-shadow-xs: 0 calc(0.0625rem * var(--mantine-scale))\n      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),\n    0 calc(0.0625rem * var(--mantine-scale)) calc(0.125rem * var(--mantine-scale))\n      rgba(0, 0, 0, 0.1);\n  --mantine-shadow-sm: 0 calc(0.0625rem * var(--mantine-scale))\n      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),\n    rgba(0, 0, 0, 0.05) 0 calc(0.625rem * var(--mantine-scale))\n      calc(0.9375rem * var(--mantine-scale)) calc(-0.3125rem * var(--mantine-scale)),\n    rgba(0, 0, 0, 0.04) 0 calc(0.4375rem * var(--mantine-scale))\n      calc(0.4375rem * var(--mantine-scale)) calc(-0.3125rem * var(--mantine-scale));\n  --mantine-shadow-md: 0 calc(0.0625rem * var(--mantine-scale))\n      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),\n    rgba(0, 0, 0, 0.05) 0 calc(1.25rem * var(--mantine-scale))\n      calc(1.5625rem * var(--mantine-scale)) calc(-0.3125rem * var(--mantine-scale)),\n    rgba(0, 0, 0, 0.04) 0 calc(0.625rem * var(--mantine-scale))\n      calc(0.625rem * var(--mantine-scale)) calc(-0.3125rem * var(--mantine-scale));\n  --mantine-shadow-lg: 0 calc(0.0625rem * var(--mantine-scale))\n      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),\n    rgba(0, 0, 0, 0.05) 0 calc(1.75rem * var(--mantine-scale))\n      calc(1.4375rem * var(--mantine-scale)) calc(-0.4375rem * var(--mantine-scale)),\n    rgba(0, 0, 0, 0.04) 0 calc(0.75rem * var(--mantine-scale)) calc(0.75rem * var(--mantine-scale))\n      calc(-0.4375rem * var(--mantine-scale));\n  --mantine-shadow-xl: 0 calc(0.0625rem * var(--mantine-scale))\n      calc(0.1875rem * var(--mantine-scale)) rgba(0, 0, 0, 0.05),\n    rgba(0, 0, 0, 0.05) 0 calc(2.25rem * var(--mantine-scale)) calc(1.75rem * var(--mantine-scale))\n      calc(-0.4375rem * var(--mantine-scale)),\n    rgba(0, 0, 0, 0.04) 0 calc(1.0625rem * var(--mantine-scale))\n      calc(1.0625rem * var(--mantine-scale)) calc(-0.4375rem * var(--mantine-scale));\n  --mantine-radius-xs: calc(0.125rem * var(--mantine-scale));\n  --mantine-radius-sm: calc(0.25rem * var(--mantine-scale));\n  --mantine-radius-md: calc(0.5rem * var(--mantine-scale));\n  --mantine-radius-lg: calc(1rem * var(--mantine-scale));\n  --mantine-radius-xl: calc(2rem * var(--mantine-scale));\n  --mantine-primary-color-0: var(--mantine-color-blue-0);\n  --mantine-primary-color-1: var(--mantine-color-blue-1);\n  --mantine-primary-color-2: var(--mantine-color-blue-2);\n  --mantine-primary-color-3: var(--mantine-color-blue-3);\n  --mantine-primary-color-4: var(--mantine-color-blue-4);\n  --mantine-primary-color-5: var(--mantine-color-blue-5);\n  --mantine-primary-color-6: var(--mantine-color-blue-6);\n  --mantine-primary-color-7: var(--mantine-color-blue-7);\n  --mantine-primary-color-8: var(--mantine-color-blue-8);\n  --mantine-primary-color-9: var(--mantine-color-blue-9);\n  --mantine-color-dark-0: #c9c9c9;\n  --mantine-color-dark-1: #b8b8b8;\n  --mantine-color-dark-2: #828282;\n  --mantine-color-dark-3: #696969;\n  --mantine-color-dark-4: #424242;\n  --mantine-color-dark-5: #3b3b3b;\n  --mantine-color-dark-6: #2e2e2e;\n  --mantine-color-dark-7: #242424;\n  --mantine-color-dark-8: #1f1f1f;\n  --mantine-color-dark-9: #141414;\n  --mantine-color-gray-0: #f8f9fa;\n  --mantine-color-gray-1: #f1f3f5;\n  --mantine-color-gray-2: #e9ecef;\n  --mantine-color-gray-3: #dee2e6;\n  --mantine-color-gray-4: #ced4da;\n  --mantine-color-gray-5: #adb5bd;\n  --mantine-color-gray-6: #868e96;\n  --mantine-color-gray-7: #495057;\n  --mantine-color-gray-8: #343a40;\n  --mantine-color-gray-9: #212529;\n  --mantine-color-red-0: #fff5f5;\n  --mantine-color-red-1: #ffe3e3;\n  --mantine-color-red-2: #ffc9c9;\n  --mantine-color-red-3: #ffa8a8;\n  --mantine-color-red-4: #ff8787;\n  --mantine-color-red-5: #ff6b6b;\n  --mantine-color-red-6: #fa5252;\n  --mantine-color-red-7: #f03e3e;\n  --mantine-color-red-8: #e03131;\n  --mantine-color-red-9: #c92a2a;\n  --mantine-color-pink-0: #fff0f6;\n  --mantine-color-pink-1: #ffdeeb;\n  --mantine-color-pink-2: #fcc2d7;\n  --mantine-color-pink-3: #faa2c1;\n  --mantine-color-pink-4: #f783ac;\n  --mantine-color-pink-5: #f06595;\n  --mantine-color-pink-6: #e64980;\n  --mantine-color-pink-7: #d6336c;\n  --mantine-color-pink-8: #c2255c;\n  --mantine-color-pink-9: #a61e4d;\n  --mantine-color-grape-0: #f8f0fc;\n  --mantine-color-grape-1: #f3d9fa;\n  --mantine-color-grape-2: #eebefa;\n  --mantine-color-grape-3: #e599f7;\n  --mantine-color-grape-4: #da77f2;\n  --mantine-color-grape-5: #cc5de8;\n  --mantine-color-grape-6: #be4bdb;\n  --mantine-color-grape-7: #ae3ec9;\n  --mantine-color-grape-8: #9c36b5;\n  --mantine-color-grape-9: #862e9c;\n  --mantine-color-violet-0: #f3f0ff;\n  --mantine-color-violet-1: #e5dbff;\n  --mantine-color-violet-2: #d0bfff;\n  --mantine-color-violet-3: #b197fc;\n  --mantine-color-violet-4: #9775fa;\n  --mantine-color-violet-5: #845ef7;\n  --mantine-color-violet-6: #7950f2;\n  --mantine-color-violet-7: #7048e8;\n  --mantine-color-violet-8: #6741d9;\n  --mantine-color-violet-9: #5f3dc4;\n  --mantine-color-indigo-0: #edf2ff;\n  --mantine-color-indigo-1: #dbe4ff;\n  --mantine-color-indigo-2: #bac8ff;\n  --mantine-color-indigo-3: #91a7ff;\n  --mantine-color-indigo-4: #748ffc;\n  --mantine-color-indigo-5: #5c7cfa;\n  --mantine-color-indigo-6: #4c6ef5;\n  --mantine-color-indigo-7: #4263eb;\n  --mantine-color-indigo-8: #3b5bdb;\n  --mantine-color-indigo-9: #364fc7;\n  --mantine-color-blue-0: #e7f5ff;\n  --mantine-color-blue-1: #d0ebff;\n  --mantine-color-blue-2: #a5d8ff;\n  --mantine-color-blue-3: #74c0fc;\n  --mantine-color-blue-4: #4dabf7;\n  --mantine-color-blue-5: #339af0;\n  --mantine-color-blue-6: #228be6;\n  --mantine-color-blue-7: #1c7ed6;\n  --mantine-color-blue-8: #1971c2;\n  --mantine-color-blue-9: #1864ab;\n  --mantine-color-cyan-0: #e3fafc;\n  --mantine-color-cyan-1: #c5f6fa;\n  --mantine-color-cyan-2: #99e9f2;\n  --mantine-color-cyan-3: #66d9e8;\n  --mantine-color-cyan-4: #3bc9db;\n  --mantine-color-cyan-5: #22b8cf;\n  --mantine-color-cyan-6: #15aabf;\n  --mantine-color-cyan-7: #1098ad;\n  --mantine-color-cyan-8: #0c8599;\n  --mantine-color-cyan-9: #0b7285;\n  --mantine-color-teal-0: #e6fcf5;\n  --mantine-color-teal-1: #c3fae8;\n  --mantine-color-teal-2: #96f2d7;\n  --mantine-color-teal-3: #63e6be;\n  --mantine-color-teal-4: #38d9a9;\n  --mantine-color-teal-5: #20c997;\n  --mantine-color-teal-6: #12b886;\n  --mantine-color-teal-7: #0ca678;\n  --mantine-color-teal-8: #099268;\n  --mantine-color-teal-9: #087f5b;\n  --mantine-color-green-0: #ebfbee;\n  --mantine-color-green-1: #d3f9d8;\n  --mantine-color-green-2: #b2f2bb;\n  --mantine-color-green-3: #8ce99a;\n  --mantine-color-green-4: #69db7c;\n  --mantine-color-green-5: #51cf66;\n  --mantine-color-green-6: #40c057;\n  --mantine-color-green-7: #37b24d;\n  --mantine-color-green-8: #2f9e44;\n  --mantine-color-green-9: #2b8a3e;\n  --mantine-color-lime-0: #f4fce3;\n  --mantine-color-lime-1: #e9fac8;\n  --mantine-color-lime-2: #d8f5a2;\n  --mantine-color-lime-3: #c0eb75;\n  --mantine-color-lime-4: #a9e34b;\n  --mantine-color-lime-5: #94d82d;\n  --mantine-color-lime-6: #82c91e;\n  --mantine-color-lime-7: #74b816;\n  --mantine-color-lime-8: #66a80f;\n  --mantine-color-lime-9: #5c940d;\n  --mantine-color-yellow-0: #fff9db;\n  --mantine-color-yellow-1: #fff3bf;\n  --mantine-color-yellow-2: #ffec99;\n  --mantine-color-yellow-3: #ffe066;\n  --mantine-color-yellow-4: #ffd43b;\n  --mantine-color-yellow-5: #fcc419;\n  --mantine-color-yellow-6: #fab005;\n  --mantine-color-yellow-7: #f59f00;\n  --mantine-color-yellow-8: #f08c00;\n  --mantine-color-yellow-9: #e67700;\n  --mantine-color-orange-0: #fff4e6;\n  --mantine-color-orange-1: #ffe8cc;\n  --mantine-color-orange-2: #ffd8a8;\n  --mantine-color-orange-3: #ffc078;\n  --mantine-color-orange-4: #ffa94d;\n  --mantine-color-orange-5: #ff922b;\n  --mantine-color-orange-6: #fd7e14;\n  --mantine-color-orange-7: #f76707;\n  --mantine-color-orange-8: #e8590c;\n  --mantine-color-orange-9: #d9480f;\n  --mantine-h1-font-size: calc(2.125rem * var(--mantine-scale));\n  --mantine-h1-line-height: 1.3;\n  --mantine-h1-font-weight: 700;\n  --mantine-h2-font-size: calc(1.625rem * var(--mantine-scale));\n  --mantine-h2-line-height: 1.35;\n  --mantine-h2-font-weight: 700;\n  --mantine-h3-font-size: calc(1.375rem * var(--mantine-scale));\n  --mantine-h3-line-height: 1.4;\n  --mantine-h3-font-weight: 700;\n  --mantine-h4-font-size: calc(1.125rem * var(--mantine-scale));\n  --mantine-h4-line-height: 1.45;\n  --mantine-h4-font-weight: 700;\n  --mantine-h5-font-size: calc(1rem * var(--mantine-scale));\n  --mantine-h5-line-height: 1.5;\n  --mantine-h5-font-weight: 700;\n  --mantine-h6-font-size: calc(0.875rem * var(--mantine-scale));\n  --mantine-h6-line-height: 1.5;\n  --mantine-h6-font-weight: 700;\n}\n:root[data-mantine-color-scheme='dark'] {\n  --mantine-color-scheme: dark;\n  --mantine-primary-color-contrast: var(--mantine-color-white);\n  --mantine-color-bright: var(--mantine-color-white);\n  --mantine-color-text: var(--mantine-color-dark-0);\n  --mantine-color-body: var(--mantine-color-dark-7);\n  --mantine-color-error: var(--mantine-color-red-8);\n  --mantine-color-placeholder: var(--mantine-color-dark-3);\n  --mantine-color-anchor: var(--mantine-color-blue-4);\n  --mantine-color-default: var(--mantine-color-dark-6);\n  --mantine-color-default-hover: var(--mantine-color-dark-5);\n  --mantine-color-default-color: var(--mantine-color-white);\n  --mantine-color-default-border: var(--mantine-color-dark-4);\n  --mantine-color-dimmed: var(--mantine-color-dark-2);\n  --mantine-color-disabled: var(--mantine-color-dark-6);\n  --mantine-color-disabled-color: var(--mantine-color-dark-3);\n  --mantine-color-disabled-border: var(--mantine-color-gray-6);\n  --mantine-color-dark-text: var(--mantine-color-dark-4);\n  --mantine-color-dark-filled: var(--mantine-color-dark-8);\n  --mantine-color-dark-filled-hover: var(--mantine-color-dark-9);\n  --mantine-color-dark-light: rgba(46, 46, 46, 0.15);\n  --mantine-color-dark-light-hover: rgba(46, 46, 46, 0.2);\n  --mantine-color-dark-light-color: var(--mantine-color-dark-3);\n  --mantine-color-dark-outline: var(--mantine-color-dark-4);\n  --mantine-color-dark-outline-hover: rgba(66, 66, 66, 0.05);\n  --mantine-color-gray-text: var(--mantine-color-gray-4);\n  --mantine-color-gray-filled: var(--mantine-color-gray-8);\n  --mantine-color-gray-filled-hover: var(--mantine-color-gray-9);\n  --mantine-color-gray-light: rgba(134, 142, 150, 0.15);\n  --mantine-color-gray-light-hover: rgba(134, 142, 150, 0.2);\n  --mantine-color-gray-light-color: var(--mantine-color-gray-3);\n  --mantine-color-gray-outline: var(--mantine-color-gray-4);\n  --mantine-color-gray-outline-hover: rgba(206, 212, 218, 0.05);\n  --mantine-color-red-text: var(--mantine-color-red-4);\n  --mantine-color-red-filled: var(--mantine-color-red-8);\n  --mantine-color-red-filled-hover: var(--mantine-color-red-9);\n  --mantine-color-red-light: rgba(250, 82, 82, 0.15);\n  --mantine-color-red-light-hover: rgba(250, 82, 82, 0.2);\n  --mantine-color-red-light-color: var(--mantine-color-red-3);\n  --mantine-color-red-outline: var(--mantine-color-red-4);\n  --mantine-color-red-outline-hover: rgba(255, 135, 135, 0.05);\n  --mantine-color-pink-text: var(--mantine-color-pink-4);\n  --mantine-color-pink-filled: var(--mantine-color-pink-8);\n  --mantine-color-pink-filled-hover: var(--mantine-color-pink-9);\n  --mantine-color-pink-light: rgba(230, 73, 128, 0.15);\n  --mantine-color-pink-light-hover: rgba(230, 73, 128, 0.2);\n  --mantine-color-pink-light-color: var(--mantine-color-pink-3);\n  --mantine-color-pink-outline: var(--mantine-color-pink-4);\n  --mantine-color-pink-outline-hover: rgba(247, 131, 172, 0.05);\n  --mantine-color-grape-text: var(--mantine-color-grape-4);\n  --mantine-color-grape-filled: var(--mantine-color-grape-8);\n  --mantine-color-grape-filled-hover: var(--mantine-color-grape-9);\n  --mantine-color-grape-light: rgba(190, 75, 219, 0.15);\n  --mantine-color-grape-light-hover: rgba(190, 75, 219, 0.2);\n  --mantine-color-grape-light-color: var(--mantine-color-grape-3);\n  --mantine-color-grape-outline: var(--mantine-color-grape-4);\n  --mantine-color-grape-outline-hover: rgba(218, 119, 242, 0.05);\n  --mantine-color-violet-text: var(--mantine-color-violet-4);\n  --mantine-color-violet-filled: var(--mantine-color-violet-8);\n  --mantine-color-violet-filled-hover: var(--mantine-color-violet-9);\n  --mantine-color-violet-light: rgba(121, 80, 242, 0.15);\n  --mantine-color-violet-light-hover: rgba(121, 80, 242, 0.2);\n  --mantine-color-violet-light-color: var(--mantine-color-violet-3);\n  --mantine-color-violet-outline: var(--mantine-color-violet-4);\n  --mantine-color-violet-outline-hover: rgba(151, 117, 250, 0.05);\n  --mantine-color-indigo-text: var(--mantine-color-indigo-4);\n  --mantine-color-indigo-filled: var(--mantine-color-indigo-8);\n  --mantine-color-indigo-filled-hover: var(--mantine-color-indigo-9);\n  --mantine-color-indigo-light: rgba(76, 110, 245, 0.15);\n  --mantine-color-indigo-light-hover: rgba(76, 110, 245, 0.2);\n  --mantine-color-indigo-light-color: var(--mantine-color-indigo-3);\n  --mantine-color-indigo-outline: var(--mantine-color-indigo-4);\n  --mantine-color-indigo-outline-hover: rgba(116, 143, 252, 0.05);\n  --mantine-color-blue-text: var(--mantine-color-blue-4);\n  --mantine-color-blue-filled: var(--mantine-color-blue-8);\n  --mantine-color-blue-filled-hover: var(--mantine-color-blue-9);\n  --mantine-color-blue-light: rgba(34, 139, 230, 0.15);\n  --mantine-color-blue-light-hover: rgba(34, 139, 230, 0.2);\n  --mantine-color-blue-light-color: var(--mantine-color-blue-3);\n  --mantine-color-blue-outline: var(--mantine-color-blue-4);\n  --mantine-color-blue-outline-hover: rgba(77, 171, 247, 0.05);\n  --mantine-color-cyan-text: var(--mantine-color-cyan-4);\n  --mantine-color-cyan-filled: var(--mantine-color-cyan-8);\n  --mantine-color-cyan-filled-hover: var(--mantine-color-cyan-9);\n  --mantine-color-cyan-light: rgba(21, 170, 191, 0.15);\n  --mantine-color-cyan-light-hover: rgba(21, 170, 191, 0.2);\n  --mantine-color-cyan-light-color: var(--mantine-color-cyan-3);\n  --mantine-color-cyan-outline: var(--mantine-color-cyan-4);\n  --mantine-color-cyan-outline-hover: rgba(59, 201, 219, 0.05);\n  --mantine-color-teal-text: var(--mantine-color-teal-4);\n  --mantine-color-teal-filled: var(--mantine-color-teal-8);\n  --mantine-color-teal-filled-hover: var(--mantine-color-teal-9);\n  --mantine-color-teal-light: rgba(18, 184, 134, 0.15);\n  --mantine-color-teal-light-hover: rgba(18, 184, 134, 0.2);\n  --mantine-color-teal-light-color: var(--mantine-color-teal-3);\n  --mantine-color-teal-outline: var(--mantine-color-teal-4);\n  --mantine-color-teal-outline-hover: rgba(56, 217, 169, 0.05);\n  --mantine-color-green-text: var(--mantine-color-green-4);\n  --mantine-color-green-filled: var(--mantine-color-green-8);\n  --mantine-color-green-filled-hover: var(--mantine-color-green-9);\n  --mantine-color-green-light: rgba(64, 192, 87, 0.15);\n  --mantine-color-green-light-hover: rgba(64, 192, 87, 0.2);\n  --mantine-color-green-light-color: var(--mantine-color-green-3);\n  --mantine-color-green-outline: var(--mantine-color-green-4);\n  --mantine-color-green-outline-hover: rgba(105, 219, 124, 0.05);\n  --mantine-color-lime-text: var(--mantine-color-lime-4);\n  --mantine-color-lime-filled: var(--mantine-color-lime-8);\n  --mantine-color-lime-filled-hover: var(--mantine-color-lime-9);\n  --mantine-color-lime-light: rgba(130, 201, 30, 0.15);\n  --mantine-color-lime-light-hover: rgba(130, 201, 30, 0.2);\n  --mantine-color-lime-light-color: var(--mantine-color-lime-3);\n  --mantine-color-lime-outline: var(--mantine-color-lime-4);\n  --mantine-color-lime-outline-hover: rgba(169, 227, 75, 0.05);\n  --mantine-color-yellow-text: var(--mantine-color-yellow-4);\n  --mantine-color-yellow-filled: var(--mantine-color-yellow-8);\n  --mantine-color-yellow-filled-hover: var(--mantine-color-yellow-9);\n  --mantine-color-yellow-light: rgba(250, 176, 5, 0.15);\n  --mantine-color-yellow-light-hover: rgba(250, 176, 5, 0.2);\n  --mantine-color-yellow-light-color: var(--mantine-color-yellow-3);\n  --mantine-color-yellow-outline: var(--mantine-color-yellow-4);\n  --mantine-color-yellow-outline-hover: rgba(255, 212, 59, 0.05);\n  --mantine-color-orange-text: var(--mantine-color-orange-4);\n  --mantine-color-orange-filled: var(--mantine-color-orange-8);\n  --mantine-color-orange-filled-hover: var(--mantine-color-orange-9);\n  --mantine-color-orange-light: rgba(253, 126, 20, 0.15);\n  --mantine-color-orange-light-hover: rgba(253, 126, 20, 0.2);\n  --mantine-color-orange-light-color: var(--mantine-color-orange-3);\n  --mantine-color-orange-outline: var(--mantine-color-orange-4);\n  --mantine-color-orange-outline-hover: rgba(255, 169, 77, 0.05);\n}\n\n:root[data-mantine-color-scheme='light'] {\n  --mantine-color-scheme: light;\n  --mantine-primary-color-contrast: var(--mantine-color-white);\n  --mantine-color-bright: var(--mantine-color-black);\n  --mantine-color-text: #000;\n  --mantine-color-body: #fff;\n  --mantine-color-error: var(--mantine-color-red-6);\n  --mantine-color-placeholder: var(--mantine-color-gray-5);\n  --mantine-color-anchor: var(--mantine-color-blue-6);\n  --mantine-color-default: var(--mantine-color-white);\n  --mantine-color-default-hover: var(--mantine-color-gray-0);\n  --mantine-color-default-color: var(--mantine-color-black);\n  --mantine-color-default-border: var(--mantine-color-gray-4);\n  --mantine-color-dimmed: var(--mantine-color-gray-6);\n  --mantine-color-disabled: var(--mantine-color-gray-2);\n  --mantine-color-disabled-color: var(--mantine-color-gray-5);\n  --mantine-color-disabled-border: var(--mantine-color-gray-3);\n  --mantine-color-dark-text: var(--mantine-color-dark-filled);\n  --mantine-color-dark-filled: var(--mantine-color-dark-6);\n  --mantine-color-dark-filled-hover: var(--mantine-color-dark-7);\n  --mantine-color-dark-light: rgba(46, 46, 46, 0.1);\n  --mantine-color-dark-light-hover: rgba(46, 46, 46, 0.12);\n  --mantine-color-dark-light-color: var(--mantine-color-dark-6);\n  --mantine-color-dark-outline: var(--mantine-color-dark-6);\n  --mantine-color-dark-outline-hover: rgba(46, 46, 46, 0.05);\n  --mantine-color-gray-text: var(--mantine-color-gray-filled);\n  --mantine-color-gray-filled: var(--mantine-color-gray-6);\n  --mantine-color-gray-filled-hover: var(--mantine-color-gray-7);\n  --mantine-color-gray-light: rgba(134, 142, 150, 0.1);\n  --mantine-color-gray-light-hover: rgba(134, 142, 150, 0.12);\n  --mantine-color-gray-light-color: var(--mantine-color-gray-6);\n  --mantine-color-gray-outline: var(--mantine-color-gray-6);\n  --mantine-color-gray-outline-hover: rgba(134, 142, 150, 0.05);\n  --mantine-color-red-text: var(--mantine-color-red-filled);\n  --mantine-color-red-filled: var(--mantine-color-red-6);\n  --mantine-color-red-filled-hover: var(--mantine-color-red-7);\n  --mantine-color-red-light: rgba(250, 82, 82, 0.1);\n  --mantine-color-red-light-hover: rgba(250, 82, 82, 0.12);\n  --mantine-color-red-light-color: var(--mantine-color-red-6);\n  --mantine-color-red-outline: var(--mantine-color-red-6);\n  --mantine-color-red-outline-hover: rgba(250, 82, 82, 0.05);\n  --mantine-color-pink-text: var(--mantine-color-pink-filled);\n  --mantine-color-pink-filled: var(--mantine-color-pink-6);\n  --mantine-color-pink-filled-hover: var(--mantine-color-pink-7);\n  --mantine-color-pink-light: rgba(230, 73, 128, 0.1);\n  --mantine-color-pink-light-hover: rgba(230, 73, 128, 0.12);\n  --mantine-color-pink-light-color: var(--mantine-color-pink-6);\n  --mantine-color-pink-outline: var(--mantine-color-pink-6);\n  --mantine-color-pink-outline-hover: rgba(230, 73, 128, 0.05);\n  --mantine-color-grape-text: var(--mantine-color-grape-filled);\n  --mantine-color-grape-filled: var(--mantine-color-grape-6);\n  --mantine-color-grape-filled-hover: var(--mantine-color-grape-7);\n  --mantine-color-grape-light: rgba(190, 75, 219, 0.1);\n  --mantine-color-grape-light-hover: rgba(190, 75, 219, 0.12);\n  --mantine-color-grape-light-color: var(--mantine-color-grape-6);\n  --mantine-color-grape-outline: var(--mantine-color-grape-6);\n  --mantine-color-grape-outline-hover: rgba(190, 75, 219, 0.05);\n  --mantine-color-violet-text: var(--mantine-color-violet-filled);\n  --mantine-color-violet-filled: var(--mantine-color-violet-6);\n  --mantine-color-violet-filled-hover: var(--mantine-color-violet-7);\n  --mantine-color-violet-light: rgba(121, 80, 242, 0.1);\n  --mantine-color-violet-light-hover: rgba(121, 80, 242, 0.12);\n  --mantine-color-violet-light-color: var(--mantine-color-violet-6);\n  --mantine-color-violet-outline: var(--mantine-color-violet-6);\n  --mantine-color-violet-outline-hover: rgba(121, 80, 242, 0.05);\n  --mantine-color-indigo-text: var(--mantine-color-indigo-filled);\n  --mantine-color-indigo-filled: var(--mantine-color-indigo-6);\n  --mantine-color-indigo-filled-hover: var(--mantine-color-indigo-7);\n  --mantine-color-indigo-light: rgba(76, 110, 245, 0.1);\n  --mantine-color-indigo-light-hover: rgba(76, 110, 245, 0.12);\n  --mantine-color-indigo-light-color: var(--mantine-color-indigo-6);\n  --mantine-color-indigo-outline: var(--mantine-color-indigo-6);\n  --mantine-color-indigo-outline-hover: rgba(76, 110, 245, 0.05);\n  --mantine-color-blue-text: var(--mantine-color-blue-filled);\n  --mantine-color-blue-filled: var(--mantine-color-blue-6);\n  --mantine-color-blue-filled-hover: var(--mantine-color-blue-7);\n  --mantine-color-blue-light: rgba(34, 139, 230, 0.1);\n  --mantine-color-blue-light-hover: rgba(34, 139, 230, 0.12);\n  --mantine-color-blue-light-color: var(--mantine-color-blue-6);\n  --mantine-color-blue-outline: var(--mantine-color-blue-6);\n  --mantine-color-blue-outline-hover: rgba(34, 139, 230, 0.05);\n  --mantine-color-cyan-text: var(--mantine-color-cyan-filled);\n  --mantine-color-cyan-filled: var(--mantine-color-cyan-6);\n  --mantine-color-cyan-filled-hover: var(--mantine-color-cyan-7);\n  --mantine-color-cyan-light: rgba(21, 170, 191, 0.1);\n  --mantine-color-cyan-light-hover: rgba(21, 170, 191, 0.12);\n  --mantine-color-cyan-light-color: var(--mantine-color-cyan-6);\n  --mantine-color-cyan-outline: var(--mantine-color-cyan-6);\n  --mantine-color-cyan-outline-hover: rgba(21, 170, 191, 0.05);\n  --mantine-color-teal-text: var(--mantine-color-teal-filled);\n  --mantine-color-teal-filled: var(--mantine-color-teal-6);\n  --mantine-color-teal-filled-hover: var(--mantine-color-teal-7);\n  --mantine-color-teal-light: rgba(18, 184, 134, 0.1);\n  --mantine-color-teal-light-hover: rgba(18, 184, 134, 0.12);\n  --mantine-color-teal-light-color: var(--mantine-color-teal-6);\n  --mantine-color-teal-outline: var(--mantine-color-teal-6);\n  --mantine-color-teal-outline-hover: rgba(18, 184, 134, 0.05);\n  --mantine-color-green-text: var(--mantine-color-green-filled);\n  --mantine-color-green-filled: var(--mantine-color-green-6);\n  --mantine-color-green-filled-hover: var(--mantine-color-green-7);\n  --mantine-color-green-light: rgba(64, 192, 87, 0.1);\n  --mantine-color-green-light-hover: rgba(64, 192, 87, 0.12);\n  --mantine-color-green-light-color: var(--mantine-color-green-6);\n  --mantine-color-green-outline: var(--mantine-color-green-6);\n  --mantine-color-green-outline-hover: rgba(64, 192, 87, 0.05);\n  --mantine-color-lime-text: var(--mantine-color-lime-filled);\n  --mantine-color-lime-filled: var(--mantine-color-lime-6);\n  --mantine-color-lime-filled-hover: var(--mantine-color-lime-7);\n  --mantine-color-lime-light: rgba(130, 201, 30, 0.1);\n  --mantine-color-lime-light-hover: rgba(130, 201, 30, 0.12);\n  --mantine-color-lime-light-color: var(--mantine-color-lime-6);\n  --mantine-color-lime-outline: var(--mantine-color-lime-6);\n  --mantine-color-lime-outline-hover: rgba(130, 201, 30, 0.05);\n  --mantine-color-yellow-text: var(--mantine-color-yellow-filled);\n  --mantine-color-yellow-filled: var(--mantine-color-yellow-6);\n  --mantine-color-yellow-filled-hover: var(--mantine-color-yellow-7);\n  --mantine-color-yellow-light: rgba(250, 176, 5, 0.1);\n  --mantine-color-yellow-light-hover: rgba(250, 176, 5, 0.12);\n  --mantine-color-yellow-light-color: var(--mantine-color-yellow-6);\n  --mantine-color-yellow-outline: var(--mantine-color-yellow-6);\n  --mantine-color-yellow-outline-hover: rgba(250, 176, 5, 0.05);\n  --mantine-color-orange-text: var(--mantine-color-orange-filled);\n  --mantine-color-orange-filled: var(--mantine-color-orange-6);\n  --mantine-color-orange-filled-hover: var(--mantine-color-orange-7);\n  --mantine-color-orange-light: rgba(253, 126, 20, 0.1);\n  --mantine-color-orange-light-hover: rgba(253, 126, 20, 0.12);\n  --mantine-color-orange-light-color: var(--mantine-color-orange-6);\n  --mantine-color-orange-outline: var(--mantine-color-orange-6);\n  --mantine-color-orange-outline-hover: rgba(253, 126, 20, 0.05);\n}\n\n.m_d57069b5 {\n  --scrollarea-scrollbar-size: calc(0.75rem * var(--mantine-scale));\n\n  position: relative;\n  overflow: hidden;\n}\n\n.m_c0783ff9 {\n  scrollbar-width: none;\n  overscroll-behavior: var(--scrollarea-over-scroll-behavior);\n  -ms-overflow-style: none;\n  -webkit-overflow-scrolling: touch;\n  width: 100%;\n  height: 100%;\n}\n\n.m_c0783ff9::-webkit-scrollbar {\n    display: none;\n  }\n\n.m_c0783ff9:where([data-scrollbars='xy'], [data-scrollbars='y']):where(\n        [data-offset-scrollbars='xy'],\n        [data-offset-scrollbars='y'],\n        [data-offset-scrollbars='present']\n      ):where([data-vertical-hidden]) {\n        padding-inline-end: 0;\n        padding-inline-start: 0;\n      }\n\n.m_c0783ff9:where([data-scrollbars='xy'], [data-scrollbars='y']):where(\n        [data-offset-scrollbars='xy'],\n        [data-offset-scrollbars='y'],\n        [data-offset-scrollbars='present']\n      ):not([data-vertical-hidden]) {\n        padding-inline-end: var(--scrollarea-scrollbar-size);\n        padding-inline-start: unset;\n      }\n\n.m_c0783ff9:where([data-scrollbars='xy'], [data-scrollbars='x']):where(\n        [data-offset-scrollbars='xy'],\n        [data-offset-scrollbars='x'],\n        [data-offset-scrollbars='present']\n      ):where([data-horizontal-hidden]) {\n        padding-bottom: 0;\n      }\n\n.m_c0783ff9:where([data-scrollbars='xy'], [data-scrollbars='x']):where(\n        [data-offset-scrollbars='xy'],\n        [data-offset-scrollbars='x'],\n        [data-offset-scrollbars='present']\n      ):not([data-horizontal-hidden]) {\n        padding-bottom: var(--scrollarea-scrollbar-size);\n      }\n\n.m_f8f631dd {\n  min-width: 100%;\n  display: table;\n}\n\n.m_c44ba933 {\n  user-select: none;\n  touch-action: none;\n  box-sizing: border-box;\n  transition:\n    background-color 150ms ease,\n    opacity 150ms ease;\n\n  padding: calc(var(--scrollarea-scrollbar-size) / 5);\n  display: flex;\n  background-color: transparent;\n  flex-direction: row;\n}\n\n@media (hover: hover) {\n    :where([data-mantine-color-scheme='light']) .m_c44ba933:hover {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n      :where([data-mantine-color-scheme='light']) .m_c44ba933:hover > .m_d8b5e363 {\n        background-color: rgba(0, 0, 0, 0.5);\n      }\n\n    :where([data-mantine-color-scheme='dark']) .m_c44ba933:hover {\n      background-color: var(--mantine-color-dark-8);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_c44ba933:hover > .m_d8b5e363 {\n        background-color: rgba(255, 255, 255, 0.5);\n      }\n}\n\n@media (hover: none) {\n    :where([data-mantine-color-scheme='light']) .m_c44ba933:active {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n      :where([data-mantine-color-scheme='light']) .m_c44ba933:active > .m_d8b5e363 {\n        background-color: rgba(0, 0, 0, 0.5);\n      }\n\n    :where([data-mantine-color-scheme='dark']) .m_c44ba933:active {\n      background-color: var(--mantine-color-dark-8);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_c44ba933:active > .m_d8b5e363 {\n        background-color: rgba(255, 255, 255, 0.5);\n      }\n}\n\n.m_c44ba933:where([data-hidden], [data-state='hidden']) {\n    display: none;\n  }\n\n.m_c44ba933:where([data-orientation='vertical']) {\n    width: var(--scrollarea-scrollbar-size);\n    top: 0;\n    bottom: var(--sa-corner-width);\n    inset-inline-end: 0;\n  }\n\n.m_c44ba933:where([data-orientation='horizontal']) {\n    height: var(--scrollarea-scrollbar-size);\n    flex-direction: column;\n    bottom: 0;\n    inset-inline-start: 0;\n    inset-inline-end: var(--sa-corner-width);\n  }\n\n.m_d8b5e363 {\n  flex: 1;\n  border-radius: var(--scrollarea-scrollbar-size);\n  position: relative;\n  transition: background-color 150ms ease;\n  overflow: hidden;\n  opacity: var(--thumb-opacity);\n}\n\n.m_d8b5e363::before {\n    content: '';\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    width: 100%;\n    height: 100%;\n    min-width: calc(2.75rem * var(--mantine-scale));\n    min-height: calc(2.75rem * var(--mantine-scale));\n  }\n\n:where([data-mantine-color-scheme='light']) .m_d8b5e363 {\n    background-color: rgba(0, 0, 0, 0.4);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_d8b5e363 {\n    background-color: rgba(255, 255, 255, 0.4);\n}\n\n.m_21657268 {\n  position: absolute;\n  opacity: 0;\n  transition: opacity 150ms ease;\n  display: block;\n  inset-inline-end: 0;\n  bottom: 0;\n}\n\n:where([data-mantine-color-scheme='light']) .m_21657268 {\n    background-color: var(--mantine-color-gray-0);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_21657268 {\n    background-color: var(--mantine-color-dark-8);\n}\n\n.m_21657268:where([data-hovered]) {\n    opacity: 1;\n  }\n\n.m_21657268:where([data-hidden]) {\n    display: none;\n  }\n\n.m_b1336c6 {\n  min-width: 100%;\n}\n\n.m_87cf2631 {\n  background-color: transparent;\n  cursor: pointer;\n  border: 0;\n  padding: 0;\n  appearance: none;\n  font-size: var(--mantine-font-size-md);\n  text-align: left;\n  text-decoration: none;\n  color: inherit;\n  touch-action: manipulation;\n  -webkit-tap-highlight-color: transparent;\n}\n\n  :where([dir=\"rtl\"]) .m_87cf2631 {\n    text-align: right;\n}\n\n.m_515a97f8 {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: calc(0.0625rem * var(--mantine-scale));\n  width: calc(0.0625rem * var(--mantine-scale));\n  margin: calc(-0.0625rem * var(--mantine-scale));\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  white-space: nowrap;\n}\n\n.m_1b7284a3 {\n  --paper-radius: var(--mantine-radius-default);\n\n  outline: 0;\n  -webkit-tap-highlight-color: transparent;\n  display: block;\n  touch-action: manipulation;\n  text-decoration: none;\n  border-radius: var(--paper-radius);\n  box-shadow: var(--paper-shadow);\n  background-color: var(--mantine-color-body);\n}\n\n  [data-mantine-color-scheme='light'] .m_1b7284a3 {\n    --paper-border-color: var(--mantine-color-gray-3);\n}\n\n  [data-mantine-color-scheme='dark'] .m_1b7284a3 {\n    --paper-border-color: var(--mantine-color-dark-4);\n}\n\n  .m_1b7284a3:where([data-with-border]) {\n    border: calc(0.0625rem * var(--mantine-scale)) solid var(--paper-border-color);\n  }\n\n.m_9814e45f {\n  inset: 0;\n  position: absolute;\n  background: var(--overlay-bg, rgba(0, 0, 0, 0.6));\n  -webkit-backdrop-filter: var(--overlay-filter);\n  backdrop-filter: var(--overlay-filter);\n  border-radius: var(--overlay-radius, 0);\n  z-index: var(--overlay-z-index);\n}\n\n  .m_9814e45f:where([data-fixed]) {\n    position: fixed;\n  }\n\n  .m_9814e45f:where([data-center]) {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n\n.m_38a85659 {\n  position: absolute;\n  border: 1px solid var(--popover-border-color);\n  padding: var(--mantine-spacing-sm) var(--mantine-spacing-md);\n  box-shadow: var(--popover-shadow, none);\n  border-radius: var(--popover-radius, var(--mantine-radius-default));\n}\n\n  .m_38a85659:where([data-fixed]) {\n    position: fixed;\n  }\n\n  .m_38a85659:focus {\n    outline: none;\n  }\n\n  :where([data-mantine-color-scheme='light']) .m_38a85659 {\n    --popover-border-color: var(--mantine-color-gray-2);\n    background-color: var(--mantine-color-white);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_38a85659 {\n    --popover-border-color: var(--mantine-color-dark-4);\n    background-color: var(--mantine-color-dark-6);\n}\n\n.m_a31dc6c1 {\n  background-color: inherit;\n  border: 1px solid var(--popover-border-color);\n  z-index: 1;\n}\n\n.m_3d7bc908 {\n  position: fixed;\n  inset: 0;\n}\n\n.m_5ae2e3c {\n  --loader-size-xs: calc(1.125rem * var(--mantine-scale));\n  --loader-size-sm: calc(1.375rem * var(--mantine-scale));\n  --loader-size-md: calc(2.25rem * var(--mantine-scale));\n  --loader-size-lg: calc(2.75rem * var(--mantine-scale));\n  --loader-size-xl: calc(3.625rem * var(--mantine-scale));\n  --loader-size: var(--loader-size-md);\n  --loader-color: var(--mantine-primary-color-filled);\n}\n\n/* ----- Bars loader ----- */\n@keyframes m_5d2b3b9d {\n  0% {\n    transform: scale(0.6);\n    opacity: 0;\n  }\n\n  50%,\n  100% {\n    transform: scale(1);\n  }\n}\n\n.m_7a2bd4cd {\n  position: relative;\n  width: var(--loader-size);\n  height: var(--loader-size);\n  display: flex;\n  gap: calc(var(--loader-size) / 5);\n}\n\n.m_870bb79 {\n  flex: 1;\n  background: var(--loader-color);\n  animation: m_5d2b3b9d 1.2s cubic-bezier(0, 0.5, 0.5, 1) infinite;\n  border-radius: calc(0.125rem * var(--mantine-scale));\n}\n\n.m_870bb79:nth-of-type(1) {\n    animation-delay: -240ms;\n  }\n\n.m_870bb79:nth-of-type(2) {\n    animation-delay: -120ms;\n  }\n\n.m_870bb79:nth-of-type(3) {\n    animation-delay: 0;\n  }\n\n/* ----- Dots loader ----- */\n@keyframes m_aac34a1 {\n  0%,\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n\n  50% {\n    transform: scale(0.6);\n    opacity: 0.5;\n  }\n}\n\n.m_4e3f22d7 {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: calc(var(--loader-size) / 10);\n  position: relative;\n  width: var(--loader-size);\n  height: var(--loader-size);\n}\n\n.m_870c4af {\n  width: calc(var(--loader-size) / 3 - var(--loader-size) / 15);\n  height: calc(var(--loader-size) / 3 - var(--loader-size) / 15);\n  border-radius: 50%;\n  background: var(--loader-color);\n  animation: m_aac34a1 0.8s infinite linear;\n}\n\n.m_870c4af:nth-child(2) {\n    animation-delay: 0.4s;\n  }\n\n/* ----- Oval loader ----- */\n@keyframes m_f8e89c4b {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.m_b34414df {\n  display: inline-block;\n  width: var(--loader-size);\n  height: var(--loader-size);\n}\n\n.m_b34414df::after {\n    content: '';\n    display: block;\n    width: var(--loader-size);\n    height: var(--loader-size);\n    border-radius: calc(625rem * var(--mantine-scale));\n    border-width: calc(var(--loader-size) / 8);\n    border-style: solid;\n    border-color: var(--loader-color) var(--loader-color) var(--loader-color) transparent;\n    animation: m_f8e89c4b 1.2s linear infinite;\n  }\n\n.m_8d3f4000 {\n  --ai-size-xs: calc(1.125rem * var(--mantine-scale));\n  --ai-size-sm: calc(1.375rem * var(--mantine-scale));\n  --ai-size-md: calc(1.75rem * var(--mantine-scale));\n  --ai-size-lg: calc(2.125rem * var(--mantine-scale));\n  --ai-size-xl: calc(2.75rem * var(--mantine-scale));\n\n  --ai-size-input-xs: calc(1.875rem * var(--mantine-scale));\n  --ai-size-input-sm: calc(2.25rem * var(--mantine-scale));\n  --ai-size-input-md: calc(2.625rem * var(--mantine-scale));\n  --ai-size-input-lg: calc(3.125rem * var(--mantine-scale));\n  --ai-size-input-xl: calc(3.75rem * var(--mantine-scale));\n\n  --ai-size: var(--ai-size-md);\n  --ai-color: var(--mantine-color-white);\n\n  line-height: 1;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  user-select: none;\n  overflow: hidden;\n\n  width: var(--ai-size);\n  height: var(--ai-size);\n  min-width: var(--ai-size);\n  min-height: var(--ai-size);\n  border-radius: var(--ai-radius, var(--mantine-radius-default));\n  background: var(--ai-bg, var(--mantine-primary-color-filled));\n  color: var(--ai-color, var(--mantine-color-white));\n  border: var(--ai-bd, calc(0.0625rem * var(--mantine-scale)) solid transparent);\n  cursor: pointer;\n}\n\n  @media (hover: hover) {\n    .m_8d3f4000:hover:where(:not([data-loading], :disabled, [data-disabled])) {\n      background-color: var(--ai-hover, var(--mantine-primary-color-filled-hover));\n      color: var(--ai-hover-color, var(--ai-color));\n    }\n}\n\n  @media (hover: none) {\n    .m_8d3f4000:active:where(:not([data-loading], :disabled, [data-disabled])) {\n      background-color: var(--ai-hover, var(--mantine-primary-color-filled-hover));\n      color: var(--ai-hover-color, var(--ai-color));\n    }\n}\n\n  .m_8d3f4000[data-loading] {\n    cursor: not-allowed;\n  }\n\n  .m_8d3f4000[data-loading] .m_8d3afb97 {\n      opacity: 0;\n      transform: translateY(100%);\n    }\n\n  .m_8d3f4000:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {\n    cursor: not-allowed;\n    border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n    color: var(--mantine-color-disabled-color);\n    background-color: var(--mantine-color-disabled);\n  }\n\n  .m_8d3f4000:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])):active {\n      transform: none;\n    }\n\n.m_302b9fb1 {\n  inset: calc(-0.0625rem * var(--mantine-scale));\n  position: absolute;\n  border-radius: var(--ai-radius, var(--mantine-radius-default));\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n:where([data-mantine-color-scheme='light']) .m_302b9fb1 {\n    background-color: rgba(255, 255, 255, 0.15);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_302b9fb1 {\n    background-color: rgba(0, 0, 0, 0.15);\n}\n\n.m_1a0f1b21 {\n  --ai-border-width: calc(0.0625rem * var(--mantine-scale));\n  display: flex;\n}\n\n.m_1a0f1b21 :where(*):focus {\n      position: relative;\n      z-index: 1;\n    }\n\n.m_1a0f1b21[data-orientation='horizontal'] {\n    flex-direction: row;\n  }\n\n.m_1a0f1b21[data-orientation='horizontal'] .m_8d3f4000:not(:only-child):first-child, .m_1a0f1b21[data-orientation='horizontal'] .m_437b6484:not(:only-child):first-child {\n        border-end-end-radius: 0;\n        border-start-end-radius: 0;\n        border-inline-end-width: calc(var(--ai-border-width) / 2);\n      }\n\n.m_1a0f1b21[data-orientation='horizontal'] .m_8d3f4000:not(:only-child):last-child, .m_1a0f1b21[data-orientation='horizontal'] .m_437b6484:not(:only-child):last-child {\n        border-end-start-radius: 0;\n        border-start-start-radius: 0;\n        border-inline-start-width: calc(var(--ai-border-width) / 2);\n      }\n\n.m_1a0f1b21[data-orientation='horizontal'] .m_8d3f4000:not(:only-child):not(:first-child):not(:last-child), .m_1a0f1b21[data-orientation='horizontal'] .m_437b6484:not(:only-child):not(:first-child):not(:last-child) {\n        border-radius: 0;\n        border-inline-width: calc(var(--ai-border-width) / 2);\n      }\n\n.m_1a0f1b21[data-orientation='vertical'] {\n    flex-direction: column;\n  }\n\n.m_1a0f1b21[data-orientation='vertical'] .m_8d3f4000:not(:only-child):first-child, .m_1a0f1b21[data-orientation='vertical'] .m_437b6484:not(:only-child):first-child {\n        border-end-start-radius: 0;\n        border-end-end-radius: 0;\n        border-bottom-width: calc(var(--ai-border-width) / 2);\n      }\n\n.m_1a0f1b21[data-orientation='vertical'] .m_8d3f4000:not(:only-child):last-child, .m_1a0f1b21[data-orientation='vertical'] .m_437b6484:not(:only-child):last-child {\n        border-start-start-radius: 0;\n        border-start-end-radius: 0;\n        border-top-width: calc(var(--ai-border-width) / 2);\n      }\n\n.m_1a0f1b21[data-orientation='vertical'] .m_8d3f4000:not(:only-child):not(:first-child):not(:last-child), .m_1a0f1b21[data-orientation='vertical'] .m_437b6484:not(:only-child):not(:first-child):not(:last-child) {\n        border-radius: 0;\n        border-bottom-width: calc(var(--ai-border-width) / 2);\n        border-top-width: calc(var(--ai-border-width) / 2);\n      }\n\n.m_8d3afb97 {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition:\n    transform 150ms ease,\n    opacity 100ms ease;\n  width: 100%;\n  height: 100%;\n}\n\n.m_437b6484 {\n  --section-height-xs: calc(1.125rem * var(--mantine-scale));\n  --section-height-sm: calc(1.375rem * var(--mantine-scale));\n  --section-height-md: calc(1.75rem * var(--mantine-scale));\n  --section-height-lg: calc(2.125rem * var(--mantine-scale));\n  --section-height-xl: calc(2.75rem * var(--mantine-scale));\n\n  --section-height-input-xs: calc(1.875rem * var(--mantine-scale));\n  --section-height-input-sm: calc(2.25rem * var(--mantine-scale));\n  --section-height-input-md: calc(2.625rem * var(--mantine-scale));\n  --section-height-input-lg: calc(3.125rem * var(--mantine-scale));\n  --section-height-input-xl: calc(3.75rem * var(--mantine-scale));\n\n  --section-padding-x-xs: calc(0.375rem * var(--mantine-scale));\n  --section-padding-x-sm: calc(0.5rem * var(--mantine-scale));\n  --section-padding-x-md: calc(0.625rem * var(--mantine-scale));\n  --section-padding-x-lg: calc(0.75rem * var(--mantine-scale));\n  --section-padding-x-xl: calc(1rem * var(--mantine-scale));\n\n  --section-height: var(--section-height-sm);\n  --section-padding-x: var(--section-padding-x-sm);\n  --section-color: var(--mantine-color-white);\n\n  font-weight: 600;\n  width: auto;\n  border-radius: var(--section-radius, var(--mantine-radius-default));\n  font-size: var(--section-fz, var(--mantine-font-size-sm));\n  background: var(--section-bg, var(--mantine-primary-color-filled));\n  border: var(--section-bd, calc(0.0625rem * var(--mantine-scale)) solid transparent);\n  color: var(--section-color, var(--mantine-color-white));\n  height: var(--section-height, var(--section-height-sm));\n  padding-inline: var(--section-padding-x, var(--section-padding-x-sm));\n  vertical-align: middle;\n  line-height: 1;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.m_86a44da5 {\n  --cb-size-xs: calc(1.125rem * var(--mantine-scale));\n  --cb-size-sm: calc(1.375rem * var(--mantine-scale));\n  --cb-size-md: calc(1.75rem * var(--mantine-scale));\n  --cb-size-lg: calc(2.125rem * var(--mantine-scale));\n  --cb-size-xl: calc(2.75rem * var(--mantine-scale));\n\n  --cb-size: var(--cb-size-md);\n  --cb-icon-size: 70%;\n  --cb-radius: var(--mantine-radius-default);\n\n  line-height: 1;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  user-select: none;\n\n  width: var(--cb-size);\n  height: var(--cb-size);\n  min-width: var(--cb-size);\n  min-height: var(--cb-size);\n  border-radius: var(--cb-radius);\n}\n\n  :where([data-mantine-color-scheme='light']) .m_86a44da5 {\n    color: var(--mantine-color-gray-7);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_86a44da5 {\n    color: var(--mantine-color-dark-1);\n}\n\n  .m_86a44da5[data-disabled],\n  .m_86a44da5:disabled {\n    cursor: not-allowed;\n    opacity: 0.6;\n  }\n\n@media (hover: hover) {\n    :where([data-mantine-color-scheme='light']) .m_220c80f2:where(:not([data-disabled], :disabled)):hover {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_220c80f2:where(:not([data-disabled], :disabled)):hover {\n      background-color: var(--mantine-color-dark-6);\n  }\n}\n\n@media (hover: none) {\n    :where([data-mantine-color-scheme='light']) .m_220c80f2:where(:not([data-disabled], :disabled)):active {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_220c80f2:where(:not([data-disabled], :disabled)):active {\n      background-color: var(--mantine-color-dark-6);\n  }\n}\n\n.m_4081bf90 {\n  display: flex;\n  flex-direction: row;\n  flex-wrap: var(--group-wrap, wrap);\n  justify-content: var(--group-justify, flex-start);\n  align-items: var(--group-align, center);\n  gap: var(--group-gap, var(--mantine-spacing-md));\n}\n\n  .m_4081bf90:where([data-grow]) > * {\n      flex-grow: 1;\n      max-width: var(--group-child-width);\n    }\n\n.m_615af6c9 {\n  line-height: 1;\n  padding: 0;\n  margin: 0;\n  font-weight: 400;\n  font-size: var(--mantine-font-size-md);\n}\n\n.m_b5489c3c {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: var(--mb-padding, var(--mantine-spacing-md));\n  padding-inline-end: calc(var(--mb-padding, var(--mantine-spacing-md)) - calc(0.3125rem * var(--mantine-scale)));\n  position: sticky;\n  top: 0;\n  background-color: var(--mantine-color-body);\n  z-index: 1000;\n  min-height: calc(3.75rem * var(--mantine-scale));\n  transition: padding-inline-end 100ms;\n}\n\n.m_60c222c7 {\n  position: fixed;\n  width: 100%;\n  top: 0;\n  bottom: 0;\n  z-index: var(--mb-z-index);\n  pointer-events: none;\n}\n\n.m_fd1ab0aa {\n  pointer-events: all;\n  box-shadow: var(--mb-shadow, var(--mantine-shadow-xl));\n}\n\n.m_fd1ab0aa [data-mantine-scrollbar] {\n    z-index: 1001;\n  }\n\n[data-offset-scrollbars] .m_fd1ab0aa:has([data-mantine-scrollbar]) .m_b5489c3c {\n    padding-inline-end: calc(var(--mb-padding, var(--mantine-spacing-md)) + calc(0.3125rem * var(--mantine-scale)));\n  }\n\n.m_606cb269 {\n  margin-inline-start: auto;\n}\n\n.m_5df29311 {\n  padding: var(--mb-padding, var(--mantine-spacing-md));\n  padding-top: var(--mb-padding, var(--mantine-spacing-md));\n}\n\n.m_5df29311:where(:not(:only-child)) {\n    padding-top: 0;\n  }\n\n.m_6c018570 {\n  position: relative;\n  margin-top: var(--input-margin-top, 0rem);\n  margin-bottom: var(--input-margin-bottom, 0rem);\n\n  --input-height-xs: calc(1.875rem * var(--mantine-scale));\n  --input-height-sm: calc(2.25rem * var(--mantine-scale));\n  --input-height-md: calc(2.625rem * var(--mantine-scale));\n  --input-height-lg: calc(3.125rem * var(--mantine-scale));\n  --input-height-xl: calc(3.75rem * var(--mantine-scale));\n\n  --input-padding-y-xs: calc(0.3125rem * var(--mantine-scale));\n  --input-padding-y-sm: calc(0.375rem * var(--mantine-scale));\n  --input-padding-y-md: calc(0.5rem * var(--mantine-scale));\n  --input-padding-y-lg: calc(0.625rem * var(--mantine-scale));\n  --input-padding-y-xl: calc(0.8125rem * var(--mantine-scale));\n\n  --input-height: var(--input-height-sm);\n  --input-radius: var(--mantine-radius-default);\n\n  --input-cursor: text;\n  --input-text-align: left;\n  --input-line-height: calc(var(--input-height) - calc(0.125rem * var(--mantine-scale)));\n  --input-padding: calc(var(--input-height) / 3);\n  --input-padding-inline-start: var(--input-padding);\n  --input-padding-inline-end: var(--input-padding);\n  --input-placeholder-color: var(--mantine-color-placeholder);\n  --input-color: var(--mantine-color-text);\n  --input-disabled-bg: var(--mantine-color-disabled);\n  --input-disabled-color: var(--mantine-color-disabled-color);\n\n  --input-left-section-size: var(--input-left-section-width, calc(var(--input-height) - calc(0.125rem * var(--mantine-scale))));\n\n  --input-right-section-size: var(\n    --input-right-section-width,\n    calc(var(--input-height) - calc(0.125rem * var(--mantine-scale)))\n  );\n\n  --input-size: var(--input-height);\n\n  --section-y: calc(0.0625rem * var(--mantine-scale));\n  --left-section-start: calc(0.0625rem * var(--mantine-scale));\n  --left-section-border-radius: var(--input-radius) 0 0 var(--input-radius);\n\n  --right-section-end: calc(0.0625rem * var(--mantine-scale));\n  --right-section-border-radius: 0 var(--input-radius) var(--input-radius) 0;\n}\n\n  .m_6c018570[data-variant='unstyled'] {\n    --input-padding: 0;\n    --input-padding-y: 0;\n    --input-padding-inline-start: 0;\n    --input-padding-inline-end: 0;\n  }\n\n  .m_6c018570[data-pointer] {\n    --input-cursor: pointer;\n  }\n\n  .m_6c018570[data-multiline] {\n    --input-padding-y-xs: calc(0.28125rem * var(--mantine-scale));\n    --input-padding-y-sm: calc(0.34375rem * var(--mantine-scale));\n    --input-padding-y-md: calc(0.4375rem * var(--mantine-scale));\n    --input-padding-y-lg: calc(0.59375rem * var(--mantine-scale));\n    --input-padding-y-xl: calc(0.8125rem * var(--mantine-scale));\n\n    --input-size: auto;\n    --input-line-height: var(--mantine-line-height);\n    --input-padding-y: var(--input-padding-y-sm);\n  }\n\n  .m_6c018570[data-with-left-section] {\n    --input-padding-inline-start: var(--input-left-section-size);\n  }\n\n  .m_6c018570[data-with-right-section] {\n    --input-padding-inline-end: var(--input-right-section-size);\n  }\n\n  [data-mantine-color-scheme='light'] .m_6c018570[data-variant='default'] {\n      --input-bd: var(--mantine-color-gray-4);\n      --input-bg: var(--mantine-color-white);\n      --input-bd-focus: var(--mantine-primary-color-filled);\n    }\n\n  [data-mantine-color-scheme='light'] .m_6c018570[data-variant='filled'] {\n      --input-bd: transparent;\n      --input-bg: var(--mantine-color-gray-1);\n      --input-bd-focus: var(--mantine-primary-color-filled);\n    }\n\n  [data-mantine-color-scheme='light'] .m_6c018570[data-variant='unstyled'] {\n      --input-bd: transparent;\n      --input-bg: transparent;\n      --input-bd-focus: transparent;\n    }\n\n  [data-mantine-color-scheme='dark'] .m_6c018570[data-variant='default'] {\n      --input-bd: var(--mantine-color-dark-4);\n      --input-bg: var(--mantine-color-dark-6);\n      --input-bd-focus: var(--mantine-primary-color-filled);\n    }\n\n  [data-mantine-color-scheme='dark'] .m_6c018570[data-variant='filled'] {\n      --input-bd: transparent;\n      --input-bg: var(--mantine-color-dark-5);\n      --input-bd-focus: var(--mantine-primary-color-filled);\n    }\n\n  [data-mantine-color-scheme='dark'] .m_6c018570[data-variant='unstyled'] {\n      --input-bd: transparent;\n      --input-bg: transparent;\n      --input-bd-focus: transparent;\n    }\n\n  [data-mantine-color-scheme] .m_6c018570[data-error]:not([data-variant='unstyled']) {\n      --input-bd: var(--mantine-color-error);\n    }\n\n  [data-mantine-color-scheme] .m_6c018570[data-error] {\n\n    --input-color: var(--mantine-color-error);\n    --input-placeholder-color: var(--mantine-color-error);\n    --input-section-color: var(--mantine-color-error);\n}\n\n  :where([dir=\"rtl\"]) .m_6c018570 {\n    --input-text-align: right;\n    --left-section-border-radius: 0 var(--input-radius) var(--input-radius) 0;\n    --right-section-border-radius: var(--input-radius) 0 0 var(--input-radius);\n}\n\n.m_8fb7ebe7 {\n  -webkit-tap-highlight-color: transparent;\n  appearance: none;\n  resize: var(--input-resize, none);\n  display: block;\n  width: 100%;\n  transition: border-color 100ms ease;\n\n  text-align: var(--input-text-align);\n  color: var(--input-color);\n  border: calc(0.0625rem * var(--mantine-scale)) solid var(--input-bd);\n  background-color: var(--input-bg);\n  font-family: var(--input-font-family, var(--mantine-font-family));\n  height: var(--input-size);\n  min-height: var(--input-height);\n  line-height: var(--input-line-height);\n  font-size: var(--input-fz, var(--input-fz, var(--mantine-font-size-sm)));\n  border-radius: var(--input-radius);\n  padding-inline-start: var(--input-padding-inline-start);\n  padding-inline-end: var(--input-padding-inline-end);\n  padding-top: var(--input-padding-y, 0rem);\n  padding-bottom: var(--input-padding-y, 0rem);\n  cursor: var(--input-cursor);\n  overflow: var(--input-overflow);\n}\n\n/* Used as data attribute in Textarea component, does not have associated prop on the Input component */\n\n.m_8fb7ebe7[data-no-overflow] {\n    --input-overflow: hidden;\n  }\n\n/* Used as data attribute in JsonInput component, does not have associated prop on the Input component */\n\n.m_8fb7ebe7[data-monospace] {\n    --input-font-family: var(--mantine-font-family-monospace);\n    --input-fz: calc(var(--input-fz, var(--mantine-font-size-sm)) - calc(0.125rem * var(--mantine-scale)));\n  }\n\n.m_8fb7ebe7:focus,\n  .m_8fb7ebe7:focus-within {\n    outline: none;\n    --input-bd: var(--input-bd-focus);\n  }\n\n[data-error] .m_8fb7ebe7:focus, [data-error] .m_8fb7ebe7:focus-within {\n      --input-bd: var(--mantine-color-error);\n    }\n\n.m_8fb7ebe7::placeholder {\n    color: var(--input-placeholder-color);\n    opacity: 1;\n  }\n\n.m_8fb7ebe7::-webkit-inner-spin-button,\n  .m_8fb7ebe7::-webkit-outer-spin-button,\n  .m_8fb7ebe7::-webkit-search-decoration,\n  .m_8fb7ebe7::-webkit-search-cancel-button,\n  .m_8fb7ebe7::-webkit-search-results-button,\n  .m_8fb7ebe7::-webkit-search-results-decoration {\n    appearance: none;\n  }\n\n.m_8fb7ebe7[type='number'] {\n    -moz-appearance: textfield;\n  }\n\n.m_8fb7ebe7:disabled,\n  .m_8fb7ebe7[data-disabled] {\n    cursor: not-allowed;\n    opacity: 0.6;\n    background-color: var(--input-disabled-bg);\n    color: var(--input-disabled-color);\n  }\n\n/* Required to be a separate selector to work in Firefox, can be merged with &:disabled once :has is supported */\n\n.m_8fb7ebe7:has(input:disabled) {\n    cursor: not-allowed;\n    opacity: 0.6;\n    background-color: var(--input-disabled-bg);\n    color: var(--input-disabled-color);\n  }\n\n.m_82577fc2 {\n  pointer-events: var(--section-pointer-events);\n  position: absolute;\n  z-index: 1;\n  inset-inline-start: var(--section-start);\n  inset-inline-end: var(--section-end);\n  bottom: var(--section-y);\n  top: var(--section-y);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: var(--section-size);\n  border-radius: var(--section-border-radius);\n  color: var(--input-section-color, var(--mantine-color-dimmed));\n}\n\n.m_82577fc2[data-position='right'] {\n    --section-pointer-events: var(--input-right-section-pointer-events);\n    --section-end: var(--right-section-end);\n    --section-size: var(--input-right-section-size);\n    --section-border-radius: var(--right-section-border-radius);\n  }\n\n.m_82577fc2[data-position='left'] {\n    --section-pointer-events: var(--input-left-section-pointer-events);\n    --section-start: var(--left-section-start);\n    --section-size: var(--input-left-section-size);\n    --section-border-radius: var(--left-section-border-radius);\n  }\n\n/* ----- Input.Placeholder ----- */\n.m_88bacfd0 {\n  color: var(--input-placeholder-color, var(--mantine-color-placeholder));\n}\n[data-error] .m_88bacfd0 {\n    --input-placeholder-color: var(--input-color, var(--mantine-color-placeholder));\n  }\n\n/* ----- Input.Wrapper ----- */\n.m_46b77525 {\n  line-height: var(--mantine-line-height);\n}\n\n.m_8fdc1311 {\n  display: inline-block;\n  font-weight: 500;\n  word-break: break-word;\n  cursor: default;\n  -webkit-tap-highlight-color: transparent;\n  font-size: var(--input-label-size, var(--mantine-font-size-sm));\n}\n\n.m_78a94662 {\n  color: var(--input-asterisk-color, var(--mantine-color-error));\n}\n\n.m_8f816625,\n.m_fe47ce59 {\n  word-wrap: break-word;\n  line-height: 1.2;\n  display: block;\n  margin: 0;\n  padding: 0;\n}\n\n.m_8f816625 {\n  color: var(--mantine-color-error);\n  font-size: var(--input-error-size, calc(var(--mantine-font-size-sm) - calc(0.125rem * var(--mantine-scale))));\n}\n\n.m_fe47ce59 {\n  color: var(--mantine-color-dimmed);\n  font-size: var(--input-description-size, calc(var(--mantine-font-size-sm) - calc(0.125rem * var(--mantine-scale))));\n}\n\n.m_8bffd616 {\n  display: flex;\n}\n\n.m_96b553a6 {\n  --transition-duration: 150ms;\n\n  top: 0;\n  left: 0;\n  position: absolute;\n  z-index: 0;\n  transition-property: transform, width, height;\n  transition-timing-function: ease;\n  transition-duration: 0ms;\n}\n\n  .m_96b553a6:where([data-initialized]) {\n    transition-duration: var(--transition-duration);\n  }\n\n  .m_96b553a6:where([data-hidden]) {\n    background-color: red;\n    display: none;\n  }\n\n.m_9bdbb667 {\n  --accordion-radius: var(--mantine-radius-default);\n}\n\n.m_df78851f {\n  word-break: break-word;\n}\n\n.m_4ba554d4 {\n  padding: var(--mantine-spacing-md);\n  padding-top: calc(var(--mantine-spacing-xs) / 2);\n}\n\n.m_8fa820a0 {\n  margin: 0;\n  padding: 0;\n}\n\n.m_4ba585b8 {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  flex-direction: row-reverse;\n  padding-inline: var(--mantine-spacing-md);\n  opacity: 1;\n  cursor: pointer;\n  background-color: transparent;\n}\n\n.m_4ba585b8:where([data-chevron-position='left']) {\n    flex-direction: row;\n    padding-inline-start: 0;\n  }\n\n:where([data-mantine-color-scheme='light']) .m_4ba585b8 {\n    color: var(--mantine-color-black);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_4ba585b8 {\n    color: var(--mantine-color-dark-0);\n}\n\n.m_4ba585b8:where(:disabled, [data-disabled]) {\n    opacity: 0.4;\n    cursor: not-allowed;\n  }\n\n@media (hover: hover) {\n      :where([data-mantine-color-scheme='light']) .m_6939a5e9:where(:not(:disabled, [data-disabled])):hover, :where([data-mantine-color-scheme='light']) .m_4271d21b:where(:not(:disabled, [data-disabled])):hover {\n        background-color: var(--mantine-color-gray-0);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_6939a5e9:where(:not(:disabled, [data-disabled])):hover, :where([data-mantine-color-scheme='dark']) .m_4271d21b:where(:not(:disabled, [data-disabled])):hover {\n        background-color: var(--mantine-color-dark-6);\n  }\n}\n\n@media (hover: none) {\n      :where([data-mantine-color-scheme='light']) .m_6939a5e9:where(:not(:disabled, [data-disabled])):active, :where([data-mantine-color-scheme='light']) .m_4271d21b:where(:not(:disabled, [data-disabled])):active {\n        background-color: var(--mantine-color-gray-0);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_6939a5e9:where(:not(:disabled, [data-disabled])):active, :where([data-mantine-color-scheme='dark']) .m_4271d21b:where(:not(:disabled, [data-disabled])):active {\n        background-color: var(--mantine-color-dark-6);\n  }\n}\n\n.m_df3ffa0f {\n  color: inherit;\n  font-weight: 400;\n  flex: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding-top: var(--mantine-spacing-sm);\n  padding-bottom: var(--mantine-spacing-sm);\n}\n\n.m_3f35ae96 {\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  transition: transform var(--accordion-transition-duration, 200ms) ease;\n  width: var(--accordion-chevron-size, calc(0.9375rem * var(--mantine-scale)));\n  min-width: var(--accordion-chevron-size, calc(0.9375rem * var(--mantine-scale)));\n  transform: rotate(0deg);\n}\n\n.m_3f35ae96:where([data-rotate]) {\n    transform: rotate(180deg);\n  }\n\n.m_3f35ae96:where([data-position='left']) {\n    margin-inline-end: var(--mantine-spacing-md);\n    margin-inline-start: var(--mantine-spacing-md);\n  }\n\n.m_9bd771fe {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-inline-end: var(--mantine-spacing-sm);\n}\n\n.m_9bd771fe:where([data-chevron-position='left']) {\n    margin-inline-end: 0;\n    margin-inline-start: var(--mantine-spacing-lg);\n  }\n\n:where([data-mantine-color-scheme='light']) .m_9bd7b098 {\n    --item-border-color: var(--mantine-color-gray-3);\n    --item-filled-color: var(--mantine-color-gray-0);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_9bd7b098 {\n    --item-border-color: var(--mantine-color-dark-4);\n    --item-filled-color: var(--mantine-color-dark-6);\n}\n\n.m_fe19b709 {\n  border-bottom: 1px solid var(--item-border-color);\n}\n\n.m_1f921b3b {\n  border: 1px solid var(--item-border-color);\n  transition: background-color 150ms ease;\n}\n\n.m_1f921b3b:where([data-active]) {\n    background-color: var(--item-filled-color);\n  }\n\n.m_1f921b3b:first-of-type {\n    border-start-start-radius: var(--accordion-radius);\n    border-start-end-radius: var(--accordion-radius);\n  }\n\n.m_1f921b3b:first-of-type > [data-accordion-control] {\n      border-start-start-radius: var(--accordion-radius);\n      border-start-end-radius: var(--accordion-radius);\n    }\n\n.m_1f921b3b:last-of-type {\n    border-end-start-radius: var(--accordion-radius);\n    border-end-end-radius: var(--accordion-radius);\n  }\n\n.m_1f921b3b:last-of-type > [data-accordion-control] {\n      border-end-start-radius: var(--accordion-radius);\n      border-end-end-radius: var(--accordion-radius);\n    }\n\n.m_1f921b3b + .m_1f921b3b {\n    border-top: 0;\n  }\n\n.m_2cdf939a {\n  border-radius: var(--accordion-radius);\n}\n\n.m_2cdf939a:where([data-active]) {\n    background-color: var(--item-filled-color);\n  }\n\n.m_9f59b069 {\n  background-color: var(--item-filled-color);\n  border-radius: var(--accordion-radius);\n  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  transition: background-color 150ms ease;\n}\n\n.m_9f59b069[data-active] {\n    border-color: var(--item-border-color);\n  }\n\n:where([data-mantine-color-scheme='light']) .m_9f59b069[data-active] {\n      background-color: var(--mantine-color-white);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_9f59b069[data-active] {\n      background-color: var(--mantine-color-dark-7);\n}\n\n.m_9f59b069 + .m_9f59b069 {\n    margin-top: var(--mantine-spacing-md);\n  }\n\n.m_7f854edf {\n  position: fixed;\n  z-index: var(--affix-z-index);\n  inset-inline-start: var(--affix-left);\n  inset-inline-end: var(--affix-right);\n  top: var(--affix-top);\n  bottom: var(--affix-bottom);\n}\n\n.m_66836ed3 {\n  --alert-radius: var(--mantine-radius-default);\n  --alert-bg: var(--mantine-primary-color-light);\n  --alert-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  --alert-color: var(--mantine-primary-color-light-color);\n\n  padding: var(--mantine-spacing-md) var(--mantine-spacing-md);\n  border-radius: var(--alert-radius);\n  position: relative;\n  overflow: hidden;\n  background-color: var(--alert-bg);\n  border: var(--alert-bd);\n  color: var(--alert-color);\n}\n\n.m_a5d60502 {\n  display: flex;\n}\n\n.m_667c2793 {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: var(--mantine-spacing-xs);\n}\n\n.m_6a03f287 {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: var(--mantine-font-size-sm);\n  font-weight: 700;\n}\n\n.m_6a03f287:where([data-with-close-button]) {\n    padding-inline-end: var(--mantine-spacing-md);\n  }\n\n.m_698f4f23 {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.m_667f2a6a {\n  line-height: 1;\n  width: calc(1.25rem * var(--mantine-scale));\n  height: calc(1.25rem * var(--mantine-scale));\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  margin-inline-end: var(--mantine-spacing-md);\n  margin-top: calc(0.0625rem * var(--mantine-scale));\n}\n\n.m_7fa78076 {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  font-size: var(--mantine-font-size-sm);\n}\n\n:where([data-mantine-color-scheme='light']) .m_7fa78076 {\n    color: var(--mantine-color-black);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_7fa78076 {\n    color: var(--mantine-color-white);\n}\n\n.m_7fa78076:where([data-variant='filled']) {\n    color: var(--alert-color);\n  }\n\n.m_7fa78076:where([data-variant='white']) {\n    color: var(--mantine-color-black);\n  }\n\n.m_87f54839 {\n  width: calc(1.25rem * var(--mantine-scale));\n  height: calc(1.25rem * var(--mantine-scale));\n  color: var(--alert-color);\n}\n\n.m_b6d8b162 {\n  -webkit-tap-highlight-color: transparent;\n  text-decoration: none;\n  font-size: var(--text-fz, var(--mantine-font-size-md));\n  line-height: var(--text-lh, var(--mantine-line-height-md));\n  font-weight: normal;\n  margin: 0;\n  padding: 0;\n  color: var(--text-color);\n}\n\n  .m_b6d8b162:where([data-truncate]) {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  .m_b6d8b162:where([data-truncate='start']) {\n    direction: rtl;\n    text-align: right;\n  }\n\n  :where([dir=\"rtl\"]) .m_b6d8b162:where([data-truncate='start']) {\n      direction: ltr;\n      text-align: left;\n}\n\n  .m_b6d8b162:where([data-variant='gradient']) {\n    background-image: var(--text-gradient);\n    background-clip: text;\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n  }\n\n  .m_b6d8b162:where([data-line-clamp]) {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: var(--text-line-clamp);\n    -webkit-box-orient: vertical;\n  }\n\n  .m_b6d8b162:where([data-inherit]) {\n    line-height: inherit;\n    font-weight: inherit;\n    font-size: inherit;\n  }\n\n  .m_b6d8b162:where([data-inline]) {\n    line-height: 1;\n  }\n\n.m_849cf0da {\n  color: var(--mantine-color-anchor);\n  text-decoration: none;\n  appearance: none;\n  border: none;\n  display: inline;\n  padding: 0;\n  margin: 0;\n  background-color: transparent;\n  cursor: pointer;\n}\n\n  @media (hover: hover) {\n\n  .m_849cf0da:where([data-underline='hover']):hover {\n      text-decoration: underline;\n  }\n}\n\n  @media (hover: none) {\n\n  .m_849cf0da:where([data-underline='hover']):active {\n      text-decoration: underline;\n  }\n}\n\n  .m_849cf0da:where([data-underline='not-hover']) {\n    text-decoration: underline;\n  }\n\n  @media (hover: hover) {\n\n  .m_849cf0da:where([data-underline='not-hover']):hover {\n      text-decoration: none;\n  }\n}\n\n  @media (hover: none) {\n\n  .m_849cf0da:where([data-underline='not-hover']):active {\n      text-decoration: none;\n  }\n}\n\n  .m_849cf0da:where([data-underline='always']) {\n    text-decoration: underline;\n  }\n\n  .m_849cf0da:where([data-variant='gradient']),\n    .m_849cf0da:where([data-variant='gradient']):hover {\n      text-decoration: none;\n    }\n\n  .m_849cf0da:where([data-line-clamp]) {\n    display: -webkit-box;\n  }\n\n.m_48204f9b {\n  width: var(--slider-size);\n  height: var(--slider-size);\n  position: relative;\n  border-radius: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  user-select: none;\n}\n\n  .m_48204f9b:focus-within {\n    outline: 2px solid var(--mantine-primary-color-filled);\n    outline-offset: calc(0.125rem * var(--mantine-scale));\n  }\n\n  .m_48204f9b {\n\n  --slider-size: calc(3.75rem * var(--mantine-scale));\n  --thumb-size: calc(var(--slider-size) / 5);\n}\n\n  :where([data-mantine-color-scheme='light']) .m_48204f9b {\n    background-color: var(--mantine-color-gray-1);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_48204f9b {\n    background-color: var(--mantine-color-dark-5);\n}\n\n.m_bb9cdbad {\n  position: absolute;\n  inset: calc(0.0625rem * var(--mantine-scale));\n  border-radius: var(--slider-size);\n  pointer-events: none;\n}\n\n.m_481dd586 {\n  width: calc(0.125rem * var(--mantine-scale));\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: calc(50% - 1px);\n  transform: rotate(var(--angle));\n}\n\n.m_481dd586::before {\n    content: '';\n    position: absolute;\n    top: calc(var(--thumb-size) / 3);\n    left: calc(0.03125rem * var(--mantine-scale));\n    width: calc(0.0625rem * var(--mantine-scale));\n    height: calc(var(--thumb-size) / 1.5);\n    transform: translate(-50%, -50%);\n  }\n\n:where([data-mantine-color-scheme='light']) .m_481dd586::before {\n      background-color: var(--mantine-color-gray-4);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_481dd586::before {\n      background-color: var(--mantine-color-dark-3);\n}\n\n.m_481dd586[data-label]::after {\n    min-width: calc(1.125rem * var(--mantine-scale));\n    text-align: center;\n    content: attr(data-label);\n    position: absolute;\n    top: calc(-1.5rem * var(--mantine-scale));\n    left: calc(-0.4375rem * var(--mantine-scale));\n    transform: rotate(calc(360deg - var(--angle)));\n    font-size: var(--mantine-font-size-xs);\n  }\n\n.m_bc02ba3d {\n  position: absolute;\n  inset-block: 0;\n  inset-inline-start: calc(50% - 1.5px);\n  inset-inline-end: 0;\n  height: 100%;\n  width: calc(0.1875rem * var(--mantine-scale));\n  outline: none;\n  pointer-events: none;\n}\n\n.m_bc02ba3d::before {\n    content: '';\n    position: absolute;\n    right: 0;\n    top: 0;\n    height: min(var(--thumb-size), calc(var(--slider-size) / 2));\n    width: calc(0.1875rem * var(--mantine-scale));\n  }\n\n:where([data-mantine-color-scheme='light']) .m_bc02ba3d::before {\n      background-color: var(--mantine-color-gray-7);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_bc02ba3d::before {\n      background-color: var(--mantine-color-dark-1);\n}\n\n.m_bb8e875b {\n  font-size: var(--mantine-font-size-xs);\n}\n\n.m_89ab340[data-resizing] {\n    --app-shell-transition-duration: 0ms !important;\n  }\n  .m_89ab340[data-disabled] {\n    --app-shell-header-offset: 0rem !important;\n    --app-shell-navbar-offset: 0rem !important;\n    --app-shell-aside-offset: 0rem !important;\n    --app-shell-footer-offset: 0rem !important;\n  }\n  [data-mantine-color-scheme='light'] .m_89ab340 {\n    --app-shell-border-color: var(--mantine-color-gray-3);\n}\n  [data-mantine-color-scheme='dark'] .m_89ab340 {\n    --app-shell-border-color: var(--mantine-color-dark-4);\n}\n\n.m_45252eee,\n.m_9cdde9a,\n.m_3b16f56b,\n.m_8983817,\n.m_3840c879 {\n  transition-duration: var(--app-shell-transition-duration);\n  transition-timing-function: var(--app-shell-transition-timing-function);\n}\n\n.m_45252eee,\n.m_9cdde9a {\n  position: fixed;\n  display: flex;\n  flex-direction: column;\n  top: var(--app-shell-header-offset, 0rem);\n  height: calc(\n    100dvh - var(--app-shell-header-offset, 0rem) - var(--app-shell-footer-offset, 0rem)\n  );\n  background-color: var(--mantine-color-body);\n  transition-property: transform, top, height;\n}\n\n:where([data-layout='alt']) .m_45252eee, :where([data-layout='alt']) .m_9cdde9a {\n    top: 0rem;\n    height: 100dvh;\n  }\n\n.m_45252eee {\n  inset-inline-start: 0;\n  width: var(--app-shell-navbar-width);\n  transition-property: transform, top, height;\n  transform: var(--app-shell-navbar-transform);\n  z-index: var(--app-shell-navbar-z-index);\n}\n\n:where([dir=\"rtl\"]) .m_45252eee {\n    transform: var(--app-shell-navbar-transform-rtl);\n}\n\n.m_45252eee:where([data-with-border]) {\n    border-inline-end: 1px solid var(--app-shell-border-color);\n  }\n\n.m_9cdde9a {\n  inset-inline-end: 0;\n  width: var(--app-shell-aside-width);\n  transform: var(--app-shell-aside-transform);\n  z-index: var(--app-shell-aside-z-index);\n}\n\n:where([dir=\"rtl\"]) .m_9cdde9a {\n    transform: var(--app-shell-aside-transform-rtl);\n}\n\n.m_9cdde9a:where([data-with-border]) {\n    border-inline-start: 1px solid var(--app-shell-border-color);\n  }\n\n.m_8983817 {\n  padding-inline-start: calc(var(--app-shell-navbar-offset, 0rem) + var(--app-shell-padding));\n  padding-inline-end: calc(var(--app-shell-aside-offset, 0rem) + var(--app-shell-padding));\n  padding-top: calc(var(--app-shell-header-offset, 0rem) + var(--app-shell-padding));\n  padding-bottom: calc(var(--app-shell-footer-offset, 0rem) + var(--app-shell-padding));\n  min-height: 100dvh;\n  transition-property: padding;\n}\n\n.m_3b16f56b,\n.m_3840c879 {\n  position: fixed;\n  inset-inline: 0;\n  transition-property: transform, left, right;\n  background-color: var(--mantine-color-body);\n}\n\n:where([data-layout='alt']) .m_3b16f56b, :where([data-layout='alt']) .m_3840c879 {\n    inset-inline-start: var(--app-shell-navbar-offset, 0rem);\n    inset-inline-end: var(--app-shell-aside-offset, 0rem);\n  }\n\n.m_3b16f56b {\n  top: 0;\n  height: var(--app-shell-header-height);\n  background-color: var(--mantine-color-body);\n  transform: var(--app-shell-header-transform);\n  z-index: var(--app-shell-header-z-index);\n}\n\n.m_3b16f56b:where([data-with-border]) {\n    border-bottom: 1px solid var(--app-shell-border-color);\n  }\n\n.m_3840c879 {\n  bottom: 0;\n  height: calc(var(--app-shell-footer-height) + env(safe-area-inset-bottom));\n  padding-bottom: env(safe-area-inset-bottom);\n  transform: var(--app-shell-footer-transform);\n  z-index: var(--app-shell-footer-z-index);\n}\n\n.m_3840c879:where([data-with-border]) {\n    border-top: 1px solid var(--app-shell-border-color);\n  }\n\n.m_6dcfc7c7 {\n  flex-grow: 0;\n}\n\n.m_6dcfc7c7:where([data-grow]) {\n    flex-grow: 1;\n  }\n\n.m_71ac47fc {\n  --ar-ratio: 1;\n  max-width: 100%;\n}\n\n  .m_71ac47fc > :where(*:not(style)) {\n    aspect-ratio: var(--ar-ratio);\n    width: 100%;\n  }\n\n  .m_71ac47fc > :where(img, video) {\n    object-fit: cover;\n  }\n\n.m_88b62a41 {\n  --combobox-padding: calc(0.25rem * var(--mantine-scale));\n  padding: var(--combobox-padding);\n}\n\n  .m_88b62a41:has([data-mantine-scrollbar]) .m_985517d8 {\n      max-width: calc(100% + var(--combobox-padding));\n    }\n\n  .m_88b62a41[data-composed] {\n    padding-inline-end: 0;\n  }\n\n  .m_88b62a41[data-hidden] {\n    display: none;\n  }\n\n/* Variables must be both on dropdown and options to support usage of Combobox.Options without Combobox.Dropdown */\n.m_88b62a41,\n.m_b2821a6e {\n  --combobox-option-padding-xs: calc(0.25rem * var(--mantine-scale)) calc(0.5rem * var(--mantine-scale));\n  --combobox-option-padding-sm: calc(0.375rem * var(--mantine-scale)) calc(0.625rem * var(--mantine-scale));\n  --combobox-option-padding-md: calc(0.5rem * var(--mantine-scale)) calc(0.75rem * var(--mantine-scale));\n  --combobox-option-padding-lg: calc(0.625rem * var(--mantine-scale)) calc(1rem * var(--mantine-scale));\n  --combobox-option-padding-xl: calc(0.875rem * var(--mantine-scale)) calc(1.25rem * var(--mantine-scale));\n  --combobox-option-padding: var(--combobox-option-padding-sm);\n}\n\n.m_92253aa5 {\n  padding: var(--combobox-option-padding);\n  font-size: var(--combobox-option-fz, var(--mantine-font-size-sm));\n  border-radius: var(--mantine-radius-default);\n  background-color: transparent;\n  color: inherit;\n  cursor: pointer;\n  word-break: break-word;\n}\n\n.m_92253aa5:where([data-combobox-selected]) {\n    background-color: var(--mantine-primary-color-filled);\n    color: var(--mantine-color-white);\n  }\n\n.m_92253aa5:where([data-combobox-disabled]) {\n    cursor: not-allowed;\n    opacity: 0.35;\n  }\n\n@media (hover: hover) {\n      :where([data-mantine-color-scheme='light']) .m_92253aa5:hover:where(:not([data-combobox-selected], [data-combobox-disabled])) {\n        background-color: var(--mantine-color-gray-0);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_92253aa5:hover:where(:not([data-combobox-selected], [data-combobox-disabled])) {\n        background-color: var(--mantine-color-dark-7);\n  }\n}\n\n@media (hover: none) {\n      :where([data-mantine-color-scheme='light']) .m_92253aa5:active:where(:not([data-combobox-selected], [data-combobox-disabled])) {\n        background-color: var(--mantine-color-gray-0);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_92253aa5:active:where(:not([data-combobox-selected], [data-combobox-disabled])) {\n        background-color: var(--mantine-color-dark-7);\n  }\n}\n\n.m_985517d8 {\n  margin-inline: calc(var(--combobox-padding) * -1);\n  margin-top: calc(var(--combobox-padding) * -1);\n  width: calc(100% + var(--combobox-padding) * 2);\n  border-top-width: 0;\n  border-inline-width: 0;\n  border-end-start-radius: 0;\n  border-end-end-radius: 0;\n  margin-bottom: var(--combobox-padding);\n  position: relative;\n}\n\n:where([data-mantine-color-scheme='light']) .m_985517d8, :where([data-mantine-color-scheme='light']) .m_985517d8:focus {\n      border-color: var(--mantine-color-gray-2);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_985517d8, :where([data-mantine-color-scheme='dark']) .m_985517d8:focus {\n      border-color: var(--mantine-color-dark-4);\n}\n\n:where([data-mantine-color-scheme='light']) .m_985517d8 {\n    background-color: var(--mantine-color-white);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_985517d8 {\n    background-color: var(--mantine-color-dark-7);\n}\n\n.m_2530cd1d {\n  font-size: var(--combobox-option-fz, var(--mantine-font-size-sm));\n  text-align: center;\n  padding: var(--combobox-option-padding);\n  color: var(--mantine-color-dimmed);\n}\n\n.m_858f94bd,\n.m_82b967cb {\n  font-size: var(--combobox-option-fz, var(--mantine-font-size-sm));\n  border: 0 solid transparent;\n  margin-inline: calc(var(--combobox-padding) * -1);\n  padding: var(--combobox-option-padding);\n}\n\n:where([data-mantine-color-scheme='light']) .m_858f94bd, :where([data-mantine-color-scheme='light']) .m_82b967cb {\n    border-color: var(--mantine-color-gray-2);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_858f94bd, :where([data-mantine-color-scheme='dark']) .m_82b967cb {\n    border-color: var(--mantine-color-dark-4);\n}\n\n.m_82b967cb {\n  border-top-width: calc(0.0625rem * var(--mantine-scale));\n  margin-top: var(--combobox-padding);\n  margin-bottom: calc(var(--combobox-padding) * -1);\n}\n\n.m_858f94bd {\n  border-bottom-width: calc(0.0625rem * var(--mantine-scale));\n  margin-bottom: var(--combobox-padding);\n  margin-top: calc(var(--combobox-padding) * -1);\n}\n\n.m_254f3e4f:has(.m_2bb2e9e5:only-child) {\n    display: none;\n  }\n\n.m_2bb2e9e5 {\n  color: var(--mantine-color-dimmed);\n  font-size: calc(var(--combobox-option-fz, var(--mantine-font-size-sm)) * 0.85);\n  padding: var(--combobox-option-padding);\n  font-weight: 500;\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.m_2bb2e9e5::after {\n    content: '';\n    flex: 1;\n    inset-inline: 0;\n    height: calc(0.0625rem * var(--mantine-scale));\n    margin-inline-start: var(--mantine-spacing-xs);\n  }\n\n:where([data-mantine-color-scheme='light']) .m_2bb2e9e5::after {\n      background-color: var(--mantine-color-gray-2);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_2bb2e9e5::after {\n      background-color: var(--mantine-color-dark-4);\n}\n\n.m_2bb2e9e5:only-child {\n    display: none;\n  }\n\n/* ------- Combobox.Chevron ------- */\n.m_2943220b {\n  --combobox-chevron-size-xs: calc(0.875rem * var(--mantine-scale));\n  --combobox-chevron-size-sm: calc(1.125rem * var(--mantine-scale));\n  --combobox-chevron-size-md: calc(1.25rem * var(--mantine-scale));\n  --combobox-chevron-size-lg: calc(1.5rem * var(--mantine-scale));\n  --combobox-chevron-size-xl: calc(1.75rem * var(--mantine-scale));\n  --combobox-chevron-size: var(--combobox-chevron-size-sm);\n}\n:where([data-mantine-color-scheme='light']) .m_2943220b {\n    --_combobox-chevron-color: var(--combobox-chevron-color, var(--mantine-color-gray-6));\n}\n:where([data-mantine-color-scheme='dark']) .m_2943220b {\n    --_combobox-chevron-color: var(--combobox-chevron-color, var(--mantine-color-dark-3));\n}\n.m_2943220b {\n\n  width: var(--combobox-chevron-size);\n  height: var(--combobox-chevron-size);\n  color: var(--_combobox-chevron-color);\n}\n.m_2943220b:where([data-error]) {\n    color: var(--combobox-chevron-color, var(--mantine-color-error));\n  }\n\n/* ------- OptionsDropdown ------- */\n.m_390b5f4 {\n  display: flex;\n  align-items: center;\n  gap: calc(0.5rem * var(--mantine-scale));\n}\n.m_390b5f4:where([data-reverse]) {\n    justify-content: space-between;\n  }\n\n.m_8ee53fc2 {\n  opacity: 0.4;\n  width: 0.8em;\n  min-width: 0.8em;\n  height: 0.8em;\n}\n\n:where([data-combobox-selected]) .m_8ee53fc2 {\n    opacity: 1;\n  }\n\n.m_5f75b09e {\n  --label-lh-xs: calc(1rem * var(--mantine-scale));\n  --label-lh-sm: calc(1.25rem * var(--mantine-scale));\n  --label-lh-md: calc(1.5rem * var(--mantine-scale));\n  --label-lh-lg: calc(1.875rem * var(--mantine-scale));\n  --label-lh-xl: calc(2.25rem * var(--mantine-scale));\n  --label-lh: var(--label-lh-sm);\n}\n\n  .m_5f75b09e[data-label-position='left'] {\n    --label-order: 1;\n    --label-offset-end: var(--mantine-spacing-sm);\n    --label-offset-start: 0;\n  }\n\n  .m_5f75b09e[data-label-position='right'] {\n    --label-order: 2;\n    --label-offset-end: 0;\n    --label-offset-start: var(--mantine-spacing-sm);\n  }\n\n.m_5f6e695e {\n  display: flex;\n}\n\n.m_d3ea56bb {\n  --label-cursor: var(--mantine-cursor-type);\n\n  -webkit-tap-highlight-color: transparent;\n  display: inline-flex;\n  flex-direction: column;\n  font-size: var(--label-fz, var(--mantine-font-size-sm));\n  line-height: var(--label-lh);\n  cursor: var(--label-cursor);\n  order: var(--label-order);\n}\n\nfieldset:disabled .m_d3ea56bb,\n  .m_d3ea56bb[data-disabled] {\n    --label-cursor: not-allowed;\n  }\n\n.m_8ee546b8 {\n  cursor: var(--label-cursor);\n  color: inherit;\n  padding-inline-start: var(--label-offset-start);\n  padding-inline-end: var(--label-offset-end);\n}\n\nfieldset:disabled .m_8ee546b8,\n  .m_8ee546b8:where([data-disabled]) {\n    color: var(--mantine-color-disabled-color);\n  }\n\n.m_328f68c0 {\n  margin-top: calc(var(--mantine-spacing-xs) / 2);\n  padding-inline-start: var(--label-offset-start);\n  padding-inline-end: var(--label-offset-end);\n}\n\n.m_8e8a99cc {\n  margin-top: calc(var(--mantine-spacing-xs) / 2);\n  padding-inline-start: var(--label-offset-start);\n  padding-inline-end: var(--label-offset-end);\n}\n\n.m_26775b0a {\n  --card-radius: var(--mantine-radius-default);\n\n  display: block;\n  width: 100%;\n  border-radius: var(--card-radius);\n  cursor: pointer;\n}\n\n  .m_26775b0a :where(*) {\n    cursor: inherit;\n  }\n\n  .m_26775b0a:where([data-with-border]) {\n    border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  }\n\n  :where([data-mantine-color-scheme='light']) .m_26775b0a:where([data-with-border]) {\n      border-color: var(--mantine-color-gray-3);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_26775b0a:where([data-with-border]) {\n      border-color: var(--mantine-color-dark-4);\n}\n\n.m_5e5256ee {\n  --checkbox-size-xs: calc(1rem * var(--mantine-scale));\n  --checkbox-size-sm: calc(1.25rem * var(--mantine-scale));\n  --checkbox-size-md: calc(1.5rem * var(--mantine-scale));\n  --checkbox-size-lg: calc(1.875rem * var(--mantine-scale));\n  --checkbox-size-xl: calc(2.25rem * var(--mantine-scale));\n\n  --checkbox-size: var(--checkbox-size-sm);\n  --checkbox-color: var(--mantine-primary-color-filled);\n  --checkbox-icon-color: var(--mantine-color-white);\n\n  position: relative;\n  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  width: var(--checkbox-size);\n  min-width: var(--checkbox-size);\n  height: var(--checkbox-size);\n  min-height: var(--checkbox-size);\n  border-radius: var(--checkbox-radius, var(--mantine-radius-default));\n  transition:\n    border-color 100ms ease,\n    background-color 100ms ease;\n  cursor: var(--mantine-cursor-type);\n  -webkit-tap-highlight-color: transparent;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n  :where([data-mantine-color-scheme='light']) .m_5e5256ee {\n    background-color: var(--mantine-color-white);\n    border-color: var(--mantine-color-gray-4);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_5e5256ee {\n    background-color: var(--mantine-color-dark-6);\n    border-color: var(--mantine-color-dark-4);\n}\n\n  .m_5e5256ee[data-indeterminate],\n  .m_5e5256ee[data-checked] {\n    background-color: var(--checkbox-color);\n    border-color: var(--checkbox-color);\n  }\n\n  .m_5e5256ee[data-indeterminate] > .m_1b1c543a, .m_5e5256ee[data-checked] > .m_1b1c543a {\n      opacity: 1;\n      transform: none;\n      color: var(--checkbox-icon-color);\n    }\n\n  .m_5e5256ee[data-disabled] {\n    cursor: not-allowed;\n    border-color: var(--mantine-color-disabled-border);\n    background-color: var(--mantine-color-disabled);\n  }\n\n  [data-mantine-color-scheme='light'] .m_5e5256ee[data-disabled][data-checked] > .m_1b1c543a {\n        color: var(--mantine-color-gray-5);\n}\n\n  [data-mantine-color-scheme='dark'] .m_5e5256ee[data-disabled][data-checked] > .m_1b1c543a {\n        color: var(--mantine-color-dark-3);\n}\n\n.m_76e20374[data-indeterminate]:not([data-disabled]),\n  .m_76e20374[data-checked]:not([data-disabled]) {\n    background-color: transparent;\n    border-color: var(--checkbox-color);\n  }\n\n.m_76e20374[data-indeterminate]:not([data-disabled]) > .m_1b1c543a, .m_76e20374[data-checked]:not([data-disabled]) > .m_1b1c543a {\n      color: var(--checkbox-color);\n      opacity: 1;\n      transform: none;\n    }\n\n.m_1b1c543a {\n  display: block;\n  width: 60%;\n  color: transparent;\n  pointer-events: none;\n  transform: translateY(calc(0.3125rem * var(--mantine-scale))) scale(0.5);\n  opacity: 1;\n  transition:\n    transform 100ms ease,\n    opacity 100ms ease;\n}\n\n.m_bf2d988c {\n  --checkbox-size-xs: calc(1rem * var(--mantine-scale));\n  --checkbox-size-sm: calc(1.25rem * var(--mantine-scale));\n  --checkbox-size-md: calc(1.5rem * var(--mantine-scale));\n  --checkbox-size-lg: calc(1.875rem * var(--mantine-scale));\n  --checkbox-size-xl: calc(2.25rem * var(--mantine-scale));\n\n  --checkbox-size: var(--checkbox-size-sm);\n  --checkbox-color: var(--mantine-primary-color-filled);\n  --checkbox-icon-color: var(--mantine-color-white);\n}\n\n.m_26062bec {\n  position: relative;\n  width: var(--checkbox-size);\n  height: var(--checkbox-size);\n  order: 1;\n}\n\n.m_26062bec:where([data-label-position='left']) {\n    order: 2;\n  }\n\n.m_26063560 {\n  appearance: none;\n  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  width: var(--checkbox-size);\n  height: var(--checkbox-size);\n  border-radius: var(--checkbox-radius, var(--mantine-radius-default));\n  padding: 0;\n  display: block;\n  margin: 0;\n  transition:\n    border-color 100ms ease,\n    background-color 100ms ease;\n  cursor: var(--mantine-cursor-type);\n  -webkit-tap-highlight-color: transparent;\n}\n\n:where([data-mantine-color-scheme='light']) .m_26063560 {\n    background-color: var(--mantine-color-white);\n    border-color: var(--mantine-color-gray-4);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_26063560 {\n    background-color: var(--mantine-color-dark-6);\n    border-color: var(--mantine-color-dark-4);\n}\n\n.m_26063560:where([data-error]) {\n    border-color: var(--mantine-color-error);\n  }\n\n.m_26063560[data-indeterminate],\n  .m_26063560:checked {\n    background-color: var(--checkbox-color);\n    border-color: var(--checkbox-color);\n  }\n\n.m_26063560[data-indeterminate] + .m_bf295423, .m_26063560:checked + .m_bf295423 {\n      opacity: 1;\n      transform: none;\n    }\n\n.m_26063560:disabled {\n    cursor: not-allowed;\n    border-color: var(--mantine-color-disabled-border);\n    background-color: var(--mantine-color-disabled);\n  }\n\n.m_26063560:disabled + .m_bf295423 {\n      color: var(--mantine-color-disabled-color);\n    }\n\n.m_215c4542 + .m_bf295423 {\n    color: var(--checkbox-color);\n  }\n\n.m_215c4542[data-indeterminate]:not(:disabled),\n  .m_215c4542:checked:not(:disabled) {\n    background-color: transparent;\n    border-color: var(--checkbox-color);\n  }\n\n.m_215c4542[data-indeterminate]:not(:disabled) + .m_bf295423, .m_215c4542:checked:not(:disabled) + .m_bf295423 {\n      color: var(--checkbox-color);\n      opacity: 1;\n      transform: none;\n    }\n\n.m_bf295423 {\n  position: absolute;\n  inset: 0;\n  width: 60%;\n  margin: auto;\n  color: var(--checkbox-icon-color);\n  pointer-events: none;\n  transform: translateY(calc(0.3125rem * var(--mantine-scale))) scale(0.5);\n  opacity: 0;\n  transition:\n    transform 100ms ease,\n    opacity 100ms ease;\n}\n\n/* Avatar.Group root element */\n.m_11def92b {\n  --ag-spacing: var(--mantine-spacing-sm);\n  --ag-offset: calc(var(--ag-spacing) * -1);\n\n  display: flex;\n  padding-inline-start: var(--ag-spacing);\n}\n\n/* Avatar root element */\n.m_f85678b6 {\n  --avatar-size-xs: calc(1rem * var(--mantine-scale));\n  --avatar-size-sm: calc(1.625rem * var(--mantine-scale));\n  --avatar-size-md: calc(2.375rem * var(--mantine-scale));\n  --avatar-size-lg: calc(3.5rem * var(--mantine-scale));\n  --avatar-size-xl: calc(5.25rem * var(--mantine-scale));\n\n  --avatar-size: var(--avatar-size-md);\n  --avatar-radius: calc(62.5rem * var(--mantine-scale));\n  --avatar-bg: var(--mantine-color-gray-light);\n  --avatar-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  --avatar-color: var(--mantine-color-gray-light-color);\n  --avatar-placeholder-fz: calc(var(--avatar-size) / 2.5);\n\n  -webkit-tap-highlight-color: transparent;\n  position: relative;\n  display: block;\n  user-select: none;\n  overflow: hidden;\n  border-radius: var(--avatar-radius);\n  text-decoration: none;\n  padding: 0;\n  width: var(--avatar-size);\n  height: var(--avatar-size);\n  min-width: var(--avatar-size);\n}\n.m_f85678b6:where([data-within-group]) {\n    margin-inline-start: var(--ag-offset);\n    border: 2px solid var(--mantine-color-body);\n    background: var(--mantine-color-body);\n  }\n\n.m_11f8ac07 {\n  object-fit: cover;\n  width: 100%;\n  height: 100%;\n  display: block;\n}\n\n.m_104cd71f {\n  font-weight: 700;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  user-select: none;\n  border-radius: var(--avatar-radius);\n  font-size: var(--avatar-placeholder-fz);\n  background: var(--avatar-bg);\n  border: var(--avatar-bd);\n  color: var(--avatar-color);\n}\n\n.m_104cd71f > [data-avatar-placeholder-icon] {\n    width: 70%;\n    height: 70%;\n  }\n\n.m_2ce0de02 {\n  background-size: cover;\n  background-position: center;\n  display: block;\n  width: 100%;\n  border: 0;\n  text-decoration: none;\n  border-radius: var(--bi-radius, 0);\n}\n\n.m_347db0ec {\n  --badge-height-xs: calc(1rem * var(--mantine-scale));\n  --badge-height-sm: calc(1.125rem * var(--mantine-scale));\n  --badge-height-md: calc(1.25rem * var(--mantine-scale));\n  --badge-height-lg: calc(1.625rem * var(--mantine-scale));\n  --badge-height-xl: calc(2rem * var(--mantine-scale));\n\n  --badge-fz-xs: calc(0.5625rem * var(--mantine-scale));\n  --badge-fz-sm: calc(0.625rem * var(--mantine-scale));\n  --badge-fz-md: calc(0.6875rem * var(--mantine-scale));\n  --badge-fz-lg: calc(0.8125rem * var(--mantine-scale));\n  --badge-fz-xl: calc(1rem * var(--mantine-scale));\n\n  --badge-padding-x-xs: calc(0.375rem * var(--mantine-scale));\n  --badge-padding-x-sm: calc(0.5rem * var(--mantine-scale));\n  --badge-padding-x-md: calc(0.625rem * var(--mantine-scale));\n  --badge-padding-x-lg: calc(0.75rem * var(--mantine-scale));\n  --badge-padding-x-xl: calc(1rem * var(--mantine-scale));\n\n  --badge-height: var(--badge-height-md);\n  --badge-fz: var(--badge-fz-md);\n  --badge-padding-x: var(--badge-padding-x-md);\n  --badge-radius: calc(62.5rem * var(--mantine-scale));\n  --badge-lh: calc(var(--badge-height) - calc(0.125rem * var(--mantine-scale)));\n  --badge-color: var(--mantine-color-white);\n  --badge-bg: var(--mantine-primary-color-filled);\n  --badge-border-width: calc(0.0625rem * var(--mantine-scale));\n  --badge-bd: var(--badge-border-width) solid transparent;\n\n  -webkit-tap-highlight-color: transparent;\n  font-size: var(--badge-fz);\n  border-radius: var(--badge-radius);\n  height: var(--badge-height);\n  line-height: var(--badge-lh);\n  text-decoration: none;\n  padding: 0 var(--badge-padding-x);\n  display: inline-grid;\n  align-items: center;\n  justify-content: center;\n  width: fit-content;\n  text-transform: uppercase;\n  font-weight: 700;\n  letter-spacing: calc(0.015625rem * var(--mantine-scale));\n  cursor: default;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  color: var(--badge-color);\n  background: var(--badge-bg);\n  border: var(--badge-bd);\n}\n\n  .m_347db0ec:where([data-with-left-section], [data-variant='dot']) {\n    grid-template-columns: auto 1fr;\n  }\n\n  .m_347db0ec:where([data-with-right-section]) {\n    grid-template-columns: 1fr auto;\n  }\n\n  .m_347db0ec:where(\n      [data-with-left-section][data-with-right-section],\n      [data-variant='dot'][data-with-right-section]\n    ) {\n    grid-template-columns: auto 1fr auto;\n  }\n\n  .m_347db0ec:where([data-block]) {\n    display: flex;\n    width: 100%;\n  }\n\n  .m_347db0ec:where([data-circle]) {\n    padding-inline: calc(0.125rem * var(--mantine-scale));\n    display: flex;\n    width: var(--badge-height);\n  }\n\n.m_fbd81e3d {\n  --badge-dot-size: calc(var(--badge-height) / 3.4);\n}\n\n:where([data-mantine-color-scheme='light']) .m_fbd81e3d {\n    background-color: var(--mantine-color-white);\n    border-color: var(--mantine-color-gray-4);\n    color: var(--mantine-color-black);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_fbd81e3d {\n    background-color: var(--mantine-color-dark-5);\n    border-color: var(--mantine-color-dark-5);\n    color: var(--mantine-color-white);\n}\n\n.m_fbd81e3d::before {\n    content: '';\n    display: block;\n    width: var(--badge-dot-size);\n    height: var(--badge-dot-size);\n    border-radius: var(--badge-dot-size);\n    background-color: var(--badge-dot-color);\n    margin-inline-end: var(--badge-dot-size);\n  }\n\n.m_5add502a {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  text-align: center;\n  cursor: inherit;\n}\n\n.m_91fdda9b {\n  --badge-section-margin: calc(var(--mantine-spacing-xs) / 2);\n\n  display: inline-flex;\n  justify-content: center;\n  align-items: center;\n  max-height: calc(var(--badge-height) - var(--badge-border-width) * 2);\n}\n\n.m_91fdda9b:where([data-position='left']) {\n    margin-inline-end: var(--badge-section-margin);\n  }\n\n.m_91fdda9b:where([data-position='right']) {\n    margin-inline-start: var(--badge-section-margin);\n  }\n\n.m_ddec01c0 {\n  --blockquote-border: 3px solid var(--bq-bd);\n\n  position: relative;\n  margin: 0;\n  border-inline-start: var(--blockquote-border);\n  border-start-end-radius: var(--bq-radius);\n  border-end-end-radius: var(--bq-radius);\n  padding: var(--mantine-spacing-xl) calc(2.375rem * var(--mantine-scale));\n}\n\n  :where([data-mantine-color-scheme='light']) .m_ddec01c0 {\n    background-color: var(--bq-bg-light);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_ddec01c0 {\n    background-color: var(--bq-bg-dark);\n}\n\n.m_dde7bd57 {\n  --blockquote-icon-offset: calc(var(--bq-icon-size) / -2);\n\n  position: absolute;\n  color: var(--bq-bd);\n  background-color: var(--mantine-color-body);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  top: var(--blockquote-icon-offset);\n  inset-inline-start: var(--blockquote-icon-offset);\n  width: var(--bq-icon-size);\n  height: var(--bq-icon-size);\n  border-radius: var(--bq-icon-size);\n}\n\n.m_dde51a35 {\n  display: block;\n  margin-top: var(--mantine-spacing-md);\n  opacity: 0.6;\n  font-size: 85%;\n}\n\n.m_8b3717df {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.m_f678d540 {\n  line-height: 1;\n  white-space: nowrap;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.m_3b8f2208 {\n  margin-inline: var(--bc-separator-margin, var(--mantine-spacing-xs));\n  line-height: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n:where([data-mantine-color-scheme='light']) .m_3b8f2208 {\n    color: var(--mantine-color-gray-7);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_3b8f2208 {\n    color: var(--mantine-color-dark-2);\n}\n\n.m_fea6bf1a {\n  --burger-size-xs: calc(0.75rem * var(--mantine-scale));\n  --burger-size-sm: calc(1.125rem * var(--mantine-scale));\n  --burger-size-md: calc(1.5rem * var(--mantine-scale));\n  --burger-size-lg: calc(2.125rem * var(--mantine-scale));\n  --burger-size-xl: calc(2.625rem * var(--mantine-scale));\n\n  --burger-size: var(--burger-size-md);\n  --burger-line-size: calc(var(--burger-size) / 12);\n\n  width: calc(var(--burger-size) + var(--mantine-spacing-xs));\n  height: calc(var(--burger-size) + var(--mantine-spacing-xs));\n  padding: calc(var(--mantine-spacing-xs) / 2);\n  cursor: pointer;\n}\n\n  :where([data-mantine-color-scheme='light']) .m_fea6bf1a {\n    --burger-color: var(--mantine-color-black);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_fea6bf1a {\n    --burger-color: var(--mantine-color-white);\n}\n\n.m_d4fb9cad {\n  position: relative;\n  user-select: none;\n}\n\n.m_d4fb9cad,\n  .m_d4fb9cad::before,\n  .m_d4fb9cad::after {\n    display: block;\n    width: var(--burger-size);\n    height: var(--burger-line-size);\n    background-color: var(--burger-color);\n    outline: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n    transition-property: background-color, transform;\n    transition-duration: var(--burger-transition-duration, 300ms);\n    transition-timing-function: var(--burger-transition-timing-function, ease);\n  }\n\n.m_d4fb9cad::before,\n  .m_d4fb9cad::after {\n    position: absolute;\n    content: '';\n    inset-inline-start: 0;\n  }\n\n.m_d4fb9cad::before {\n    top: calc(var(--burger-size) / -3);\n  }\n\n.m_d4fb9cad::after {\n    top: calc(var(--burger-size) / 3);\n  }\n\n.m_d4fb9cad[data-opened] {\n    background-color: transparent;\n  }\n\n.m_d4fb9cad[data-opened]::before {\n      transform: translateY(calc(var(--burger-size) / 3)) rotate(45deg);\n    }\n\n.m_d4fb9cad[data-opened]::after {\n      transform: translateY(calc(var(--burger-size) / -3)) rotate(-45deg);\n    }\n\n.m_77c9d27d {\n  --button-height-xs: calc(1.875rem * var(--mantine-scale));\n  --button-height-sm: calc(2.25rem * var(--mantine-scale));\n  --button-height-md: calc(2.625rem * var(--mantine-scale));\n  --button-height-lg: calc(3.125rem * var(--mantine-scale));\n  --button-height-xl: calc(3.75rem * var(--mantine-scale));\n\n  --button-height-compact-xs: calc(1.375rem * var(--mantine-scale));\n  --button-height-compact-sm: calc(1.625rem * var(--mantine-scale));\n  --button-height-compact-md: calc(1.875rem * var(--mantine-scale));\n  --button-height-compact-lg: calc(2.125rem * var(--mantine-scale));\n  --button-height-compact-xl: calc(2.5rem * var(--mantine-scale));\n\n  --button-padding-x-xs: calc(0.875rem * var(--mantine-scale));\n  --button-padding-x-sm: calc(1.125rem * var(--mantine-scale));\n  --button-padding-x-md: calc(1.375rem * var(--mantine-scale));\n  --button-padding-x-lg: calc(1.625rem * var(--mantine-scale));\n  --button-padding-x-xl: calc(2rem * var(--mantine-scale));\n\n  --button-padding-x-compact-xs: calc(0.4375rem * var(--mantine-scale));\n  --button-padding-x-compact-sm: calc(0.5rem * var(--mantine-scale));\n  --button-padding-x-compact-md: calc(0.625rem * var(--mantine-scale));\n  --button-padding-x-compact-lg: calc(0.75rem * var(--mantine-scale));\n  --button-padding-x-compact-xl: calc(0.875rem * var(--mantine-scale));\n\n  --button-height: var(--button-height-sm);\n  --button-padding-x: var(--button-padding-x-sm);\n  --button-color: var(--mantine-color-white);\n\n  user-select: none;\n  font-weight: 600;\n  position: relative;\n  line-height: 1;\n  text-align: center;\n  overflow: hidden;\n\n  width: auto;\n  cursor: pointer;\n  display: inline-block;\n  border-radius: var(--button-radius, var(--mantine-radius-default));\n  font-size: var(--button-fz, var(--mantine-font-size-sm));\n  background: var(--button-bg, var(--mantine-primary-color-filled));\n  border: var(--button-bd, calc(0.0625rem * var(--mantine-scale)) solid transparent);\n  color: var(--button-color, var(--mantine-color-white));\n  height: var(--button-height, var(--button-height-sm));\n  padding-inline: var(--button-padding-x, var(--button-padding-x-sm));\n  vertical-align: middle;\n}\n\n  .m_77c9d27d:where([data-block]) {\n    display: block;\n    width: 100%;\n  }\n\n  .m_77c9d27d:where([data-with-left-section]) {\n    padding-inline-start: calc(var(--button-padding-x) / 1.5);\n  }\n\n  .m_77c9d27d:where([data-with-right-section]) {\n    padding-inline-end: calc(var(--button-padding-x) / 1.5);\n  }\n\n  .m_77c9d27d:where(:disabled:not([data-loading]), [data-disabled]:not([data-loading])) {\n    cursor: not-allowed;\n    border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n    transform: none;\n    color: var(--mantine-color-disabled-color);\n    background: var(--mantine-color-disabled);\n  }\n\n  .m_77c9d27d::before {\n    content: '';\n    pointer-events: none;\n    position: absolute;\n    inset: calc(-0.0625rem * var(--mantine-scale));\n    border-radius: var(--button-radius, var(--mantine-radius-default));\n    transform: translateY(-100%);\n    opacity: 0;\n    filter: blur(12px);\n    transition:\n      transform 150ms ease,\n      opacity 100ms ease;\n  }\n\n  :where([data-mantine-color-scheme='light']) .m_77c9d27d::before {\n      background-color: rgba(255, 255, 255, 0.15);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_77c9d27d::before {\n      background-color: rgba(0, 0, 0, 0.15);\n}\n\n  .m_77c9d27d:where([data-loading]) {\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  .m_77c9d27d:where([data-loading])::before {\n      transform: translateY(0);\n      opacity: 1;\n    }\n\n  .m_77c9d27d:where([data-loading]) .m_80f1301b {\n      opacity: 0;\n      transform: translateY(100%);\n    }\n\n  @media (hover: hover) {\n    .m_77c9d27d:hover:where(:not([data-loading], :disabled, [data-disabled])) {\n      background-color: var(--button-hover, var(--mantine-primary-color-filled-hover));\n      color: var(--button-hover-color, var(--button-color));\n    }\n}\n\n  @media (hover: none) {\n    .m_77c9d27d:active:where(:not([data-loading], :disabled, [data-disabled])) {\n      background-color: var(--button-hover, var(--mantine-primary-color-filled-hover));\n      color: var(--button-hover-color, var(--button-color));\n    }\n}\n\n.m_80f1301b {\n  display: flex;\n  align-items: center;\n  justify-content: var(--button-justify, center);\n  height: 100%;\n  overflow: visible;\n  transition:\n    transform 150ms ease,\n    opacity 100ms ease;\n}\n\n.m_811560b9 {\n  white-space: nowrap;\n  height: 100%;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  opacity: 1;\n}\n\n.m_811560b9:where([data-loading]) {\n    opacity: 0.2;\n  }\n\n.m_a74036a {\n  display: flex;\n  align-items: center;\n}\n\n.m_a74036a:where([data-position='left']) {\n    margin-inline-end: var(--mantine-spacing-xs);\n  }\n\n.m_a74036a:where([data-position='right']) {\n    margin-inline-start: var(--mantine-spacing-xs);\n  }\n\n.m_a25b86ee {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n}\n\n.m_80d6d844 {\n  --button-border-width: calc(0.0625rem * var(--mantine-scale));\n  display: flex;\n}\n\n.m_80d6d844 :where(.m_77c9d27d):focus {\n      position: relative;\n      z-index: 1;\n    }\n\n.m_80d6d844[data-orientation='horizontal'] {\n    flex-direction: row;\n  }\n\n.m_80d6d844[data-orientation='horizontal'] .m_77c9d27d:not(:only-child):first-child, .m_80d6d844[data-orientation='horizontal'] .m_70be2a01:not(:only-child):first-child {\n        border-end-end-radius: 0;\n        border-start-end-radius: 0;\n        border-inline-end-width: calc(var(--button-border-width) / 2);\n      }\n\n.m_80d6d844[data-orientation='horizontal'] .m_77c9d27d:not(:only-child):last-child, .m_80d6d844[data-orientation='horizontal'] .m_70be2a01:not(:only-child):last-child {\n        border-end-start-radius: 0;\n        border-start-start-radius: 0;\n        border-inline-start-width: calc(var(--button-border-width) / 2);\n      }\n\n.m_80d6d844[data-orientation='horizontal'] .m_77c9d27d:not(:only-child):not(:first-child):not(:last-child), .m_80d6d844[data-orientation='horizontal'] .m_70be2a01:not(:only-child):not(:first-child):not(:last-child) {\n        border-radius: 0;\n        border-inline-width: calc(var(--button-border-width) / 2);\n      }\n\n.m_80d6d844[data-orientation='vertical'] {\n    flex-direction: column;\n  }\n\n.m_80d6d844[data-orientation='vertical'] .m_77c9d27d:not(:only-child):first-child, .m_80d6d844[data-orientation='vertical'] .m_70be2a01:not(:only-child):first-child {\n        border-end-start-radius: 0;\n        border-end-end-radius: 0;\n        border-bottom-width: calc(var(--button-border-width) / 2);\n      }\n\n.m_80d6d844[data-orientation='vertical'] .m_77c9d27d:not(:only-child):last-child, .m_80d6d844[data-orientation='vertical'] .m_70be2a01:not(:only-child):last-child {\n        border-start-start-radius: 0;\n        border-start-end-radius: 0;\n        border-top-width: calc(var(--button-border-width) / 2);\n      }\n\n.m_80d6d844[data-orientation='vertical'] .m_77c9d27d:not(:only-child):not(:first-child):not(:last-child), .m_80d6d844[data-orientation='vertical'] .m_70be2a01:not(:only-child):not(:first-child):not(:last-child) {\n        border-radius: 0;\n        border-bottom-width: calc(var(--button-border-width) / 2);\n        border-top-width: calc(var(--button-border-width) / 2);\n      }\n\n.m_70be2a01 {\n  --section-height-xs: calc(1.875rem * var(--mantine-scale));\n  --section-height-sm: calc(2.25rem * var(--mantine-scale));\n  --section-height-md: calc(2.625rem * var(--mantine-scale));\n  --section-height-lg: calc(3.125rem * var(--mantine-scale));\n  --section-height-xl: calc(3.75rem * var(--mantine-scale));\n\n  --section-height-compact-xs: calc(1.375rem * var(--mantine-scale));\n  --section-height-compact-sm: calc(1.625rem * var(--mantine-scale));\n  --section-height-compact-md: calc(1.875rem * var(--mantine-scale));\n  --section-height-compact-lg: calc(2.125rem * var(--mantine-scale));\n  --section-height-compact-xl: calc(2.5rem * var(--mantine-scale));\n\n  --section-padding-x-xs: calc(0.875rem * var(--mantine-scale));\n  --section-padding-x-sm: calc(1.125rem * var(--mantine-scale));\n  --section-padding-x-md: calc(1.375rem * var(--mantine-scale));\n  --section-padding-x-lg: calc(1.625rem * var(--mantine-scale));\n  --section-padding-x-xl: calc(2rem * var(--mantine-scale));\n\n  --section-padding-x-compact-xs: calc(0.4375rem * var(--mantine-scale));\n  --section-padding-x-compact-sm: calc(0.5rem * var(--mantine-scale));\n  --section-padding-x-compact-md: calc(0.625rem * var(--mantine-scale));\n  --section-padding-x-compact-lg: calc(0.75rem * var(--mantine-scale));\n  --section-padding-x-compact-xl: calc(0.875rem * var(--mantine-scale));\n\n  --section-height: var(--section-height-sm);\n  --section-padding-x: var(--section-padding-x-sm);\n  --section-color: var(--mantine-color-white);\n\n  font-weight: 600;\n  width: auto;\n  border-radius: var(--section-radius, var(--mantine-radius-default));\n  font-size: var(--section-fz, var(--mantine-font-size-sm));\n  background: var(--section-bg, var(--mantine-primary-color-filled));\n  border: var(--section-bd, calc(0.0625rem * var(--mantine-scale)) solid transparent);\n  color: var(--section-color, var(--mantine-color-white));\n  height: var(--section-height, var(--section-height-sm));\n  padding-inline: var(--section-padding-x, var(--section-padding-x-sm));\n  vertical-align: middle;\n  line-height: 1;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.m_e615b15f {\n  --card-padding: var(--mantine-spacing-md);\n\n  position: relative;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  padding: var(--card-padding);\n  color: var(--mantine-color-text);\n}\n\n  :where([data-mantine-color-scheme='light']) .m_e615b15f {\n    background-color: var(--mantine-color-white);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_e615b15f {\n    background-color: var(--mantine-color-dark-6);\n}\n\n.m_599a2148 {\n  display: block;\n  margin-inline: calc(var(--card-padding) * -1);\n}\n\n.m_599a2148:where(:first-child) {\n    margin-top: calc(var(--card-padding) * -1);\n    border-top: none !important;\n  }\n\n.m_599a2148:where(:last-child) {\n    margin-bottom: calc(var(--card-padding) * -1);\n    border-bottom: none !important;\n  }\n\n.m_599a2148:where([data-inherit-padding]) {\n    padding-inline: var(--card-padding);\n  }\n\n.m_599a2148:where([data-with-border]) {\n    border-top: calc(0.0625rem * var(--mantine-scale)) solid;\n    border-bottom: calc(0.0625rem * var(--mantine-scale)) solid;\n  }\n\n:where([data-mantine-color-scheme='light']) .m_599a2148 {\n    border-color: var(--mantine-color-gray-3);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_599a2148 {\n    border-color: var(--mantine-color-dark-4);\n}\n\n.m_599a2148 + .m_599a2148 {\n    border-top: none !important;\n  }\n\n.m_4451eb3a {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n  .m_4451eb3a:where([data-inline]) {\n    display: inline-flex;\n  }\n\n.m_f59ffda3 {\n  --chip-size-xs: calc(1.4375rem * var(--mantine-scale));\n  --chip-size-sm: calc(1.75rem * var(--mantine-scale));\n  --chip-size-md: calc(2rem * var(--mantine-scale));\n  --chip-size-lg: calc(2.25rem * var(--mantine-scale));\n  --chip-size-xl: calc(2.5rem * var(--mantine-scale));\n\n  --chip-icon-size-xs: calc(0.625rem * var(--mantine-scale));\n  --chip-icon-size-sm: calc(0.75rem * var(--mantine-scale));\n  --chip-icon-size-md: calc(0.875rem * var(--mantine-scale));\n  --chip-icon-size-lg: calc(1rem * var(--mantine-scale));\n  --chip-icon-size-xl: calc(1.125rem * var(--mantine-scale));\n\n  --chip-padding-xs: calc(1rem * var(--mantine-scale));\n  --chip-padding-sm: calc(1.25rem * var(--mantine-scale));\n  --chip-padding-md: calc(1.5rem * var(--mantine-scale));\n  --chip-padding-lg: calc(1.75rem * var(--mantine-scale));\n  --chip-padding-xl: calc(2rem * var(--mantine-scale));\n\n  --chip-checked-padding-xs: calc(0.46875rem * var(--mantine-scale));\n  --chip-checked-padding-sm: calc(0.625rem * var(--mantine-scale));\n  --chip-checked-padding-md: calc(0.73125rem * var(--mantine-scale));\n  --chip-checked-padding-lg: calc(0.84375rem * var(--mantine-scale));\n  --chip-checked-padding-xl: calc(0.98125rem * var(--mantine-scale));\n\n  --chip-spacing-xs: calc(0.625rem * var(--mantine-scale));\n  --chip-spacing-sm: calc(0.75rem * var(--mantine-scale));\n  --chip-spacing-md: calc(1rem * var(--mantine-scale));\n  --chip-spacing-lg: calc(1.25rem * var(--mantine-scale));\n  --chip-spacing-xl: calc(1.375rem * var(--mantine-scale));\n\n  --chip-size: var(--chip-size-sm);\n  --chip-icon-size: var(--chip-icon-size-sm);\n  --chip-padding: var(--chip-padding-sm);\n  --chip-spacing: var(--chip-spacing-sm);\n  --chip-checked-padding: var(--chip-checked-padding-sm);\n  --chip-bg: var(--mantine-primary-color-filled);\n  --chip-hover: var(--mantine-primary-color-filled-hover);\n  --chip-color: var(--mantine-color-white);\n  --chip-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n}\n\n.m_be049a53 {\n  display: inline-flex;\n  align-items: center;\n  user-select: none;\n  border-radius: var(--chip-radius, 1000rem);\n  height: var(--chip-size);\n  font-size: var(--chip-fz, var(--mantine-font-size-sm));\n  line-height: calc(var(--chip-size) - calc(0.125rem * var(--mantine-scale)));\n  padding-inline: var(--chip-padding);\n  cursor: pointer;\n  white-space: nowrap;\n  -webkit-tap-highlight-color: transparent;\n  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  color: var(--mantine-color-text);\n}\n\n.m_be049a53:where([data-checked]) {\n    padding: var(--chip-checked-padding);\n  }\n\n.m_be049a53:where([data-disabled]) {\n    cursor: not-allowed;\n    background-color: var(--mantine-color-disabled);\n    color: var(--mantine-color-disabled-color);\n  }\n\n:where([data-mantine-color-scheme='light']) .m_3904c1af:not([data-disabled]) {\n    background-color: var(--mantine-color-white);\n    border: 1px solid var(--mantine-color-gray-3);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_3904c1af:not([data-disabled]) {\n    background-color: var(--mantine-color-dark-6);\n    border: 1px solid var(--mantine-color-dark-4);\n}\n\n@media (hover: hover) {\n    :where([data-mantine-color-scheme='light']) .m_3904c1af:not([data-disabled]):hover {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_3904c1af:not([data-disabled]):hover {\n      background-color: var(--mantine-color-dark-5);\n  }\n}\n\n@media (hover: none) {\n    :where([data-mantine-color-scheme='light']) .m_3904c1af:not([data-disabled]):active {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_3904c1af:not([data-disabled]):active {\n      background-color: var(--mantine-color-dark-5);\n  }\n}\n\n.m_3904c1af:not([data-disabled]):where([data-checked]) {\n    --chip-icon-color: var(--chip-color);\n    border: var(--chip-bd);\n  }\n\n@media (hover: hover) {\n\n  .m_3904c1af:not([data-disabled]):where([data-checked]):hover {\n      background-color: var(--chip-hover);\n  }\n}\n\n@media (hover: none) {\n\n  .m_3904c1af:not([data-disabled]):where([data-checked]):active {\n      background-color: var(--chip-hover);\n  }\n}\n\n.m_fa109255:not([data-disabled]),\n.m_f7e165c3:not([data-disabled]) {\n  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  color: var(--mantine-color-text);\n}\n\n:where([data-mantine-color-scheme='light']) .m_fa109255:not([data-disabled]), :where([data-mantine-color-scheme='light']) .m_f7e165c3:not([data-disabled]) {\n    background-color: var(--mantine-color-gray-1);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_fa109255:not([data-disabled]), :where([data-mantine-color-scheme='dark']) .m_f7e165c3:not([data-disabled]) {\n    background-color: var(--mantine-color-dark-5);\n}\n\n@media (hover: hover) {\n    :where([data-mantine-color-scheme='light']) .m_fa109255:not([data-disabled]):hover, :where([data-mantine-color-scheme='light']) .m_f7e165c3:not([data-disabled]):hover {\n      background-color: var(--mantine-color-gray-2);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_fa109255:not([data-disabled]):hover, :where([data-mantine-color-scheme='dark']) .m_f7e165c3:not([data-disabled]):hover {\n      background-color: var(--mantine-color-dark-4);\n  }\n}\n\n@media (hover: none) {\n    :where([data-mantine-color-scheme='light']) .m_fa109255:not([data-disabled]):active, :where([data-mantine-color-scheme='light']) .m_f7e165c3:not([data-disabled]):active {\n      background-color: var(--mantine-color-gray-2);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_fa109255:not([data-disabled]):active, :where([data-mantine-color-scheme='dark']) .m_f7e165c3:not([data-disabled]):active {\n      background-color: var(--mantine-color-dark-4);\n  }\n}\n\n.m_fa109255:not([data-disabled]):where([data-checked]), .m_f7e165c3:not([data-disabled]):where([data-checked]) {\n    --chip-icon-color: var(--chip-color);\n    color: var(--chip-color);\n    background-color: var(--chip-bg);\n  }\n\n@media (hover: hover) {\n\n  .m_fa109255:not([data-disabled]):where([data-checked]):hover, .m_f7e165c3:not([data-disabled]):where([data-checked]):hover {\n      background-color: var(--chip-hover);\n  }\n}\n\n@media (hover: none) {\n\n  .m_fa109255:not([data-disabled]):where([data-checked]):active, .m_f7e165c3:not([data-disabled]):where([data-checked]):active {\n      background-color: var(--chip-hover);\n  }\n}\n\n.m_9ac86df9 {\n  width: calc(var(--chip-icon-size) + (var(--chip-spacing) / 1.5));\n  max-width: calc(var(--chip-icon-size) + (var(--chip-spacing) / 1.5));\n  height: var(--chip-icon-size);\n  display: flex;\n  align-items: center;\n  overflow: hidden;\n}\n\n.m_d6d72580 {\n  width: var(--chip-icon-size);\n  height: var(--chip-icon-size);\n  display: block;\n  color: var(--chip-icon-color, inherit);\n}\n\n.m_bde07329 {\n  width: 0;\n  height: 0;\n  padding: 0;\n  opacity: 0;\n  margin: 0;\n}\n\n.m_bde07329:focus-visible + .m_be049a53 {\n    outline: 2px solid var(--mantine-primary-color-filled);\n    outline-offset: calc(0.125rem * var(--mantine-scale));\n  }\n\n.m_b183c0a2 {\n  font-family: var(--mantine-font-family-monospace);\n  line-height: var(--mantine-line-height);\n  padding: 2px calc(var(--mantine-spacing-xs) / 2);\n  border-radius: var(--mantine-radius-sm);\n  font-size: var(--mantine-font-size-xs);\n  margin: 0;\n  overflow: auto;\n}\n\n  :where([data-mantine-color-scheme='light']) .m_b183c0a2 {\n    background-color: var(--code-bg, var(--mantine-color-gray-0));\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_b183c0a2 {\n    background-color: var(--code-bg, var(--mantine-color-dark-6));\n}\n\n  .m_b183c0a2[data-block] {\n    padding: var(--mantine-spacing-xs);\n  }\n\n.m_de3d2490 {\n  --cs-size: calc(1.75rem * var(--mantine-scale));\n  --cs-radius: calc(62.5rem * var(--mantine-scale));\n\n  -webkit-tap-highlight-color: transparent;\n  border: none;\n  appearance: none;\n  display: block;\n  line-height: 1;\n  position: relative;\n  width: var(--cs-size);\n  height: var(--cs-size);\n  min-width: var(--cs-size);\n  min-height: var(--cs-size);\n  border-radius: var(--cs-radius);\n  color: inherit;\n  text-decoration: none;\n}\n\n  [data-mantine-color-scheme='light'] .m_de3d2490 {\n    --alpha-overlay-color: var(--mantine-color-gray-3);\n    --alpha-overlay-bg: var(--mantine-color-white);\n}\n\n  [data-mantine-color-scheme='dark'] .m_de3d2490 {\n    --alpha-overlay-color: var(--mantine-color-dark-4);\n    --alpha-overlay-bg: var(--mantine-color-dark-7);\n}\n\n.m_862f3d1b {\n  position: absolute;\n  inset: 0;\n  border-radius: var(--cs-radius);\n}\n\n.m_98ae7f22 {\n  position: absolute;\n  inset: 0;\n  border-radius: var(--cs-radius);\n  z-index: 1;\n  box-shadow:\n    rgba(0, 0, 0, 0.1) 0 0 0 calc(0.0625rem * var(--mantine-scale)) inset,\n    rgb(0, 0, 0, 0.15) 0 0 calc(0.25rem * var(--mantine-scale)) inset;\n}\n\n.m_95709ac0 {\n  position: absolute;\n  inset: 0;\n  border-radius: var(--cs-radius);\n  background-size: calc(0.5rem * var(--mantine-scale)) calc(0.5rem * var(--mantine-scale));\n  background-position:\n    0 0,\n    0 calc(0.25rem * var(--mantine-scale)),\n    calc(0.25rem * var(--mantine-scale)) calc(-0.25rem * var(--mantine-scale)),\n    calc(-0.25rem * var(--mantine-scale)) 0;\n  background-image: linear-gradient(45deg, var(--alpha-overlay-color) 25%, transparent 25%),\n    linear-gradient(-45deg, var(--alpha-overlay-color) 25%, transparent 25%),\n    linear-gradient(45deg, transparent 75%, var(--alpha-overlay-color) 75%),\n    linear-gradient(-45deg, var(--alpha-overlay-bg) 75%, var(--alpha-overlay-color) 75%);\n}\n\n.m_93e74e3 {\n  position: absolute;\n  inset: 0;\n  border-radius: var(--cs-radius);\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.m_fee9c77 {\n  --cp-width-xs: calc(11.25rem * var(--mantine-scale));\n  --cp-width-sm: calc(12.5rem * var(--mantine-scale));\n  --cp-width-md: calc(15rem * var(--mantine-scale));\n  --cp-width-lg: calc(17.5rem * var(--mantine-scale));\n  --cp-width-xl: calc(20rem * var(--mantine-scale));\n\n  --cp-preview-size-xs: calc(1.625rem * var(--mantine-scale));\n  --cp-preview-size-sm: calc(2.125rem * var(--mantine-scale));\n  --cp-preview-size-md: calc(2.625rem * var(--mantine-scale));\n  --cp-preview-size-lg: calc(3.125rem * var(--mantine-scale));\n  --cp-preview-size-xl: calc(3.375rem * var(--mantine-scale));\n\n  --cp-thumb-size-xs: calc(0.5rem * var(--mantine-scale));\n  --cp-thumb-size-sm: calc(0.75rem * var(--mantine-scale));\n  --cp-thumb-size-md: calc(1rem * var(--mantine-scale));\n  --cp-thumb-size-lg: calc(1.25rem * var(--mantine-scale));\n  --cp-thumb-size-xl: calc(1.375rem * var(--mantine-scale));\n\n  --cp-saturation-height-xs: calc(6.25rem * var(--mantine-scale));\n  --cp-saturation-height-sm: calc(6.875rem * var(--mantine-scale));\n  --cp-saturation-height-md: calc(7.5rem * var(--mantine-scale));\n  --cp-saturation-height-lg: calc(8.75rem * var(--mantine-scale));\n  --cp-saturation-height-xl: calc(10rem * var(--mantine-scale));\n\n  --cp-preview-size: var(--cp-preview-size-sm);\n  --cp-thumb-size: var(--cp-thumb-size-sm);\n  --cp-saturation-height: var(--cp-saturation-height-sm);\n  --cp-width: var(--cp-width-sm);\n  --cp-body-spacing: var(--mantine-spacing-sm);\n\n  width: var(--cp-width);\n  padding: calc(0.0625rem * var(--mantine-scale));\n}\n\n  .m_fee9c77:where([data-full-width]) {\n    width: 100%;\n  }\n\n.m_9dddfbac {\n  width: var(--cp-preview-size);\n  height: var(--cp-preview-size);\n}\n\n.m_bffecc3e {\n  display: flex;\n  padding-top: calc(var(--cp-body-spacing) / 2);\n}\n\n.m_3283bb96 {\n  flex: 1;\n}\n\n.m_3283bb96:not(:only-child) {\n    margin-inline-end: var(--mantine-spacing-xs);\n  }\n\n.m_40d572ba {\n  overflow: hidden;\n  position: absolute;\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.6);\n  border: 2px solid var(--mantine-color-white);\n  width: var(--cp-thumb-size);\n  height: var(--cp-thumb-size);\n  border-radius: var(--cp-thumb-size);\n  left: calc(var(--thumb-x-offset) - var(--cp-thumb-size) / 2);\n  top: calc(var(--thumb-y-offset) - var(--cp-thumb-size) / 2);\n}\n\n.m_d8ee6fd8 {\n  height: unset !important;\n  width: unset !important;\n  min-width: 0 !important;\n  min-height: 0 !important;\n  margin: calc(0.125rem * var(--mantine-scale));\n  cursor: pointer;\n  padding-bottom: calc(var(--cp-swatch-size) - calc(0.25rem * var(--mantine-scale)));\n  flex: 0 0 calc(var(--cp-swatch-size) - calc(0.25rem * var(--mantine-scale)));\n}\n\n.m_5711e686 {\n  margin-top: calc(0.3125rem * var(--mantine-scale));\n  margin-inline: calc(-0.125rem * var(--mantine-scale));\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.m_202a296e {\n  --cp-thumb-size-xs: calc(0.5rem * var(--mantine-scale));\n  --cp-thumb-size-sm: calc(0.75rem * var(--mantine-scale));\n  --cp-thumb-size-md: calc(1rem * var(--mantine-scale));\n  --cp-thumb-size-lg: calc(1.25rem * var(--mantine-scale));\n  --cp-thumb-size-xl: calc(1.375rem * var(--mantine-scale));\n\n  -webkit-tap-highlight-color: transparent;\n  position: relative;\n  height: var(--cp-saturation-height);\n  border-radius: var(--mantine-radius-sm);\n  margin: calc(var(--cp-thumb-size) / 2);\n}\n\n.m_202a296e:where([data-focus-ring='auto']):focus:focus-visible .m_40d572ba {\n        outline: 2px solid var(--mantine-color-blue-filled);\n      }\n\n.m_202a296e:where([data-focus-ring='always']):focus .m_40d572ba {\n        outline: 2px solid var(--mantine-color-blue-filled);\n      }\n\n.m_11b3db02 {\n  position: absolute;\n  border-radius: var(--mantine-radius-sm);\n  inset: calc(var(--cp-thumb-size) * -1 / 2 - calc(0.0625rem * var(--mantine-scale)));\n}\n\n.m_d856d47d {\n  --cp-thumb-size-xs: calc(0.5rem * var(--mantine-scale));\n  --cp-thumb-size-sm: calc(0.75rem * var(--mantine-scale));\n  --cp-thumb-size-md: calc(1rem * var(--mantine-scale));\n  --cp-thumb-size-lg: calc(1.25rem * var(--mantine-scale));\n  --cp-thumb-size-xl: calc(1.375rem * var(--mantine-scale));\n  --cp-thumb-size: var(--cp-thumb-size, calc(0.75rem * var(--mantine-scale)));\n\n  position: relative;\n  height: calc(var(--cp-thumb-size) + calc(0.125rem * var(--mantine-scale)));\n  margin-inline: calc(var(--cp-thumb-size) / 2);\n  outline: none;\n}\n\n.m_d856d47d + .m_d856d47d {\n    margin-top: calc(0.375rem * var(--mantine-scale));\n  }\n\n.m_d856d47d:where([data-focus-ring='auto']):focus:focus-visible .m_40d572ba {\n        outline: 2px solid var(--mantine-color-blue-filled);\n      }\n\n.m_d856d47d:where([data-focus-ring='always']):focus .m_40d572ba {\n        outline: 2px solid var(--mantine-color-blue-filled);\n      }\n\n:where([data-mantine-color-scheme='light']) .m_d856d47d {\n    --slider-checkers: var(--mantine-color-gray-3);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_d856d47d {\n    --slider-checkers: var(--mantine-color-dark-4);\n}\n\n.m_8f327113 {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  inset-inline: calc(var(--cp-thumb-size) * -1 / 2 - calc(0.0625rem * var(--mantine-scale)));\n  border-radius: 10000rem;\n}\n\n.m_b077c2bc {\n  --ci-eye-dropper-icon-size-xs: calc(0.875rem * var(--mantine-scale));\n  --ci-eye-dropper-icon-size-sm: calc(1rem * var(--mantine-scale));\n  --ci-eye-dropper-icon-size-md: calc(1.125rem * var(--mantine-scale));\n  --ci-eye-dropper-icon-size-lg: calc(1.25rem * var(--mantine-scale));\n  --ci-eye-dropper-icon-size-xl: calc(1.375rem * var(--mantine-scale));\n  --ci-eye-dropper-icon-size: var(--ci-eye-dropper-icon-size-sm);\n}\n\n.m_c5ccdcab {\n  --ci-preview-size-xs: calc(1rem * var(--mantine-scale));\n  --ci-preview-size-sm: calc(1.125rem * var(--mantine-scale));\n  --ci-preview-size-md: calc(1.375rem * var(--mantine-scale));\n  --ci-preview-size-lg: calc(1.75rem * var(--mantine-scale));\n  --ci-preview-size-xl: calc(2.25rem * var(--mantine-scale));\n  --ci-preview-size: var(--ci-preview-size-sm);\n}\n\n.m_5ece2cd7 {\n  padding: calc(0.5rem * var(--mantine-scale));\n}\n\n.m_7485cace {\n  --container-size-xs: calc(33.75rem * var(--mantine-scale));\n  --container-size-sm: calc(45rem * var(--mantine-scale));\n  --container-size-md: calc(60rem * var(--mantine-scale));\n  --container-size-lg: calc(71.25rem * var(--mantine-scale));\n  --container-size-xl: calc(82.5rem * var(--mantine-scale));\n  --container-size: var(--container-size-md);\n\n  max-width: var(--container-size);\n  padding-inline: var(--mantine-spacing-md);\n  margin-inline: auto;\n}\n\n  .m_7485cace:where([data-fluid]) {\n    max-width: 100%;\n  }\n\n.m_e2125a27 {\n  --dialog-size-xs: calc(10rem * var(--mantine-scale));\n  --dialog-size-sm: calc(12.5rem * var(--mantine-scale));\n  --dialog-size-md: calc(21.25rem * var(--mantine-scale));\n  --dialog-size-lg: calc(25rem * var(--mantine-scale));\n  --dialog-size-xl: calc(31.25rem * var(--mantine-scale));\n  --dialog-size: var(--dialog-size-md);\n\n  position: relative;\n  width: var(--dialog-size);\n  max-width: calc(100vw - var(--mantine-spacing-xl) * 2);\n  min-height: calc(3.125rem * var(--mantine-scale));\n}\n\n.m_5abab665 {\n  position: absolute;\n  top: calc(var(--mantine-spacing-md) / 2);\n  inset-inline-end: calc(var(--mantine-spacing-md) / 2);\n}\n\n.m_3eebeb36 {\n  --divider-size-xs: calc(0.0625rem * var(--mantine-scale));\n  --divider-size-sm: calc(0.125rem * var(--mantine-scale));\n  --divider-size-md: calc(0.1875rem * var(--mantine-scale));\n  --divider-size-lg: calc(0.25rem * var(--mantine-scale));\n  --divider-size-xl: calc(0.3125rem * var(--mantine-scale));\n  --divider-size: var(--divider-size-xs);\n}\n\n  :where([data-mantine-color-scheme='light']) .m_3eebeb36 {\n    --divider-color: var(--mantine-color-gray-3);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_3eebeb36 {\n    --divider-color: var(--mantine-color-dark-4);\n}\n\n  .m_3eebeb36:where([data-orientation='horizontal']) {\n    border-top: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);\n  }\n\n  .m_3eebeb36:where([data-orientation='vertical']) {\n    border-inline-start: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);\n    height: auto;\n    align-self: stretch;\n  }\n\n  .m_3eebeb36:where([data-with-label]) {\n    border: 0;\n  }\n\n.m_9e365f20 {\n  display: flex;\n  align-items: center;\n  font-size: var(--mantine-font-size-xs);\n  color: var(--mantine-color-dimmed);\n  white-space: nowrap;\n}\n\n.m_9e365f20:where([data-position='left'])::before {\n    display: none;\n  }\n\n.m_9e365f20:where([data-position='right'])::after {\n    display: none;\n  }\n\n.m_9e365f20::before {\n    content: '';\n    flex: 1;\n    height: calc(0.0625rem * var(--mantine-scale));\n    border-top: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);\n    margin-inline-end: var(--mantine-spacing-xs);\n  }\n\n.m_9e365f20::after {\n    content: '';\n    flex: 1;\n    height: calc(0.0625rem * var(--mantine-scale));\n    border-top: var(--divider-size) var(--divider-border-style, solid) var(--divider-color);\n    margin-inline-start: var(--mantine-spacing-xs);\n  }\n\n.m_f11b401e {\n  --drawer-size-xs: calc(20rem * var(--mantine-scale));\n  --drawer-size-sm: calc(23.75rem * var(--mantine-scale));\n  --drawer-size-md: calc(27.5rem * var(--mantine-scale));\n  --drawer-size-lg: calc(38.75rem * var(--mantine-scale));\n  --drawer-size-xl: calc(48.75rem * var(--mantine-scale));\n  --drawer-size: var(--drawer-size-md);\n  --drawer-offset: 0rem;\n}\n\n.m_5a7c2c9 {\n  z-index: 1000;\n}\n\n.m_b8a05bbd {\n  flex: var(--drawer-flex, 0 0 var(--drawer-size));\n  height: var(--drawer-height, calc(100% - var(--drawer-offset) * 2));\n  margin: var(--drawer-offset);\n  max-width: calc(100% - var(--drawer-offset) * 2);\n  max-height: calc(100% - var(--drawer-offset) * 2);\n  overflow-y: auto;\n}\n\n.m_b8a05bbd[data-hidden] {\n    opacity: 0 !important;\n    pointer-events: none;\n  }\n\n.m_31cd769a {\n  display: flex;\n  justify-content: var(--drawer-justify, flex-start);\n  align-items: var(--drawer-align, flex-start);\n}\n\n.m_e9408a47 {\n  padding: var(--mantine-spacing-lg);\n  padding-top: var(--mantine-spacing-xs);\n  border-radius: var(--fieldset-radius, var(--mantine-radius-default));\n  min-inline-size: auto;\n}\n\n.m_84c9523a {\n  border: calc(0.0625rem * var(--mantine-scale)) solid;\n}\n\n:where([data-mantine-color-scheme='light']) .m_84c9523a {\n    border-color: var(--mantine-color-gray-3);\n    background-color: var(--mantine-color-white);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_84c9523a {\n    border-color: var(--mantine-color-dark-4);\n    background-color: var(--mantine-color-dark-7);\n}\n\n.m_ef274e49 {\n  border: calc(0.0625rem * var(--mantine-scale)) solid;\n}\n\n:where([data-mantine-color-scheme='light']) .m_ef274e49 {\n    border-color: var(--mantine-color-gray-3);\n    background-color: var(--mantine-color-gray-0);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_ef274e49 {\n    border-color: var(--mantine-color-dark-4);\n    background-color: var(--mantine-color-dark-6);\n}\n\n.m_eda993d3 {\n  padding: 0;\n  border: 0;\n  border-radius: 0;\n}\n\n.m_90794832 {\n  font-size: var(--mantine-font-size-sm);\n}\n\n.m_74ca27fe {\n  padding: 0;\n  margin-bottom: var(--mantine-spacing-sm);\n}\n\n.m_8478a6da {\n  container: mantine-grid / inline-size;\n}\n\n.m_410352e9 {\n  --grid-overflow: visible;\n  --grid-margin: calc(var(--grid-gutter) / -2);\n  --grid-col-padding: calc(var(--grid-gutter) / 2);\n\n  overflow: var(--grid-overflow);\n}\n\n.m_dee7bd2f {\n  width: calc(100% + var(--grid-gutter));\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: var(--grid-justify);\n  align-items: var(--grid-align);\n  margin: var(--grid-margin);\n}\n\n.m_96bdd299 {\n  --col-flex-grow: 0;\n  --col-offset: 0rem;\n\n  flex-shrink: 0;\n  order: var(--col-order);\n  flex-basis: var(--col-flex-basis);\n  width: var(--col-width);\n  max-width: var(--col-max-width);\n  flex-grow: var(--col-flex-grow);\n  margin-inline-start: var(--col-offset);\n  padding: var(--grid-col-padding);\n}\n\n.m_bcb3f3c2 {\n  color: var(--mantine-color-black);\n}\n\n  :where([data-mantine-color-scheme='light']) .m_bcb3f3c2 {\n    background-color: var(--mark-bg-light);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_bcb3f3c2 {\n    background-color: var(--mark-bg-dark);\n}\n\n.m_9e117634 {\n  display: block;\n  object-fit: var(--image-object-fit, cover);\n  width: 100%;\n  border-radius: var(--image-radius, 0);\n}\n\n@keyframes m_885901b1 {\n  0% {\n    opacity: 0.6;\n    transform: scale(0);\n  }\n\n  100% {\n    opacity: 0;\n    transform: scale(2.8);\n  }\n}\n\n.m_e5262200 {\n  --indicator-size: calc(0.625rem * var(--mantine-scale));\n  --indicator-color: var(--mantine-primary-color-filled);\n\n  position: relative;\n  display: block;\n}\n\n.m_e5262200:where([data-inline]) {\n    display: inline-block;\n  }\n\n.m_760d1fb1 {\n  position: absolute;\n  top: var(--indicator-top);\n  left: var(--indicator-left);\n  right: var(--indicator-right);\n  bottom: var(--indicator-bottom);\n  transform: translate(var(--indicator-translate-x), var(--indicator-translate-y));\n  min-width: var(--indicator-size);\n  height: var(--indicator-size);\n  border-radius: var(--indicator-radius, 1000rem);\n  z-index: var(--indicator-z-index, 200);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: var(--mantine-font-size-xs);\n  background-color: var(--indicator-color);\n  color: var(--indicator-text-color, var(--mantine-color-white));\n  white-space: nowrap;\n}\n\n.m_760d1fb1::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    background-color: var(--indicator-color);\n    border-radius: var(--indicator-radius, 1000rem);\n    z-index: -1;\n  }\n\n.m_760d1fb1:where([data-with-label]) {\n    padding-inline: calc(var(--mantine-spacing-xs) / 2);\n  }\n\n.m_760d1fb1:where([data-with-border]) {\n    border: 2px solid var(--mantine-color-body);\n  }\n\n.m_760d1fb1[data-processing]::before {\n      animation: m_885901b1 1000ms linear infinite;\n    }\n\n.m_dc6f14e2 {\n  --kbd-fz-xs: calc(0.625rem * var(--mantine-scale));\n  --kbd-fz-sm: calc(0.75rem * var(--mantine-scale));\n  --kbd-fz-md: calc(0.875rem * var(--mantine-scale));\n  --kbd-fz-lg: calc(1rem * var(--mantine-scale));\n  --kbd-fz-xl: calc(1.25rem * var(--mantine-scale));\n  --kbd-fz: var(--kbd-fz-sm);\n\n  font-family: var(--mantine-font-family-monospace);\n  line-height: var(--mantine-line-height);\n  font-weight: 700;\n  font-size: var(--kbd-fz);\n  border-radius: var(--mantine-radius-sm);\n  border: calc(0.0625rem * var(--mantine-scale)) solid;\n  border-bottom-width: calc(0.1875rem * var(--mantine-scale));\n  unicode-bidi: embed;\n  text-align: center;\n  padding: 0.12em 0.45em;\n}\n\n  :where([data-mantine-color-scheme='light']) .m_dc6f14e2 {\n    border-color: var(--mantine-color-gray-3);\n    color: var(--mantine-color-gray-7);\n    background-color: var(--mantine-color-gray-0);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_dc6f14e2 {\n    border-color: var(--mantine-color-dark-4);\n    color: var(--mantine-color-dark-0);\n    background-color: var(--mantine-color-dark-6);\n}\n\n.m_abbac491 {\n  --list-fz: var(--mantine-font-size-md);\n  --list-lh: var(--mantine-line-height-md);\n\n  list-style-position: inside;\n  font-size: var(--list-fz);\n  line-height: var(--list-lh);\n  margin: 0;\n  padding: 0;\n}\n\n  .m_abbac491:where([data-with-padding]) {\n    padding-inline-start: var(--mantine-spacing-md);\n  }\n\n.m_abb6bec2 {\n  white-space: nowrap;\n  line-height: var(--list-lh);\n}\n\n.m_abb6bec2:where([data-with-icon]) {\n    list-style: none;\n  }\n\n.m_abb6bec2:where([data-with-icon]) .m_75cd9f71 {\n      --li-direction: row;\n      --li-align: center;\n    }\n\n.m_abb6bec2:where(:not(:first-of-type)) {\n    margin-top: var(--list-spacing, 0);\n  }\n\n.m_abb6bec2:where([data-centered]) {\n    line-height: 1;\n  }\n\n.m_75cd9f71 {\n  display: inline-flex;\n  flex-direction: var(--li-direction, column);\n  align-items: var(--li-align, flex-start);\n  white-space: normal;\n}\n\n.m_60f83e5b {\n  display: inline-block;\n  vertical-align: middle;\n  margin-inline-end: var(--mantine-spacing-sm);\n}\n\n.m_6e45937b {\n  position: absolute;\n  inset: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  z-index: var(--lo-z-index);\n}\n\n.m_e8eb006c {\n  position: relative;\n  z-index: calc(var(--lo-z-index) + 1);\n}\n\n.m_df587f17 {\n  z-index: var(--lo-z-index);\n}\n\n.m_dc9b7c9f {\n  padding: calc(0.25rem * var(--mantine-scale));\n}\n\n.m_9bfac126 {\n  color: var(--mantine-color-dimmed);\n  font-weight: 500;\n  font-size: var(--mantine-font-size-xs);\n  padding: calc(var(--mantine-spacing-xs) / 2) var(--mantine-spacing-sm);\n  cursor: default;\n}\n\n.m_efdf90cb {\n  margin-top: calc(0.25rem * var(--mantine-scale));\n  margin-bottom: calc(0.25rem * var(--mantine-scale));\n  border-top: calc(0.0625rem * var(--mantine-scale)) solid;\n}\n\n:where([data-mantine-color-scheme='light']) .m_efdf90cb {\n    border-color: var(--mantine-color-gray-2);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_efdf90cb {\n    border-color: var(--mantine-color-dark-4);\n}\n\n.m_99ac2aa1 {\n  font-size: var(--mantine-font-size-sm);\n  width: 100%;\n  padding: calc(var(--mantine-spacing-xs) / 1.5) var(--mantine-spacing-sm);\n  border-radius: var(--popover-radius, var(--mantine-radius-default));\n  color: var(--menu-item-color, var(--mantine-color-text));\n  display: flex;\n  align-items: center;\n  user-select: none;\n}\n\n.m_99ac2aa1:where([data-disabled], :disabled) {\n    color: var(--mantine-color-disabled-color);\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n\n:where([data-mantine-color-scheme='light']) .m_99ac2aa1:where(:hover, :focus):where(:not(:disabled, [data-disabled])) {\n        background-color: var(--menu-item-hover, var(--mantine-color-gray-1));\n}\n\n:where([data-mantine-color-scheme='dark']) .m_99ac2aa1:where(:hover, :focus):where(:not(:disabled, [data-disabled])) {\n        background-color: var(--menu-item-hover, var(--mantine-color-dark-4));\n}\n\n.m_99ac2aa1:where([data-sub-menu-item]) {\n    padding-inline-end: calc(0.3125rem * var(--mantine-scale));\n  }\n\n.m_5476e0d3 {\n  flex: 1;\n}\n\n.m_8b75e504 {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.m_8b75e504:where([data-position='left']) {\n    margin-inline-end: var(--mantine-spacing-xs);\n  }\n\n.m_8b75e504:where([data-position='right']) {\n    margin-inline-start: var(--mantine-spacing-xs);\n  }\n\n.m_b85b0bed {\n  transform: rotate(-90deg);\n}\n\n:where([dir=\"rtl\"]) .m_b85b0bed {\n    transform: rotate(90deg);\n}\n\n.m_9df02822 {\n  --modal-size-xs: calc(20rem * var(--mantine-scale));\n  --modal-size-sm: calc(23.75rem * var(--mantine-scale));\n  --modal-size-md: calc(27.5rem * var(--mantine-scale));\n  --modal-size-lg: calc(38.75rem * var(--mantine-scale));\n  --modal-size-xl: calc(48.75rem * var(--mantine-scale));\n  --modal-size: var(--modal-size-md);\n\n  --modal-y-offset: 5dvh;\n  --modal-x-offset: 5vw;\n}\n\n  .m_9df02822[data-full-screen] {\n    --modal-border-radius: 0 !important;\n  }\n\n  .m_9df02822[data-full-screen] .m_54c44539 {\n      --modal-content-flex: 0 0 100%;\n      --modal-content-max-height: auto;\n      --modal-content-height: 100dvh;\n    }\n\n  .m_9df02822[data-full-screen] .m_1f958f16 {\n      --modal-inner-y-offset: 0;\n      --modal-inner-x-offset: 0;\n    }\n\n  .m_9df02822[data-centered] .m_1f958f16 {\n      --modal-inner-align: center;\n    }\n\n.m_d0e2b9cd {\n  border-start-start-radius: var(--modal-radius, var(--mantine-radius-default));\n  border-start-end-radius: var(--modal-radius, var(--mantine-radius-default));\n}\n\n.m_54c44539 {\n  flex: var(--modal-content-flex, 0 0 var(--modal-size));\n  max-width: 100%;\n  max-height: var(--modal-content-max-height, calc(100dvh - var(--modal-y-offset) * 2));\n  height: var(--modal-content-height, auto);\n  overflow-y: auto;\n}\n\n.m_54c44539[data-full-screen] {\n    border-radius: 0;\n  }\n\n.m_54c44539[data-hidden] {\n    opacity: 0 !important;\n    pointer-events: none;\n  }\n\n.m_1f958f16 {\n  display: flex;\n  justify-content: center;\n  align-items: var(--modal-inner-align, flex-start);\n  padding-top: var(--modal-inner-y-offset, var(--modal-y-offset));\n  padding-bottom: var(--modal-inner-y-offset, var(--modal-y-offset));\n  padding-inline: var(--modal-inner-x-offset, var(--modal-x-offset));\n}\n\n.m_7cda1cd6 {\n  --pill-fz-xs: calc(0.625rem * var(--mantine-scale));\n  --pill-fz-sm: calc(0.75rem * var(--mantine-scale));\n  --pill-fz-md: calc(0.875rem * var(--mantine-scale));\n  --pill-fz-lg: calc(1rem * var(--mantine-scale));\n  --pill-fz-xl: calc(1.125rem * var(--mantine-scale));\n\n  --pill-height-xs: calc(1.125rem * var(--mantine-scale));\n  --pill-height-sm: calc(1.375rem * var(--mantine-scale));\n  --pill-height-md: calc(1.5625rem * var(--mantine-scale));\n  --pill-height-lg: calc(1.75rem * var(--mantine-scale));\n  --pill-height-xl: calc(2rem * var(--mantine-scale));\n\n  --pill-fz: var(--pill-fz-sm);\n  --pill-height: var(--pill-height-sm);\n\n  font-size: var(--pill-fz);\n  flex: 0;\n  height: var(--pill-height);\n  padding-inline: 0.8em;\n  display: inline-flex;\n  align-items: center;\n  border-radius: var(--pill-radius, 1000rem);\n  line-height: 1;\n  white-space: nowrap;\n  user-select: none;\n  -webkit-user-select: none;\n  max-width: 100%;\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_7cda1cd6 {\n    background-color: var(--mantine-color-dark-7);\n    color: var(--mantine-color-dark-0);\n}\n\n  :where([data-mantine-color-scheme='light']) .m_7cda1cd6 {\n    color: var(--mantine-color-black);\n}\n\n  .m_7cda1cd6:where([data-with-remove]:not(:has(button:disabled))) {\n    padding-inline-end: 0;\n  }\n\n  .m_7cda1cd6:where([data-disabled], :has(button:disabled)) {\n    cursor: not-allowed;\n  }\n\n:where([data-mantine-color-scheme='light']) .m_44da308b {\n    background-color: var(--mantine-color-gray-1);\n}\n\n:where([data-mantine-color-scheme='light']) .m_44da308b:where([data-disabled], :has(button:disabled)) {\n      background-color: var(--mantine-color-disabled);\n    }\n\n:where([data-mantine-color-scheme='light']) .m_e3a01f8 {\n    background-color: var(--mantine-color-white);\n}\n\n:where([data-mantine-color-scheme='light']) .m_e3a01f8:where([data-disabled], :has(button:disabled)) {\n      background-color: var(--mantine-color-disabled);\n    }\n\n.m_1e0e6180 {\n  cursor: inherit;\n  overflow: hidden;\n  height: 100%;\n  line-height: var(--pill-height);\n  text-overflow: ellipsis;\n}\n\n.m_ae386778 {\n  color: inherit;\n  font-size: inherit;\n  height: 100%;\n  min-height: unset;\n  min-width: 2em;\n  width: unset;\n  border-radius: 0;\n  padding-inline-start: 0.1em;\n  padding-inline-end: 0.3em;\n  flex: 0;\n  border-end-end-radius: var(--pill-radius, 50%);\n  border-start-end-radius: var(--pill-radius, 50%);\n}\n\n.m_7cda1cd6[data-disabled] > .m_ae386778,\n  .m_ae386778:disabled {\n    display: none;\n    background-color: transparent;\n    width: 0.8em;\n    min-width: 0.8em;\n    padding: 0;\n    cursor: not-allowed;\n  }\n\n.m_7cda1cd6[data-disabled] > .m_ae386778 > svg, .m_ae386778:disabled > svg {\n      display: none;\n    }\n\n.m_ae386778 > svg {\n    pointer-events: none;\n  }\n\n.m_1dcfd90b {\n  --pg-gap-xs: calc(0.375rem * var(--mantine-scale));\n  --pg-gap-sm: calc(0.5rem * var(--mantine-scale));\n  --pg-gap-md: calc(0.625rem * var(--mantine-scale));\n  --pg-gap-lg: calc(0.75rem * var(--mantine-scale));\n  --pg-gap-xl: calc(0.75rem * var(--mantine-scale));\n  --pg-gap: var(--pg-gap-sm);\n\n  display: flex;\n  align-items: center;\n  gap: var(--pg-gap);\n  flex-wrap: wrap;\n}\n\n.m_45c4369d {\n  background-color: transparent;\n  appearance: none;\n  min-width: calc(6.25rem * var(--mantine-scale));\n  flex: 1;\n  border: 0;\n  font-size: inherit;\n  height: 1.6em;\n  color: inherit;\n  padding: 0;\n}\n\n  .m_45c4369d::placeholder {\n    color: var(--input-placeholder-color);\n    opacity: 1;\n  }\n\n  .m_45c4369d:where([data-type='hidden'], [data-type='auto']) {\n    height: calc(0.0625rem * var(--mantine-scale));\n    width: calc(0.0625rem * var(--mantine-scale));\n    top: 0;\n    left: 0;\n    pointer-events: none;\n    position: absolute;\n    opacity: 0;\n  }\n\n  .m_45c4369d:focus {\n    outline: none;\n  }\n\n  .m_45c4369d:where([data-type='auto']:focus) {\n    height: 1.6em;\n    visibility: visible;\n    opacity: 1;\n    position: static;\n  }\n\n  .m_45c4369d:where([data-pointer]:not([data-disabled], :disabled)) {\n    cursor: pointer;\n  }\n\n  .m_45c4369d:where([data-disabled], :disabled) {\n    cursor: not-allowed;\n  }\n\n.m_f0824112 {\n  --nl-bg: var(--mantine-primary-color-light);\n  --nl-hover: var(--mantine-primary-color-light-hover);\n  --nl-color: var(--mantine-primary-color-light-color);\n\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: 8px var(--mantine-spacing-sm);\n  user-select: none;\n}\n\n  @media (hover: hover) {\n    :where([data-mantine-color-scheme='light']) .m_f0824112:hover {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_f0824112:hover {\n      background-color: var(--mantine-color-dark-6);\n  }\n}\n\n  @media (hover: none) {\n    :where([data-mantine-color-scheme='light']) .m_f0824112:active {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_f0824112:active {\n      background-color: var(--mantine-color-dark-6);\n  }\n}\n\n  .m_f0824112:where([data-disabled]) {\n    opacity: 0.4;\n    pointer-events: none;\n  }\n\n  .m_f0824112:where([data-active], [aria-current='page']) {\n    background-color: var(--nl-bg);\n    color: var(--nl-color);\n  }\n\n  @media (hover: hover) {\n\n  .m_f0824112:where([data-active], [aria-current='page']):hover {\n      background-color: var(--nl-hover);\n  }\n}\n\n  @media (hover: none) {\n\n  .m_f0824112:where([data-active], [aria-current='page']):active {\n      background-color: var(--nl-hover);\n  }\n}\n\n  .m_f0824112:where([data-active], [aria-current='page']) .m_57492dcc {\n      --description-opacity: 0.9;\n      --description-color: var(--nl-color);\n    }\n\n.m_690090b5 {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: transform 150ms ease;\n}\n\n.m_690090b5 > svg {\n    display: block;\n  }\n\n.m_690090b5:where([data-position='left']) {\n    margin-inline-end: var(--mantine-spacing-sm);\n  }\n\n.m_690090b5:where([data-position='right']) {\n    margin-inline-start: var(--mantine-spacing-sm);\n  }\n\n.m_690090b5:where([data-rotate]) {\n    transform: rotate(90deg);\n  }\n\n.m_1f6ac4c4 {\n  font-size: var(--mantine-font-size-sm);\n}\n\n.m_f07af9d2 {\n  flex: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.m_f07af9d2:where([data-no-wrap]) {\n    white-space: nowrap;\n  }\n\n.m_57492dcc {\n  display: block;\n  font-size: var(--mantine-font-size-xs);\n  opacity: var(--description-opacity, 1);\n  color: var(--description-color, var(--mantine-color-dimmed));\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n:where([data-no-wrap]) .m_57492dcc {\n    white-space: nowrap;\n  }\n\n.m_e17b862f {\n  padding-inline-start: var(--nl-offset, var(--mantine-spacing-lg));\n}\n\n.m_1fd8a00b {\n  transform: rotate(-90deg);\n}\n\n.m_a513464 {\n  --notification-radius: var(--mantine-radius-default);\n  --notification-color: var(--mantine-primary-color-filled);\n\n  overflow: hidden;\n  box-sizing: border-box;\n  position: relative;\n  display: flex;\n  align-items: center;\n  padding-inline-start: calc(1.375rem * var(--mantine-scale));\n  padding-inline-end: var(--mantine-spacing-xs);\n  padding-top: var(--mantine-spacing-xs);\n  padding-bottom: var(--mantine-spacing-xs);\n  border-radius: var(--notification-radius);\n  box-shadow: var(--mantine-shadow-lg);\n}\n\n  .m_a513464::before {\n    content: '';\n    display: block;\n    position: absolute;\n    width: calc(0.375rem * var(--mantine-scale));\n    top: var(--notification-radius);\n    bottom: var(--notification-radius);\n    inset-inline-start: calc(0.25rem * var(--mantine-scale));\n    border-radius: var(--notification-radius);\n    background-color: var(--notification-color);\n  }\n\n  :where([data-mantine-color-scheme='light']) .m_a513464 {\n    background-color: var(--mantine-color-white);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_a513464 {\n    background-color: var(--mantine-color-dark-6);\n}\n\n  .m_a513464:where([data-with-icon])::before {\n      display: none;\n    }\n\n  :where([data-mantine-color-scheme='light']) .m_a513464:where([data-with-border]) {\n      border: 1px solid var(--mantine-color-gray-3);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_a513464:where([data-with-border]) {\n      border: 1px solid var(--mantine-color-dark-4);\n}\n\n.m_a4ceffb {\n  box-sizing: border-box;\n  margin-inline-end: var(--mantine-spacing-md);\n  width: calc(1.75rem * var(--mantine-scale));\n  height: calc(1.75rem * var(--mantine-scale));\n  border-radius: calc(1.75rem * var(--mantine-scale));\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: var(--notification-color);\n  color: var(--mantine-color-white);\n}\n\n.m_b0920b15 {\n  margin-inline-end: var(--mantine-spacing-md);\n}\n\n.m_a49ed24 {\n  flex: 1;\n  overflow: hidden;\n  margin-inline-end: var(--mantine-spacing-xs);\n}\n\n.m_3feedf16 {\n  margin-bottom: calc(0.125rem * var(--mantine-scale));\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-size: var(--mantine-font-size-sm);\n  line-height: var(--mantine-line-height-sm);\n  font-weight: 500;\n}\n\n:where([data-mantine-color-scheme='light']) .m_3feedf16 {\n    color: var(--mantine-color-gray-9);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_3feedf16 {\n    color: var(--mantine-color-white);\n}\n\n.m_3d733a3a {\n  font-size: var(--mantine-font-size-sm);\n  line-height: var(--mantine-line-height-sm);\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n:where([data-mantine-color-scheme='light']) .m_3d733a3a {\n    color: var(--mantine-color-black);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_3d733a3a {\n    color: var(--mantine-color-dark-0);\n}\n\n:where([data-mantine-color-scheme='light']) .m_3d733a3a:where([data-with-title]) {\n      color: var(--mantine-color-gray-6);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_3d733a3a:where([data-with-title]) {\n      color: var(--mantine-color-dark-2);\n}\n\n@media (hover: hover) {\n    :where([data-mantine-color-scheme='light']) .m_919a4d88:hover {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_919a4d88:hover {\n      background-color: var(--mantine-color-dark-8);\n  }\n}\n\n@media (hover: none) {\n    :where([data-mantine-color-scheme='light']) .m_919a4d88:active {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_919a4d88:active {\n      background-color: var(--mantine-color-dark-8);\n  }\n}\n\n.m_e2f5cd4e {\n  --ni-right-section-width-xs: calc(1.0625rem * var(--mantine-scale));\n  --ni-right-section-width-sm: calc(1.5rem * var(--mantine-scale));\n  --ni-right-section-width-md: calc(1.6875rem * var(--mantine-scale));\n  --ni-right-section-width-lg: calc(1.9375rem * var(--mantine-scale));\n  --ni-right-section-width-xl: calc(2.125rem * var(--mantine-scale));\n}\n\n.m_95e17d22 {\n  --ni-chevron-size-xs: calc(0.625rem * var(--mantine-scale));\n  --ni-chevron-size-sm: calc(0.875rem * var(--mantine-scale));\n  --ni-chevron-size-md: calc(1rem * var(--mantine-scale));\n  --ni-chevron-size-lg: calc(1.125rem * var(--mantine-scale));\n  --ni-chevron-size-xl: calc(1.25rem * var(--mantine-scale));\n  --ni-chevron-size: var(--ni-chevron-size-sm);\n\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  height: calc(var(--input-height) - calc(0.125rem * var(--mantine-scale)));\n  max-width: calc(var(--ni-chevron-size) * 1.7);\n  margin-inline-start: auto;\n}\n\n.m_80b4b171 {\n  --control-border: 1px solid var(--input-bd);\n  --control-radius: calc(var(--input-radius) - calc(0.0625rem * var(--mantine-scale)));\n\n  flex: 0 0 50%;\n  width: 100%;\n  padding: 0;\n  height: calc(var(--input-height) / 2 - calc(0.0625rem * var(--mantine-scale)));\n  border-inline-start: var(--control-border);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--mantine-color-text);\n  background-color: transparent;\n  cursor: pointer;\n}\n\n.m_80b4b171:where(:disabled) {\n    background-color: transparent;\n    cursor: not-allowed;\n    opacity: 0.6;\n    color: var(--mantine-color-disabled-color);\n  }\n\n.m_e2f5cd4e[data-error] :where(.m_80b4b171) {\n    color: var(--mantine-color-error);\n  }\n\n@media (hover: hover) {\n    :where([data-mantine-color-scheme='light']) .m_80b4b171:hover {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_80b4b171:hover {\n      background-color: var(--mantine-color-dark-4);\n  }\n}\n\n@media (hover: none) {\n    :where([data-mantine-color-scheme='light']) .m_80b4b171:active {\n      background-color: var(--mantine-color-gray-0);\n  }\n\n    :where([data-mantine-color-scheme='dark']) .m_80b4b171:active {\n      background-color: var(--mantine-color-dark-4);\n  }\n}\n\n.m_80b4b171:where(:first-of-type) {\n    border-radius: 0;\n    border-start-end-radius: var(--control-radius);\n  }\n\n.m_80b4b171:last-of-type {\n    border-radius: 0;\n    border-end-end-radius: var(--control-radius);\n  }\n\n.m_4addd315 {\n  --pagination-control-size-xs: calc(1.375rem * var(--mantine-scale));\n  --pagination-control-size-sm: calc(1.625rem * var(--mantine-scale));\n  --pagination-control-size-md: calc(2rem * var(--mantine-scale));\n  --pagination-control-size-lg: calc(2.375rem * var(--mantine-scale));\n  --pagination-control-size-xl: calc(2.75rem * var(--mantine-scale));\n  --pagination-control-size: var(--pagination-control-size-md);\n  --pagination-control-fz: var(--mantine-font-size-md);\n  --pagination-active-bg: var(--mantine-primary-color-filled);\n}\n\n.m_326d024a {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: calc(0.0625rem * var(--mantine-scale)) solid;\n  cursor: pointer;\n  color: var(--mantine-color-text);\n  height: var(--pagination-control-size);\n  min-width: var(--pagination-control-size);\n  font-size: var(--pagination-control-fz);\n  line-height: 1;\n  border-radius: var(--pagination-control-radius, var(--mantine-radius-default));\n}\n\n.m_326d024a:where([data-with-padding]) {\n    padding: calc(var(--pagination-control-size) / 4);\n  }\n\n.m_326d024a:where(:disabled, [data-disabled]) {\n    cursor: not-allowed;\n    opacity: 0.4;\n  }\n\n:where([data-mantine-color-scheme='light']) .m_326d024a {\n    border-color: var(--mantine-color-gray-4);\n    background-color: var(--mantine-color-white);\n}\n\n@media (hover: hover) {\n      :where([data-mantine-color-scheme='light']) .m_326d024a:hover:where(:not(:disabled, [data-disabled])) {\n        background-color: var(--mantine-color-gray-0);\n      }\n}\n\n@media (hover: none) {\n      :where([data-mantine-color-scheme='light']) .m_326d024a:active:where(:not(:disabled, [data-disabled])) {\n        background-color: var(--mantine-color-gray-0);\n      }\n}\n\n:where([data-mantine-color-scheme='dark']) .m_326d024a {\n    border-color: var(--mantine-color-dark-4);\n    background-color: var(--mantine-color-dark-6);\n}\n\n@media (hover: hover) {\n      :where([data-mantine-color-scheme='dark']) .m_326d024a:hover:where(:not(:disabled, [data-disabled])) {\n        background-color: var(--mantine-color-dark-5);\n      }\n}\n\n@media (hover: none) {\n      :where([data-mantine-color-scheme='dark']) .m_326d024a:active:where(:not(:disabled, [data-disabled])) {\n        background-color: var(--mantine-color-dark-5);\n      }\n}\n\n.m_326d024a:where([data-active]) {\n    background-color: var(--pagination-active-bg);\n    border-color: var(--pagination-active-bg);\n    color: var(--pagination-active-color, var(--mantine-color-white));\n  }\n\n@media (hover: hover) {\n\n  .m_326d024a:where([data-active]):hover {\n      background-color: var(--pagination-active-bg);\n  }\n}\n\n@media (hover: none) {\n\n  .m_326d024a:where([data-active]):active {\n      background-color: var(--pagination-active-bg);\n  }\n}\n\n.m_4ad7767d {\n  height: var(--pagination-control-size);\n  min-width: var(--pagination-control-size);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  pointer-events: none;\n}\n\n.m_f61ca620 {\n  --psi-button-size-xs: calc(1.375rem * var(--mantine-scale));\n  --psi-button-size-sm: calc(1.625rem * var(--mantine-scale));\n  --psi-button-size-md: calc(1.75rem * var(--mantine-scale));\n  --psi-button-size-lg: calc(2rem * var(--mantine-scale));\n  --psi-button-size-xl: calc(2.5rem * var(--mantine-scale));\n\n  --psi-icon-size-xs: calc(0.75rem * var(--mantine-scale));\n  --psi-icon-size-sm: calc(0.9375rem * var(--mantine-scale));\n  --psi-icon-size-md: calc(1.0625rem * var(--mantine-scale));\n  --psi-icon-size-lg: calc(1.1875rem * var(--mantine-scale));\n  --psi-icon-size-xl: calc(1.3125rem * var(--mantine-scale));\n\n  --psi-button-size: var(--psi-button-size-sm);\n  --psi-icon-size: var(--psi-icon-size-sm);\n}\n\n.m_ccf8da4c {\n  position: relative;\n  overflow: hidden;\n}\n\n.m_f2d85dd2 {\n  font-family: var(--mantine-font-family);\n  background-color: transparent;\n  border: 0;\n  padding-inline-end: var(--input-padding-inline-end);\n  padding-inline-start: var(--input-padding-inline-start);\n  position: absolute;\n  inset: 0;\n  outline: 0;\n  font-size: inherit;\n  line-height: var(--mantine-line-height);\n  height: 100%;\n  width: 100%;\n  color: inherit;\n}\n\n.m_ccf8da4c[data-disabled] .m_f2d85dd2,\n  .m_f2d85dd2:disabled {\n    cursor: not-allowed;\n  }\n\n.m_f2d85dd2::placeholder {\n    color: var(--input-placeholder-color);\n    opacity: 1;\n  }\n\n.m_f2d85dd2::-ms-reveal {\n    display: none;\n  }\n\n.m_b1072d44 {\n  width: var(--psi-button-size);\n  height: var(--psi-button-size);\n  min-width: var(--psi-button-size);\n  min-height: var(--psi-button-size);\n}\n\n.m_b1072d44:disabled {\n    display: none;\n  }\n\n.m_f1cb205a {\n  --pin-input-size-xs: calc(1.875rem * var(--mantine-scale));\n  --pin-input-size-sm: calc(2.25rem * var(--mantine-scale));\n  --pin-input-size-md: calc(2.625rem * var(--mantine-scale));\n  --pin-input-size-lg: calc(3.125rem * var(--mantine-scale));\n  --pin-input-size-xl: calc(3.75rem * var(--mantine-scale));\n  --pin-input-size: var(--pin-input-size-sm);\n}\n\n.m_cb288ead {\n  width: var(--pin-input-size);\n  height: var(--pin-input-size);\n}\n\n@keyframes m_81a374bd {\n  0% {\n    background-position: 0 0;\n  }\n\n  100% {\n    background-position: calc(2.5rem * var(--mantine-scale)) 0;\n  }\n}\n\n.m_db6d6462 {\n  --progress-radius: var(--mantine-radius-default);\n  --progress-size: var(--progress-size-md);\n\n  --progress-size-xs: calc(0.1875rem * var(--mantine-scale));\n  --progress-size-sm: calc(0.3125rem * var(--mantine-scale));\n  --progress-size-md: calc(0.5rem * var(--mantine-scale));\n  --progress-size-lg: calc(0.75rem * var(--mantine-scale));\n  --progress-size-xl: calc(1rem * var(--mantine-scale));\n\n  position: relative;\n  height: var(--progress-size);\n  border-radius: var(--progress-radius);\n  overflow: hidden;\n  display: flex;\n}\n\n:where([data-mantine-color-scheme='light']) .m_db6d6462 {\n    background-color: var(--mantine-color-gray-2);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_db6d6462 {\n    background-color: var(--mantine-color-dark-4);\n}\n\n.m_2242eb65 {\n  background-color: var(--progress-section-color);\n  height: 100%;\n  width: var(--progress-section-width);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background-size: calc(1.25rem * var(--mantine-scale)) calc(1.25rem * var(--mantine-scale));\n  transition: width var(--progress-transition-duration, 100ms) ease;\n}\n\n.m_2242eb65:where([data-striped]) {\n    background-image: linear-gradient(\n      45deg,\n      rgba(255, 255, 255, 0.15) 25%,\n      transparent 25%,\n      transparent 50%,\n      rgba(255, 255, 255, 0.15) 50%,\n      rgba(255, 255, 255, 0.15) 75%,\n      transparent 75%,\n      transparent\n    );\n  }\n\n.m_2242eb65:where([data-animated]) {\n    animation: m_81a374bd 1s linear infinite;\n  }\n\n.m_2242eb65:where(:last-of-type) {\n    border-radius: 0;\n    border-start-end-radius: var(--progress-radius);\n    border-end-end-radius: var(--progress-radius);\n  }\n\n.m_2242eb65:where(:first-of-type) {\n    border-radius: 0;\n    border-start-start-radius: var(--progress-radius);\n    border-end-start-radius: var(--progress-radius);\n  }\n\n.m_91e40b74 {\n  color: var(--progress-label-color, var(--mantine-color-white));\n  font-weight: bold;\n  user-select: none;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  font-size: min(calc(var(--progress-size) * 0.65), calc(1.125rem * var(--mantine-scale)));\n  line-height: 1;\n  padding-inline: calc(0.25rem * var(--mantine-scale));\n}\n\n.m_9dc8ae12 {\n  --card-radius: var(--mantine-radius-default);\n\n  display: block;\n  width: 100%;\n  border-radius: var(--card-radius);\n  cursor: pointer;\n}\n\n  .m_9dc8ae12 :where(*) {\n    cursor: inherit;\n  }\n\n  .m_9dc8ae12:where([data-with-border]) {\n    border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  }\n\n  :where([data-mantine-color-scheme='light']) .m_9dc8ae12:where([data-with-border]) {\n      border-color: var(--mantine-color-gray-3);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_9dc8ae12:where([data-with-border]) {\n      border-color: var(--mantine-color-dark-4);\n}\n\n.m_717d7ff6 {\n  --radio-size-xs: calc(1rem * var(--mantine-scale));\n  --radio-size-sm: calc(1.25rem * var(--mantine-scale));\n  --radio-size-md: calc(1.5rem * var(--mantine-scale));\n  --radio-size-lg: calc(1.875rem * var(--mantine-scale));\n  --radio-size-xl: calc(2.25rem * var(--mantine-scale));\n\n  --radio-icon-size-xs: calc(0.375rem * var(--mantine-scale));\n  --radio-icon-size-sm: calc(0.5rem * var(--mantine-scale));\n  --radio-icon-size-md: calc(0.625rem * var(--mantine-scale));\n  --radio-icon-size-lg: calc(0.875rem * var(--mantine-scale));\n  --radio-icon-size-xl: calc(1rem * var(--mantine-scale));\n\n  --radio-icon-size: var(--radio-icon-size-sm);\n  --radio-size: var(--radio-size-sm);\n  --radio-color: var(--mantine-primary-color-filled);\n  --radio-icon-color: var(--mantine-color-white);\n\n  position: relative;\n  border: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  width: var(--radio-size);\n  min-width: var(--radio-size);\n  height: var(--radio-size);\n  min-height: var(--radio-size);\n  border-radius: var(--radio-radius, 10000px);\n  transition:\n    border-color 100ms ease,\n    background-color 100ms ease;\n  cursor: var(--mantine-cursor-type);\n  -webkit-tap-highlight-color: transparent;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n  :where([data-mantine-color-scheme='light']) .m_717d7ff6 {\n    background-color: var(--mantine-color-white);\n    border-color: var(--mantine-color-gray-4);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_717d7ff6 {\n    background-color: var(--mantine-color-dark-6);\n    border-color: var(--mantine-color-dark-4);\n}\n\n  .m_717d7ff6[data-indeterminate],\n  .m_717d7ff6[data-checked] {\n    background-color: var(--radio-color);\n    border-color: var(--radio-color);\n  }\n\n  .m_717d7ff6[data-indeterminate] > .m_3e4da632, .m_717d7ff6[data-checked] > .m_3e4da632 {\n      opacity: 1;\n      transform: none;\n      color: var(--radio-icon-color);\n    }\n\n  .m_717d7ff6[data-disabled] {\n    cursor: not-allowed;\n    background-color: var(--mantine-color-disabled);\n    border-color: var(--mantine-color-disabled-border);\n  }\n\n  .m_717d7ff6[data-disabled][data-checked] > .m_3e4da632 {\n      color: var(--mantine-color-disabled-color);\n    }\n\n.m_2980836c[data-indeterminate]:not([data-disabled]),\n  .m_2980836c[data-checked]:not([data-disabled]) {\n    background-color: transparent;\n    border-color: var(--radio-color);\n  }\n\n.m_2980836c[data-indeterminate]:not([data-disabled]) > .m_3e4da632, .m_2980836c[data-checked]:not([data-disabled]) > .m_3e4da632 {\n      color: var(--radio-color);\n      opacity: 1;\n      transform: none;\n    }\n\n.m_3e4da632 {\n  display: block;\n  width: var(--radio-icon-size);\n  height: var(--radio-icon-size);\n  color: transparent;\n  pointer-events: none;\n  transform: translateY(calc(0.3125rem * var(--mantine-scale))) scale(0.5);\n  opacity: 1;\n  transition:\n    transform 100ms ease,\n    opacity 100ms ease;\n}\n\n.m_f3f1af94 {\n  --radio-size-xs: calc(1rem * var(--mantine-scale));\n  --radio-size-sm: calc(1.25rem * var(--mantine-scale));\n  --radio-size-md: calc(1.5rem * var(--mantine-scale));\n  --radio-size-lg: calc(1.875rem * var(--mantine-scale));\n  --radio-size-xl: calc(2.25rem * var(--mantine-scale));\n  --radio-size: var(--radio-size-sm);\n\n  --radio-icon-size-xs: calc(0.375rem * var(--mantine-scale));\n  --radio-icon-size-sm: calc(0.5rem * var(--mantine-scale));\n  --radio-icon-size-md: calc(0.625rem * var(--mantine-scale));\n  --radio-icon-size-lg: calc(0.875rem * var(--mantine-scale));\n  --radio-icon-size-xl: calc(1rem * var(--mantine-scale));\n  --radio-icon-size: var(--radio-icon-size-sm);\n  --radio-icon-color: var(--mantine-color-white);\n}\n\n.m_89c4f5e4 {\n  position: relative;\n  width: var(--radio-size);\n  height: var(--radio-size);\n  order: 1;\n}\n\n.m_89c4f5e4:where([data-label-position='left']) {\n    order: 2;\n  }\n\n.m_f3ed6b2b {\n  color: var(--radio-icon-color);\n  opacity: var(--radio-icon-opacity, 0);\n  transform: var(--radio-icon-transform, scale(0.2) translateY(calc(0.625rem * var(--mantine-scale))));\n  transition:\n    opacity 100ms ease,\n    transform 200ms ease;\n  pointer-events: none;\n  width: var(--radio-icon-size);\n  height: var(--radio-icon-size);\n  position: absolute;\n  top: calc(50% - var(--radio-icon-size) / 2);\n  left: calc(50% - var(--radio-icon-size) / 2);\n}\n\n.m_8a3dbb89 {\n  border: calc(0.0625rem * var(--mantine-scale)) solid;\n  position: relative;\n  appearance: none;\n  width: var(--radio-size);\n  height: var(--radio-size);\n  border-radius: var(--radio-radius, var(--radio-size));\n  margin: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition-property: background-color, border-color;\n  transition-timing-function: ease;\n  transition-duration: 100ms;\n  cursor: var(--mantine-cursor-type);\n  -webkit-tap-highlight-color: transparent;\n}\n\n:where([data-mantine-color-scheme='light']) .m_8a3dbb89 {\n    background-color: var(--mantine-color-white);\n    border-color: var(--mantine-color-gray-4);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_8a3dbb89 {\n    background-color: var(--mantine-color-dark-6);\n    border-color: var(--mantine-color-dark-4);\n}\n\n.m_8a3dbb89:checked {\n    background-color: var(--radio-color, var(--mantine-primary-color-filled));\n    border-color: var(--radio-color, var(--mantine-primary-color-filled));\n  }\n\n.m_8a3dbb89:checked + .m_f3ed6b2b {\n      --radio-icon-opacity: 1;\n      --radio-icon-transform: scale(1);\n    }\n\n.m_8a3dbb89:disabled {\n    cursor: not-allowed;\n    background-color: var(--mantine-color-disabled);\n    border-color: var(--mantine-color-disabled-border);\n  }\n\n.m_8a3dbb89:disabled + .m_f3ed6b2b {\n      --radio-icon-color: var(--mantine-color-disabled-color);\n    }\n\n.m_8a3dbb89:where([data-error]) {\n    border-color: var(--mantine-color-error);\n  }\n\n.m_1bfe9d39 + .m_f3ed6b2b {\n    --radio-icon-color: var(--radio-color);\n  }\n\n.m_1bfe9d39:checked:not(:disabled) {\n    background-color: transparent;\n    border-color: var(--radio-color);\n  }\n\n.m_1bfe9d39:checked:not(:disabled) + .m_f3ed6b2b {\n      --radio-icon-color: var(--radio-color);\n      --radio-icon-opacity: 1;\n      --radio-icon-transform: none;\n    }\n\n.m_f8d312f2 {\n  --rating-size-xs: calc(0.875rem * var(--mantine-scale));\n  --rating-size-sm: calc(1.125rem * var(--mantine-scale));\n  --rating-size-md: calc(1.25rem * var(--mantine-scale));\n  --rating-size-lg: calc(1.75rem * var(--mantine-scale));\n  --rating-size-xl: calc(2rem * var(--mantine-scale));\n\n  display: flex;\n  width: max-content;\n}\n\n  .m_f8d312f2:where(:has(input:disabled)) {\n    pointer-events: none;\n  }\n\n.m_61734bb7 {\n  position: relative;\n  transition: transform 100ms ease;\n}\n\n.m_61734bb7:where([data-active]) {\n    z-index: 1;\n    transform: scale(1.1);\n  }\n\n.m_5662a89a {\n  width: var(--rating-size);\n  height: var(--rating-size);\n  display: block;\n}\n\n:where([data-mantine-color-scheme='light']) .m_5662a89a {\n    fill: var(--mantine-color-gray-3);\n    stroke: var(--mantine-color-gray-3);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_5662a89a {\n    fill: var(--mantine-color-dark-3);\n    stroke: var(--mantine-color-dark-3);\n}\n\n.m_5662a89a:where([data-filled]) {\n    fill: var(--rating-color);\n    stroke: var(--rating-color);\n  }\n\n.m_211007ba {\n  height: 0;\n  width: 0;\n  position: absolute;\n  overflow: hidden;\n  white-space: nowrap;\n  opacity: 0;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.m_211007ba:focus-visible + label {\n    outline: 2px solid var(--mantine-primary-color-filled);\n    outline-offset: calc(0.125rem * var(--mantine-scale));\n  }\n\n.m_21342ee4 {\n  display: block;\n  cursor: pointer;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: var(--rating-item-z-index, 0);\n  -webkit-tap-highlight-color: transparent;\n}\n\n.m_21342ee4:where([data-read-only]) {\n    cursor: default;\n  }\n\n.m_21342ee4:where(:last-of-type) {\n    position: relative;\n  }\n\n.m_fae05d6a {\n  clip-path: var(--rating-symbol-clip-path);\n}\n\n.m_1b3c8819 {\n  --tooltip-radius: var(--mantine-radius-default);\n\n  position: absolute;\n  padding: calc(var(--mantine-spacing-xs) / 2) var(--mantine-spacing-xs);\n  pointer-events: none;\n  font-size: var(--mantine-font-size-sm);\n  white-space: nowrap;\n  border-radius: var(--tooltip-radius);\n}\n\n  :where([data-mantine-color-scheme='light']) .m_1b3c8819 {\n    background-color: var(--tooltip-bg, var(--mantine-color-gray-9));\n    color: var(--tooltip-color, var(--mantine-color-white));\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_1b3c8819 {\n    background-color: var(--tooltip-bg, var(--mantine-color-gray-2));\n    color: var(--tooltip-color, var(--mantine-color-black));\n}\n\n  .m_1b3c8819:where([data-multiline]) {\n    white-space: normal;\n  }\n\n  .m_1b3c8819:where([data-fixed]) {\n    position: fixed;\n  }\n\n.m_f898399f {\n  background-color: inherit;\n  border: 0;\n  z-index: 1;\n}\n\n.m_b32e4812 {\n  position: relative;\n  width: var(--rp-size);\n  height: var(--rp-size);\n  min-width: var(--rp-size);\n  min-height: var(--rp-size);\n  --rp-transition-duration: 0ms;\n}\n\n.m_d43b5134 {\n  width: var(--rp-size);\n  height: var(--rp-size);\n  min-width: var(--rp-size);\n  min-height: var(--rp-size);\n  transform: rotate(-90deg);\n}\n\n.m_b1ca1fbf {\n  stroke: var(--curve-color, var(--rp-curve-root-color));\n  transition:\n    stroke-dashoffset var(--rp-transition-duration) ease,\n    stroke-dasharray var(--rp-transition-duration) ease,\n    stroke var(--rp-transition-duration);\n}\n\n[data-mantine-color-scheme='light'] .m_b1ca1fbf {\n    --rp-curve-root-color: var(--mantine-color-gray-2);\n}\n\n[data-mantine-color-scheme='dark'] .m_b1ca1fbf {\n    --rp-curve-root-color: var(--mantine-color-dark-4);\n}\n\n.m_b23f9dc4 {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  inset-inline: var(--rp-label-offset);\n}\n\n.m_cf365364 {\n  --sc-padding-xs: calc(0.125rem * var(--mantine-scale)) calc(0.375rem * var(--mantine-scale));\n  --sc-padding-sm: calc(0.1875rem * var(--mantine-scale)) calc(0.625rem * var(--mantine-scale));\n  --sc-padding-md: calc(0.25rem * var(--mantine-scale)) calc(0.875rem * var(--mantine-scale));\n  --sc-padding-lg: calc(0.4375rem * var(--mantine-scale)) calc(1rem * var(--mantine-scale));\n  --sc-padding-xl: calc(0.625rem * var(--mantine-scale)) calc(1.25rem * var(--mantine-scale));\n\n  --sc-transition-duration: 200ms;\n  --sc-padding: var(--sc-padding-sm);\n  --sc-transition-timing-function: ease;\n  --sc-font-size: var(--mantine-font-size-sm);\n\n  position: relative;\n  display: inline-flex;\n  flex-direction: row;\n  width: auto;\n  border-radius: var(--sc-radius, var(--mantine-radius-default));\n  overflow: hidden;\n  padding: calc(0.25rem * var(--mantine-scale));\n}\n\n  .m_cf365364:where([data-full-width]) {\n    display: flex;\n  }\n\n  .m_cf365364:where([data-orientation='vertical']) {\n    display: flex;\n    flex-direction: column;\n    width: max-content;\n  }\n\n  .m_cf365364:where([data-orientation='vertical']):where([data-full-width]) {\n      width: auto;\n    }\n\n  :where([data-mantine-color-scheme='light']) .m_cf365364 {\n    background-color: var(--mantine-color-gray-1);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_cf365364 {\n    background-color: var(--mantine-color-dark-8);\n}\n\n.m_9e182ccd {\n  position: absolute;\n  display: block;\n  z-index: 1;\n  border-radius: var(--sc-radius, var(--mantine-radius-default));\n}\n\n:where([data-mantine-color-scheme='light']) .m_9e182ccd {\n    box-shadow: var(--sc-shadow, none);\n    background-color: var(--sc-color, var(--mantine-color-white));\n}\n\n:where([data-mantine-color-scheme='dark']) .m_9e182ccd {\n    box-shadow: none;\n    background-color: var(--sc-color, var(--mantine-color-dark-5));\n}\n\n.m_1738fcb2 {\n  -webkit-tap-highlight-color: transparent;\n  font-weight: 500;\n  display: block;\n  text-align: center;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  user-select: none;\n  border-radius: var(--sc-radius, var(--mantine-radius-default));\n  font-size: var(--sc-font-size);\n  padding: var(--sc-padding);\n  transition: color var(--sc-transition-duration) var(--sc-transition-timing-function);\n  cursor: pointer;\n\n  /* outline is controlled by .input */\n  outline: var(--segmented-control-outline, none);\n}\n\n:where([data-mantine-color-scheme='light']) .m_1738fcb2 {\n    color: var(--mantine-color-gray-7);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_1738fcb2 {\n    color: var(--mantine-color-dark-1);\n}\n\n.m_1738fcb2:where([data-read-only]) {\n    cursor: default;\n  }\n\nfieldset:disabled .m_1738fcb2,\n  .m_1738fcb2:where([data-disabled]) {\n    cursor: not-allowed;\n    color: var(--mantine-color-disabled-color);\n  }\n\n:where([data-mantine-color-scheme='light']) .m_1738fcb2:where([data-active]) {\n      color: var(--sc-label-color, var(--mantine-color-black));\n}\n\n:where([data-mantine-color-scheme='dark']) .m_1738fcb2:where([data-active]) {\n      color: var(--sc-label-color, var(--mantine-color-white));\n}\n\n.m_cf365364:where([data-initialized]) .m_1738fcb2:where([data-active])::before {\n        display: none;\n      }\n\n.m_1738fcb2:where([data-active])::before {\n      content: '';\n      inset: 0;\n      z-index: 0;\n      position: absolute;\n      border-radius: var(--sc-radius, var(--mantine-radius-default));\n}\n\n:where([data-mantine-color-scheme='light']) .m_1738fcb2:where([data-active])::before {\n        box-shadow: var(--sc-shadow, none);\n        background-color: var(--sc-color, var(--mantine-color-white));\n}\n\n:where([data-mantine-color-scheme='dark']) .m_1738fcb2:where([data-active])::before {\n        box-shadow: none;\n        background-color: var(--sc-color, var(--mantine-color-dark-5));\n}\n\n@media (hover: hover) {\n      :where([data-mantine-color-scheme='light']) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):hover {\n        color: var(--mantine-color-black);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):hover {\n        color: var(--mantine-color-white);\n  }\n}\n\n@media (hover: none) {\n      :where([data-mantine-color-scheme='light']) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):active {\n        color: var(--mantine-color-black);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_1738fcb2:where(:not([data-disabled], [data-active], [data-read-only])):active {\n        color: var(--mantine-color-white);\n  }\n}\n\n@media (hover: hover) {\n\n  fieldset:disabled .m_1738fcb2:hover {\n      color: var(--mantine-color-disabled-color) !important;\n  }\n}\n\n@media (hover: none) {\n\n  fieldset:disabled .m_1738fcb2:active {\n      color: var(--mantine-color-disabled-color) !important;\n  }\n}\n\n.m_1714d588 {\n  height: 0;\n  width: 0;\n  position: absolute;\n  overflow: hidden;\n  white-space: nowrap;\n  opacity: 0;\n}\n\n.m_1714d588[data-focus-ring='auto']:focus:focus-visible + .m_1738fcb2 {\n        --segmented-control-outline: 2px solid var(--mantine-primary-color-filled);\n      }\n\n.m_1714d588[data-focus-ring='always']:focus + .m_1738fcb2 {\n        --segmented-control-outline: 2px solid var(--mantine-primary-color-filled);\n      }\n\n.m_69686b9b {\n  position: relative;\n  flex: 1;\n  z-index: 2;\n  transition: border-color var(--sc-transition-duration) var(--sc-transition-timing-function);\n}\n\n.m_cf365364[data-with-items-borders] :where(.m_69686b9b)::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    inset-inline-start: 0;\n    background-color: var(--separator-color);\n    width: calc(0.0625rem * var(--mantine-scale));\n    transition: background-color var(--sc-transition-duration) var(--sc-transition-timing-function);\n  }\n\n.m_69686b9b[data-orientation='vertical']::before {\n      top: 0;\n      inset-inline: 0;\n      bottom: auto;\n      height: calc(0.0625rem * var(--mantine-scale));\n      width: auto;\n    }\n\n:where([data-mantine-color-scheme='light']) .m_69686b9b {\n    --separator-color: var(--mantine-color-gray-3);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_69686b9b {\n    --separator-color: var(--mantine-color-dark-4);\n}\n\n.m_69686b9b:first-of-type::before {\n      --separator-color: transparent;\n    }\n\n[data-mantine-color-scheme] .m_69686b9b[data-active]::before, [data-mantine-color-scheme] .m_69686b9b[data-active] + .m_69686b9b::before {\n          --separator-color: transparent;\n        }\n\n.m_78882f40 {\n  position: relative;\n  z-index: 2;\n}\n\n.m_fa528724 {\n  --scp-filled-segment-color: var(--mantine-primary-color-filled);\n  --scp-transition-duration: 0ms;\n  --scp-thickness: calc(0.625rem * var(--mantine-scale));\n}\n\n  :where([data-mantine-color-scheme='light']) .m_fa528724 {\n    --scp-empty-segment-color: var(--mantine-color-gray-2);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_fa528724 {\n    --scp-empty-segment-color: var(--mantine-color-dark-4);\n}\n\n  .m_fa528724 {\n\n  position: relative;\n  width: fit-content;\n}\n\n.m_62e9e7e2 {\n  display: block;\n  transform: var(--scp-rotation);\n  overflow: hidden;\n}\n\n.m_c573fb6f {\n  transition:\n    stroke-dashoffset var(--scp-transition-duration) ease,\n    stroke-dasharray var(--scp-transition-duration) ease,\n    stroke var(--scp-transition-duration);\n}\n\n.m_4fa340f2 {\n  position: absolute;\n  margin: 0;\n  padding: 0;\n  inset-inline: 0;\n  text-align: center;\n  z-index: 1;\n}\n\n.m_4fa340f2:where([data-position='bottom']) {\n    bottom: 0;\n    padding-inline: calc(var(--scp-thickness) * 2);\n  }\n\n.m_4fa340f2:where([data-position='bottom']):where([data-orientation='down']) {\n      bottom: auto;\n      top: 0;\n    }\n\n.m_4fa340f2:where([data-position='center']) {\n    top: 50%;\n    padding-inline: calc(var(--scp-thickness) * 3);\n  }\n\n.m_925c2d2c {\n  container: simple-grid / inline-size;\n}\n\n.m_2415a157 {\n  display: grid;\n  grid-template-columns: repeat(var(--sg-cols), minmax(0, 1fr));\n  gap: var(--sg-spacing-y) var(--sg-spacing-x);\n}\n\n@keyframes m_299c329c {\n  0%,\n  100% {\n    opacity: 0.4;\n  }\n\n  50% {\n    opacity: 1;\n  }\n}\n\n.m_18320242 {\n  height: var(--skeleton-height, auto);\n  width: var(--skeleton-width, 100%);\n  border-radius: var(--skeleton-radius, var(--mantine-radius-default));\n  position: relative;\n  transform: translateZ(0);\n  -webkit-transform: translateZ(0);\n}\n\n.m_18320242:where([data-animate])::after {\n    animation: m_299c329c 1500ms linear infinite;\n  }\n\n.m_18320242:where([data-visible]) {\n    overflow: hidden;\n  }\n\n.m_18320242:where([data-visible])::before {\n      position: absolute;\n      content: '';\n      inset: 0;\n      z-index: 10;\n      background-color: var(--mantine-color-body);\n    }\n\n.m_18320242:where([data-visible])::after {\n      position: absolute;\n      content: '';\n      inset: 0;\n      z-index: 11;\n    }\n\n:where([data-mantine-color-scheme='light']) .m_18320242:where([data-visible])::after {\n        background-color: var(--mantine-color-gray-3);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_18320242:where([data-visible])::after {\n        background-color: var(--mantine-color-dark-4);\n}\n\n.m_dd36362e {\n  --slider-size-xs: calc(0.25rem * var(--mantine-scale));\n  --slider-size-sm: calc(0.375rem * var(--mantine-scale));\n  --slider-size-md: calc(0.5rem * var(--mantine-scale));\n  --slider-size-lg: calc(0.625rem * var(--mantine-scale));\n  --slider-size-xl: calc(0.75rem * var(--mantine-scale));\n\n  --slider-size: var(--slider-size-md);\n  --slider-radius: calc(62.5rem * var(--mantine-scale));\n  --slider-color: var(--mantine-primary-color-filled);\n  --slider-track-disabled-bg: var(--mantine-color-disabled);\n\n  -webkit-tap-highlight-color: transparent;\n  outline: none;\n  height: calc(var(--slider-size) * 2);\n  padding-inline: var(--slider-size);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  touch-action: none;\n  position: relative;\n}\n\n  [data-mantine-color-scheme='light'] .m_dd36362e {\n    --slider-track-bg: var(--mantine-color-gray-2);\n}\n\n  [data-mantine-color-scheme='dark'] .m_dd36362e {\n    --slider-track-bg: var(--mantine-color-dark-4);\n}\n\n.m_c9357328 {\n  position: absolute;\n  top: calc(-2.25rem * var(--mantine-scale));\n  font-size: var(--mantine-font-size-xs);\n  color: var(--mantine-color-white);\n  padding: calc(var(--mantine-spacing-xs) / 2);\n  border-radius: var(--mantine-radius-sm);\n  white-space: nowrap;\n  pointer-events: none;\n  user-select: none;\n  touch-action: none;\n}\n\n:where([data-mantine-color-scheme='light']) .m_c9357328 {\n    background-color: var(--mantine-color-gray-9);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_c9357328 {\n    background-color: var(--mantine-color-dark-4);\n}\n\n.m_c9a9a60a {\n  position: absolute;\n  display: flex;\n  height: var(--slider-thumb-size);\n  width: var(--slider-thumb-size);\n  border: calc(0.25rem * var(--mantine-scale)) solid;\n  transform: translate(-50%, -50%);\n  top: 50%;\n  cursor: pointer;\n  border-radius: var(--slider-radius);\n  align-items: center;\n  justify-content: center;\n  transition:\n    box-shadow 100ms ease,\n    transform 100ms ease;\n  z-index: 3;\n  user-select: none;\n  touch-action: none;\n  outline-offset: calc(0.125rem * var(--mantine-scale));\n  left: var(--slider-thumb-offset);\n}\n\n:where([dir=\"rtl\"]) .m_c9a9a60a {\n    left: auto;\n    right: calc(var(--slider-thumb-offset) - var(--slider-thumb-size));\n}\n\nfieldset:disabled .m_c9a9a60a,\n  .m_c9a9a60a:where([data-disabled]) {\n    display: none;\n  }\n\n.m_c9a9a60a:where([data-dragging]) {\n    transform: translate(-50%, -50%) scale(1.05);\n    box-shadow: var(--mantine-shadow-sm);\n  }\n\n:where([data-mantine-color-scheme='light']) .m_c9a9a60a {\n    color: var(--slider-color);\n    border-color: var(--slider-color);\n    background-color: var(--mantine-color-white);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_c9a9a60a {\n    color: var(--mantine-color-white);\n    border-color: var(--mantine-color-white);\n    background-color: var(--slider-color);\n}\n\n.m_a8645c2 {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  height: calc(var(--slider-size) * 2);\n  cursor: pointer;\n}\n\nfieldset:disabled .m_a8645c2,\n  .m_a8645c2:where([data-disabled]) {\n    cursor: not-allowed;\n  }\n\n.m_c9ade57f {\n  position: relative;\n  width: 100%;\n  height: var(--slider-size);\n}\n\n.m_c9ade57f:where([data-inverted]:not([data-disabled])) {\n    --track-bg: var(--slider-color);\n  }\n\nfieldset:disabled .m_c9ade57f:where([data-inverted]),\n  .m_c9ade57f:where([data-inverted][data-disabled]) {\n    --track-bg: var(--slider-track-disabled-bg);\n  }\n\n.m_c9ade57f::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    border-radius: var(--slider-radius);\n    inset-inline: calc(var(--slider-size) * -1);\n    background-color: var(--track-bg, var(--slider-track-bg));\n    z-index: 0;\n  }\n\n.m_38aeed47 {\n  position: absolute;\n  z-index: 1;\n  top: 0;\n  bottom: 0;\n  background-color: var(--slider-color);\n  border-radius: var(--slider-radius);\n  width: var(--slider-bar-width);\n  inset-inline-start: var(--slider-bar-offset);\n}\n\n.m_38aeed47:where([data-inverted]) {\n    background-color: var(--slider-track-bg);\n  }\n\nfieldset:disabled .m_38aeed47:where(:not([data-inverted])),\n  .m_38aeed47:where([data-disabled]:not([data-inverted])) {\n    background-color: var(--mantine-color-disabled-color);\n  }\n\n.m_b7b0423a {\n  position: absolute;\n  inset-inline-start: calc(var(--mark-offset) - var(--slider-size) / 2);\n  top: 0;\n  z-index: 2;\n  height: 0;\n  pointer-events: none;\n}\n\n.m_dd33bc19 {\n  border: calc(0.125rem * var(--mantine-scale)) solid;\n  height: var(--slider-size);\n  width: var(--slider-size);\n  border-radius: calc(62.5rem * var(--mantine-scale));\n  background-color: var(--mantine-color-white);\n  pointer-events: none;\n}\n\n:where([data-mantine-color-scheme='light']) .m_dd33bc19 {\n    border-color: var(--mantine-color-gray-2);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_dd33bc19 {\n    border-color: var(--mantine-color-dark-4);\n}\n\n.m_dd33bc19:where([data-filled]) {\n    border-color: var(--slider-color);\n  }\n\n.m_dd33bc19:where([data-filled]):where([data-disabled]) {\n      border-color: var(--mantine-color-disabled-border);\n    }\n\n.m_68c77a5b {\n  transform: translate(calc(-50% + var(--slider-size) / 2), calc(var(--mantine-spacing-xs) / 2));\n  font-size: var(--mantine-font-size-sm);\n  white-space: nowrap;\n  cursor: pointer;\n  user-select: none;\n}\n\n:where([data-mantine-color-scheme='light']) .m_68c77a5b {\n    color: var(--mantine-color-gray-6);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_68c77a5b {\n    color: var(--mantine-color-dark-2);\n}\n\n.m_559cce2d {\n  position: relative;\n}\n\n  .m_559cce2d:where([data-has-spoiler]) {\n    margin-bottom: calc(1.5rem * var(--mantine-scale));\n  }\n\n.m_b912df4e {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  transition: max-height var(--spoiler-transition-duration, 200ms) ease;\n}\n\n.m_b9131032 {\n  position: absolute;\n  inset-inline-start: 0;\n  top: 100%;\n  height: calc(1.5rem * var(--mantine-scale));\n}\n\n.m_6d731127 {\n  display: flex;\n  flex-direction: column;\n  align-items: var(--stack-align, stretch);\n  justify-content: var(--stack-justify, flex-start);\n  gap: var(--stack-gap, var(--mantine-spacing-md));\n}\n\n.m_cbb4ea7e {\n  --stepper-icon-size-xs: calc(2.125rem * var(--mantine-scale));\n  --stepper-icon-size-sm: calc(2.25rem * var(--mantine-scale));\n  --stepper-icon-size-md: calc(2.625rem * var(--mantine-scale));\n  --stepper-icon-size-lg: calc(3rem * var(--mantine-scale));\n  --stepper-icon-size-xl: calc(3.25rem * var(--mantine-scale));\n\n  --stepper-icon-size: var(--stepper-icon-size-md);\n  --stepper-color: var(--mantine-primary-color-filled);\n  --stepper-content-padding: var(--mantine-spacing-md);\n  --stepper-spacing: var(--mantine-spacing-md);\n  --stepper-radius: calc(62.5rem * var(--mantine-scale));\n  --stepper-fz: var(--mantine-font-size-md);\n  --stepper-outline-thickness: calc(0.125rem * var(--mantine-scale));\n}\n\n  [data-mantine-color-scheme='light'] .m_cbb4ea7e {\n    --stepper-outline-color: var(--mantine-color-gray-2);\n}\n\n  [data-mantine-color-scheme='dark'] .m_cbb4ea7e {\n    --stepper-outline-color: var(--mantine-color-dark-5);\n}\n\n.m_aaf89d0b {\n  display: flex;\n  flex-wrap: nowrap;\n  align-items: center;\n}\n\n.m_aaf89d0b:where([data-wrap]) {\n    flex-wrap: wrap;\n    gap: var(--mantine-spacing-md) 0;\n  }\n\n.m_aaf89d0b:where([data-orientation='vertical']) {\n    flex-direction: column;\n  }\n\n.m_aaf89d0b:where([data-orientation='vertical']):where([data-icon-position='left']) {\n      align-items: flex-start;\n    }\n\n.m_aaf89d0b:where([data-orientation='vertical']):where([data-icon-position='right']) {\n      align-items: flex-end;\n    }\n\n.m_aaf89d0b:where([data-orientation='horizontal']) {\n    flex-direction: row;\n  }\n\n.m_2a371ac9 {\n  transition: background-color 150ms ease;\n  flex: 1;\n  height: var(--stepper-outline-thickness);\n  margin-inline: var(--mantine-spacing-md);\n  background-color: var(--stepper-outline-color);\n}\n\n.m_2a371ac9:where([data-active]) {\n    background-color: var(--stepper-color);\n  }\n\n.m_78da155d {\n  padding-top: var(--stepper-content-padding);\n}\n\n.m_cbb57068 {\n  --step-color: var(--stepper-color);\n\n  display: flex;\n  cursor: default;\n}\n\n.m_cbb57068:where([data-allow-click]) {\n    cursor: pointer;\n  }\n\n.m_cbb57068:where([data-icon-position='left']) {\n    flex-direction: row;\n  }\n\n.m_cbb57068:where([data-icon-position='right']) {\n    flex-direction: row-reverse;\n  }\n\n.m_f56b1e2c {\n  align-items: center;\n}\n\n.m_833edb7e {\n  --separator-spacing: calc(var(--mantine-spacing-xs) / 2);\n\n  justify-content: flex-start;\n  min-height: calc(var(--stepper-icon-size) + var(--mantine-spacing-xl) + var(--separator-spacing));\n  margin-top: var(--separator-spacing);\n  overflow: hidden;\n}\n\n.m_833edb7e:where(:first-of-type) {\n    margin-top: 0;\n  }\n\n.m_833edb7e:where(:last-of-type) {\n    min-height: auto;\n  }\n\n.m_833edb7e:where(:last-of-type) .m_6496b3f3 {\n      display: none;\n    }\n\n.m_818e70b {\n  position: relative;\n}\n\n.m_6496b3f3 {\n  top: calc(var(--stepper-icon-size) + var(--separator-spacing));\n  inset-inline-start: calc(var(--stepper-icon-size) / 2);\n  height: 100vh;\n  position: absolute;\n  border-inline-start: var(--stepper-outline-thickness) solid var(--stepper-outline-color);\n}\n\n.m_6496b3f3:where([data-active]) {\n    border-color: var(--stepper-color);\n  }\n\n.m_1959ad01 {\n  height: var(--stepper-icon-size);\n  width: var(--stepper-icon-size);\n  min-height: var(--stepper-icon-size);\n  min-width: var(--stepper-icon-size);\n  border-radius: var(--stepper-radius);\n  font-size: var(--stepper-fz);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  font-weight: bold;\n  transition:\n    background-color 150ms ease,\n    border-color 150ms ease;\n  border: var(--stepper-outline-thickness) solid var(--stepper-outline-color);\n  background-color: var(--stepper-outline-color);\n}\n\n:where([data-mantine-color-scheme='light']) .m_1959ad01 {\n    color: var(--mantine-color-gray-7);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_1959ad01 {\n    color: var(--mantine-color-dark-1);\n}\n\n.m_1959ad01:where([data-progress]) {\n    border-color: var(--step-color);\n  }\n\n.m_1959ad01:where([data-completed]) {\n    color: var(--stepper-icon-color, var(--mantine-color-white));\n    background-color: var(--step-color);\n    border-color: var(--step-color);\n  }\n\n.m_a79331dc {\n  position: absolute;\n  inset: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--stepper-icon-color, var(--mantine-color-white));\n}\n\n.m_1956aa2a {\n  display: flex;\n  flex-direction: column;\n}\n\n.m_1956aa2a:where([data-icon-position='left']) {\n    margin-inline-start: var(--mantine-spacing-sm);\n  }\n\n.m_1956aa2a:where([data-icon-position='right']) {\n    text-align: right;\n    margin-inline-end: var(--mantine-spacing-sm);\n  }\n\n:where([dir=\"rtl\"]) .m_1956aa2a:where([data-icon-position='right']) {\n      text-align: left;\n}\n\n.m_12051f6c {\n  font-weight: 500;\n  font-size: var(--stepper-fz);\n  line-height: 1;\n}\n\n.m_164eea74 {\n  margin-top: calc(var(--stepper-spacing) / 3);\n  margin-bottom: calc(var(--stepper-spacing) / 3);\n  font-size: calc(var(--stepper-fz) - calc(0.125rem * var(--mantine-scale)));\n  line-height: 1;\n  color: var(--mantine-color-dimmed);\n}\n\n.m_5f93f3bb {\n  --switch-height-xs: calc(1rem * var(--mantine-scale));\n  --switch-height-sm: calc(1.25rem * var(--mantine-scale));\n  --switch-height-md: calc(1.5rem * var(--mantine-scale));\n  --switch-height-lg: calc(1.875rem * var(--mantine-scale));\n  --switch-height-xl: calc(2.25rem * var(--mantine-scale));\n\n  --switch-width-xs: calc(2rem * var(--mantine-scale));\n  --switch-width-sm: calc(2.375rem * var(--mantine-scale));\n  --switch-width-md: calc(2.875rem * var(--mantine-scale));\n  --switch-width-lg: calc(3.5rem * var(--mantine-scale));\n  --switch-width-xl: calc(4.5rem * var(--mantine-scale));\n\n  --switch-thumb-size-xs: calc(0.75rem * var(--mantine-scale));\n  --switch-thumb-size-sm: calc(0.875rem * var(--mantine-scale));\n  --switch-thumb-size-md: calc(1.125rem * var(--mantine-scale));\n  --switch-thumb-size-lg: calc(1.375rem * var(--mantine-scale));\n  --switch-thumb-size-xl: calc(1.75rem * var(--mantine-scale));\n\n  --switch-label-font-size-xs: calc(0.3125rem * var(--mantine-scale));\n  --switch-label-font-size-sm: calc(0.375rem * var(--mantine-scale));\n  --switch-label-font-size-md: calc(0.4375rem * var(--mantine-scale));\n  --switch-label-font-size-lg: calc(0.5625rem * var(--mantine-scale));\n  --switch-label-font-size-xl: calc(0.6875rem * var(--mantine-scale));\n\n  --switch-track-label-padding-xs: calc(0.125rem * var(--mantine-scale));\n  --switch-track-label-padding-sm: calc(0.15625rem * var(--mantine-scale));\n  --switch-track-label-padding-md: calc(0.1875rem * var(--mantine-scale));\n  --switch-track-label-padding-lg: calc(0.1875rem * var(--mantine-scale));\n  --switch-track-label-padding-xl: calc(0.21875rem * var(--mantine-scale));\n\n  --switch-height: var(--switch-height-sm);\n  --switch-width: var(--switch-width-sm);\n  --switch-thumb-size: var(--switch-thumb-size-sm);\n  --switch-label-font-size: var(--switch-label-font-size-sm);\n  --switch-track-label-padding: var(--switch-track-label-padding-sm);\n  --switch-radius: calc(62.5rem * var(--mantine-scale));\n  --switch-color: var(--mantine-primary-color-filled);\n  --switch-disabled-color: var(--mantine-color-disabled);\n\n  position: relative;\n}\n\n.m_926b4011 {\n  height: 0;\n  width: 0;\n  opacity: 0;\n  margin: 0;\n  padding: 0;\n  position: absolute;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.m_9307d992 {\n  -webkit-tap-highlight-color: transparent;\n  cursor: var(--switch-cursor, var(--mantine-cursor-type));\n  overflow: hidden;\n  position: relative;\n  border-radius: var(--switch-radius);\n  background-color: var(--switch-bg);\n  height: var(--switch-height);\n  min-width: var(--switch-width);\n  margin: 0;\n  transition:\n    background-color 150ms ease,\n    border-color 150ms ease;\n  appearance: none;\n  display: flex;\n  align-items: center;\n  font-size: var(--switch-label-font-size);\n  font-weight: 600;\n  order: var(--switch-order, 1);\n  user-select: none;\n  z-index: 0;\n  line-height: 0;\n  color: var(--switch-text-color);\n}\n\n.m_9307d992:where([data-without-labels]) {\n    width: var(--switch-width);\n  }\n\n.m_926b4011:focus-visible + .m_9307d992 {\n    outline: 2px solid var(--mantine-primary-color-filled);\n    outline-offset: calc(0.125rem * var(--mantine-scale));\n  }\n\n.m_926b4011:checked + .m_9307d992 {\n    --switch-bg: var(--switch-color);\n    --switch-text-color: var(--mantine-color-white);\n  }\n\n.m_926b4011:disabled + .m_9307d992,\n  .m_926b4011[data-disabled] + .m_9307d992 {\n    --switch-bg: var(--switch-disabled-color);\n    --switch-cursor: not-allowed;\n  }\n\n[data-mantine-color-scheme='light'] .m_9307d992 {\n    --switch-bg: var(--mantine-color-gray-3);\n    --switch-text-color: var(--mantine-color-gray-6);\n}\n\n[data-mantine-color-scheme='dark'] .m_9307d992 {\n    --switch-bg: var(--mantine-color-dark-5);\n    --switch-text-color: var(--mantine-color-dark-1);\n}\n\n.m_9307d992[data-label-position='left'] {\n    --switch-order: 2;\n  }\n\n.m_93039a1d {\n  position: absolute;\n  z-index: 1;\n  border-radius: var(--switch-radius);\n  display: flex;\n  background-color: var(--switch-thumb-bg, var(--mantine-color-white));\n  height: var(--switch-thumb-size);\n  width: var(--switch-thumb-size);\n  inset-inline-start: var(--switch-thumb-start, var(--switch-track-label-padding));\n  transition: inset-inline-start 150ms ease;\n}\n\n.m_93039a1d:where([data-with-thumb-indicator])::before {\n    content: '';\n    width: 40%;\n    height: 40%;\n    background-color: var(--switch-bg);\n    position: absolute;\n    border-radius: var(--switch-radius);\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n  }\n\n.m_93039a1d > * {\n    margin: auto;\n  }\n\n.m_926b4011:checked + * > .m_93039a1d {\n    --switch-thumb-start: calc(100% - var(--switch-thumb-size) - var(--switch-track-label-padding));\n  }\n\n.m_926b4011:disabled + * > .m_93039a1d,\n  .m_926b4011[data-disabled] + * > .m_93039a1d {\n    --switch-thumb-bg: var(--switch-thumb-bg-disabled);\n  }\n\n[data-mantine-color-scheme='light'] .m_93039a1d {\n    --switch-thumb-bg-disabled: var(--mantine-color-gray-0);\n}\n\n[data-mantine-color-scheme='dark'] .m_93039a1d {\n    --switch-thumb-bg-disabled: var(--mantine-color-dark-3);\n}\n\n.m_8277e082 {\n  height: 100%;\n  display: grid;\n  place-content: center;\n  min-width: calc(var(--switch-width) - var(--switch-thumb-size));\n  padding-inline: var(--switch-track-label-padding);\n  margin-inline-start: calc(var(--switch-thumb-size) + var(--switch-track-label-padding));\n  transition: margin 150ms ease;\n}\n\n.m_926b4011:checked + * > .m_8277e082 {\n    margin-inline-end: calc(var(--switch-thumb-size) + var(--switch-track-label-padding));\n    margin-inline-start: 0;\n  }\n\n.m_b23fa0ef {\n  width: 100%;\n  border-collapse: collapse;\n  border-spacing: 0;\n  line-height: var(--mantine-line-height);\n  font-size: var(--mantine-font-size-sm);\n  table-layout: var(--table-layout, auto);\n  caption-side: var(--table-caption-side, bottom);\n  border: none;\n}\n\n  :where([data-mantine-color-scheme='light']) .m_b23fa0ef {\n    --table-hover-color: var(--mantine-color-gray-1);\n    --table-striped-color: var(--mantine-color-gray-0);\n    --table-border-color: var(--mantine-color-gray-3);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_b23fa0ef {\n    --table-hover-color: var(--mantine-color-dark-5);\n    --table-striped-color: var(--mantine-color-dark-6);\n    --table-border-color: var(--mantine-color-dark-4);\n}\n\n  .m_b23fa0ef:where([data-with-table-border]) {\n    border: calc(0.0625rem * var(--mantine-scale)) solid var(--table-border-color);\n  }\n\n  .m_b23fa0ef:where([data-tabular-nums]) {\n    font-variant-numeric: tabular-nums;\n  }\n\n  .m_b23fa0ef:where([data-variant='vertical']) :where(.m_4e7aa4f3) {\n    font-weight: 500;\n  }\n\n  :where([data-mantine-color-scheme='light']) .m_b23fa0ef:where([data-variant='vertical']) :where(.m_4e7aa4f3) {\n      background-color: var(--mantine-color-gray-0);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_b23fa0ef:where([data-variant='vertical']) :where(.m_4e7aa4f3) {\n      background-color: var(--mantine-color-dark-6);\n}\n\n.m_4e7aa4f3 {\n  text-align: left;\n}\n\n:where([dir=\"rtl\"]) .m_4e7aa4f3 {\n    text-align: right;\n}\n\n.m_4e7aa4fd {\n  border-bottom: none;\n  background-color: transparent;\n}\n\n@media (hover: hover) {\n    .m_4e7aa4fd:hover:where([data-hover]) {\n      background-color: var(--tr-hover-bg);\n    }\n}\n\n@media (hover: none) {\n    .m_4e7aa4fd:active:where([data-hover]) {\n      background-color: var(--tr-hover-bg);\n    }\n}\n\n.m_4e7aa4fd:where([data-with-row-border]) {\n    border-bottom: calc(0.0625rem * var(--mantine-scale)) solid var(--table-border-color);\n  }\n\n.m_4e7aa4ef,\n.m_4e7aa4f3 {\n  padding: var(--table-vertical-spacing) var(--table-horizontal-spacing, var(--mantine-spacing-xs));\n}\n\n.m_4e7aa4ef:where([data-with-column-border]:not(:last-child)), .m_4e7aa4f3:where([data-with-column-border]:not(:last-child)) {\n    border-inline-end: calc(0.0625rem * var(--mantine-scale)) solid var(--table-border-color);\n  }\n\n.m_b2404537 > :where(tr):where([data-with-row-border]:last-of-type) {\n      border-bottom: none;\n    }\n\n.m_b2404537 > :where(tr):where([data-striped='odd']:nth-of-type(odd)) {\n      background-color: var(--table-striped-color);\n    }\n\n.m_b2404537 > :where(tr):where([data-striped='even']:nth-of-type(even)) {\n      background-color: var(--table-striped-color);\n    }\n\n.m_b2404537 > :where(tr)[data-hover] {\n      --tr-hover-bg: var(--table-highlight-on-hover-color, var(--table-hover-color));\n    }\n\n.m_b242d975 {\n  top: var(--table-sticky-header-offset, 0);\n  z-index: 3;\n}\n\n.m_b242d975:where([data-sticky]) {\n    position: sticky;\n  }\n\n.m_b242d975:where([data-sticky]) :where(.m_4e7aa4f3) {\n      position: sticky;\n      top: var(--table-sticky-header-offset, 0);\n      background-color: var(--mantine-color-body);\n    }\n\n:where([data-with-table-border]) .m_b242d975[data-sticky] .m_4e7aa4f3 {\n  top: initial;\n}\n\n.m_9e5a3ac7 {\n  color: var(--mantine-color-dimmed);\n}\n\n.m_9e5a3ac7:where([data-side='top']) {\n    margin-bottom: var(--mantine-spacing-xs);\n  }\n\n.m_9e5a3ac7:where([data-side='bottom']) {\n    margin-top: var(--mantine-spacing-xs);\n  }\n\n.m_a100c15 {\n  overflow-x: var(--table-overflow);\n}\n\n.m_62259741 {\n  min-width: var(--table-min-width);\n  max-height: var(--table-max-height);\n}\n\n.m_bcaa9990 {\n  display: flex;\n  flex-direction: column;\n  --toc-depth-offset: 0.8em;\n}\n\n.m_375a65ef {\n  display: block;\n  padding: 0.3em 0.8em;\n  font-size: var(--toc-size, var(--mantine-font-size-md));\n  border-radius: var(--toc-radius, var(--mantine-radius-default));\n  padding-left: max(calc(var(--depth-offset) * var(--toc-depth-offset)), 0.8em);\n}\n\n@media (hover: hover) {\n      :where([data-mantine-color-scheme='light']) .m_375a65ef:where(:hover):where(:not([data-variant='none'])) {\n        background-color: var(--mantine-color-gray-1);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_375a65ef:where(:hover):where(:not([data-variant='none'])) {\n        background-color: var(--mantine-color-dark-5);\n  }\n}\n\n@media (hover: none) {\n      :where([data-mantine-color-scheme='light']) .m_375a65ef:where(:active):where(:not([data-variant='none'])) {\n        background-color: var(--mantine-color-gray-1);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_375a65ef:where(:active):where(:not([data-variant='none'])) {\n        background-color: var(--mantine-color-dark-5);\n  }\n}\n\n.m_375a65ef:where([data-active]) {\n    background-color: var(--toc-bg);\n    color: var(--toc-color);\n  }\n\n[data-mantine-color-scheme='light'] .m_89d60db1 {\n    --tab-border-color: var(--mantine-color-gray-3);\n}\n  [data-mantine-color-scheme='dark'] .m_89d60db1 {\n    --tab-border-color: var(--mantine-color-dark-4);\n}\n  .m_89d60db1 {\n\n  display: var(--tabs-display);\n  flex-direction: var(--tabs-flex-direction);\n\n  --tabs-list-direction: row;\n  --tabs-panel-grow: unset;\n  --tabs-display: block;\n  --tabs-flex-direction: row;\n  --tabs-list-border-width: 0;\n  --tabs-list-border-size: 0 0 var(--tabs-list-border-width) 0;\n  --tabs-list-gap: unset;\n\n  --tabs-list-line-bottom: 0;\n  --tabs-list-line-top: unset;\n  --tabs-list-line-start: 0;\n  --tabs-list-line-end: 0;\n\n  --tab-radius: var(--tabs-radius) var(--tabs-radius) 0 0;\n  --tab-border-width: 0 0 var(--tabs-list-border-width) 0;\n}\n\n  .m_89d60db1[data-inverted] {\n    --tabs-list-line-bottom: unset;\n    --tabs-list-line-top: 0;\n    --tab-radius: 0 0 var(--tabs-radius) var(--tabs-radius);\n    --tab-border-width: var(--tabs-list-border-width) 0 0 0;\n  }\n\n  .m_89d60db1[data-inverted] .m_576c9d4::before {\n      top: 0;\n      bottom: unset;\n    }\n\n  .m_89d60db1[data-orientation='vertical'] {\n    --tabs-list-line-start: unset;\n    --tabs-list-line-end: 0;\n    --tabs-list-line-top: 0;\n    --tabs-list-line-bottom: 0;\n    --tabs-list-border-size: 0 var(--tabs-list-border-width) 0 0;\n    --tab-border-width: 0 var(--tabs-list-border-width) 0 0;\n    --tab-radius: var(--tabs-radius) 0 0 var(--tabs-radius);\n    --tabs-list-direction: column;\n    --tabs-panel-grow: 1;\n    --tabs-display: flex;\n  }\n\n  [dir=\"rtl\"] .m_89d60db1[data-orientation='vertical'] {\n      --tabs-list-border-size: 0 0 0 var(--tabs-list-border-width);\n      --tab-border-width: 0 0 0 var(--tabs-list-border-width);\n      --tab-radius: 0 var(--tabs-radius) var(--tabs-radius) 0;\n}\n\n  .m_89d60db1[data-orientation='vertical'][data-placement='right'] {\n      --tabs-flex-direction: row-reverse;\n      --tabs-list-line-start: 0;\n      --tabs-list-line-end: unset;\n      --tabs-list-border-size: 0 0 0 var(--tabs-list-border-width);\n      --tab-border-width: 0 0 0 var(--tabs-list-border-width);\n      --tab-radius: 0 var(--tabs-radius) var(--tabs-radius) 0;\n    }\n\n  [dir=\"rtl\"] .m_89d60db1[data-orientation='vertical'][data-placement='right'] {\n        --tabs-list-border-size: 0 var(--tabs-list-border-width) 0 0;\n        --tab-border-width: 0 var(--tabs-list-border-width) 0 0;\n        --tab-radius: var(--tabs-radius) 0 0 var(--tabs-radius);\n}\n\n  .m_89d60db1[data-variant='default'] {\n    --tabs-list-border-width: calc(0.125rem * var(--mantine-scale));\n  }\n\n  [data-mantine-color-scheme='light'] .m_89d60db1[data-variant='default'] {\n      --tab-hover-color: var(--mantine-color-gray-0);\n}\n\n  [data-mantine-color-scheme='dark'] .m_89d60db1[data-variant='default'] {\n      --tab-hover-color: var(--mantine-color-dark-6);\n}\n\n  .m_89d60db1[data-variant='outline'] {\n    --tabs-list-border-width: calc(0.0625rem * var(--mantine-scale));\n  }\n\n  .m_89d60db1[data-variant='pills'] {\n    --tabs-list-gap: calc(var(--mantine-spacing-sm) / 2);\n  }\n\n  [data-mantine-color-scheme='light'] .m_89d60db1[data-variant='pills'] {\n      --tab-hover-color: var(--mantine-color-gray-0);\n}\n\n  [data-mantine-color-scheme='dark'] .m_89d60db1[data-variant='pills'] {\n      --tab-hover-color: var(--mantine-color-dark-6);\n}\n\n.m_89d33d6d {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: var(--tabs-justify, flex-start);\n  flex-direction: var(--tabs-list-direction);\n  gap: var(--tabs-list-gap);\n}\n\n.m_89d33d6d:where([data-grow]) .m_4ec4dce6 {\n    flex: 1;\n  }\n\n.m_b0c91715 {\n  flex-grow: var(--tabs-panel-grow);\n}\n\n.m_4ec4dce6 {\n  position: relative;\n  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);\n  font-size: var(--mantine-font-size-sm);\n  white-space: nowrap;\n  z-index: 0;\n  display: flex;\n  align-items: center;\n  line-height: 1;\n  user-select: none;\n}\n\n.m_4ec4dce6:where(:disabled, [data-disabled]) {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n\n.m_4ec4dce6:focus {\n    z-index: 1;\n  }\n\n.m_fc420b1f {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.m_fc420b1f:where([data-position='left']:not(:only-child)) {\n    margin-inline-end: var(--mantine-spacing-xs);\n  }\n\n.m_fc420b1f:where([data-position='right']:not(:only-child)) {\n    margin-inline-start: var(--mantine-spacing-xs);\n  }\n\n.m_42bbd1ae {\n  flex: 1;\n  text-align: center;\n}\n\n/*************************************** default variant ***************************************/\n.m_576c9d4 {\n  position: relative;\n}\n.m_576c9d4::before {\n    content: '';\n    position: absolute;\n    border: 1px solid var(--tab-border-color);\n    bottom: var(--tabs-list-line-bottom);\n    inset-inline-start: var(--tabs-list-line-start);\n    inset-inline-end: var(--tabs-list-line-end);\n    top: var(--tabs-list-line-top);\n  }\n\n.m_539e827b {\n  border-radius: var(--tab-radius);\n  border-width: var(--tab-border-width);\n  border-style: solid;\n  border-color: transparent;\n  background-color: transparent;\n}\n\n.m_539e827b:where([data-active]) {\n    border-color: var(--tabs-color);\n  }\n\n@media (hover: hover) {\n    .m_539e827b:hover {\n    background-color: var(--tab-hover-color);\n    }\n\n    .m_539e827b:hover:where(:not([data-active])) {\n      border-color: var(--tab-border-color);\n    }\n}\n\n@media (hover: none) {\n    .m_539e827b:active {\n    background-color: var(--tab-hover-color);\n    }\n\n    .m_539e827b:active:where(:not([data-active])) {\n      border-color: var(--tab-border-color);\n    }\n}\n\n@media (hover: hover) {\n    .m_539e827b:disabled:hover, .m_539e827b[data-disabled]:hover {\n      background-color: transparent;\n    }\n}\n\n@media (hover: none) {\n    .m_539e827b:disabled:active, .m_539e827b[data-disabled]:active {\n      background-color: transparent;\n    }\n}\n\n/*************************************** outline variant ***************************************/\n.m_6772fbd5 {\n  position: relative;\n}\n.m_6772fbd5::before {\n    content: '';\n    position: absolute;\n    border-color: var(--tab-border-color);\n    border-width: var(--tabs-list-border-size);\n    border-style: solid;\n    bottom: var(--tabs-list-line-bottom);\n    inset-inline-start: var(--tabs-list-line-start);\n    inset-inline-end: var(--tabs-list-line-end);\n    top: var(--tabs-list-line-top);\n  }\n\n.m_b59ab47c {\n  border-top: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  border-bottom: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  border-right: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  border-left: calc(0.0625rem * var(--mantine-scale)) solid transparent;\n  border-top-color: var(--tab-border-top-color);\n  border-bottom-color: var(--tab-border-bottom-color);\n  border-radius: var(--tab-radius);\n  position: relative;\n\n  --tab-border-bottom-color: transparent;\n  --tab-border-top-color: transparent;\n  --tab-border-inline-end-color: transparent;\n  --tab-border-inline-start-color: transparent;\n}\n\n.m_b59ab47c:where([data-active])::before {\n      content: '';\n      position: absolute;\n      background-color: var(--tab-border-color);\n      bottom: var(--tab-before-bottom, calc(-0.0625rem * var(--mantine-scale)));\n      left: var(--tab-before-left, calc(-0.0625rem * var(--mantine-scale)));\n      right: var(--tab-before-right, auto);\n      top: var(--tab-before-top, auto);\n      width: calc(0.0625rem * var(--mantine-scale));\n      height: calc(0.0625rem * var(--mantine-scale));\n    }\n\n.m_b59ab47c:where([data-active])::after {\n      content: '';\n      position: absolute;\n      background-color: var(--tab-border-color);\n      bottom: var(--tab-after-bottom, calc(-0.0625rem * var(--mantine-scale)));\n      right: var(--tab-after-right, calc(-0.0625rem * var(--mantine-scale)));\n      left: var(--tab-after-left, auto);\n      top: var(--tab-after-top, auto);\n      width: calc(0.0625rem * var(--mantine-scale));\n      height: calc(0.0625rem * var(--mantine-scale));\n    }\n\n.m_b59ab47c:where([data-active]) {\n\n    border-top-color: var(--tab-border-top-color);\n    border-bottom-color: var(--tab-border-bottom-color);\n    border-inline-start-color: var(--tab-border-inline-start-color);\n    border-inline-end-color: var(--tab-border-inline-end-color);\n\n    --tab-border-top-color: var(--tab-border-color);\n    --tab-border-inline-start-color: var(--tab-border-color);\n    --tab-border-inline-end-color: var(--tab-border-color);\n    --tab-border-bottom-color: var(--mantine-color-body);\n}\n\n.m_b59ab47c:where([data-active])[data-inverted] {\n      --tab-border-bottom-color: var(--tab-border-color);\n      --tab-border-top-color: var(--mantine-color-body);\n\n      --tab-before-bottom: auto;\n      --tab-before-top: calc(-0.0625rem * var(--mantine-scale));\n      --tab-after-bottom: auto;\n      --tab-after-top: calc(-0.0625rem * var(--mantine-scale));\n    }\n\n.m_b59ab47c:where([data-active])[data-orientation='vertical'][data-placement='left'] {\n        --tab-border-inline-end-color: var(--mantine-color-body);\n        --tab-border-inline-start-color: var(--tab-border-color);\n        --tab-border-bottom-color: var(--tab-border-color);\n\n        --tab-before-right: calc(-0.0625rem * var(--mantine-scale));\n        --tab-before-left: auto;\n        --tab-before-bottom: auto;\n        --tab-before-top: calc(-0.0625rem * var(--mantine-scale));\n        --tab-after-left: auto;\n        --tab-after-right: calc(-0.0625rem * var(--mantine-scale));\n      }\n\n[dir=\"rtl\"] .m_b59ab47c:where([data-active])[data-orientation='vertical'][data-placement='left'] {\n          --tab-before-right: auto;\n          --tab-before-left: calc(-0.0625rem * var(--mantine-scale));\n          --tab-after-left: calc(-0.0625rem * var(--mantine-scale));\n          --tab-after-right: auto;\n}\n\n.m_b59ab47c:where([data-active])[data-orientation='vertical'][data-placement='right'] {\n        --tab-border-inline-start-color: var(--mantine-color-body);\n        --tab-border-inline-end-color: var(--tab-border-color);\n        --tab-border-bottom-color: var(--tab-border-color);\n\n        --tab-before-left: calc(-0.0625rem * var(--mantine-scale));\n        --tab-before-right: auto;\n        --tab-before-bottom: auto;\n        --tab-before-top: calc(-0.0625rem * var(--mantine-scale));\n        --tab-after-right: auto;\n        --tab-after-left: calc(-0.0625rem * var(--mantine-scale));\n      }\n\n[dir=\"rtl\"] .m_b59ab47c:where([data-active])[data-orientation='vertical'][data-placement='right'] {\n          --tab-before-left: auto;\n          --tab-before-right: calc(-0.0625rem * var(--mantine-scale));\n          --tab-after-right: calc(-0.0625rem * var(--mantine-scale));\n          --tab-after-left: auto;\n}\n\n/*************************************** pills variant ***************************************/\n.m_c3381914 {\n  border-radius: var(--tabs-radius);\n  background-color: var(--tab-bg);\n  color: var(--tab-color);\n\n  --tab-bg: transparent;\n  --tab-color: inherit;\n}\n@media (hover: hover) {\n    .m_c3381914:not([data-disabled]):hover {\n      --tab-bg: var(--tab-hover-color);\n    }\n}\n@media (hover: none) {\n    .m_c3381914:not([data-disabled]):active {\n      --tab-bg: var(--tab-hover-color);\n    }\n}\n.m_c3381914[data-active][data-active] {\n    --tab-bg: var(--tabs-color);\n    --tab-color: var(--tabs-text-color, var(--mantine-color-white));\n  }\n@media (hover: hover) {\n    .m_c3381914[data-active][data-active]:hover {\n      --tab-bg: var(--tabs-color);\n    }\n}\n@media (hover: none) {\n    .m_c3381914[data-active][data-active]:active {\n      --tab-bg: var(--tabs-color);\n    }\n}\n\n.m_7341320d {\n  --ti-size-xs: calc(1.125rem * var(--mantine-scale));\n  --ti-size-sm: calc(1.375rem * var(--mantine-scale));\n  --ti-size-md: calc(1.75rem * var(--mantine-scale));\n  --ti-size-lg: calc(2.125rem * var(--mantine-scale));\n  --ti-size-xl: calc(2.75rem * var(--mantine-scale));\n  --ti-size: var(--ti-size-md);\n\n  line-height: 1;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  user-select: none;\n\n  width: var(--ti-size);\n  height: var(--ti-size);\n  min-width: var(--ti-size);\n  min-height: var(--ti-size);\n  border-radius: var(--ti-radius, var(--mantine-radius-default));\n  background: var(--ti-bg, var(--mantine-primary-color-filled));\n  color: var(--ti-color, var(--mantine-color-white));\n  border: var(--ti-bd, 1px solid transparent);\n}\n\n.m_43657ece {\n  --offset: calc(var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2);\n  --tl-bullet-size: calc(1.25rem * var(--mantine-scale));\n  --tl-line-width: calc(0.25rem * var(--mantine-scale));\n  --tl-radius: calc(62.5rem * var(--mantine-scale));\n  --tl-color: var(--mantine-primary-color-filled);\n}\n\n  .m_43657ece:where([data-align='left']) {\n    padding-inline-start: var(--offset);\n  }\n\n  .m_43657ece:where([data-align='right']) {\n    padding-inline-end: var(--offset);\n  }\n\n.m_2ebe8099 {\n  font-weight: 500;\n  line-height: 1;\n  margin-bottom: calc(var(--mantine-spacing-xs) / 2);\n}\n\n.m_436178ff {\n  --item-border: var(--tl-line-width) var(--tli-border-style, solid) var(--item-border-color);\n\n  position: relative;\n  color: var(--mantine-color-text);\n}\n\n.m_436178ff::before {\n    content: '';\n    pointer-events: none;\n    position: absolute;\n    top: 0;\n    left: var(--timeline-line-left, 0);\n    right: var(--timeline-line-right, 0);\n    bottom: calc(var(--mantine-spacing-xl) * -1);\n    border-inline-start: var(--item-border);\n    display: var(--timeline-line-display, none);\n  }\n\n.m_43657ece[data-align='left'] .m_436178ff::before {\n      --timeline-line-left: calc(var(--tl-line-width) * -1);\n      --timeline-line-right: auto;\n    }\n\n[dir=\"rtl\"] .m_43657ece[data-align='left'] .m_436178ff::before {\n        --timeline-line-left: auto;\n        --timeline-line-right: calc(var(--tl-line-width) * -1);\n}\n\n.m_43657ece[data-align='right'] .m_436178ff::before {\n      --timeline-line-left: auto;\n      --timeline-line-right: calc(var(--tl-line-width) * -1);\n    }\n\n[dir=\"rtl\"] .m_43657ece[data-align='right'] .m_436178ff::before {\n        --timeline-line-left: calc(var(--tl-line-width) * -1);\n        --timeline-line-right: auto;\n}\n\n.m_43657ece:where([data-align='left']) .m_436178ff {\n    padding-inline-start: var(--offset);\n    text-align: left;\n  }\n\n.m_43657ece:where([data-align='right']) .m_436178ff {\n    padding-inline-end: var(--offset);\n    text-align: right;\n  }\n\n:where([data-mantine-color-scheme='light']) .m_436178ff {\n    --item-border-color: var(--mantine-color-gray-3);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_436178ff {\n    --item-border-color: var(--mantine-color-dark-4);\n}\n\n.m_436178ff:where([data-line-active])::before {\n      border-color: var(--tli-color, var(--tl-color));\n    }\n\n.m_436178ff:where(:not(:last-of-type)) {\n    --timeline-line-display: block;\n  }\n\n.m_436178ff:where(:not(:first-of-type)) {\n    margin-top: var(--mantine-spacing-xl);\n  }\n\n.m_8affcee1 {\n  width: var(--tl-bullet-size);\n  height: var(--tl-bullet-size);\n  border-radius: var(--tli-radius, var(--tl-radius));\n  border: var(--tl-line-width) solid;\n  background-color: var(--mantine-color-body);\n  position: absolute;\n  top: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--mantine-color-text);\n}\n\n:where([data-mantine-color-scheme='light']) .m_8affcee1 {\n    border-color: var(--mantine-color-gray-3);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_8affcee1 {\n    border-color: var(--mantine-color-dark-4);\n}\n\n.m_43657ece:where([data-align='left']) .m_8affcee1 {\n    left: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);\n    right: auto;\n  }\n\n:where([dir=\"rtl\"]) .m_43657ece:where([data-align='left']) .m_8affcee1 {\n      left: auto;\n      right: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);\n}\n\n.m_43657ece:where([data-align='right']) .m_8affcee1 {\n    left: auto;\n    right: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);\n  }\n\n:where([dir=\"rtl\"]) .m_43657ece:where([data-align='right']) .m_8affcee1 {\n      left: calc((var(--tl-bullet-size) / 2 + var(--tl-line-width) / 2) * -1);\n      right: auto;\n}\n\n.m_8affcee1:where([data-with-child]) {\n    border-width: var(--tl-line-width);\n  }\n\n:where([data-mantine-color-scheme='light']) .m_8affcee1:where([data-with-child]) {\n      background-color: var(--mantine-color-gray-3);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_8affcee1:where([data-with-child]) {\n      background-color: var(--mantine-color-dark-4);\n}\n\n.m_8affcee1:where([data-active]) {\n    border-color: var(--tli-color, var(--tl-color));\n    background-color: var(--mantine-color-white);\n    color: var(--tl-icon-color, var(--mantine-color-white));\n  }\n\n.m_8affcee1:where([data-active]):where([data-with-child]) {\n      background-color: var(--tli-color, var(--tl-color));\n      color: var(--tl-icon-color, var(--mantine-color-white));\n    }\n\n.m_43657ece:where([data-align='left']) .m_540e8f41 {\n    padding-inline-start: var(--offset);\n    text-align: left;\n  }\n\n:where([dir=\"rtl\"]) .m_43657ece:where([data-align='left']) .m_540e8f41 {\n      text-align: right;\n}\n\n.m_43657ece:where([data-align='right']) .m_540e8f41 {\n    padding-inline-end: var(--offset);\n    text-align: right;\n  }\n\n:where([dir=\"rtl\"]) .m_43657ece:where([data-align='right']) .m_540e8f41 {\n      text-align: left;\n}\n\n.m_8a5d1357 {\n  margin: 0;\n  font-weight: var(--title-fw);\n  font-size: var(--title-fz);\n  line-height: var(--title-lh);\n  font-family: var(--mantine-font-family-headings);\n  text-wrap: var(--title-text-wrap, var(--mantine-heading-text-wrap));\n}\n\n  .m_8a5d1357:where([data-line-clamp]) {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: var(--title-line-clamp);\n    -webkit-box-orient: vertical;\n  }\n\n.m_f698e191 {\n  --level-offset: var(--mantine-spacing-lg);\n  margin: 0;\n  padding: 0;\n  user-select: none;\n}\n\n.m_75f3ecf {\n  margin: 0;\n  padding: 0;\n}\n\n.m_f6970eb1 {\n  cursor: pointer;\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  outline: 0;\n}\n\n.m_f6970eb1:focus-visible > .m_dc283425 {\n      outline: 2px solid var(--mantine-primary-color-filled);\n      outline-offset: calc(0.125rem * var(--mantine-scale));\n    }\n\n.m_dc283425 {\n  padding-inline-start: var(--label-offset);\n}\n\n:where([data-mantine-color-scheme='light']) .m_dc283425:where([data-selected]) {\n      background-color: var(--mantine-color-gray-1);\n}\n\n:where([data-mantine-color-scheme='dark']) .m_dc283425:where([data-selected]) {\n      background-color: var(--mantine-color-dark-5);\n}\n\n.m_d6493fad :first-child {\n    margin-top: 0;\n  }\n  .m_d6493fad :last-child {\n    margin-bottom: 0;\n  }\n  .m_d6493fad :where(h1, h2, h3, h4, h5, h6) {\n    margin-bottom: var(--mantine-spacing-xs);\n    text-wrap: var(--mantine-heading-text-wrap);\n    font-family: var(--mantine-font-family-headings);\n  }\n  .m_d6493fad :where(h1) {\n    margin-top: calc(1.5 * var(--mantine-spacing-xl));\n    font-size: var(--mantine-h1-font-size);\n    line-height: var(--mantine-h1-line-height);\n    font-weight: var(--mantine-h1-font-weight);\n  }\n  .m_d6493fad :where(h2) {\n    margin-top: var(--mantine-spacing-xl);\n    font-size: var(--mantine-h2-font-size);\n    line-height: var(--mantine-h2-line-height);\n    font-weight: var(--mantine-h2-font-weight);\n  }\n  .m_d6493fad :where(h3) {\n    margin-top: calc(0.8 * var(--mantine-spacing-xl));\n    font-size: var(--mantine-h3-font-size);\n    line-height: var(--mantine-h3-line-height);\n    font-weight: var(--mantine-h3-font-weight);\n  }\n  .m_d6493fad :where(h4) {\n    margin-top: calc(0.8 * var(--mantine-spacing-xl));\n    font-size: var(--mantine-h4-font-size);\n    line-height: var(--mantine-h4-line-height);\n    font-weight: var(--mantine-h4-font-weight);\n  }\n  .m_d6493fad :where(h5) {\n    margin-top: calc(0.5 * var(--mantine-spacing-xl));\n    font-size: var(--mantine-h5-font-size);\n    line-height: var(--mantine-h5-line-height);\n    font-weight: var(--mantine-h5-font-weight);\n  }\n  .m_d6493fad :where(h6) {\n    margin-top: calc(0.5 * var(--mantine-spacing-xl));\n    font-size: var(--mantine-h6-font-size);\n    line-height: var(--mantine-h6-line-height);\n    font-weight: var(--mantine-h6-font-weight);\n  }\n  .m_d6493fad :where(img) {\n    max-width: 100%;\n    margin-bottom: var(--mantine-spacing-xs);\n  }\n  .m_d6493fad :where(p) {\n    margin-top: 0;\n    margin-bottom: var(--mantine-spacing-lg);\n  }\n  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(mark) {\n      background-color: var(--mantine-color-yellow-2);\n      color: inherit;\n}\n  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(mark) {\n      background-color: var(--mantine-color-yellow-5);\n      color: var(--mantine-color-black);\n}\n  .m_d6493fad :where(a) {\n    color: var(--mantine-color-anchor);\n    text-decoration: none;\n  }\n  @media (hover: hover) {\n    .m_d6493fad :where(a):hover {\n      text-decoration: underline;\n    }\n}\n  @media (hover: none) {\n    .m_d6493fad :where(a):active {\n      text-decoration: underline;\n    }\n}\n  .m_d6493fad :where(hr) {\n    margin-top: var(--mantine-spacing-md);\n    margin-bottom: var(--mantine-spacing-md);\n    border: 0;\n    border-top: calc(0.0625rem * var(--mantine-scale)) solid;\n  }\n  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(hr) {\n      border-color: var(--mantine-color-gray-3);\n}\n  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(hr) {\n      border-color: var(--mantine-color-dark-3);\n}\n  .m_d6493fad :where(pre) {\n    padding: var(--mantine-spacing-xs);\n    line-height: var(--mantine-line-height);\n    margin: 0;\n    margin-top: var(--mantine-spacing-md);\n    margin-bottom: var(--mantine-spacing-md);\n    overflow-x: auto;\n    font-family: var(--mantine-font-family-monospace);\n    font-size: var(--mantine-font-size-xs);\n    border-radius: var(--mantine-radius-sm);\n  }\n  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(pre) {\n      background-color: var(--mantine-color-gray-0);\n}\n  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(pre) {\n      background-color: var(--mantine-color-dark-8);\n}\n  .m_d6493fad :where(pre) :where(code) {\n      background-color: transparent;\n      padding: 0;\n      border-radius: 0;\n      color: inherit;\n      border: 0;\n    }\n  .m_d6493fad :where(kbd) {\n    --kbd-fz: calc(0.75rem * var(--mantine-scale));\n    --kbd-padding: calc(0.1875rem * var(--mantine-scale)) calc(0.3125rem * var(--mantine-scale));\n\n    font-family: var(--mantine-font-family-monospace);\n    line-height: var(--mantine-line-height);\n    font-weight: 700;\n    padding: var(--kbd-padding);\n    font-size: var(--kbd-fz);\n    border-radius: var(--mantine-radius-sm);\n    border: calc(0.0625rem * var(--mantine-scale)) solid;\n    border-bottom-width: calc(0.1875rem * var(--mantine-scale));\n  }\n  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(kbd) {\n      border-color: var(--mantine-color-gray-3);\n      color: var(--mantine-color-gray-7);\n      background-color: var(--mantine-color-gray-0);\n}\n  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(kbd) {\n      border-color: var(--mantine-color-dark-3);\n      color: var(--mantine-color-dark-0);\n      background-color: var(--mantine-color-dark-5);\n}\n  .m_d6493fad :where(code) {\n    line-height: var(--mantine-line-height);\n    padding: calc(0.0625rem * var(--mantine-scale)) calc(0.3125rem * var(--mantine-scale));\n    border-radius: var(--mantine-radius-sm);\n    font-family: var(--mantine-font-family-monospace);\n    font-size: var(--mantine-font-size-xs);\n  }\n  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(code) {\n      background-color: var(--mantine-color-gray-0);\n      color: var(--mantine-color-black);\n}\n  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(code) {\n      background-color: var(--mantine-color-dark-5);\n      color: var(--mantine-color-white);\n}\n  .m_d6493fad :where(ul, ol):not([data-type='taskList']) {\n    margin-bottom: var(--mantine-spacing-md);\n    padding-inline-start: var(--mantine-spacing-xl);\n    list-style-position: outside;\n  }\n  .m_d6493fad :where(table) {\n    width: 100%;\n    border-collapse: collapse;\n    caption-side: bottom;\n    margin-bottom: var(--mantine-spacing-md);\n  }\n  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(table) {\n      --table-border-color: var(--mantine-color-gray-3);\n}\n  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(table) {\n      --table-border-color: var(--mantine-color-dark-4);\n}\n  .m_d6493fad :where(table) :where(caption) {\n      margin-top: var(--mantine-spacing-xs);\n      font-size: var(--mantine-font-size-sm);\n      color: var(--mantine-color-dimmed);\n    }\n  .m_d6493fad :where(table) :where(th) {\n      text-align: left;\n      font-weight: bold;\n      font-size: var(--mantine-font-size-sm);\n      padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);\n    }\n  .m_d6493fad :where(table) :where(thead th) {\n      border-bottom: calc(0.0625rem * var(--mantine-scale)) solid;\n      border-color: var(--table-border-color);\n    }\n  .m_d6493fad :where(table) :where(tfoot th) {\n      border-top: calc(0.0625rem * var(--mantine-scale)) solid;\n      border-color: var(--table-border-color);\n    }\n  .m_d6493fad :where(table) :where(td) {\n      padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);\n      border-bottom: calc(0.0625rem * var(--mantine-scale)) solid;\n      border-color: var(--table-border-color);\n      font-size: var(--mantine-font-size-sm);\n    }\n  .m_d6493fad :where(table) :where(tr:last-of-type td) {\n      border-bottom: 0;\n    }\n  .m_d6493fad :where(blockquote) {\n    font-size: var(--mantine-font-size-lg);\n    line-height: var(--mantine-line-height);\n    margin: var(--mantine-spacing-md) 0;\n    border-radius: var(--mantine-radius-sm);\n    padding: var(--mantine-spacing-md) var(--mantine-spacing-lg);\n  }\n  :where([data-mantine-color-scheme='light']) .m_d6493fad :where(blockquote) {\n      background-color: var(--mantine-color-gray-0);\n}\n  :where([data-mantine-color-scheme='dark']) .m_d6493fad :where(blockquote) {\n      background-color: var(--mantine-color-dark-8);\n}\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;AAMA;;;;AAOA;;;;AAKA;;;;;;;;;;;AAYA;EAEA;;;;;AAKA;EACI;;;;;;AAMJ;;;;AAQA;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0PA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;;;;;;AAOA;;;;;;;;;AASA;;;;AAIA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAQA;;;;;AAKA;;;;;;;;;;;AAcA;EACI;;;;EAIE;;;;EAIF;;;;EAIE;;;;;AAKN;EACI;;;;EAIE;;;;EAIF;;;;EAIE;;;;;AAKN;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;AAcE;;;;AAIF;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAaE;;;;AAIA;;;;AAIA;;;;AAIF;;;;;;;;;AAUE;;;;AAIA;;;;;;AAMF;;;;;;;;AAQE;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKF;;;;;;AAMA;;;;;AAKA;;;;;;;;;;AAWA;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;;;AAaA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;AAKA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCE;EACE;;;;;;AAMF;EACE;;;;;;AAMF;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;AAIF;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AAWA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;;;;;;;;;;;;;;;;;;;;;;AAyBE;;;;AAIA;;;;AAIA;;;;;AAMF;EACI;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;AAKJ;;;;;;;;;AASE;;;;;AAKF;;;;;;;;AAQA;;;;;;;;;;;;;;AAcA;;;;;;;;;AASA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDE;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAOA;;;;;;AAMF;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AASA;;;;AAIA;;;;;;;AAiBA;;;;;;;;;;;;;;;;AAgBA;;;;;;;AAOA;;;;;;;AAQA;;;;AAGA;;;;AAKA;;;;AAIA;;;;;;;;;AASA;;;;AAIA;;;;;;;;AASA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;;;;;;AAYE;;;;AAIA;;;;;AAKF;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;EACM;;;;EAIA;;;;;AAKN;EACM;;;;EAIA;;;;;AAKN;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAUA;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;;;;;;AAeA;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;AAWE;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;AAIF;;;;;;;;;;;;AAYE;EAEA;;;;;AAKA;EAEA;;;;;AAKA;;;;AAIA;EAEA;;;;;AAKA;EAEA;;;;;AAKA;;;;AAIA;;;;AAKA;;;;AAIF;;;;;;;;;;;AAWE;;;;;AAKA;;;;;AAMA;;;;AAIA;;;;AAIF;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAGE;;;;;;;AAMA;;;;AAGA;;;;AAIF;;;;;AASA;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;AAQA;;;;;AAKA;;;;;;;;AAQA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKE;;;;;AAKA;;;;AAIF;;;;;AAKE;;;;AAIA;;;;AAIA;;;;AAKF;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAKA;EACM;;;;EAIA;;;;;AAKN;EACM;;;;EAIA;;;;;AAKN;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;;AAMA;;;;AAKA;;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;;;;;;AASE;;;;;;AAMA;;;;;;AAMF;;;;AAIA;;;;;;;;;;;AAYA;;;;AAKA;;;;;;;AAOA;;;;AAKA;;;;;;AAYA;;;;;;;;AASE;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIF;;;;;;;;;;;;;;;;;;;;;;;;AA4BE;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIF;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAYA;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAeA;;;;;;;AASA;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDE;;;;AAIA;;;;AAIA;;;;AAOA;;;;;AAKA;;;;;;AAMF;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;;;;;;AAWE;;;;AAIA;;;;AAIF;;;;;;;;;;;;;;;AAgBA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;AAgBE;;;;AAIA;;;;AAIF;;;;;AAKA;;;;;;;;;;;AAaA;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDE;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;;;AAcA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;EACE;;;;;;AAMF;EACE;;;;;;AAMJ;;;;;;;;;AAWA;;;;;;;;;AASA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;;;;;;;;;;AAWE;;;;AAIA;;;;AAIF;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAME;;;;AAIF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA;;;;;;;;;;;;;;;;AAgBA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;EACI;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;AAKJ;;;;;AAKA;EAEE;;;;;AAKF;EAEE;;;;;AAKF;;;;;AAMA;;;;AAIA;;;;AAIA;EACI;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;AAKJ;;;;;;AAMA;EAEE;;;;;AAKF;EAEE;;;;;AAKF;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;AAQA;;;;;AAKA;;;;;;;;;;AAUE;;;;AAIA;;;;AAIA;;;;AAIF;;;;;;;;;;;;;;;;;;AAmBE;;;;;AAKA;;;;;AAKF;;;;;;AAMA;;;;;;;;AAUA;;;;;;;;;AAgBA;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCE;;;;AAIF;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;;;;;;;;;AAcA;;;;AAQA;;;;;;AAMA;;;;;;;;;;;;;AAcA;;;;AAIA;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;;AAaE;;;;AAIF;;;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;;;;AASE;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIF;;;;;;;;AAQA;;;;AAQA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;;;;;AAcA;;;;AAIE;;;;AAIA;;;;AAIF;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;;AAoBE;;;;;;AAMA;;;;;;AAMF;;;;;;;;;;AAWE;;;;AAIF;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;AAYE;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;AAIF;;;;;AAKA;;;;;;;;AAQA;;;;AAIA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BE;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIF;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;;;;;AAeA;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;AAcA;;;;;;;;;;;;AAYE;;;;;AAKA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;AAIF;;;;;;;;;;;AAYE;EACE;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;AAKF;;;;;AAKA;;;;;AAKA;EAEA;;;;;AAKA;EAEA;;;;;AAKA;;;;;AAKF;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;AAiBE;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIF;;;;;;;;;;;;;AAaA;;;;AAIA;;;;;;AAMA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;EACI;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;AAKJ;;;;;;;;AAQA;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;;;AAiBA;;;;;;;AAOA;;;;AAIA;EACI;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;AAKJ;;;;;AAKA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;AAKA;;;;;AAKA;EACM;;;;;AAKN;EACM;;;;;AAKN;;;;;AAKA;EACM;;;;;AAKN;EACM;;;;;AAKN;;;;;;AAMA;EAEE;;;;;AAKF;EAEE;;;;;AAKF;;;;;;;;;AASA;;;;;;;;;;;;;;;AAiBA;;;;;AAKA;;;;;;;;;;;;;;;;AAgBA;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAaA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;;;;AASE;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCE;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIF;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAaA;;;;;;;;;;;;;;;;AAiBA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;;;;AAkBA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;AAWE;;;;AAIF;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AAWE;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIF;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;AAqBE;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIF;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;;;;;;;AAmBA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;EACM;;;;EAIA;;;;;AAKN;EACM;;;;EAIA;;;;;AAKN;EAEE;;;;;AAKF;EAEE;;;;;AAKF;;;;;;;;;AASA;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAQA;;;;;AAKA;;;;;;AAME;;;;AAIA;;;;AAIA;;;;;AAMF;;;;;;AAMA;;;;AAOA;;;;;;;;;AASA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;AAuBE;;;;AAIA;;;;AAIF;;;;;;;;;;;;;AAaA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;;;AAsBA;;;;;AAKA;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;AAIA;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIE;;;;AAIF;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAgBE;;;;AAIA;;;;AAIF;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;;;;;;AAmBA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;;;;;;;;;;AAyBA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;AAWE;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIF;;;;AAIA;;;;AAIA;;;;;AAKA;EACI;;;;;AAKJ;EACI;;;;;AAKJ;;;;AAIA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAQA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;;;;AAQA;EACM;;;;EAIA;;;;;AAKN;EACM;;;;EAIA;;;;;AAKN;;;;;AAKA;;;;AAGE;;;;AAGA;;;;;;;;;;;;;;;;;;AAsBA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIF;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;AAMA;;;;AAGA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;AAIA;EACI;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;AAKJ;EACI;;;;;AAKJ;EACI;;;;;AAMJ;;;;AAGA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAYA;;;;;;;;;;;AAaA;;;;;;;;;AAUA;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;;;;;;AAaA;;;;;;;AAQA;;;;;;;;AAQA;EACI;;;;;AAIJ;EACI;;;;;AAIJ;;;;;AAIA;EACI;;;;;AAIJ;EACI;;;;;AAKJ;;;;;;;;;;;;;;;;;;;;;;;AAyBA;;;;;;;;AAQE;;;;AAIA;;;;AAIF;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;AAUA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAUA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;;;AASE;;;;;;;;AAQF;;;;;;;AAOA;;;;;AAKA;;;;;;;;AAQA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAGE;;;;AAGA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;EACE;;;;;AAIF;EACE;;;;;AAIF;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;AAKA;;;;;;AAKA;;;;;;;;AAOA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;AAGA;;;;;;;;AAOA;;;;AAGA", "ignoreList": [0]}}]}