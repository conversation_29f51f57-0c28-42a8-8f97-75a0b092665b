{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/app/api/test-webhook/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// Simple test endpoint to verify webhook functionality\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const verifyToken = process.env.FACEBOOK_VERIFY_TOKEN;\n    \n    // Test webhook verification\n    const mode = searchParams.get('hub.mode');\n    const token = searchParams.get('hub.verify_token');\n    const challenge = searchParams.get('hub.challenge');\n\n    if (mode === 'subscribe' && token === verifyToken) {\n      return new NextResponse(challenge);\n    }\n\n    // Return test information\n    return NextResponse.json({\n      webhook_url: `${request.nextUrl.origin}/api/webhook/facebook`,\n      verify_token_configured: !!verifyToken,\n      page_token_configured: !!process.env.FACEBOOK_PAGE_ACCESS_TOKEN,\n      test_verification_url: `${request.nextUrl.origin}/api/webhook/facebook?hub.mode=subscribe&hub.verify_token=${verifyToken}&hub.challenge=test_challenge`,\n      instructions: {\n        step1: 'Configure FACEBOOK_VERIFY_TOKEN and FACEBOOK_PAGE_ACCESS_TOKEN in .env.local',\n        step2: 'Use ngrok to expose localhost: ngrok http 3000',\n        step3: 'Set webhook URL in Facebook App to: https://your-ngrok-url.ngrok.io/api/webhook/facebook',\n        step4: 'Test verification by visiting the test_verification_url above',\n      }\n    });\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Test webhook failed', details: error },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    \n    return NextResponse.json({\n      message: 'Test webhook received POST request',\n      received_data: body,\n      timestamp: new Date().toISOString(),\n    });\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Test webhook POST failed', details: error },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,QAAQ,GAAG,CAAC,qBAAqB;QAErD,4BAA4B;QAC5B,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,YAAY,aAAa,GAAG,CAAC;QAEnC,IAAI,SAAS,eAAe,UAAU,aAAa;YACjD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC;QAC1B;QAEA,0BAA0B;QAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,aAAa,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC;YAC7D,yBAAyB,CAAC,CAAC;YAC3B,uBAAuB,CAAC,CAAC,QAAQ,GAAG,CAAC,0BAA0B;YAC/D,uBAAuB,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAC,0DAA0D,EAAE,YAAY,6BAA6B,CAAC;YACvJ,cAAc;gBACZ,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,OAAO;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAuB,SAAS;QAAM,GAC/C;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,eAAe;YACf,WAAW,IAAI,OAAO,WAAW;QACnC;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAA4B,SAAS;QAAM,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}