'use client';

import { Container, Title, Text, Button, Stack, Group, Card, Grid } from '@mantine/core';
import { IconLogin, IconDashboard, IconPackage, IconMessages } from '@tabler/icons-react';
import Link from 'next/link';

export default function Home() {
  return (
    <Container size="lg" py="xl">
      <Stack align="center" gap="xl">
        <div style={{ textAlign: 'center' }}>
          <Title order={1} size="3rem" mb="md">
            Product Management System
          </Title>
          <Text size="xl" c="dimmed" mb="xl">
            Full-stack Next.js application with Mantine UI, Supabase, and Facebook Messenger integration
          </Text>
        </div>

        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder h="100%">
              <Stack>
                <IconPackage size="2rem" color="blue" />
                <Title order={3}>Product Management</Title>
                <Text c="dimmed">
                  Manage your product catalog with full CRUD operations, search functionality, and detailed specifications.
                </Text>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder h="100%">
              <Stack>
                <IconMessages size="2rem" color="green" />
                <Title order={3}>Message Handling</Title>
                <Text c="dimmed">
                  Handle customer messages from Facebook Messenger with automated responses and manual override capabilities.
                </Text>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder h="100%">
              <Stack>
                <IconDashboard size="2rem" color="orange" />
                <Title order={3}>Admin Dashboard</Title>
                <Text c="dimmed">
                  Comprehensive admin interface for managing products, viewing messages, and configuring system settings.
                </Text>
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder h="100%">
              <Stack>
                <IconLogin size="2rem" color="purple" />
                <Title order={3}>Secure Authentication</Title>
                <Text c="dimmed">
                  Session-based authentication with JWT tokens and protected admin routes for secure access.
                </Text>
              </Stack>
            </Card>
          </Grid.Col>
        </Grid>

        <Group>
          <Button
            component={Link}
            href="/admin"
            size="lg"
            leftSection={<IconDashboard size="1.2rem" />}
          >
            Go to Admin Dashboard
          </Button>
          <Button
            component={Link}
            href="/login"
            variant="outline"
            size="lg"
            leftSection={<IconLogin size="1.2rem" />}
          >
            Admin Login
          </Button>
        </Group>

        <Card withBorder bg="gray.0" mt="xl">
          <Stack>
            <Title order={4}>Getting Started</Title>
            <Text size="sm">
              1. Set up your Supabase database using the provided schema file
            </Text>
            <Text size="sm">
              2. Configure your environment variables in .env.local
            </Text>
            <Text size="sm">
              3. Login with default credentials: admin / admin123
            </Text>
            <Text size="sm">
              4. Start managing your products and customer messages!
            </Text>
          </Stack>
        </Card>
      </Stack>
    </Container>
  );
}
