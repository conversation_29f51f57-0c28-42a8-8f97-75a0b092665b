(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{128:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let i=r(6203);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${i.version}`}},155:(e,t,r)=>{"use strict";let i=/\s+/g;class s{constructor(e,t){if(t=a(t),e instanceof s)if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;else return new s(e.raw,t);if(e instanceof o)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(i," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!m(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&v(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let t=((this.options.includePrerelease&&g)|(this.options.loose&&b))+":"+e,r=n.get(t);if(r)return r;let i=this.options.loose,s=i?u[h.HYPHENRANGELOOSE]:u[h.HYPHENRANGE];l("hyphen replace",e=e.replace(s,I(this.options.includePrerelease))),l("comparator trim",e=e.replace(u[h.COMPARATORTRIM],d)),l("tilde trim",e=e.replace(u[h.TILDETRIM],f)),l("caret trim",e=e.replace(u[h.CARETTRIM],p));let a=e.split(" ").map(e=>x(e,this.options)).join(" ").split(/\s+/).map(e=>k(e,this.options));i&&(a=a.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(u[h.COMPARATORLOOSE])))),l("range list",a);let c=new Map;for(let e of a.map(e=>new o(e,this.options))){if(m(e))return[e];c.set(e.value,e)}c.size>1&&c.has("")&&c.delete("");let v=[...c.values()];return n.set(t,v),v}intersects(e,t){if(!(e instanceof s))throw TypeError("a Range is required");return this.set.some(r=>y(r,t)&&e.set.some(e=>y(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(C(this.set[t],e,this.options))return!0;return!1}}e.exports=s;let n=new(r(5480)),a=r(7763),o=r(674),l=r(5938),c=r(7814),{safeRe:u,t:h,comparatorTrimReplace:d,tildeTrimReplace:f,caretTrimReplace:p}=r(1708),{FLAG_INCLUDE_PRERELEASE:g,FLAG_LOOSE:b}=r(3280),m=e=>"<0.0.0-0"===e.value,v=e=>""===e.value,y=(e,t)=>{let r=!0,i=e.slice(),s=i.pop();for(;r&&i.length;)r=i.every(e=>s.intersects(e,t)),s=i.pop();return r},x=(e,t)=>(l("comp",e,t),l("caret",e=S(e,t)),l("tildes",e=_(e,t)),l("xrange",e=T(e,t)),l("stars",e=P(e,t)),e),w=e=>!e||"x"===e.toLowerCase()||"*"===e,_=(e,t)=>e.trim().split(/\s+/).map(e=>E(e,t)).join(" "),E=(e,t)=>{let r=t.loose?u[h.TILDELOOSE]:u[h.TILDE];return e.replace(r,(t,r,i,s,n)=>{let a;return l("tilde",e,t,r,i,s,n),w(r)?a="":w(i)?a=`>=${r}.0.0 <${+r+1}.0.0-0`:w(s)?a=`>=${r}.${i}.0 <${r}.${+i+1}.0-0`:n?(l("replaceTilde pr",n),a=`>=${r}.${i}.${s}-${n} <${r}.${+i+1}.0-0`):a=`>=${r}.${i}.${s} <${r}.${+i+1}.0-0`,l("tilde return",a),a})},S=(e,t)=>e.trim().split(/\s+/).map(e=>O(e,t)).join(" "),O=(e,t)=>{l("caret",e,t);let r=t.loose?u[h.CARETLOOSE]:u[h.CARET],i=t.includePrerelease?"-0":"";return e.replace(r,(t,r,s,n,a)=>{let o;return l("caret",e,t,r,s,n,a),w(r)?o="":w(s)?o=`>=${r}.0.0${i} <${+r+1}.0.0-0`:w(n)?o="0"===r?`>=${r}.${s}.0${i} <${r}.${+s+1}.0-0`:`>=${r}.${s}.0${i} <${+r+1}.0.0-0`:a?(l("replaceCaret pr",a),o="0"===r?"0"===s?`>=${r}.${s}.${n}-${a} <${r}.${s}.${+n+1}-0`:`>=${r}.${s}.${n}-${a} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${n}-${a} <${+r+1}.0.0-0`):(l("no pr"),o="0"===r?"0"===s?`>=${r}.${s}.${n}${i} <${r}.${s}.${+n+1}-0`:`>=${r}.${s}.${n}${i} <${r}.${+s+1}.0-0`:`>=${r}.${s}.${n} <${+r+1}.0.0-0`),l("caret return",o),o})},T=(e,t)=>(l("replaceXRanges",e,t),e.split(/\s+/).map(e=>R(e,t)).join(" ")),R=(e,t)=>{e=e.trim();let r=t.loose?u[h.XRANGELOOSE]:u[h.XRANGE];return e.replace(r,(r,i,s,n,a,o)=>{l("xRange",e,r,i,s,n,a,o);let c=w(s),u=c||w(n),h=u||w(a);return"="===i&&h&&(i=""),o=t.includePrerelease?"-0":"",c?r=">"===i||"<"===i?"<0.0.0-0":"*":i&&h?(u&&(n=0),a=0,">"===i?(i=">=",u?(s=+s+1,n=0):n=+n+1,a=0):"<="===i&&(i="<",u?s=+s+1:n=+n+1),"<"===i&&(o="-0"),r=`${i+s}.${n}.${a}${o}`):u?r=`>=${s}.0.0${o} <${+s+1}.0.0-0`:h&&(r=`>=${s}.${n}.0${o} <${s}.${+n+1}.0-0`),l("xRange return",r),r})},P=(e,t)=>(l("replaceStars",e,t),e.trim().replace(u[h.STAR],"")),k=(e,t)=>(l("replaceGTE0",e,t),e.trim().replace(u[t.includePrerelease?h.GTE0PRE:h.GTE0],"")),I=e=>(t,r,i,s,n,a,o,l,c,u,h,d)=>(r=w(i)?"":w(s)?`>=${i}.0.0${e?"-0":""}`:w(n)?`>=${i}.${s}.0${e?"-0":""}`:a?`>=${r}`:`>=${r}${e?"-0":""}`,l=w(c)?"":w(u)?`<${+c+1}.0.0-0`:w(h)?`<${c}.${+u+1}.0-0`:d?`<=${c}.${u}.${h}-${d}`:e?`<${c}.${u}.${+h+1}-0`:`<=${l}`,`${r} ${l}`.trim()),C=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(l(e[r].semver),e[r].semver!==o.ANY&&e[r].semver.prerelease.length>0){let i=e[r].semver;if(i.major===t.major&&i.minor===t.minor&&i.patch===t.patch)return!0}return!1}return!0}},373:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(6861));class n extends s.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:i}={}){let s="";"plain"===i?s="pl":"phrase"===i?s="ph":"websearch"===i&&(s="w");let n=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${s}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=n},476:(e,t,r)=>{var i=r(5609),s=function(e,t){i.call(this,e),this.name="NotBeforeError",this.date=t};s.prototype=Object.create(i.prototype),s.prototype.constructor=s,e.exports=s},674:(e,t,r)=>{"use strict";let i=Symbol("SemVer ANY");class s{static get ANY(){return i}constructor(e,t){if(t=n(t),e instanceof s)if(!!t.loose===e.loose)return e;else e=e.value;c("comparator",e=e.trim().split(/\s+/).join(" "),t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===i?this.value="":this.value=this.operator+this.semver.version,c("comp",this)}parse(e){let t=this.options.loose?a[o.COMPARATORLOOSE]:a[o.COMPARATOR],r=e.match(t);if(!r)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new u(r[2],this.options.loose):this.semver=i}toString(){return this.value}test(e){if(c("Comparator.test",e,this.options.loose),this.semver===i||e===i)return!0;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof s))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new h(e.value,t).test(this.value):""===e.operator?""===e.value||new h(this.value,t).test(e.semver):!((t=n(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=s;let n=r(7763),{safeRe:a,t:o}=r(1708),l=r(2603),c=r(5938),u=r(7814),h=r(155)},771:e=>{"use strict";let t=/^[0-9]+$/,r=(e,r)=>{let i=t.test(e),s=t.test(r);return i&&s&&(e*=1,r*=1),e===r?0:i&&!s?-1:s&&!i?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},810:e=>{var t=1/0,r=0/0,i=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,a=/^0o[0-7]+$/i,o=parseInt,l=Object.prototype.toString;function c(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){var u,h,d;return"number"==typeof e&&e==(d=(h=(u=e)?(u=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(c(e)){var t,u="function"==typeof e.valueOf?e.valueOf():e;e=c(u)?u+"":u}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var h=n.test(e);return h||a.test(e)?o(e.slice(2),h?2:8):s.test(e)?r:+e}(u))===t||u===-t?(u<0?-1:1)*17976931348623157e292:u==u?u:0:0===u?u:0)%1,h==h?d?h-d:h:0)}},905:(e,t,r)=>{"use strict";let i=r(1708),s=r(3280),n=r(7814),a=r(771),o=r(9410),l=r(3705),c=r(8348),u=r(9879),h=r(9574),d=r(7684),f=r(5320),p=r(4605),g=r(2909),b=r(4966),m=r(9372),v=r(1947),y=r(4401),x=r(9263),w=r(3105),_=r(9694),E=r(983),S=r(2145),O=r(8275),T=r(3681),R=r(4554),P=r(2603),k=r(4316),I=r(674),C=r(155),j=r(8540),A=r(1691),N=r(6170),$=r(2372),L=r(8229),M=r(3912),D=r(1319),U=r(2463),B=r(2124),q=r(7106);e.exports={parse:o,valid:l,clean:c,inc:u,diff:h,major:d,minor:f,patch:p,prerelease:g,compare:b,rcompare:m,compareLoose:v,compareBuild:y,sort:x,rsort:w,gt:_,lt:E,eq:S,neq:O,gte:T,lte:R,cmp:P,coerce:k,Comparator:I,Range:C,satisfies:j,toComparators:A,maxSatisfying:N,minSatisfying:$,minVersion:L,validRange:M,outside:D,gtr:U,ltr:B,intersects:q,simplifyRange:r(3689),subset:r(9166),SemVer:n,re:i.re,src:i.src,tokens:i.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:a.compareIdentifiers,rcompareIdentifiers:a.rcompareIdentifiers}},977:(e,t,r)=>{var i=r(5356),s=i.Buffer;function n(e,t){for(var r in e)t[r]=e[r]}function a(e,t,r){return s(e,t,r)}s.from&&s.alloc&&s.allocUnsafe&&s.allocUnsafeSlow?e.exports=i:(n(i,t),t.Buffer=a),a.prototype=Object.create(s.prototype),n(s,a),a.from=function(e,t,r){if("number"==typeof e)throw TypeError("Argument must not be a number");return s(e,t,r)},a.alloc=function(e,t,r){if("number"!=typeof e)throw TypeError("Argument must be a number");var i=s(e);return void 0!==t?"string"==typeof r?i.fill(t,r):i.fill(t):i.fill(0),i},a.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return s(e)},a.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return i.SlowBuffer(e)}},983:(e,t,r)=>{"use strict";let i=r(4966);e.exports=(e,t,r)=>0>i(e,t,r)},999:e=>{"use strict";e.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},1003:e=>{var t=Object.prototype.toString,r=Array.isArray;e.exports=function(e){var i;return"string"==typeof e||!r(e)&&!!(i=e)&&"object"==typeof i&&"[object String]"==t.call(e)}},1281:(e,t,r)=>{"use strict";var i=r(5356).Buffer,s=r(5356).SlowBuffer;function n(e,t){if(!i.isBuffer(e)||!i.isBuffer(t)||e.length!==t.length)return!1;for(var r=0,s=0;s<e.length;s++)r|=e[s]^t[s];return 0===r}e.exports=n,n.install=function(){i.prototype.equal=s.prototype.equal=function(e){return n(this,e)}};var a=i.prototype.equal,o=s.prototype.equal;n.restore=function(){i.prototype.equal=a,s.prototype.equal=o}},1319:(e,t,r)=>{"use strict";let i=r(7814),s=r(674),{ANY:n}=s,a=r(155),o=r(8540),l=r(9694),c=r(983),u=r(4554),h=r(3681);e.exports=(e,t,r,d)=>{let f,p,g,b,m;switch(e=new i(e,d),t=new a(t,d),r){case">":f=l,p=u,g=c,b=">",m=">=";break;case"<":f=c,p=h,g=l,b="<",m="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(o(e,t,d))return!1;for(let r=0;r<t.set.length;++r){let i=t.set[r],a=null,o=null;if(i.forEach(e=>{e.semver===n&&(e=new s(">=0.0.0")),a=a||e,o=o||e,f(e.semver,a.semver,d)?a=e:g(e.semver,o.semver,d)&&(o=e)}),a.operator===b||a.operator===m||(!o.operator||o.operator===b)&&p(e,o.semver)||o.operator===m&&g(e,o.semver))return!1}return!0}},1552:(e,t,r)=>{"use strict";var i=r(5356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return n}});let s=r(5201),n={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:s,headers:n,body:a,cache:o,credentials:l,integrity:c,mode:u,redirect:h,referrer:d,referrerPolicy:f}=t;return{testData:e,api:"fetch",request:{url:r,method:s,headers:[...Array.from(n),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?i.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:c,mode:u,redirect:h,referrer:d,referrerPolicy:f}}}async function o(e,t){let r=(0,s.getTestReqInfo)(t,n);if(!r)return e(t);let{testData:o,proxyPort:l}=r,c=await a(o,t),u=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let h=await u.json(),{api:d}=h;switch(d){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:f,headers:p,body:g}=h.response;return new Response(g?i.from(g,"base64"):null,{status:f,headers:new Headers(p)})}function l(e){return r.g.fetch=function(t,r){var i;return(null==r||null==(i=r.next)?void 0:i.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},1665:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(373));class n{constructor(e,{headers:t={},schema:r,fetch:i}){this.url=e,this.headers=t,this.schema=r,this.fetch=i}select(e,{head:t=!1,count:r}={}){let i=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",n),r&&(this.headers.Prefer=`count=${r}`),new s.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),t&&i.push(`count=${t}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:i,defaultToNull:n=!0}={}){let a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new s.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new s.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=n},1691:(e,t,r)=>{"use strict";let i=r(155);e.exports=(e,t)=>new i(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},1708:(e,t,r)=>{"use strict";let{MAX_SAFE_COMPONENT_LENGTH:i,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:n}=r(3280),a=r(5938),o=(t=e.exports={}).re=[],l=t.safeRe=[],c=t.src=[],u=t.safeSrc=[],h=t.t={},d=0,f="[a-zA-Z0-9-]",p=[["\\s",1],["\\d",n],[f,s]],g=e=>{for(let[t,r]of p)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e},b=(e,t,r)=>{let i=g(t),s=d++;a(e,s,t),h[e]=s,c[s]=t,u[s]=i,o[s]=new RegExp(t,r?"g":void 0),l[s]=new RegExp(i,r?"g":void 0)};b("NUMERICIDENTIFIER","0|[1-9]\\d*"),b("NUMERICIDENTIFIERLOOSE","\\d+"),b("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${f}*`),b("MAINVERSION",`(${c[h.NUMERICIDENTIFIER]})\\.(${c[h.NUMERICIDENTIFIER]})\\.(${c[h.NUMERICIDENTIFIER]})`),b("MAINVERSIONLOOSE",`(${c[h.NUMERICIDENTIFIERLOOSE]})\\.(${c[h.NUMERICIDENTIFIERLOOSE]})\\.(${c[h.NUMERICIDENTIFIERLOOSE]})`),b("PRERELEASEIDENTIFIER",`(?:${c[h.NONNUMERICIDENTIFIER]}|${c[h.NUMERICIDENTIFIER]})`),b("PRERELEASEIDENTIFIERLOOSE",`(?:${c[h.NONNUMERICIDENTIFIER]}|${c[h.NUMERICIDENTIFIERLOOSE]})`),b("PRERELEASE",`(?:-(${c[h.PRERELEASEIDENTIFIER]}(?:\\.${c[h.PRERELEASEIDENTIFIER]})*))`),b("PRERELEASELOOSE",`(?:-?(${c[h.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[h.PRERELEASEIDENTIFIERLOOSE]})*))`),b("BUILDIDENTIFIER",`${f}+`),b("BUILD",`(?:\\+(${c[h.BUILDIDENTIFIER]}(?:\\.${c[h.BUILDIDENTIFIER]})*))`),b("FULLPLAIN",`v?${c[h.MAINVERSION]}${c[h.PRERELEASE]}?${c[h.BUILD]}?`),b("FULL",`^${c[h.FULLPLAIN]}$`),b("LOOSEPLAIN",`[v=\\s]*${c[h.MAINVERSIONLOOSE]}${c[h.PRERELEASELOOSE]}?${c[h.BUILD]}?`),b("LOOSE",`^${c[h.LOOSEPLAIN]}$`),b("GTLT","((?:<|>)?=?)"),b("XRANGEIDENTIFIERLOOSE",`${c[h.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),b("XRANGEIDENTIFIER",`${c[h.NUMERICIDENTIFIER]}|x|X|\\*`),b("XRANGEPLAIN",`[v=\\s]*(${c[h.XRANGEIDENTIFIER]})(?:\\.(${c[h.XRANGEIDENTIFIER]})(?:\\.(${c[h.XRANGEIDENTIFIER]})(?:${c[h.PRERELEASE]})?${c[h.BUILD]}?)?)?`),b("XRANGEPLAINLOOSE",`[v=\\s]*(${c[h.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[h.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[h.XRANGEIDENTIFIERLOOSE]})(?:${c[h.PRERELEASELOOSE]})?${c[h.BUILD]}?)?)?`),b("XRANGE",`^${c[h.GTLT]}\\s*${c[h.XRANGEPLAIN]}$`),b("XRANGELOOSE",`^${c[h.GTLT]}\\s*${c[h.XRANGEPLAINLOOSE]}$`),b("COERCEPLAIN",`(^|[^\\d])(\\d{1,${i}})(?:\\.(\\d{1,${i}}))?(?:\\.(\\d{1,${i}}))?`),b("COERCE",`${c[h.COERCEPLAIN]}(?:$|[^\\d])`),b("COERCEFULL",c[h.COERCEPLAIN]+`(?:${c[h.PRERELEASE]})?`+`(?:${c[h.BUILD]})?`+"(?:$|[^\\d])"),b("COERCERTL",c[h.COERCE],!0),b("COERCERTLFULL",c[h.COERCEFULL],!0),b("LONETILDE","(?:~>?)"),b("TILDETRIM",`(\\s*)${c[h.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",b("TILDE",`^${c[h.LONETILDE]}${c[h.XRANGEPLAIN]}$`),b("TILDELOOSE",`^${c[h.LONETILDE]}${c[h.XRANGEPLAINLOOSE]}$`),b("LONECARET","(?:\\^)"),b("CARETTRIM",`(\\s*)${c[h.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",b("CARET",`^${c[h.LONECARET]}${c[h.XRANGEPLAIN]}$`),b("CARETLOOSE",`^${c[h.LONECARET]}${c[h.XRANGEPLAINLOOSE]}$`),b("COMPARATORLOOSE",`^${c[h.GTLT]}\\s*(${c[h.LOOSEPLAIN]})$|^$`),b("COMPARATOR",`^${c[h.GTLT]}\\s*(${c[h.FULLPLAIN]})$|^$`),b("COMPARATORTRIM",`(\\s*)${c[h.GTLT]}\\s*(${c[h.LOOSEPLAIN]}|${c[h.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",b("HYPHENRANGE",`^\\s*(${c[h.XRANGEPLAIN]})\\s+-\\s+(${c[h.XRANGEPLAIN]})\\s*$`),b("HYPHENRANGELOOSE",`^\\s*(${c[h.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[h.XRANGEPLAINLOOSE]})\\s*$`),b("STAR","(<|>)?=?\\s*\\*"),b("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),b("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},1802:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function i(){}function s(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function n(e,t,i,n,a){if("function"!=typeof i)throw TypeError("The listener must be a function");var o=new s(i,n||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new i:delete e._events[t]}function o(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,i,s=[];if(0===this._eventsCount)return s;for(i in e=this._events)t.call(e,i)&&s.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(e)):s},o.prototype.listeners=function(e){var t=r?r+e:e,i=this._events[t];if(!i)return[];if(i.fn)return[i.fn];for(var s=0,n=i.length,a=Array(n);s<n;s++)a[s]=i[s].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,i=this._events[t];return i?i.fn?1:i.length:0},o.prototype.emit=function(e,t,i,s,n,a){var o=r?r+e:e;if(!this._events[o])return!1;var l,c,u=this._events[o],h=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),h){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,i),!0;case 4:return u.fn.call(u.context,t,i,s),!0;case 5:return u.fn.call(u.context,t,i,s,n),!0;case 6:return u.fn.call(u.context,t,i,s,n,a),!0}for(c=1,l=Array(h-1);c<h;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var d,f=u.length;for(c=0;c<f;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),h){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,i);break;case 4:u[c].fn.call(u[c].context,t,i,s);break;default:if(!l)for(d=1,l=Array(h-1);d<h;d++)l[d-1]=arguments[d];u[c].fn.apply(u[c].context,l)}}return!0},o.prototype.on=function(e,t,r){return n(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return n(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,i,s){var n=r?r+e:e;if(!this._events[n])return this;if(!t)return a(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||s&&!o.once||i&&o.context!==i||a(this,n);else{for(var l=0,c=[],u=o.length;l<u;l++)(o[l].fn!==t||s&&!o[l].once||i&&o[l].context!==i)&&c.push(o[l]);c.length?this._events[n]=1===c.length?c[0]:c:a(this,n)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let i=0,s=e.length;for(;s>0;){let n=s/2|0,a=i+n;0>=r(e[a],t)?(i=++a,s-=n+1):s=n}return i}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let i=r(574);class s{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let s=i.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(s,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=s},816:(e,t,r)=>{let i=r(213);class s extends Error{constructor(e){super(e),this.name="TimeoutError"}}let n=(e,t,r)=>new Promise((n,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void n(e);let o=setTimeout(()=>{if("function"==typeof r){try{n(r())}catch(e){a(e)}return}let i="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new s(i);"function"==typeof e.cancel&&e.cancel(),a(o)},t);i(e.then(n,a),()=>{clearTimeout(o)})});e.exports=n,e.exports.default=n,e.exports.TimeoutError=s}},r={};function i(e){var s=r[e];if(void 0!==s)return s.exports;var n=r[e]={exports:{}},a=!0;try{t[e](n,n.exports,i),a=!1}finally{a&&delete r[e]}return n.exports}i.ab="//";var s={};(()=>{Object.defineProperty(s,"__esModule",{value:!0});let e=i(993),t=i(816),r=i(821),n=()=>{},a=new t.TimeoutError;class o extends e{constructor(e){var t,i,s,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=n,this._resolveIdle=n,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(i=null==(t=e.intervalCap)?void 0:t.toString())?i:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(s=e.interval)?void 0:s.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=n,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=n,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((i,s)=>{let n=async()=>{this._pendingCount++,this._intervalCount++;try{let n=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&s(a)});i(await n)}catch(e){s(e)}this._next()};this._queue.enqueue(n,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}s.default=o})(),e.exports=s})()},1947:(e,t,r)=>{"use strict";let i=r(4966);e.exports=(e,t)=>i(e,t,!0)},2124:(e,t,r)=>{"use strict";let i=r(1319);e.exports=(e,t,r)=>i(e,t,"<",r)},2145:(e,t,r)=>{"use strict";let i=r(4966);e.exports=(e,t,r)=>0===i(e,t,r)},2279:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(6003)),n=i(r(8784));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=s.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,i;let s=null,a=null,o=null,l=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let i=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),n=null==(r=e.headers.get("content-range"))?void 0:r.split("/");i&&n&&n.length>1&&(o=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(s={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,c="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{s=JSON.parse(t),Array.isArray(s)&&404===e.status&&(a=[],s=null,l=200,c="OK")}catch(r){404===e.status&&""===t?(l=204,c="No Content"):s={message:t}}if(s&&this.isMaybeSingle&&(null==(i=null==s?void 0:s.details)?void 0:i.includes("0 rows"))&&(s=null,l=200,c="OK"),s&&this.shouldThrowOnError)throw new n.default(s)}return{error:s,data:a,count:o,status:l,statusText:c}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,i;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(r=null==e?void 0:e.stack)?r:""}`,hint:"",code:`${null!=(i=null==e?void 0:e.code)?i:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},2372:(e,t,r)=>{"use strict";let i=r(7814),s=r(155);e.exports=(e,t,r)=>{let n=null,a=null,o=null;try{o=new s(t,r)}catch(e){return null}return e.forEach(e=>{o.test(e)&&(!n||1===a.compare(e))&&(a=new i(n=e,r))}),n}},2463:(e,t,r)=>{"use strict";let i=r(1319);e.exports=(e,t,r)=>i(e,t,">",r)},2603:(e,t,r)=>{"use strict";let i=r(2145),s=r(8275),n=r(9694),a=r(3681),o=r(983),l=r(4554);e.exports=(e,t,r,c)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return i(e,r,c);case"!=":return s(e,r,c);case">":return n(e,r,c);case">=":return a(e,r,c);case"<":return o(e,r,c);case"<=":return l(e,r,c);default:throw TypeError(`Invalid operator: ${t}`)}}},2775:(e,t,r)=>{var i=r(8973);e.exports=function(e,t){var r=t||Math.floor(Date.now()/1e3);if("string"==typeof e){var s=i(e);if(void 0===s)return;return Math.floor(r+s/1e3)}if("number"==typeof e)return r+e}},2797:e=>{var t,r,i=Object.prototype,s=Function.prototype.toString,n=i.hasOwnProperty,a=s.call(Object),o=i.toString,l=(t=Object.getPrototypeOf,r=Object,function(e){return t(r(e))});e.exports=function(e){if(!(e&&"object"==typeof e)||"[object Object]"!=o.call(e)||function(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}(e))return!1;var t=l(e);if(null===t)return!0;var r=n.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&s.call(r)==a}},2804:e=>{var t=Object.prototype.toString;e.exports=function(e){var r;return!0===e||!1===e||!!(r=e)&&"object"==typeof r&&"[object Boolean]"==t.call(e)}},2815:(e,t,r)=>{"use strict";e.exports=r(7035)},2905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return n},wrapRequestHandler:function(){return a}});let i=r(5201),s=r(1552);function n(){return(0,s.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,i.withRequest)(t,s.reader,()=>e(t,r))}},2909:(e,t,r)=>{"use strict";let i=r(9410);e.exports=(e,t)=>{let r=i(e,t);return r&&r.prerelease.length?r.prerelease:null}},2932:(e,t,r)=>{"use strict";var i=r(977).Buffer,s=r(5607);function n(e){if(i.isBuffer(e))return e;if("string"==typeof e)return i.from(e,"base64");throw TypeError("ECDSA signature must be a Base64 string or a Buffer")}function a(e,t,r){for(var i=0;t+i<r&&0===e[t+i];)++i;return e[t+i]>=128&&--i,i}e.exports={derToJose:function(e,t){e=n(e);var r=s(t),a=r+1,o=e.length,l=0;if(48!==e[l++])throw Error('Could not find expected "seq"');var c=e[l++];if(129===c&&(c=e[l++]),o-l<c)throw Error('"seq" specified length of "'+c+'", only "'+(o-l)+'" remaining');if(2!==e[l++])throw Error('Could not find expected "int" for "r"');var u=e[l++];if(o-l-2<u)throw Error('"r" specified length of "'+u+'", only "'+(o-l-2)+'" available');if(a<u)throw Error('"r" specified length of "'+u+'", max of "'+a+'" is acceptable');var h=l;if(l+=u,2!==e[l++])throw Error('Could not find expected "int" for "s"');var d=e[l++];if(o-l!==d)throw Error('"s" specified length of "'+d+'", expected "'+(o-l)+'"');if(a<d)throw Error('"s" specified length of "'+d+'", max of "'+a+'" is acceptable');var f=l;if((l+=d)!==o)throw Error('Expected to consume entire buffer, but "'+(o-l)+'" bytes remain');var p=r-u,g=r-d,b=i.allocUnsafe(p+u+g+d);for(l=0;l<p;++l)b[l]=0;e.copy(b,l,h+Math.max(-p,0),h+u),l=r;for(var m=l;l<m+g;++l)b[l]=0;return e.copy(b,l,f+Math.max(-g,0),f+d),b=(b=b.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(e,t){e=n(e);var r=s(t),o=e.length;if(o!==2*r)throw TypeError('"'+t+'" signatures must be "'+2*r+'" bytes, saw "'+o+'"');var l=a(e,0,r),c=a(e,r,e.length),u=r-l,h=r-c,d=2+u+1+1+h,f=d<128,p=i.allocUnsafe((f?2:3)+d),g=0;return p[g++]=48,f?p[g++]=d:(p[g++]=129,p[g++]=255&d),p[g++]=2,p[g++]=u,l<0?(p[g++]=0,g+=e.copy(p,g,0,r)):g+=e.copy(p,g,l,r),p[g++]=2,p[g++]=h,c<0?(p[g++]=0,e.copy(p,g,r)):e.copy(p,g,r+c),p}}},3055:(e,t,r)=>{var i=r(977).Buffer,s=r(4758),n=r(8961),a=r(3667),o=r(8443),l=r(7418);function c(e,t){return i.from(e,t).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function u(e){var t,r,i,s=e.header,a=e.payload,u=e.secret||e.privateKey,h=e.encoding,d=n(s.alg),f=(t=(t=h)||"utf8",r=c(o(s),"binary"),i=c(o(a),t),l.format("%s.%s",r,i)),p=d.sign(f,u);return l.format("%s.%s",f,p)}function h(e){var t=new s(e.secret||e.privateKey||e.key);this.readable=!0,this.header=e.header,this.encoding=e.encoding,this.secret=this.privateKey=this.key=t,this.payload=new s(e.payload),this.secret.once("close",(function(){!this.payload.writable&&this.readable&&this.sign()}).bind(this)),this.payload.once("close",(function(){!this.secret.writable&&this.readable&&this.sign()}).bind(this))}l.inherits(h,a),h.prototype.sign=function(){try{var e=u({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",e),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},h.sign=u,e.exports=h},3105:(e,t,r)=>{"use strict";let i=r(4401);e.exports=(e,t)=>e.sort((e,r)=>i(r,e,t))},3280:e=>{"use strict";e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||0x1fffffffffffff,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},3667:e=>{"use strict";e.exports=globalThis.__import_unsupported("stream")},3681:(e,t,r)=>{"use strict";let i=r(4966);e.exports=(e,t,r)=>i(e,t,r)>=0},3689:(e,t,r)=>{"use strict";let i=r(8540),s=r(4966);e.exports=(e,t,r)=>{let n=[],a=null,o=null,l=e.sort((e,t)=>s(e,t,r));for(let e of l)i(e,t,r)?(o=e,a||(a=e)):(o&&n.push([a,o]),o=null,a=null);a&&n.push([a,null]);let c=[];for(let[e,t]of n)e===t?c.push(e):t||e!==l[0]?t?e===l[0]?c.push(`<=${t}`):c.push(`${e} - ${t}`):c.push(`>=${e}`):c.push("*");let u=c.join(" || "),h="string"==typeof t.raw?t.raw:String(t);return u.length<h.length?u:t}},3705:(e,t,r)=>{"use strict";let i=r(9410);e.exports=(e,t)=>{let r=i(e,t);return r?r.version:null}},3912:(e,t,r)=>{"use strict";let i=r(155);e.exports=(e,t)=>{try{return new i(e,t).range||"*"}catch(e){return null}}},3956:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let i=r(223),s=r(172),n=r(930),a="context",o=new i.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,s.registerGlobal)(a,e,n.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...i){return this._getContextManager().with(e,t,r,...i)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,s.getGlobal)(a)||o}disable(){this._getContextManager().disable(),(0,s.unregisterGlobal)(a,n.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let i=r(56),s=r(912),n=r(957),a=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,a.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:n.DiagLogLevel.INFO})=>{var i,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(i=e.stack)?i:e.message),!1}"number"==typeof r&&(r={logLevel:r});let c=(0,a.getGlobal)("diag"),u=(0,s.createLogLevelDiagLogger)(null!=(o=r.logLevel)?o:n.DiagLogLevel.INFO,e);if(c&&!r.suppressOverrideMessage){let e=null!=(l=Error().stack)?l:"<failed to generate stacktrace>";c.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new i.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let i=r(660),s=r(172),n=r(930),a="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,s.registerGlobal)(a,e,n.DiagAPI.instance())}getMeterProvider(){return(0,s.getGlobal)(a)||i.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,s.unregisterGlobal)(a,n.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let i=r(172),s=r(874),n=r(194),a=r(277),o=r(369),l=r(930),c="propagation",u=new s.NoopTextMapPropagator;class h{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new h),this._instance}setGlobalPropagator(e){return(0,i.registerGlobal)(c,e,l.DiagAPI.instance())}inject(e,t,r=n.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=n.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,i.unregisterGlobal)(c,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,i.getGlobal)(c)||u}}t.PropagationAPI=h},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let i=r(172),s=r(846),n=r(139),a=r(607),o=r(930),l="trace";class c{constructor(){this._proxyTracerProvider=new s.ProxyTracerProvider,this.wrapSpanContext=n.wrapSpanContext,this.isSpanContextValid=n.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalTracerProvider(e){let t=(0,i.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,i.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,i.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new s.ProxyTracerProvider}}t.TraceAPI=c},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let i=r(491),s=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function n(e){return e.getValue(s)||void 0}t.getBaggage=n,t.getActiveBaggage=function(){return n(i.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(s,t)},t.deleteBaggage=function(e){return e.deleteValue(s)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let i=new r(this._entries);return i._entries.set(e,t),i}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let i=r(930),s=r(993),n=r(830),a=i.DiagAPI.instance();t.createBaggage=function(e={}){return new s.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:n.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let i=r(780);class s{active(){return i.ROOT_CONTEXT}with(e,t,r,...i){return t.call(r,...i)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=s},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,i)=>{let s=new r(t._currentContext);return s._currentContext.set(e,i),s},t.deleteValue=e=>{let i=new r(t._currentContext);return i._currentContext.delete(e),i}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let i=r(172);class s{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return n("debug",this._namespace,e)}error(...e){return n("error",this._namespace,e)}info(...e){return n("info",this._namespace,e)}warn(...e){return n("warn",this._namespace,e)}verbose(...e){return n("verbose",this._namespace,e)}}function n(e,t,r){let s=(0,i.getGlobal)("diag");if(s)return r.unshift(t),s[e](...r)}t.DiagComponentLogger=s},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class i{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=i},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let i=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,i){let s=t[r];return"function"==typeof s&&e>=i?s.bind(t):function(){}}return e<i.DiagLogLevel.NONE?e=i.DiagLogLevel.NONE:e>i.DiagLogLevel.ALL&&(e=i.DiagLogLevel.ALL),t=t||{},{error:r("error",i.DiagLogLevel.ERROR),warn:r("warn",i.DiagLogLevel.WARN),info:r("info",i.DiagLogLevel.INFO),debug:r("debug",i.DiagLogLevel.DEBUG),verbose:r("verbose",i.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let i=r(200),s=r(521),n=r(130),a=s.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${a}`),l=i._globalThis;t.registerGlobal=function(e,t,r,i=!1){var n;let a=l[o]=null!=(n=l[o])?n:{version:s.VERSION};if(!i&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(a.version!==s.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${s.VERSION}`);return r.error(t.stack||t.message),!1}return a[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${s.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let i=null==(t=l[o])?void 0:t.version;if(i&&(0,n.isCompatible)(i))return null==(r=l[o])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${s.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let i=r(521),s=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function n(e){let t=new Set([e]),r=new Set,i=e.match(s);if(!i)return()=>!1;let n={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=n.prerelease)return function(t){return t===e};function a(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let i=e.match(s);if(!i)return a(e);let o={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=o.prerelease||n.major!==o.major)return a(e);if(0===n.major)return n.minor===o.minor&&n.patch<=o.patch?(t.add(e),!0):a(e);return n.minor<=o.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=n,t.isCompatible=n(i.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class i{}t.NoopMetric=i;class s extends i{add(e,t){}}t.NoopCounterMetric=s;class n extends i{add(e,t){}}t.NoopUpDownCounterMetric=n;class a extends i{record(e,t){}}t.NoopHistogramMetric=a;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class c extends o{}t.NoopObservableGaugeMetric=c;class u extends o{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new s,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new n,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new c,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let i=r(102);class s{getMeter(e,t,r){return i.NOOP_METER}}t.NoopMeterProvider=s,t.NOOP_METER_PROVIDER=new s},200:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let i=r(476);class s{constructor(e=i.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=s},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let i=r(491),s=r(607),n=r(403),a=r(139),o=i.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){var i;if(null==t?void 0:t.root)return new n.NonRecordingSpan;let l=r&&(0,s.getSpanContext)(r);return"object"==typeof(i=l)&&"string"==typeof i.spanId&&"string"==typeof i.traceId&&"number"==typeof i.traceFlags&&(0,a.isSpanContextValid)(l)?new n.NonRecordingSpan(l):new n.NonRecordingSpan}startActiveSpan(e,t,r,i){let n,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(n=t,l=r):(n=t,a=r,l=i);let c=null!=a?a:o.active(),u=this.startSpan(e,n,c),h=(0,s.setSpan)(c,u);return o.with(h,l,void 0,u)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let i=r(614);class s{getTracer(e,t,r){return new i.NoopTracer}}t.NoopTracerProvider=s},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let i=new(r(614)).NoopTracer;class s{constructor(e,t,r,i){this._provider=e,this.name=t,this.version=r,this.options=i}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,i){let s=this._getTracer();return Reflect.apply(s.startActiveSpan,s,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):i}}t.ProxyTracer=s},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let i=r(125),s=new(r(124)).NoopTracerProvider;class n{getTracer(e,t,r){var s;return null!=(s=this.getDelegateTracer(e,t,r))?s:new i.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:s}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var i;return null==(i=this._delegate)?void 0:i.getTracer(e,t,r)}}t.ProxyTracerProvider=n},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let i=r(780),s=r(403),n=r(491),a=(0,i.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=o,t.getActiveSpan=function(){return o(n.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new s.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=o(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let i=r(564);class s{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),s=r.indexOf("=");if(-1!==s){let n=r.slice(0,s),a=r.slice(s+1,t.length);(0,i.validateKey)(n)&&(0,i.validateValue)(a)&&e.set(n,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new s;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=s},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",i=`[a-z]${r}{0,255}`,s=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,n=RegExp(`^(?:${i}|${s})$`),a=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return n.test(e)},t.validateValue=function(e){return a.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let i=r(325);t.createTraceState=function(e){return new i.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let i=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:i.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let i=r(476),s=r(403),n=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function o(e){return n.test(e)&&e!==i.INVALID_TRACEID}function l(e){return a.test(e)&&e!==i.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new s.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},i={};function s(e){var r=i[e];if(void 0!==r)return r.exports;var n=i[e]={exports:{}},a=!0;try{t[e].call(n.exports,n,n.exports,s),a=!1}finally{a&&delete i[e]}return n.exports}s.ab="//";var n={};(()=>{Object.defineProperty(n,"__esModule",{value:!0}),n.trace=n.propagation=n.metrics=n.diag=n.context=n.INVALID_SPAN_CONTEXT=n.INVALID_TRACEID=n.INVALID_SPANID=n.isValidSpanId=n.isValidTraceId=n.isSpanContextValid=n.createTraceState=n.TraceFlags=n.SpanStatusCode=n.SpanKind=n.SamplingDecision=n.ProxyTracerProvider=n.ProxyTracer=n.defaultTextMapSetter=n.defaultTextMapGetter=n.ValueType=n.createNoopMeter=n.DiagLogLevel=n.DiagConsoleLogger=n.ROOT_CONTEXT=n.createContextKey=n.baggageEntryMetadataFromString=void 0;var e=s(369);Object.defineProperty(n,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=s(780);Object.defineProperty(n,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(n,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=s(972);Object.defineProperty(n,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var i=s(957);Object.defineProperty(n,"DiagLogLevel",{enumerable:!0,get:function(){return i.DiagLogLevel}});var a=s(102);Object.defineProperty(n,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var o=s(901);Object.defineProperty(n,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=s(194);Object.defineProperty(n,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(n,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var c=s(125);Object.defineProperty(n,"ProxyTracer",{enumerable:!0,get:function(){return c.ProxyTracer}});var u=s(846);Object.defineProperty(n,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var h=s(996);Object.defineProperty(n,"SamplingDecision",{enumerable:!0,get:function(){return h.SamplingDecision}});var d=s(357);Object.defineProperty(n,"SpanKind",{enumerable:!0,get:function(){return d.SpanKind}});var f=s(847);Object.defineProperty(n,"SpanStatusCode",{enumerable:!0,get:function(){return f.SpanStatusCode}});var p=s(475);Object.defineProperty(n,"TraceFlags",{enumerable:!0,get:function(){return p.TraceFlags}});var g=s(98);Object.defineProperty(n,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var b=s(139);Object.defineProperty(n,"isSpanContextValid",{enumerable:!0,get:function(){return b.isSpanContextValid}}),Object.defineProperty(n,"isValidTraceId",{enumerable:!0,get:function(){return b.isValidTraceId}}),Object.defineProperty(n,"isValidSpanId",{enumerable:!0,get:function(){return b.isValidSpanId}});var m=s(476);Object.defineProperty(n,"INVALID_SPANID",{enumerable:!0,get:function(){return m.INVALID_SPANID}}),Object.defineProperty(n,"INVALID_TRACEID",{enumerable:!0,get:function(){return m.INVALID_TRACEID}}),Object.defineProperty(n,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return m.INVALID_SPAN_CONTEXT}});let v=s(67);Object.defineProperty(n,"context",{enumerable:!0,get:function(){return v.context}});let y=s(506);Object.defineProperty(n,"diag",{enumerable:!0,get:function(){return y.diag}});let x=s(886);Object.defineProperty(n,"metrics",{enumerable:!0,get:function(){return x.metrics}});let w=s(939);Object.defineProperty(n,"propagation",{enumerable:!0,get:function(){return w.propagation}});let _=s(845);Object.defineProperty(n,"trace",{enumerable:!0,get:function(){return _.trace}}),n.default={context:v.context,diag:y.diag,metrics:x.metrics,propagation:w.propagation,trace:_.trace}})(),e.exports=n})()},4202:(e,t,r)=>{e.exports={decode:r(6494),verify:r(4599),sign:r(5887),JsonWebTokenError:r(5609),NotBeforeError:r(476),TokenExpiredError:r(7730)}},4316:(e,t,r)=>{"use strict";let i=r(7814),s=r(9410),{safeRe:n,t:a}=r(1708);e.exports=(e,t)=>{if(e instanceof i)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let i,s=t.includePrerelease?n[a.COERCERTLFULL]:n[a.COERCERTL];for(;(i=s.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&i.index+i[0].length===r.index+r[0].length||(r=i),s.lastIndex=i.index+i[1].length+i[2].length;s.lastIndex=-1}else r=e.match(t.includePrerelease?n[a.COERCEFULL]:n[a.COERCE]);if(null===r)return null;let o=r[2],l=r[3]||"0",c=r[4]||"0",u=t.includePrerelease&&r[5]?`-${r[5]}`:"",h=t.includePrerelease&&r[6]?`+${r[6]}`:"";return s(`${o}.${l}.${c}${u}${h}`,t)}},4401:(e,t,r)=>{"use strict";let i=r(7814);e.exports=(e,t,r)=>{let s=new i(e,r),n=new i(t,r);return s.compare(n)||s.compareBuild(n)}},4554:(e,t,r)=>{"use strict";let i=r(4966);e.exports=(e,t,r)=>0>=i(e,t,r)},4599:(e,t,r)=>{var i=r(5356).Buffer;let s=r(5609),n=r(476),a=r(7730),o=r(6494),l=r(2775),c=r(4747),u=r(7625),h=r(5711),{KeyObject:d,createSecretKey:f,createPublicKey:p}=r(9418),g=["RS256","RS384","RS512"],b=["ES256","ES384","ES512"],m=["RS256","RS384","RS512"],v=["HS256","HS384","HS512"];u&&(g.splice(g.length,0,"PS256","PS384","PS512"),m.splice(m.length,0,"PS256","PS384","PS512")),e.exports=function(e,t,r,u){let y,x,w;if("function"!=typeof r||u||(u=r,r={}),r||(r={}),r=Object.assign({},r),y=u||function(e,t){if(e)throw e;return t},r.clockTimestamp&&"number"!=typeof r.clockTimestamp)return y(new s("clockTimestamp must be a number"));if(void 0!==r.nonce&&("string"!=typeof r.nonce||""===r.nonce.trim()))return y(new s("nonce must be a non-empty string"));if(void 0!==r.allowInvalidAsymmetricKeyTypes&&"boolean"!=typeof r.allowInvalidAsymmetricKeyTypes)return y(new s("allowInvalidAsymmetricKeyTypes must be a boolean"));let _=r.clockTimestamp||Math.floor(Date.now()/1e3);if(!e)return y(new s("jwt must be provided"));if("string"!=typeof e)return y(new s("jwt must be a string"));let E=e.split(".");if(3!==E.length)return y(new s("jwt malformed"));try{x=o(e,{complete:!0})}catch(e){return y(e)}if(!x)return y(new s("invalid token"));let S=x.header;if("function"==typeof t){if(!u)return y(new s("verify must be called asynchronous if secret or public key is provided as a callback"));w=t}else w=function(e,r){return r(null,t)};return w(S,function(t,o){let u;if(t)return y(new s("error in secret or public key callback: "+t.message));let w=""!==E[2].trim();if(!w&&o)return y(new s("jwt signature is required"));if(w&&!o)return y(new s("secret or public key must be provided"));if(!w&&!r.algorithms)return y(new s('please specify "none" in "algorithms" to verify unsigned tokens'));if(null!=o&&!(o instanceof d))try{o=p(o)}catch(e){try{o=f("string"==typeof o?i.from(o):o)}catch(e){return y(new s("secretOrPublicKey is not valid key material"))}}if(r.algorithms||("secret"===o.type?r.algorithms=v:["rsa","rsa-pss"].includes(o.asymmetricKeyType)?r.algorithms=m:"ec"===o.asymmetricKeyType?r.algorithms=b:r.algorithms=g),-1===r.algorithms.indexOf(x.header.alg))return y(new s("invalid algorithm"));if(S.alg.startsWith("HS")&&"secret"!==o.type)return y(new s(`secretOrPublicKey must be a symmetric key when using ${S.alg}`));if(/^(?:RS|PS|ES)/.test(S.alg)&&"public"!==o.type)return y(new s(`secretOrPublicKey must be an asymmetric key when using ${S.alg}`));if(!r.allowInvalidAsymmetricKeyTypes)try{c(S.alg,o)}catch(e){return y(e)}try{u=h.verify(e,x.header.alg,o)}catch(e){return y(e)}if(!u)return y(new s("invalid signature"));let O=x.payload;if(void 0!==O.nbf&&!r.ignoreNotBefore){if("number"!=typeof O.nbf)return y(new s("invalid nbf value"));if(O.nbf>_+(r.clockTolerance||0))return y(new n("jwt not active",new Date(1e3*O.nbf)))}if(void 0!==O.exp&&!r.ignoreExpiration){if("number"!=typeof O.exp)return y(new s("invalid exp value"));if(_>=O.exp+(r.clockTolerance||0))return y(new a("jwt expired",new Date(1e3*O.exp)))}if(r.audience){let e=Array.isArray(r.audience)?r.audience:[r.audience];if(!(Array.isArray(O.aud)?O.aud:[O.aud]).some(function(t){return e.some(function(e){return e instanceof RegExp?e.test(t):e===t})}))return y(new s("jwt audience invalid. expected: "+e.join(" or ")))}if(r.issuer&&("string"==typeof r.issuer&&O.iss!==r.issuer||Array.isArray(r.issuer)&&-1===r.issuer.indexOf(O.iss)))return y(new s("jwt issuer invalid. expected: "+r.issuer));if(r.subject&&O.sub!==r.subject)return y(new s("jwt subject invalid. expected: "+r.subject));if(r.jwtid&&O.jti!==r.jwtid)return y(new s("jwt jwtid invalid. expected: "+r.jwtid));if(r.nonce&&O.nonce!==r.nonce)return y(new s("jwt nonce invalid. expected: "+r.nonce));if(r.maxAge){if("number"!=typeof O.iat)return y(new s("iat required when maxAge is specified"));let e=l(r.maxAge,O.iat);if(void 0===e)return y(new s('"maxAge" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'));if(_>=e+(r.clockTolerance||0))return y(new a("maxAge exceeded",new Date(1e3*e)))}return!0===r.complete?y(null,{header:S,payload:O,signature:x.signature}):y(null,O)})}},4605:(e,t,r)=>{"use strict";let i=r(7814);e.exports=(e,t)=>new i(e,t).patch},4675:e=>{var t=Object.prototype.toString;e.exports=function(e){return"number"==typeof e||!!e&&"object"==typeof e&&"[object Number]"==t.call(e)}},4747:(e,t,r)=>{let i=r(5745),s=r(8647),n={ec:["ES256","ES384","ES512"],rsa:["RS256","PS256","RS384","PS384","RS512","PS512"],"rsa-pss":["PS256","PS384","PS512"]},a={ES256:"prime256v1",ES384:"secp384r1",ES512:"secp521r1"};e.exports=function(e,t){if(!e||!t)return;let r=t.asymmetricKeyType;if(!r)return;let o=n[r];if(!o)throw Error(`Unknown key type "${r}".`);if(!o.includes(e))throw Error(`"alg" parameter for "${r}" key type must be one of: ${o.join(", ")}.`);if(i)switch(r){case"ec":let l=t.asymmetricKeyDetails.namedCurve,c=a[e];if(l!==c)throw Error(`"alg" parameter "${e}" requires curve "${c}".`);break;case"rsa-pss":if(s){let r=parseInt(e.slice(-3),10),{hashAlgorithm:i,mgf1HashAlgorithm:s,saltLength:n}=t.asymmetricKeyDetails;if(i!==`sha${r}`||s!==i)throw Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}.`);if(void 0!==n&&n>r>>3)throw Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}.`)}}}},4758:(e,t,r)=>{var i=r(977).Buffer,s=r(3667);function n(e){if(this.buffer=null,this.writable=!0,this.readable=!0,!e)return this.buffer=i.alloc(0),this;if("function"==typeof e.pipe)return this.buffer=i.alloc(0),e.pipe(this),this;if(e.length||"object"==typeof e)return this.buffer=e,this.writable=!1,process.nextTick((function(){this.emit("end",e),this.readable=!1,this.emit("close")}).bind(this)),this;throw TypeError("Unexpected data type ("+typeof e+")")}r(7418).inherits(n,s),n.prototype.write=function(e){this.buffer=i.concat([this.buffer,i.from(e)]),this.emit("data",e)},n.prototype.end=function(e){e&&this.write(e),this.emit("end",e),this.emit("close"),this.writable=!1,this.readable=!1},e.exports=n},4811:e=>{var t=1/0,r=0/0,i=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,a=/^0o[0-7]+$/i,o=parseInt,l=Object.prototype.toString;function c(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){var u,h,d,f,p=2,g=e;if("function"!=typeof g)throw TypeError("Expected a function");return d=(h=(u=p)?(u=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(c(e)){var t,u="function"==typeof e.valueOf?e.valueOf():e;e=c(u)?u+"":u}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var h=n.test(e);return h||a.test(e)?o(e.slice(2),h?2:8):s.test(e)?r:+e}(u))===t||u===-t?(u<0?-1:1)*17976931348623157e292:u==u?u:0:0===u?u:0)%1,p=h==h?d?h-d:h:0,function(){return--p>0&&(f=g.apply(this,arguments)),p<=1&&(g=void 0),f}}},4890:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var s={},n=t.split(i),a=(r||{}).decode||e,o=0;o<n.length;o++){var l=n[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),h=l.substr(++c,l.length).trim();'"'==h[0]&&(h=h.slice(1,-1)),void 0==s[u]&&(s[u]=function(e,t){try{return t(e)}catch(t){return e}}(h,a))}}return s},t.serialize=function(e,t,i){var n=i||{},a=n.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!s.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!s.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=n.maxAge){var c=n.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(n.domain){if(!s.test(n.domain))throw TypeError("option domain is invalid");l+="; Domain="+n.domain}if(n.path){if(!s.test(n.path))throw TypeError("option path is invalid");l+="; Path="+n.path}if(n.expires){if("function"!=typeof n.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,i=/; */,s=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},4966:(e,t,r)=>{"use strict";let i=r(7814);e.exports=(e,t,r)=>new i(e,r).compare(new i(t,r))},5201:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return n}});let i=new(r(5521)).AsyncLocalStorage;function s(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let i=t.url(e);return{url:i,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function n(e,t,r){let n=s(e,t);return n?i.run(n,r):r()}function a(e,t){let r=i.getStore();return r||(e&&t?s(e,t):void 0)}},5320:(e,t,r)=>{"use strict";let i=r(7814);e.exports=(e,t)=>new i(e,t).minor},5356:e=>{"use strict";e.exports=require("node:buffer")},5480:e=>{"use strict";class t{constructor(){this.max=1e3,this.map=new Map}get(e){let t=this.map.get(e);if(void 0!==t)return this.map.delete(e),this.map.set(e,t),t}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}e.exports=t},5521:e=>{"use strict";e.exports=require("node:async_hooks")},5607:e=>{"use strict";function t(e){return(e/8|0)+ +(e%8!=0)}var r={ES256:t(256),ES384:t(384),ES512:t(521)};e.exports=function(e){var t=r[e];if(t)return t;throw Error('Unknown algorithm "'+e+'"')}},5609:e=>{var t=function(e,t){Error.call(this,e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="JsonWebTokenError",this.message=e,t&&(this.inner=t)};t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,e.exports=t},5711:(e,t,r)=>{var i=r(3055),s=r(8155);t.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],t.sign=i.sign,t.verify=s.verify,t.decode=s.decode,t.isValid=s.isValid,t.createSign=function(e){return new i(e)},t.createVerify=function(e){return new s(e)}},5729:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(1665)),n=i(r(373)),a=r(128);class o{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=i}from(e){let t=new URL(`${this.url}/${e}`);return new s.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:i=!1,count:s}={}){let a,o,l=new URL(`${this.url}/rpc/${e}`);r||i?(a=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let c=Object.assign({},this.headers);return s&&(c.Prefer=`count=${s}`),new n.default({method:a,url:l,headers:c,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},5745:(e,t,r)=>{e.exports=r(905).satisfies(process.version,">=15.7.0")},5887:(e,t,r)=>{var i=r(5356).Buffer;let s=r(2775),n=r(7625),a=r(4747),o=r(5711),l=r(8655),c=r(2804),u=r(810),h=r(4675),d=r(2797),f=r(1003),p=r(4811),{KeyObject:g,createSecretKey:b,createPrivateKey:m}=r(9418),v=["RS256","RS384","RS512","ES256","ES384","ES512","HS256","HS384","HS512","none"];n&&v.splice(3,0,"PS256","PS384","PS512");let y={expiresIn:{isValid:function(e){return u(e)||f(e)&&e},message:'"expiresIn" should be a number of seconds or string representing a timespan'},notBefore:{isValid:function(e){return u(e)||f(e)&&e},message:'"notBefore" should be a number of seconds or string representing a timespan'},audience:{isValid:function(e){return f(e)||Array.isArray(e)},message:'"audience" must be a string or array'},algorithm:{isValid:l.bind(null,v),message:'"algorithm" must be a valid string enum value'},header:{isValid:d,message:'"header" must be an object'},encoding:{isValid:f,message:'"encoding" must be a string'},issuer:{isValid:f,message:'"issuer" must be a string'},subject:{isValid:f,message:'"subject" must be a string'},jwtid:{isValid:f,message:'"jwtid" must be a string'},noTimestamp:{isValid:c,message:'"noTimestamp" must be a boolean'},keyid:{isValid:f,message:'"keyid" must be a string'},mutatePayload:{isValid:c,message:'"mutatePayload" must be a boolean'},allowInsecureKeySizes:{isValid:c,message:'"allowInsecureKeySizes" must be a boolean'},allowInvalidAsymmetricKeyTypes:{isValid:c,message:'"allowInvalidAsymmetricKeyTypes" must be a boolean'}},x={iat:{isValid:h,message:'"iat" should be a number of seconds'},exp:{isValid:h,message:'"exp" should be a number of seconds'},nbf:{isValid:h,message:'"nbf" should be a number of seconds'}};function w(e,t,r,i){if(!d(r))throw Error('Expected "'+i+'" to be a plain object.');Object.keys(r).forEach(function(s){let n=e[s];if(!n){if(!t)throw Error('"'+s+'" is not allowed in "'+i+'"');return}if(!n.isValid(r[s]))throw Error(n.message)})}let _={audience:"aud",issuer:"iss",subject:"sub",jwtid:"jti"},E=["expiresIn","notBefore","noTimestamp","audience","issuer","subject","jwtid"];e.exports=function(e,t,r,n){var l,c;"function"==typeof r?(n=r,r={}):r=r||{};let u="object"==typeof e&&!i.isBuffer(e),h=Object.assign({alg:r.algorithm||"HS256",typ:u?"JWT":void 0,kid:r.keyid},r.header);function d(e){if(n)return n(e);throw e}if(!t&&"none"!==r.algorithm)return d(Error("secretOrPrivateKey must have a value"));if(null!=t&&!(t instanceof g))try{t=m(t)}catch(e){try{t=b("string"==typeof t?i.from(t):t)}catch(e){return d(Error("secretOrPrivateKey is not valid key material"))}}if(h.alg.startsWith("HS")&&"secret"!==t.type)return d(Error(`secretOrPrivateKey must be a symmetric key when using ${h.alg}`));if(/^(?:RS|PS|ES)/.test(h.alg)){if("private"!==t.type)return d(Error(`secretOrPrivateKey must be an asymmetric key when using ${h.alg}`));if(!r.allowInsecureKeySizes&&!h.alg.startsWith("ES")&&void 0!==t.asymmetricKeyDetails&&t.asymmetricKeyDetails.modulusLength<2048)return d(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${h.alg}`))}if(void 0===e)return d(Error("payload is required"));if(u){try{l=e,w(x,!0,l,"payload")}catch(e){return d(e)}r.mutatePayload||(e=Object.assign({},e))}else{let t=E.filter(function(e){return void 0!==r[e]});if(t.length>0)return d(Error("invalid "+t.join(",")+" option for "+typeof e+" payload"))}if(void 0!==e.exp&&void 0!==r.expiresIn)return d(Error('Bad "options.expiresIn" option the payload already has an "exp" property.'));if(void 0!==e.nbf&&void 0!==r.notBefore)return d(Error('Bad "options.notBefore" option the payload already has an "nbf" property.'));try{c=r,w(y,!1,c,"options")}catch(e){return d(e)}if(!r.allowInvalidAsymmetricKeyTypes)try{a(h.alg,t)}catch(e){return d(e)}let f=e.iat||Math.floor(Date.now()/1e3);if(r.noTimestamp?delete e.iat:u&&(e.iat=f),void 0!==r.notBefore){try{e.nbf=s(r.notBefore,f)}catch(e){return d(e)}if(void 0===e.nbf)return d(Error('"notBefore" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}if(void 0!==r.expiresIn&&"object"==typeof e){try{e.exp=s(r.expiresIn,f)}catch(e){return d(e)}if(void 0===e.exp)return d(Error('"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}Object.keys(_).forEach(function(t){let i=_[t];if(void 0!==r[t]){if(void 0!==e[i])return d(Error('Bad "options.'+t+'" option. The payload already has an "'+i+'" property.'));e[i]=r[t]}});let v=r.encoding||"utf8";if("function"==typeof n)n=n&&p(n),o.createSign({header:h,privateKey:t,payload:e,encoding:v}).once("error",n).once("done",function(e){if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(h.alg)&&e.length<256)return n(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${h.alg}`));n(null,e)});else{let i=o.sign({header:h,payload:e,secret:t,encoding:v});if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(h.alg)&&i.length<256)throw Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${h.alg}`);return i}}},5938:e=>{"use strict";e.exports="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{}},6003:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Headers:()=>a,Request:()=>o,Response:()=>l,default:()=>n,fetch:()=>s});var i=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let s=i.fetch,n=i.fetch.bind(i),a=i.Headers,o=i.Request,l=i.Response},6170:(e,t,r)=>{"use strict";let i=r(7814),s=r(155);e.exports=(e,t,r)=>{let n=null,a=null,o=null;try{o=new s(t,r)}catch(e){return null}return e.forEach(e=>{o.test(e)&&(!n||-1===a.compare(e))&&(a=new i(n=e,r))}),n}},6203:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},6280:(e,t,r)=>{var i;(()=>{var s={226:function(s,n){!function(a,o){"use strict";var l="function",c="undefined",u="object",h="string",d="major",f="model",p="name",g="type",b="vendor",m="version",v="architecture",y="console",x="mobile",w="tablet",_="smarttv",E="wearable",S="embedded",O="Amazon",T="Apple",R="ASUS",P="BlackBerry",k="Browser",I="Chrome",C="Firefox",j="Google",A="Huawei",N="Microsoft",$="Motorola",L="Opera",M="Samsung",D="Sharp",U="Sony",B="Xiaomi",q="Zebra",V="Facebook",G="Chromium OS",F="Mac OS",z=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},H=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},W=function(e,t){return typeof e===h&&-1!==K(t).indexOf(K(e))},K=function(e){return e.toLowerCase()},X=function(e,t){if(typeof e===h)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},J=function(e,t){for(var r,i,s,n,a,c,h=0;h<t.length&&!a;){var d=t[h],f=t[h+1];for(r=i=0;r<d.length&&!a&&d[r];)if(a=d[r++].exec(e))for(s=0;s<f.length;s++)c=a[++i],typeof(n=f[s])===u&&n.length>0?2===n.length?typeof n[1]==l?this[n[0]]=n[1].call(this,c):this[n[0]]=n[1]:3===n.length?typeof n[1]!==l||n[1].exec&&n[1].test?this[n[0]]=c?c.replace(n[1],n[2]):void 0:this[n[0]]=c?n[1].call(this,c,n[2]):void 0:4===n.length&&(this[n[0]]=c?n[3].call(this,c.replace(n[1],n[2])):o):this[n]=c||o;h+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var i=0;i<t[r].length;i++)if(W(t[r][i],e))return"?"===r?o:r}else if(W(t[r],e))return"?"===r?o:r;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,m],[/opios[\/ ]+([\w\.]+)/i],[m,[p,L+" Mini"]],[/\bopr\/([\w\.]+)/i],[m,[p,L]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,m],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[p,"UC"+k]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[m,[p,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[m,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[p,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+k],m],[/\bfocus\/([\w\.]+)/i],[m,[p,C+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[p,L+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[p,L+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[p,"MIUI "+k]],[/fxios\/([-\w\.]+)/i],[m,[p,C]],[/\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+k]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1 "+k],m],[/(comodo_dragon)\/([\w\.]+)/i],[[p,/_/g," "],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[p,m],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,V],m],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[p,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[p,I+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,I+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[p,"Android "+k]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[m,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[p,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[p,C+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[p,m],[/(cobalt)\/([\w\.]+)/i],[p,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,K]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,"",K]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,K]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[b,M],[g,w]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[b,M],[g,x]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[b,T],[g,x]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[b,T],[g,w]],[/(macintosh);/i],[f,[b,T]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[b,D],[g,x]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[b,A],[g,w]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[b,A],[g,x]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[b,B],[g,x]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[b,B],[g,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[b,"OPPO"],[g,x]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[b,"Vivo"],[g,x]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[b,"Realme"],[g,x]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[b,$],[g,x]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[b,$],[g,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[b,"LG"],[g,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[b,"LG"],[g,x]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[b,"Lenovo"],[g,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[b,"Nokia"],[g,x]],[/(pixel c)\b/i],[f,[b,j],[g,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[b,j],[g,x]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[b,U],[g,x]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[b,U],[g,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[b,"OnePlus"],[g,x]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[b,O],[g,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[b,O],[g,x]],[/(playbook);[-\w\),; ]+(rim)/i],[f,b,[g,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[b,P],[g,x]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[b,R],[g,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[b,R],[g,x]],[/(nexus 9)/i],[f,[b,"HTC"],[g,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[b,[f,/_/g," "],[g,x]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[b,"Acer"],[g,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[b,"Meizu"],[g,x]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[b,f,[g,x]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[b,f,[g,w]],[/(surface duo)/i],[f,[b,N],[g,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[b,"Fairphone"],[g,x]],[/(u304aa)/i],[f,[b,"AT&T"],[g,x]],[/\bsie-(\w*)/i],[f,[b,"Siemens"],[g,x]],[/\b(rct\w+) b/i],[f,[b,"RCA"],[g,w]],[/\b(venue[\d ]{2,7}) b/i],[f,[b,"Dell"],[g,w]],[/\b(q(?:mv|ta)\w+) b/i],[f,[b,"Verizon"],[g,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[b,"Barnes & Noble"],[g,w]],[/\b(tm\d{3}\w+) b/i],[f,[b,"NuVision"],[g,w]],[/\b(k88) b/i],[f,[b,"ZTE"],[g,w]],[/\b(nx\d{3}j) b/i],[f,[b,"ZTE"],[g,x]],[/\b(gen\d{3}) b.+49h/i],[f,[b,"Swiss"],[g,x]],[/\b(zur\d{3}) b/i],[f,[b,"Swiss"],[g,w]],[/\b((zeki)?tb.*\b) b/i],[f,[b,"Zeki"],[g,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[b,"Dragon Touch"],f,[g,w]],[/\b(ns-?\w{0,9}) b/i],[f,[b,"Insignia"],[g,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[b,"NextBook"],[g,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[b,"Voice"],f,[g,x]],[/\b(lvtel\-)?(v1[12]) b/i],[[b,"LvTel"],f,[g,x]],[/\b(ph-1) /i],[f,[b,"Essential"],[g,x]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[b,"Envizen"],[g,w]],[/\b(trio[-\w\. ]+) b/i],[f,[b,"MachSpeed"],[g,w]],[/\btu_(1491) b/i],[f,[b,"Rotor"],[g,w]],[/(shield[\w ]+) b/i],[f,[b,"Nvidia"],[g,w]],[/(sprint) (\w+)/i],[b,f,[g,x]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[b,N],[g,x]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[b,q],[g,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[b,q],[g,x]],[/smart-tv.+(samsung)/i],[b,[g,_]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[b,M],[g,_]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[b,"LG"],[g,_]],[/(apple) ?tv/i],[b,[f,T+" TV"],[g,_]],[/crkey/i],[[f,I+"cast"],[b,j],[g,_]],[/droid.+aft(\w)( bui|\))/i],[f,[b,O],[g,_]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[b,D],[g,_]],[/(bravia[\w ]+)( bui|\))/i],[f,[b,U],[g,_]],[/(mitv-\w{5}) bui/i],[f,[b,B],[g,_]],[/Hbbtv.*(technisat) (.*);/i],[b,f,[g,_]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[b,X],[f,X],[g,_]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,_]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[b,f,[g,y]],[/droid.+; (shield) bui/i],[f,[b,"Nvidia"],[g,y]],[/(playstation [345portablevi]+)/i],[f,[b,U],[g,y]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[b,N],[g,y]],[/((pebble))app/i],[b,f,[g,E]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[b,T],[g,E]],[/droid.+; (glass) \d/i],[f,[b,j],[g,E]],[/droid.+; (wt63?0{2,3})\)/i],[f,[b,q],[g,E]],[/(quest( 2| pro)?)/i],[f,[b,V],[g,E]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[b,[g,S]],[/(aeobc)\b/i],[f,[b,O],[g,S]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[g,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[g,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,x]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[b,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[p,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,m],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[p,[m,Y,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,"Windows"],[m,Y,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,F],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,m],[/\(bb(10);/i],[m,[p,P]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[p,C+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[p,I+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,G],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,m],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,m]]},ee=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==c&&a.navigator?a.navigator:o,i=e||(r&&r.userAgent?r.userAgent:""),s=r&&r.userAgentData?r.userAgentData:o,n=t?z(Z,t):Z,y=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[p]=o,t[m]=o,J.call(t,i,n.browser),t[d]=typeof(e=t[m])===h?e.replace(/[^\d\.]/g,"").split(".")[0]:o,y&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[p]="Brave"),t},this.getCPU=function(){var e={};return e[v]=o,J.call(e,i,n.cpu),e},this.getDevice=function(){var e={};return e[b]=o,e[f]=o,e[g]=o,J.call(e,i,n.device),y&&!e[g]&&s&&s.mobile&&(e[g]=x),y&&"Macintosh"==e[f]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[g]=w),e},this.getEngine=function(){var e={};return e[p]=o,e[m]=o,J.call(e,i,n.engine),e},this.getOS=function(){var e={};return e[p]=o,e[m]=o,J.call(e,i,n.os),y&&!e[p]&&s&&"Unknown"!=s.platform&&(e[p]=s.platform.replace(/chrome os/i,G).replace(/macos/i,F)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===h&&e.length>350?X(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=H([p,m,d]),ee.CPU=H([v]),ee.DEVICE=H([f,b,g,y,x,_,w,E,S]),ee.ENGINE=ee.OS=H([p,m]),typeof n!==c?(s.exports&&(n=s.exports=ee),n.UAParser=ee):r.amdO?void 0===(i=(function(){return ee}).call(t,r,t,e))||(e.exports=i):typeof a!==c&&(a.UAParser=ee);var et=typeof a!==c&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;var r=n[e]={exports:{}},i=!0;try{s[e].call(r.exports,r,r.exports,a),i=!1}finally{i&&delete n[e]}return r.exports}a.ab="//",e.exports=a(226)})()},6494:(e,t,r)=>{var i=r(5711);e.exports=function(e,t){t=t||{};var r=i.decode(e,t);if(!r)return null;var s=r.payload;if("string"==typeof s)try{var n=JSON.parse(s);null!==n&&"object"==typeof n&&(s=n)}catch(e){}return!0===t.complete?{header:r.header,payload:s,signature:r.signature}:s}},6724:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,n={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,s]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=s?s:"true"))}catch{}}return t}function l(e){if(!e)return;let[[t,r],...i]=o(e),{domain:s,expires:n,httponly:a,maxage:l,path:h,samesite:d,secure:f,partitioned:p,priority:g}=Object.fromEntries(i.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var b,m,v={name:t,value:decodeURIComponent(r),domain:s,...n&&{expires:new Date(n)},...a&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:h,...d&&{sameSite:c.includes(b=(b=d).toLowerCase())?b:void 0},...f&&{secure:!0},...g&&{priority:u.includes(m=(m=g).toLowerCase())?m:void 0},...p&&{partitioned:!0}};let e={};for(let t in v)v[t]&&(e[t]=v[t]);return e}}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(n,{RequestCookies:()=>h,ResponseCookies:()=>d,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,n,a,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let l of i(n))s.call(e,l)||l===a||t(e,l,{get:()=>n[l],enumerable:!(o=r(n,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var c=["strict","lax","none"],u=["low","medium","high"],h=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let s=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(s)?s:function(e){if(!e)return[];var t,r,i,s,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=s,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(s)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,s=this._parsed;return s.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(s,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},6861:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(2279));class n extends s.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:i,referencedTable:s=i}={}){let n=s?`${s}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let i=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:i=r}={}){let s=void 0===i?"offset":`${i}.offset`,n=void 0===i?"limit":`${i}.limit`;return this.url.searchParams.set(s,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:i=!1,wal:s=!1,format:n="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,r?"settings":null,i?"buffers":null,s?"wal":null].filter(Boolean).join("|"),l=null!=(a=this.headers.Accept)?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=n},7035:(e,t)=>{"use strict";var r=Array.isArray,i=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),n=(Symbol.for("react.fragment"),Symbol.for("react.strict_mode"),Symbol.for("react.profiler"),Symbol.for("react.forward_ref"),Symbol.for("react.suspense"),Symbol.for("react.memo"),Symbol.for("react.lazy")),a=Symbol.iterator;Object.prototype.hasOwnProperty,Object.assign;var o=/\/+/g;function l(e,t){var r,i;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,i={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return i[e]})):t.toString(36)}function c(){}},7106:(e,t,r)=>{"use strict";let i=r(155);e.exports=(e,t,r)=>(e=new i(e,r),t=new i(t,r),e.intersects(t,r))},7418:e=>{"use strict";e.exports=require("node:util")},7625:(e,t,r)=>{e.exports=r(905).satisfies(process.version,"^6.12.0 || >=8.0.0")},7684:(e,t,r)=>{"use strict";let i=r(7814);e.exports=(e,t)=>new i(e,t).major},7730:(e,t,r)=>{var i=r(5609),s=function(e,t){i.call(this,e),this.name="TokenExpiredError",this.expiredAt=t};s.prototype=Object.create(i.prototype),s.prototype.constructor=s,e.exports=s},7763:e=>{"use strict";let t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},7814:(e,t,r)=>{"use strict";let i=r(5938),{MAX_LENGTH:s,MAX_SAFE_INTEGER:n}=r(3280),{safeRe:a,t:o}=r(1708),l=r(7763),{compareIdentifiers:c}=r(771);class u{constructor(e,t){if(t=l(t),e instanceof u)if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;else e=e.version;else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>s)throw TypeError(`version is longer than ${s} characters`);i("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let r=e.trim().match(t.loose?a[o.LOOSE]:a[o.FULL]);if(!r)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>n||this.major<0)throw TypeError("Invalid major version");if(this.minor>n||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>n||this.patch<0)throw TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let t=+e;if(t>=0&&t<n)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(i("SemVer.compare",this.version,this.options,e),!(e instanceof u)){if("string"==typeof e&&e===this.version)return 0;e=new u(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof u||(e=new u(e,this.options)),c(this.major,e.major)||c(this.minor,e.minor)||c(this.patch,e.patch)}comparePre(e){if(e instanceof u||(e=new u(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{let r=this.prerelease[t],s=e.prerelease[t];if(i("prerelease compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return -1;else if(r===s)continue;else return c(r,s)}while(++t)}compareBuild(e){e instanceof u||(e=new u(e,this.options));let t=0;do{let r=this.build[t],s=e.build[t];if(i("build compare",t,r,s),void 0===r&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===r)return -1;else if(r===s)continue;else return c(r,s)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw Error("invalid increment argument: identifier is empty");if(t){let e=`-${t}`.match(this.options.loose?a[o.PRERELEASELOOSE]:a[o.PRERELEASE]);if(!e||e[1]!==t)throw Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=+!!Number(r);if(0===this.prerelease.length)this.prerelease=[e];else{let i=this.prerelease.length;for(;--i>=0;)"number"==typeof this.prerelease[i]&&(this.prerelease[i]++,i=-2);if(-1===i){if(t===this.prerelease.join(".")&&!1===r)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let i=[t,e];!1===r&&(i=[t]),0===c(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=i):this.prerelease=i}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=u},8155:(e,t,r)=>{var i=r(977).Buffer,s=r(4758),n=r(8961),a=r(3667),o=r(8443),l=r(7418),c=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function u(e){var t=e.split(".",1)[0],r=i.from(t,"base64").toString("binary");if("[object Object]"===Object.prototype.toString.call(r))return r;try{return JSON.parse(r)}catch(e){return}}function h(e){return e.split(".")[2]}function d(e){return c.test(e)&&!!u(e)}function f(e,t,r){if(!t){var i=Error("Missing algorithm parameter for jws.verify");throw i.code="MISSING_ALGORITHM",i}var s=h(e=o(e)),a=e.split(".",2).join(".");return n(t).verify(a,s,r)}function p(e,t){if(t=t||{},!d(e=o(e)))return null;var r,s,n=u(e);if(!n)return null;var a=(r=r||"utf8",s=e.split(".")[1],i.from(s,"base64").toString(r));return("JWT"===n.typ||t.json)&&(a=JSON.parse(a,t.encoding)),{header:n,payload:a,signature:h(e)}}function g(e){var t=new s((e=e||{}).secret||e.publicKey||e.key);this.readable=!0,this.algorithm=e.algorithm,this.encoding=e.encoding,this.secret=this.publicKey=this.key=t,this.signature=new s(e.signature),this.secret.once("close",(function(){!this.signature.writable&&this.readable&&this.verify()}).bind(this)),this.signature.once("close",(function(){!this.secret.writable&&this.readable&&this.verify()}).bind(this))}l.inherits(g,a),g.prototype.verify=function(){try{var e=f(this.signature.buffer,this.algorithm,this.key.buffer),t=p(this.signature.buffer,this.encoding);return this.emit("done",e,t),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},g.decode=p,g.isValid=d,g.verify=f,e.exports=g},8229:(e,t,r)=>{"use strict";let i=r(7814),s=r(155),n=r(9694);e.exports=(e,t)=>{e=new s(e,t);let r=new i("0.0.0");if(e.test(r)||(r=new i("0.0.0-0"),e.test(r)))return r;r=null;for(let t=0;t<e.set.length;++t){let s=e.set[t],a=null;s.forEach(e=>{let t=new i(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":(!a||n(t,a))&&(a=t);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),a&&(!r||n(r,a))&&(r=a)}return r&&e.test(r)?r:null}},8275:(e,t,r)=>{"use strict";let i=r(4966);e.exports=(e,t,r)=>0!==i(e,t,r)},8348:(e,t,r)=>{"use strict";let i=r(9410);e.exports=(e,t)=>{let r=i(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},8355:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let s=i(r(5729));t.PostgrestClient=s.default;let n=i(r(1665));t.PostgrestQueryBuilder=n.default;let a=i(r(373));t.PostgrestFilterBuilder=a.default;let o=i(r(6861));t.PostgrestTransformBuilder=o.default;let l=i(r(2279));t.PostgrestBuilder=l.default;let c=i(r(8784));t.PostgrestError=c.default,t.default={PostgrestClient:s.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:c.default}},8443:(e,t,r)=>{var i=r(5356).Buffer;e.exports=function(e){return"string"==typeof e?e:"number"==typeof e||i.isBuffer(e)?e.toString():JSON.stringify(e)}},8540:(e,t,r)=>{"use strict";let i=r(155);e.exports=(e,t,r)=>{try{t=new i(t,r)}catch(e){return!1}return t.test(e)}},8647:(e,t,r)=>{e.exports=r(905).satisfies(process.version,">=16.9.0")},8655:e=>{var t,r,i=1/0,s=0/0,n=/^\s+|\s+$/g,a=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,l=/^0o[0-7]+$/i,c=/^(?:0|[1-9]\d*)$/,u=parseInt;function h(e){return e!=e}var d=Object.prototype,f=d.hasOwnProperty,p=d.toString,g=d.propertyIsEnumerable,b=(t=Object.keys,r=Object,function(e){return t(r(e))}),m=Math.max,v=Array.isArray;function y(e){var t,r,i;return null!=e&&"number"==typeof(t=e.length)&&t>-1&&t%1==0&&t<=0x1fffffffffffff&&"[object Function]"!=(i=x(r=e)?p.call(r):"")&&"[object GeneratorFunction]"!=i}function x(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function w(e){return!!e&&"object"==typeof e}e.exports=function(e,t,r,_){e=y(e)?e:function(e){return e?function(e,t){for(var r=-1,i=e?e.length:0,s=Array(i);++r<i;)s[r]=t(e[r],r,e);return s}(y(e)?function(e,t){var r,i,s,n,a=v(e)||w(i=r=e)&&y(i)&&f.call(r,"callee")&&(!g.call(r,"callee")||"[object Arguments]"==p.call(r))?function(e,t){for(var r=-1,i=Array(e);++r<e;)i[r]=t(r);return i}(e.length,String):[],o=a.length,l=!!o;for(var u in e){f.call(e,u)&&!(l&&("length"==u||(s=u,(n=null==(n=o)?0x1fffffffffffff:n)&&("number"==typeof s||c.test(s))&&s>-1&&s%1==0&&s<n)))&&a.push(u)}return a}(e):function(e){if(r=(t=e)&&t.constructor,t!==("function"==typeof r&&r.prototype||d))return b(e);var t,r,i=[];for(var s in Object(e))f.call(e,s)&&"constructor"!=s&&i.push(s);return i}(e),function(t){return e[t]}):[]}(e),r=r&&!_?(O=(S=(E=r)?(E=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||w(t)&&"[object Symbol]"==p.call(t))return s;if(x(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=x(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var i=o.test(e);return i||l.test(e)?u(e.slice(2),i?2:8):a.test(e)?s:+e}(E))===i||E===-i?(E<0?-1:1)*17976931348623157e292:E==E?E:0:0===E?E:0)%1,S==S?O?S-O:S:0):0;var E,S,O,T,R=e.length;return r<0&&(r=m(R+r,0)),"string"==typeof(T=e)||!v(T)&&w(T)&&"[object String]"==p.call(T)?r<=R&&e.indexOf(t,r)>-1:!!R&&function(e,t,r){if(t!=t){for(var i,s=e.length,n=r+-1;i?n--:++n<s;)if(h(e[n],n,e))return n;return -1}for(var a=r-1,o=e.length;++a<o;)if(e[a]===t)return a;return -1}(e,t,r)>-1}},8784:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},8961:(e,t,r)=>{var i,s=r(977).Buffer,n=r(9418),a=r(2932),o=r(7418),l="secret must be a string or buffer",c="key must be a string or a buffer",u="function"==typeof n.createPublicKey;function h(e){if(!s.isBuffer(e)&&"string"!=typeof e&&(!u||"object"!=typeof e||"string"!=typeof e.type||"string"!=typeof e.asymmetricKeyType||"function"!=typeof e.export))throw g(c)}function d(e){if(!s.isBuffer(e)&&"string"!=typeof e&&"object"!=typeof e)throw g("key must be a string, a buffer or an object")}function f(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function p(e){var t=4-(e=e.toString()).length%4;if(4!==t)for(var r=0;r<t;++r)e+="=";return e.replace(/\-/g,"+").replace(/_/g,"/")}function g(e){var t=[].slice.call(arguments,1);return TypeError(o.format.bind(o,e).apply(null,t))}function b(e){var t;return t=e,s.isBuffer(t)||"string"==typeof t||(e=JSON.stringify(e)),e}function m(e){return function(t,r){!function(e){if(!s.isBuffer(e)){if("string"!=typeof e){if(!u||"object"!=typeof e||"secret"!==e.type||"function"!=typeof e.export)throw g(l)}}}(r),t=b(t);var i=n.createHmac("sha"+e,r);return f((i.update(t),i.digest("base64")))}}u&&(c+=" or a KeyObject",l+="or a KeyObject");var v="timingSafeEqual"in n?function(e,t){return e.byteLength===t.byteLength&&n.timingSafeEqual(e,t)}:function(e,t){return i||(i=r(1281)),i(e,t)};function y(e){return function(t,r,i){var n=m(e)(t,i);return v(s.from(r),s.from(n))}}function x(e){return function(t,r){d(r),t=b(t);var i=n.createSign("RSA-SHA"+e);return f((i.update(t),i.sign(r,"base64")))}}function w(e){return function(t,r,i){h(i),t=b(t),r=p(r);var s=n.createVerify("RSA-SHA"+e);return s.update(t),s.verify(i,r,"base64")}}function _(e){return function(t,r){d(r),t=b(t);var i=n.createSign("RSA-SHA"+e);return f((i.update(t),i.sign({key:r,padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function E(e){return function(t,r,i){h(i),t=b(t),r=p(r);var s=n.createVerify("RSA-SHA"+e);return s.update(t),s.verify({key:i,padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST},r,"base64")}}function S(e){var t=x(e);return function(){var r=t.apply(null,arguments);return a.derToJose(r,"ES"+e)}}function O(e){var t=w(e);return function(r,i,s){return t(r,i=a.joseToDer(i,"ES"+e).toString("base64"),s)}}function T(){return function(){return""}}function R(){return function(e,t){return""===t}}e.exports=function(e){var t=e.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);if(!t)throw g('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',e);var r=(t[1]||t[3]).toLowerCase(),i=t[2];return{sign:({hs:m,rs:x,ps:_,es:S,none:T})[r](i),verify:({hs:y,rs:w,ps:E,es:O,none:R})[r](i)}}},8973:e=>{function t(e,t,r,i){return Math.round(e/r)+" "+i+(t>=1.5*r?"s":"")}e.exports=function(e,r){r=r||{};var i,s,n,a,o=typeof e;if("string"===o&&e.length>0){var l=e;if(!((l=String(l)).length>100)){var c=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(l);if(c){var u=parseFloat(c[1]);switch((c[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*u;case"weeks":case"week":case"w":return 6048e5*u;case"days":case"day":case"d":return 864e5*u;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*u;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*u;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*u;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return u;default:break}}}return}if("number"===o&&isFinite(e)){return r.long?(s=Math.abs(i=e))>=864e5?t(i,s,864e5,"day"):s>=36e5?t(i,s,36e5,"hour"):s>=6e4?t(i,s,6e4,"minute"):s>=1e3?t(i,s,1e3,"second"):i+" ms":(a=Math.abs(n=e))>=864e5?Math.round(n/864e5)+"d":a>=36e5?Math.round(n/36e5)+"h":a>=6e4?Math.round(n/6e4)+"m":a>=1e3?Math.round(n/1e3)+"s":n+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},9166:(e,t,r)=>{"use strict";let i=r(155),s=r(674),{ANY:n}=s,a=r(8540),o=r(4966),l=[new s(">=0.0.0-0")],c=[new s(">=0.0.0")],u=(e,t,r)=>{let i,s,u,f,p,g,b;if(e===t)return!0;if(1===e.length&&e[0].semver===n)if(1===t.length&&t[0].semver===n)return!0;else e=r.includePrerelease?l:c;if(1===t.length&&t[0].semver===n)if(r.includePrerelease)return!0;else t=c;let m=new Set;for(let t of e)">"===t.operator||">="===t.operator?i=h(i,t,r):"<"===t.operator||"<="===t.operator?s=d(s,t,r):m.add(t.semver);if(m.size>1)return null;if(i&&s&&((u=o(i.semver,s.semver,r))>0||0===u&&(">="!==i.operator||"<="!==s.operator)))return null;for(let e of m){if(i&&!a(e,String(i),r)||s&&!a(e,String(s),r))return null;for(let i of t)if(!a(e,String(i),r))return!1;return!0}let v=!!s&&!r.includePrerelease&&!!s.semver.prerelease.length&&s.semver,y=!!i&&!r.includePrerelease&&!!i.semver.prerelease.length&&i.semver;for(let e of(v&&1===v.prerelease.length&&"<"===s.operator&&0===v.prerelease[0]&&(v=!1),t)){if(b=b||">"===e.operator||">="===e.operator,g=g||"<"===e.operator||"<="===e.operator,i){if(y&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===y.major&&e.semver.minor===y.minor&&e.semver.patch===y.patch&&(y=!1),">"===e.operator||">="===e.operator){if((f=h(i,e,r))===e&&f!==i)return!1}else if(">="===i.operator&&!a(i.semver,String(e),r))return!1}if(s){if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),"<"===e.operator||"<="===e.operator){if((p=d(s,e,r))===e&&p!==s)return!1}else if("<="===s.operator&&!a(s.semver,String(e),r))return!1}if(!e.operator&&(s||i)&&0!==u)return!1}return(!i||!g||!!s||0===u)&&(!s||!b||!!i||0===u)&&!y&&!v&&!0},h=(e,t,r)=>{if(!e)return t;let i=o(e.semver,t.semver,r);return i>0?e:i<0||">"===t.operator&&">="===e.operator?t:e},d=(e,t,r)=>{if(!e)return t;let i=o(e.semver,t.semver,r);return i<0?e:i>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new i(e,r),t=new i(t,r);let s=!1;e:for(let i of e.set){for(let e of t.set){let t=u(i,e,r);if(s=s||null!==t,t)continue e}if(s)return!1}return!0}},9263:(e,t,r)=>{"use strict";let i=r(4401);e.exports=(e,t)=>e.sort((e,r)=>i(e,r,t))},9372:(e,t,r)=>{"use strict";let i=r(4966);e.exports=(e,t,r)=>i(t,e,r)},9410:(e,t,r)=>{"use strict";let i=r(7814);e.exports=(e,t,r=!1)=>{if(e instanceof i)return e;try{return new i(e,t)}catch(e){if(!r)return null;throw e}}},9418:e=>{"use strict";e.exports=globalThis.__import_unsupported("crypto")},9574:(e,t,r)=>{"use strict";let i=r(9410);e.exports=(e,t)=>{let r=i(e,null,!0),s=i(t,null,!0),n=r.compare(s);if(0===n)return null;let a=n>0,o=a?r:s,l=a?s:r,c=!!o.prerelease.length;if(l.prerelease.length&&!c){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(o))return l.minor&&!l.patch?"minor":"patch"}let u=c?"pre":"";return r.major!==s.major?u+"major":r.minor!==s.minor?u+"minor":r.patch!==s.patch?u+"patch":"prerelease"}},9623:(e,t,r)=>{"use strict";let i,s;r.r(t),r.d(t,{default:()=>i6});var n,a,o,l,c,u,h,d,f,p,g,b={};async function m(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(b),r.d(b,{config:()=>i0,middleware:()=>iZ});let v=null;async function y(){if("phase-production-build"===process.env.NEXT_PHASE)return;v||(v=m());let e=await v;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function x(...e){let t=await m();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let w=null;function _(){return w||(w=y()),w}function E(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(E(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(E(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,i,s){if("function"==typeof s[0])return s[0](t);throw Object.defineProperty(Error(E(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),_();class S extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class O extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class T extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let R="_N_T_",P={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function k(e){var t,r,i,s,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=s,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function I(e){let t={},r=[];if(e)for(let[i,s]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...k(s)),t[i]=1===r.length?r[0]:r):t[i]=s;return t}function C(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...P,GROUP:{builtinReact:[P.reactServerComponents,P.actionBrowser],serverOnly:[P.reactServerComponents,P.actionBrowser,P.instrument,P.middleware],neutralTarget:[P.apiNode,P.apiEdge],clientOnly:[P.serverSideRendering,P.appPagesBrowser],bundled:[P.reactServerComponents,P.actionBrowser,P.serverSideRendering,P.appPagesBrowser,P.shared,P.instrument,P.middleware],appPages:[P.reactServerComponents,P.serverSideRendering,P.appPagesBrowser,P.actionBrowser]}});let j=Symbol("response"),A=Symbol("passThrough"),N=Symbol("waitUntil");class ${constructor(e,t){this[A]=!1,this[N]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[j]||(this[j]=Promise.resolve(e))}passThroughOnException(){this[A]=!0}waitUntil(e){if("external"===this[N].kind)return(0,this[N].function)(e);this[N].promises.push(e)}}class L extends ${constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function M(e){return e.replace(/\/$/,"")||"/"}function D(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function U(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=D(e);return""+t+r+i+s}function B(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=D(e);return""+r+t+i+s}function q(e,t){if("string"!=typeof e)return!1;let{pathname:r}=D(e);return r===t||r.startsWith(t+"/")}let V=new WeakMap;function G(e,t){let r;if(!t)return{pathname:e};let i=V.get(t);i||(i=t.map(e=>e.toLowerCase()),V.set(t,i));let s=e.split("/",2);if(!s[1])return{pathname:e};let n=s[1].toLowerCase(),a=i.indexOf(n);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let F=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function z(e,t){return new URL(String(e).replace(F,"localhost"),t&&String(t).replace(F,"localhost"))}let H=Symbol("NextURLInternal");class W{constructor(e,t,r){let i,s;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,s=r||{}):s=r||t||{},this[H]={url:z(e,i??s.base),options:s,basePath:""},this.analyze()}analyze(){var e,t,r,i,s;let n=function(e,t){var r,i;let{basePath:s,i18n:n,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};s&&q(o.pathname,s)&&(o.pathname=function(e,t){if(!q(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,s),o.basePath=s);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");o.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(n){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):G(o.pathname,n.locales);o.locale=e.detectedLocale,o.pathname=null!=(i=e.pathname)?i:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):G(l,n.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[H].url.pathname,{nextConfig:this[H].options.nextConfig,parseData:!0,i18nProvider:this[H].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[H].url,this[H].options.headers);this[H].domainLocale=this[H].options.i18nProvider?this[H].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let n of(r&&(r=r.toLowerCase()),e)){var i,s;if(t===(null==(i=n.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===n.defaultLocale.toLowerCase()||(null==(s=n.locales)?void 0:s.some(e=>e.toLowerCase()===r)))return n}}(null==(t=this[H].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[H].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[H].options.nextConfig)||null==(i=s.i18n)?void 0:i.defaultLocale);this[H].url.pathname=n.pathname,this[H].defaultLocale=o,this[H].basePath=n.basePath??"",this[H].buildId=n.buildId,this[H].locale=n.locale??o,this[H].trailingSlash=n.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,i){if(!t||t===r)return e;let s=e.toLowerCase();return!i&&(q(s,"/api")||q(s,"/"+t.toLowerCase()))?e:U(e,"/"+t)}((e={basePath:this[H].basePath,buildId:this[H].buildId,defaultLocale:this[H].options.forceLocale?void 0:this[H].defaultLocale,locale:this[H].locale,pathname:this[H].url.pathname,trailingSlash:this[H].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=M(t)),e.buildId&&(t=B(U(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=U(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:B(t,"/"):M(t)}formatSearch(){return this[H].url.search}get buildId(){return this[H].buildId}set buildId(e){this[H].buildId=e}get locale(){return this[H].locale??""}set locale(e){var t,r;if(!this[H].locale||!(null==(r=this[H].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[H].locale=e}get defaultLocale(){return this[H].defaultLocale}get domainLocale(){return this[H].domainLocale}get searchParams(){return this[H].url.searchParams}get host(){return this[H].url.host}set host(e){this[H].url.host=e}get hostname(){return this[H].url.hostname}set hostname(e){this[H].url.hostname=e}get port(){return this[H].url.port}set port(e){this[H].url.port=e}get protocol(){return this[H].url.protocol}set protocol(e){this[H].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[H].url=z(e),this.analyze()}get origin(){return this[H].url.origin}get pathname(){return this[H].url.pathname}set pathname(e){this[H].url.pathname=e}get hash(){return this[H].url.hash}set hash(e){this[H].url.hash=e}get search(){return this[H].url.search}set search(e){this[H].url.search=e}get password(){return this[H].url.password}set password(e){this[H].url.password=e}get username(){return this[H].url.username}set username(e){this[H].url.username=e}get basePath(){return this[H].basePath}set basePath(e){this[H].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new W(String(this),this[H].options)}}var K=r(6724);let X=Symbol("internal request");class J extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);C(r),e instanceof Request?super(e,t):super(r,t);let i=new W(r,{headers:I(this.headers),nextConfig:t.nextConfig});this[X]={cookies:new K.RequestCookies(this.headers),nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[X].cookies}get nextUrl(){return this[X].nextUrl}get page(){throw new O}get ua(){throw new T}get url(){return this[X].url}}class Y{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let Q=Symbol("internal response"),Z=new Set([301,302,303,307,308]);function ee(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[i,s]of e.request.headers)t.set("x-middleware-request-"+i,s),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class et extends Response{constructor(e,t={}){super(e,t);let r=this.headers,i=new Proxy(new K.ResponseCookies(r),{get(e,i,s){switch(i){case"delete":case"set":return(...s)=>{let n=Reflect.apply(e[i],e,s),a=new Headers(r);return n instanceof K.ResponseCookies&&r.set("x-middleware-set-cookie",n.getAll().map(e=>(0,K.stringifyCookie)(e)).join(",")),ee(t,a),n};default:return Y.get(e,i,s)}}});this[Q]={cookies:i,url:t.url?new W(t.url,{headers:I(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[Q].cookies}static json(e,t){let r=Response.json(e,t);return new et(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!Z.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let i="object"==typeof t?t:{},s=new Headers(null==i?void 0:i.headers);return s.set("Location",C(e)),new et(null,{...i,headers:s,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",C(e)),ee(t,r),new et(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),ee(e,t),new et(null,{...e,headers:t})}}function er(e,t){let r="string"==typeof t?new URL(t):t,i=new URL(e,t),s=i.origin===r.origin;return{url:s?i.toString().slice(r.origin.length):i.toString(),isRelative:s}}let ei="Next-Router-Prefetch",es=["RSC","Next-Router-State-Tree",ei,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],en="_rsc";class ea extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ea}}class eo extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return Y.get(t,r,i);let s=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==n)return Y.get(t,n,i)},set(t,r,i,s){if("symbol"==typeof r)return Y.set(t,r,i,s);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return Y.set(t,a??r,i,s)},has(t,r){if("symbol"==typeof r)return Y.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&Y.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return Y.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||Y.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return ea.callable;default:return Y.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new eo(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let el=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class ec{disable(){throw el}getStore(){}run(){throw el}exit(){throw el}enterWith(){throw el}static bind(e){return e}}let eu="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function eh(){return eu?new eu:new ec}let ed=eh(),ef=eh();class ep extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new ep}}class eg{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return ep.callable;default:return Y.get(e,t,r)}}})}}let eb=Symbol.for("next.mutated.cookies");class em{static wrap(e,t){let r=new K.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],s=new Set,n=()=>{let e=ed.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of i){let r=new K.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},a=new Proxy(r,{get(e,t,r){switch(t){case eb:return i;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),a}finally{n()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),a}finally{n()}};default:return Y.get(e,t,r)}}});return a}}function ev(e){if("action"!==function(e){let t=ef.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}(e).phase)throw new ep}var ey=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(ey||{}),ex=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(ex||{}),ew=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(ew||{}),e_=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(e_||{}),eE=function(e){return e.startServer="startServer.startServer",e}(eE||{}),eS=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(eS||{}),eO=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(eO||{}),eT=function(e){return e.executeRoute="Router.executeRoute",e}(eT||{}),eR=function(e){return e.runHandler="Node.runHandler",e}(eR||{}),eP=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(eP||{}),ek=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(ek||{}),eI=function(e){return e.execute="Middleware.execute",e}(eI||{});let eC=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],ej=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eA(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:eN,propagation:e$,trace:eL,SpanStatusCode:eM,SpanKind:eD,ROOT_CONTEXT:eU}=i=r(3956);class eB extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eq=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eB})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eM.ERROR,message:null==t?void 0:t.message})),e.end()},eV=new Map,eG=i.createContextKey("next.rootSpanId"),eF=0,ez=()=>eF++,eH={set(e,t,r){e.push({key:t,value:r})}};class eW{getTracerInstance(){return eL.getTracer("next.js","0.0.1")}getContext(){return eN}getTracePropagationData(){let e=eN.active(),t=[];return e$.inject(e,t,eH),t}getActiveScopeSpan(){return eL.getSpan(null==eN?void 0:eN.active())}withPropagatedContext(e,t,r){let i=eN.active();if(eL.getSpanContext(i))return t();let s=e$.extract(i,e,r);return eN.with(s,t)}trace(...e){var t;let[r,i,s]=e,{fn:n,options:a}="function"==typeof i?{fn:i,options:{}}:{fn:s,options:{...i}},o=a.spanName??r;if(!eC.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return n();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=eL.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==eN?void 0:eN.active())??eU,c=!0);let u=ez();return a.attributes={"next.span_name":o,"next.span_type":r,...a.attributes},eN.with(l.setValue(eG,u),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,i=()=>{eV.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&ej.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&eV.set(u,new Map(Object.entries(a.attributes??{})));try{if(n.length>1)return n(e,t=>eq(e,t));let t=n(e);if(eA(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eq(e,t),t}).finally(i);return e.end(),i(),t}catch(t){throw eq(e,t),i(),t}}))}wrap(...e){let t=this,[r,i,s]=3===e.length?e:[e[0],{},e[1]];return eC.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=i;"function"==typeof e&&"function"==typeof s&&(e=e.apply(this,arguments));let n=arguments.length-1,a=arguments[n];if("function"!=typeof a)return t.trace(r,e,()=>s.apply(this,arguments));{let i=t.getContext().bind(eN.active(),a);return t.trace(r,e,(e,t)=>(arguments[n]=function(e){return null==t||t(e),i.apply(this,arguments)},s.apply(this,arguments)))}}:s}startSpan(...e){let[t,r]=e,i=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,i)}getSpanContext(e){return e?eL.setSpan(eN.active(),e):void 0}getRootSpanAttributes(){let e=eN.active().getValue(eG);return eV.get(e)}setRootSpanAttribute(e,t){let r=eN.active().getValue(eG),i=eV.get(r);i&&i.set(e,t)}}let eK=(()=>{let e=new eW;return()=>e})(),eX="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eX);class eJ{constructor(e,t,r,i){var s;let n=e&&function(e,t){let r=eo.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(s=r.get(eX))?void 0:s.value;this._isEnabled=!!(!n&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=i}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:eX,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:eX,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eY(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],i=new Headers;for(let e of k(r))i.append("set-cookie",e);for(let e of new K.ResponseCookies(i).getAll())t.set(e)}}var eQ=r(1802),eZ=r.n(eQ);class e0 extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}class e1{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(5356).Buffer,new e1(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let e2=Symbol.for("@next/cache-handlers-map"),e5=Symbol.for("@next/cache-handlers-set"),e6=globalThis;function e4(){if(e6[e2])return e6[e2].entries()}async function e3(e,t){if(!e)return t();let r=e8(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),i=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!i.has(e))}}(r,e8(e));await e7(e,t)}}function e8(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function e9(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let i=function(){if(e6[e5])return e6[e5].values()}();if(i)for(let t of i)r.push(t.expireTags(...e));await Promise.all(r)}async function e7(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],i=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},s=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([e9(r,e.incrementalCache),...Object.values(i),...s])}let te=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class tt{disable(){throw te}getStore(){}run(){throw te}exit(){throw te}enterWith(){throw te}static bind(e){return e}}let tr="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,ti=tr?new tr:new tt;class ts{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eZ()),this.callbackQueue.pause()}after(e){if(eA(e))this.waitUntil||tn(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){var t;this.waitUntil||tn();let r=ef.getStore();r&&this.workUnitStores.add(r);let i=ti.getStore(),s=i?i.rootTaskSpawnPhase:null==r?void 0:r.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let n=(t=async()=>{try{await ti.run({rootTaskSpawnPhase:s},()=>e())}catch(e){this.reportTaskError("function",e)}},tr?tr.bind(t):tt.bind(t));this.callbackQueue.add(n)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=ed.getStore();if(!e)throw Object.defineProperty(new e0("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return e3(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new e0("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function tn(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function ta(e){let t,r={then:(i,s)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(i,s))};return r}class to{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function tl(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let tc=Symbol.for("@next/request-context"),tu=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let i=r.slice(0,e).join("/");i&&(i.endsWith("/page")||i.endsWith("/route")||(i=`${i}${!i.endsWith("/")?"/":""}layout`),t.push(i))}}return t};async function th(e,t,r){let i=[],s=r&&r.size>0;for(let t of tu(e))t=`${R}${t}`,i.push(t);if(t.pathname&&!s){let e=`${R}${t.pathname}`;i.push(e)}return{tags:i,expirationsByCacheKind:function(e){let t=new Map,r=e4();if(r)for(let[i,s]of r)"getExpiration"in s&&t.set(i,ta(async()=>s.getExpiration(...e)));return t}(i)}}class td extends J{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new S({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let tf={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},tp=(e,t)=>eK().withPropagatedContext(e.headers,t,tf),tg=!1;async function tb(e){var t;let i,s;if(!tg&&(tg=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(2905);e(),tp=t(tp)}await _();let n=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new W(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let o=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),c=l.has("x-nextjs-data"),u="1"===l.get("RSC");c&&"/index"===a.pathname&&(a.pathname="/");let h=new Map;if(!n)for(let e of es){let t=e.toLowerCase(),r=l.get(t);null!==r&&(h.set(t,r),l.delete(t))}let d=new td({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(en),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});c&&Object.defineProperty(d,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:tl()})}));let f=e.request.waitUntil??(null==(t=function(){let e=globalThis[tc];return null==e?void 0:e.get()}())?void 0:t.waitUntil),p=new L({request:d,page:e.page,context:f?{waitUntil:f}:void 0});if((i=await tp(d,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=p.waitUntil.bind(p),r=new to;return eK().trace(eI.execute,{spanName:`middleware ${d.method} ${d.nextUrl.pathname}`,attributes:{"http.target":d.nextUrl.pathname,"http.method":d.method}},async()=>{try{var i,n,a,l,c,u;let h=tl(),f=await th("/",d.nextUrl,null),g=(c=d.nextUrl,u=e=>{s=e},function(e,t,r,i,s,n,a,o,l,c,u){function h(e){r&&r.setHeader("Set-Cookie",e)}let d={};return{type:"request",phase:e,implicitTags:n,url:{pathname:i.pathname,search:i.search??""},rootParams:s,get headers(){return d.headers||(d.headers=function(e){let t=eo.from(e);for(let e of es)t.delete(e.toLowerCase());return eo.seal(t)}(t.headers)),d.headers},get cookies(){if(!d.cookies){let e=new K.RequestCookies(eo.from(t.headers));eY(t,e),d.cookies=eg.seal(e)}return d.cookies},set cookies(value){d.cookies=value},get mutableCookies(){if(!d.mutableCookies){let e=function(e,t){let r=new K.RequestCookies(eo.from(e));return em.wrap(r,t)}(t.headers,a||(r?h:void 0));eY(t,e),d.mutableCookies=e}return d.mutableCookies},get userspaceMutableCookies(){return d.userspaceMutableCookies||(d.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,r,i){switch(r){case"delete":return function(...r){return ev("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return ev("cookies().set"),e.set(...r),t};default:return Y.get(e,r,i)}}});return t}(this.mutableCookies)),d.userspaceMutableCookies},get draftMode(){return d.draftMode||(d.draftMode=new eJ(l,t,this.cookies,this.mutableCookies)),d.draftMode},renderResumeDataCache:o??null,isHmrRefresh:c,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",d,void 0,c,{},f,u,void 0,h,!1,void 0)),b=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:i,isPrefetchRequest:s,buildId:n,previouslyRevalidatedTags:a}){var o;let l={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(o=e.split("/").reduce((e,t,r,i)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===i.length-1?e:e+"/"+t:e,"")).startsWith("/")?o:"/"+o,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:i,isPrefetchRequest:s,buildId:n,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:i}=e;return new ts({waitUntil:t,onClose:r,onTaskError:i})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=e4();if(t)for(let[r,i]of t)"refreshTags"in i&&e.set(r,ta(async()=>i.refreshTags()));return e}()};return r.store=l,l}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(n=e.request.nextConfig)||null==(i=n.experimental)?void 0:i.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:d.headers.has(ei),buildId:o??"",previouslyRevalidatedTags:[]});return await ed.run(b,()=>ef.run(g,e.handler,d,p))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(d,p)}))&&!(i instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});i&&s&&i.headers.set("set-cookie",s);let g=null==i?void 0:i.headers.get("x-middleware-rewrite");if(i&&g&&(u||!n)){let t=new W(g,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});n||t.host!==d.nextUrl.host||(t.buildId=o||t.buildId,i.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:s}=er(t.toString(),a.toString());!n&&c&&i.headers.set("x-nextjs-rewrite",r),u&&s&&(a.pathname!==t.pathname&&i.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&i.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let b=null==i?void 0:i.headers.get("Location");if(i&&b&&!n){let t=new W(b,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});i=new Response(i.body,i),t.host===a.host&&(t.buildId=o||t.buildId,i.headers.set("Location",t.toString())),c&&(i.headers.delete("Location"),i.headers.set("x-nextjs-redirect",er(t.toString(),a.toString()).url))}let m=i||et.next(),v=m.headers.get("x-middleware-override-headers"),y=[];if(v){for(let[e,t]of h)m.headers.set(`x-middleware-request-${e}`,t),y.push(e);y.length>0&&m.headers.set("x-middleware-override-headers",v+","+y.join(","))}return{response:m,waitUntil:("internal"===p[N].kind?Promise.all(p[N].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:d.fetchMetrics}}r(6280),"undefined"==typeof URLPattern||URLPattern;var tm=r(2815);new WeakMap;let tv="function"==typeof tm.unstable_postpone;function ty(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(ty("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`),new WeakMap;var tx=r(9825),tw="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout,t_="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),tE=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function tS(e,t){var r,i,s=0,n=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;s<t;){if(r=255&e[s++],n.push(t_[r>>2&63]),r=(3&r)<<4,s>=t||(r|=(i=255&e[s++])>>4&15,n.push(t_[63&r]),r=(15&i)<<2,s>=t)){n.push(t_[63&r]);break}r|=(i=255&e[s++])>>6&3,n.push(t_[63&r]),n.push(t_[63&i])}return n.join("")}var tO=16,tT=10,tR=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],tP=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],tk=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274];function tI(e,t,r,i){var s,n=e[t],a=e[t+1];return n^=r[0],a^=(i[n>>>24]+i[256|n>>16&255]^i[512|n>>8&255])+i[768|255&n]^r[1],n^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[2],a^=(i[n>>>24]+i[256|n>>16&255]^i[512|n>>8&255])+i[768|255&n]^r[3],n^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[4],a^=(i[n>>>24]+i[256|n>>16&255]^i[512|n>>8&255])+i[768|255&n]^r[5],n^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[6],a^=(i[n>>>24]+i[256|n>>16&255]^i[512|n>>8&255])+i[768|255&n]^r[7],n^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[8],a^=(i[n>>>24]+i[256|n>>16&255]^i[512|n>>8&255])+i[768|255&n]^r[9],n^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[10],a^=(i[n>>>24]+i[256|n>>16&255]^i[512|n>>8&255])+i[768|255&n]^r[11],n^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[12],a^=(i[n>>>24]+i[256|n>>16&255]^i[512|n>>8&255])+i[768|255&n]^r[13],n^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[14],a^=(i[n>>>24]+i[256|n>>16&255]^i[512|n>>8&255])+i[768|255&n]^r[15],n^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[16],e[t]=a^r[17],e[t+1]=n,e}function tC(e,t){for(var r=0,i=0;r<4;++r)i=i<<8|255&e[t],t=(t+1)%e.length;return{key:i,offp:t}}function tj(e,t,r){for(var i,s=0,n=[0,0],a=t.length,o=r.length,l=0;l<a;l++)s=(i=tC(e,s)).offp,t[l]=t[l]^i.key;for(l=0;l<a;l+=2)n=tI(n,0,t,r),t[l]=n[0],t[l+1]=n[1];for(l=0;l<o;l+=2)n=tI(n,0,t,r),r[l]=n[0],r[l+1]=n[1]}function tA(e,t,r,i,s){var n,a,o=tk.slice(),l=o.length;if(r<4||r>31){if(a=Error("Illegal number of rounds (4-31): "+r),i)return void tw(i.bind(this,a));throw a}if(t.length!==tO){if(a=Error("Illegal salt length: "+t.length+" != "+tO),i)return void tw(i.bind(this,a));throw a}r=1<<r>>>0;var c,u,h,d=0;function f(){if(s&&s(d/r),d<r)for(var n=Date.now();d<r&&(d+=1,tj(e,c,u),tj(t,c,u),!(Date.now()-n>100)););else{for(d=0;d<64;d++)for(h=0;h<l>>1;h++)tI(o,h<<1,c,u);var a=[];for(d=0;d<l;d++)a.push((o[d]>>24&255)>>>0),a.push((o[d]>>16&255)>>>0),a.push((o[d]>>8&255)>>>0),a.push((255&o[d])>>>0);return i?void i(null,a):a}i&&tw(f)}if("function"==typeof Int32Array?(c=new Int32Array(tR),u=new Int32Array(tP)):(c=tR.slice(),u=tP.slice()),!function(e,t,r,i){for(var s,n=0,a=[0,0],o=r.length,l=i.length,c=0;c<o;c++)n=(s=tC(t,n)).offp,r[c]=r[c]^s.key;for(c=0,n=0;c<o;c+=2)n=(s=tC(e,n)).offp,a[0]^=s.key,n=(s=tC(e,n)).offp,a[1]^=s.key,a=tI(a,0,r,i),r[c]=a[0],r[c+1]=a[1];for(c=0;c<l;c+=2)n=(s=tC(e,n)).offp,a[0]^=s.key,n=(s=tC(e,n)).offp,a[1]^=s.key,a=tI(a,0,r,i),i[c]=a[0],i[c+1]=a[1]}(t,e,c,u),void 0!==i)f();else for(;;)if(void 0!==(n=f()))return n||[]}var tN=r(4202),t$=r.n(tN);let tL=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,6003)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)};class tM extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class tD extends tM{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class tU extends tM{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class tB extends tM{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(n||(n={}));class tq{constructor(e,{headers:t={},customFetch:r,region:i=n.Any}={}){this.url=e,this.headers=t,this.region=i,this.fetch=tL(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,i,s,n,a;return i=this,s=void 0,n=void 0,a=function*(){try{let i,s,{headers:n,method:a,body:o}=t,l={},{region:c}=t;c||(c=this.region),c&&"any"!==c&&(l["x-region"]=c),o&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(l["Content-Type"]="application/octet-stream",i=o):"string"==typeof o?(l["Content-Type"]="text/plain",i=o):"undefined"!=typeof FormData&&o instanceof FormData?i=o:(l["Content-Type"]="application/json",i=JSON.stringify(o)));let u=yield this.fetch(`${this.url}/${e}`,{method:a||"POST",headers:Object.assign(Object.assign(Object.assign({},l),this.headers),n),body:i}).catch(e=>{throw new tD(e)}),h=u.headers.get("x-relay-error");if(h&&"true"===h)throw new tU(u);if(!u.ok)throw new tB(u);let d=(null!=(r=u.headers.get("Content-Type"))?r:"text/plain").split(";")[0].trim();return{data:"application/json"===d?yield u.json():"application/octet-stream"===d?yield u.blob():"text/event-stream"===d?u:"multipart/form-data"===d?yield u.formData():yield u.text(),error:null}}catch(e){return{data:null,error:e}}},new(n||(n=Promise))(function(e,t){function r(e){try{l(a.next(e))}catch(e){t(e)}}function o(e){try{l(a.throw(e))}catch(e){t(e)}}function l(t){var i;t.done?e(t.value):((i=t.value)instanceof n?i:new n(function(e){e(i)})).then(r,o)}l((a=a.apply(i,s||[])).next())})}}let{PostgrestClient:tV,PostgrestQueryBuilder:tG,PostgrestFilterBuilder:tF,PostgrestTransformBuilder:tz,PostgrestBuilder:tH,PostgrestError:tW}=r(8355),tK="undefined"==typeof window?r(999):window.WebSocket,tX={"X-Client-Info":"realtime-js/2.11.10"};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(a||(a={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(o||(o={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(l||(l={})),(c||(c={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(u||(u={}));class tJ{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let i=t.getUint8(1),s=t.getUint8(2),n=this.HEADER_LENGTH+2,a=r.decode(e.slice(n,n+i));n+=i;let o=r.decode(e.slice(n,n+s));return n+=s,{ref:null,topic:a,event:o,payload:JSON.parse(r.decode(e.slice(n,e.byteLength)))}}}class tY{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(h||(h={}));let tQ=(e,t,r={})=>{var i;let s=null!=(i=r.skipTypes)?i:[];return Object.keys(t).reduce((r,i)=>(r[i]=tZ(i,e,t,s),r),{})},tZ=(e,t,r,i)=>{let s=t.find(t=>t.name===e),n=null==s?void 0:s.type,a=r[e];return n&&!i.includes(n)?t0(n,a):t1(a)},t0=(e,t)=>{if("_"===e.charAt(0))return t4(t,e.slice(1,e.length));switch(e){case h.bool:return t2(t);case h.float4:case h.float8:case h.int2:case h.int4:case h.int8:case h.numeric:case h.oid:return t5(t);case h.json:case h.jsonb:return t6(t);case h.timestamp:return t3(t);case h.abstime:case h.date:case h.daterange:case h.int4range:case h.int8range:case h.money:case h.reltime:case h.text:case h.time:case h.timestamptz:case h.timetz:case h.tsrange:case h.tstzrange:default:return t1(t)}},t1=e=>e,t2=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},t5=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},t6=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},t4=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,i=e[r];if("{"===e[0]&&"}"===i){let i,s=e.slice(1,r);try{i=JSON.parse("["+s+"]")}catch(e){i=s?s.split(","):[]}return i.map(e=>t0(t,e))}return e},t3=e=>"string"==typeof e?e.replace(" ","T"):e,t8=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class t9{constructor(e,t,r={},i=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null==(r=this.receivedResp)?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(d||(d={}));class t7{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=t7.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=t7.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],i()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=t7.syncDiff(this.state,e,t,r),i())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,i){let s=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(s,(e,t)=>{n[e]||(o[e]=t)}),this.map(n,(e,t)=>{let r=s[e];if(r){let i=t.map(e=>e.presence_ref),s=r.map(e=>e.presence_ref),n=t.filter(e=>0>s.indexOf(e.presence_ref)),l=r.filter(e=>0>i.indexOf(e.presence_ref));n.length>0&&(a[e]=n),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(s,{joins:a,leaves:o},r,i)}static syncDiff(e,t,r,i){let{joins:s,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(s,(t,i)=>{var s;let n=null!=(s=e[t])?s:[];if(e[t]=this.cloneDeep(i),n.length>0){let r=e[t].map(e=>e.presence_ref),i=n.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...i)}r(t,n,i)}),this.map(n,(t,r)=>{let s=e[t];if(!s)return;let n=r.map(e=>e.presence_ref);s=s.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=s,i(t,s,r),0===s.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let i=e[r];return"metas"in i?t[r]=i.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(f||(f={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(p||(p={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(g||(g={}));class re{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=o.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new t9(this,l.join,this.params,this.timeout),this.rejoinTimer=new tY(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=o.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=o.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=o.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=o.errored,this.rejoinTimer.scheduleTimeout())}),this._on(l.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new t7(this),this.broadcastEndpointURL=t8(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:s,presence:n,private:a}}=this.params;this._onError(t=>null==e?void 0:e(g.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(g.CLOSED));let l={},c={broadcast:s,presence:n,postgres_changes:null!=(i=null==(r=this.bindings.postgres_changes)?void 0:r.map(e=>e.filter))?i:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(g.SUBSCRIBED);return}{let i=this.bindings.postgres_changes,s=null!=(r=null==i?void 0:i.length)?r:0,n=[];for(let r=0;r<s;r++){let s=i[r],{filter:{event:a,schema:l,table:c,filter:u}}=s,h=t&&t[r];if(h&&h.event===a&&h.schema===l&&h.table===c&&h.filter===u)n.push(Object.assign(Object.assign({},s),{id:h.id}));else{this.unsubscribe(),this.state=o.errored,null==e||e(g.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(g.SUBSCRIBED);return}}).receive("error",t=>{this.state=o.errored,null==e||e(g.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(g.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,i;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var i,s,n;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(n=null==(s=null==(i=this.params)?void 0:i.config)?void 0:s.broadcast)?void 0:n.ack)||r("ok"),a.receive("ok",()=>r("ok")),a.receive("error",()=>r("error")),a.receive("timeout",()=>r("timed out"))});{let{event:s,payload:n}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:s,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!=(r=t.timeout)?r:this.timeout);return await (null==(i=e.body)?void 0:i.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=o.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(l.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(r=>{let i=new t9(this,l.leave,{},e);i.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),i.send(),this._canPush()||i.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){let i=new AbortController,s=setTimeout(()=>i.abort(),r),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:i.signal}));return clearTimeout(s),n}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new t9(this,e,t,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var i,s;let n=e.toLocaleLowerCase(),{close:a,error:o,leave:c,join:u}=l;if(r&&[a,o,c,u].indexOf(n)>=0&&r!==this._joinRef())return;let h=this._onMessage(n,t,r);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null==(i=this.bindings.postgres_changes)||i.filter(e=>{var t,r,i;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(i=null==(r=e.filter)?void 0:r.event)?void 0:i.toLocaleLowerCase())===n}).map(e=>e.callback(h,r)):null==(s=this.bindings[n])||s.filter(e=>{var r,i,s,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,a=null==(r=e.filter)?void 0:r.event;return n&&(null==(i=t.ids)?void 0:i.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null==(s=t.data)?void 0:s.type.toLocaleLowerCase()))}{let r=null==(o=null==(a=null==e?void 0:e.filter)?void 0:a.event)?void 0:o.toLocaleLowerCase();return"*"===r||r===(null==(l=null==t?void 0:t.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof h&&"ids"in h){let e=h.data,{schema:t,table:r,commit_timestamp:i,type:s,errors:n}=e;h=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:i,eventType:s,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(h,r)})}_isClosed(){return this.state===o.closed}_isJoined(){return this.state===o.joined}_isJoining(){return this.state===o.joining}_isLeaving(){return this.state===o.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let i=e.toLocaleLowerCase(),s={type:i,filter:t,callback:r};return this.bindings[i]?this.bindings[i].push(s):this.bindings[i]=[s],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var i;return!((null==(i=e.type)?void 0:i.toLocaleLowerCase())===r&&re.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(l.close,{},e)}_onError(e){this._on(l.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=o.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=tQ(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=tQ(e.columns,e.old_record)),t}}let rt=()=>{},rr=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class ri{constructor(e,t){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=tX,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=rt,this.ref=0,this.logger=rt,this.conn=null,this.sendBuffer=[],this.serializer=new tJ,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,6003)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${c.websocket}`,this.httpEndpoint=t8(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let s=null==(i=null==t?void 0:t.params)?void 0:i.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new tY(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=tK),this.transport){"undefined"!=typeof window&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new rs(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return this.channels=this.channels.filter(t=>t._joinRef!==e._joinRef),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case a.connecting:return u.Connecting;case a.open:return u.Open;case a.closing:return u.Closing;default:return u.Closed}}isConnected(){return this.connectionState()===u.Open}channel(e,t={config:{}}){let r=`realtime:${e}`,i=this.getChannels().find(e=>e.topic===r);if(i)return i;{let r=new re(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){let{topic:t,event:r,payload:i,ref:s}=e,n=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${r} (${s})`,i),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),e.joinedOnce&&e._isJoined()&&e._push(l.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected())return void this.heartbeatCallback("disconnected");if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null==(e=this.conn)||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:i,ref:s}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),s&&s===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${t} ${r} ${s&&"("+s+")"||""}`,i),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,i,s)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(l.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${r}${i}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([rr],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class rs{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=a.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class rn extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function ra(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class ro extends rn{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class rl extends rn{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let rc=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,6003)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},ru=()=>(function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,6003))).Response:Response}),rh=e=>{if(Array.isArray(e))return e.map(e=>rh(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=rh(r)}),t};var rd=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let rf=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),rp=(e,t,r)=>rd(void 0,void 0,void 0,function*(){e instanceof(yield ru())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new ro(rf(r),e.status||500))}).catch(e=>{t(new rl(rf(e),e))}):t(new rl(rf(e),e))}),rg=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),i&&(s.body=JSON.stringify(i)),Object.assign(Object.assign({},s),r))};function rb(e,t,r,i,s,n){return rd(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(r,rg(t,i,s,n)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>rp(e,o,i))})})}function rm(e,t,r,i){return rd(this,void 0,void 0,function*(){return rb(e,"GET",t,r,i)})}function rv(e,t,r,i,s){return rd(this,void 0,void 0,function*(){return rb(e,"POST",t,i,s,r)})}function ry(e,t,r,i,s){return rd(this,void 0,void 0,function*(){return rb(e,"DELETE",t,i,s,r)})}var rx=r(5356).Buffer,rw=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let r_={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},rE={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class rS{constructor(e,t={},r,i){this.url=e,this.headers=t,this.bucketId=r,this.fetch=rc(i)}uploadOrUpdate(e,t,r,i){return rw(this,void 0,void 0,function*(){try{let s,n=Object.assign(Object.assign({},rE),i),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),o=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((s=new FormData).append("cacheControl",n.cacheControl),o&&s.append("metadata",this.encodeMetadata(o)),s.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((s=r).append("cacheControl",n.cacheControl),o&&s.append("metadata",this.encodeMetadata(o))):(s=r,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==i?void 0:i.headers)&&(a=Object.assign(Object.assign({},a),i.headers));let l=this._removeEmptyFolders(t),c=this._getFinalPath(l),u=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:s,headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),h=yield u.json();if(u.ok)return{data:{path:l,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}upload(e,t,r){return rw(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,i){return rw(this,void 0,void 0,function*(){let s=this._removeEmptyFolders(e),n=this._getFinalPath(s),a=new URL(this.url+`/object/upload/sign/${n}`);a.searchParams.set("token",t);try{let e,t=Object.assign({upsert:rE.upsert},i),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),l=yield o.json();if(o.ok)return{data:{path:s,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return rw(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(i["x-upsert"]="true");let s=yield rv(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),n=new URL(this.url+s.url),a=n.searchParams.get("token");if(!a)throw new rn("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:a},error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}update(e,t,r){return rw(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return rw(this,void 0,void 0,function*(){try{return{data:yield rv(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}copy(e,t,r){return rw(this,void 0,void 0,function*(){try{return{data:{path:(yield rv(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return rw(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),s=yield rv(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s={signedUrl:encodeURI(`${this.url}${s.signedURL}${n}`)},error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return rw(this,void 0,void 0,function*(){try{let i=yield rv(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:i.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${s}`):null})),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}download(e,t){return rw(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),s=i?`?${i}`:"";try{let t=this._getFinalPath(e),i=yield rm(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${s}`,{headers:this.headers,noResolveJson:!0});return{data:yield i.blob(),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}info(e){return rw(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield rm(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:rh(e),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}exists(e){return rw(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,i){return rd(this,void 0,void 0,function*(){return rb(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(ra(e)&&e instanceof rl){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),i=[],s=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==s&&i.push(s);let n=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&i.push(a);let o=i.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${r}${o}`)}}}remove(e){return rw(this,void 0,void 0,function*(){try{return{data:yield ry(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}list(e,t,r){return rw(this,void 0,void 0,function*(){try{let i=Object.assign(Object.assign(Object.assign({},r_),t),{prefix:e||""});return{data:yield rv(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==rx?rx.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let rO={"X-Client-Info":"storage-js/2.7.1"};var rT=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};class rR{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},rO),t),this.fetch=rc(r)}listBuckets(){return rT(this,void 0,void 0,function*(){try{return{data:yield rm(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}getBucket(e){return rT(this,void 0,void 0,function*(){try{return{data:yield rm(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return rT(this,void 0,void 0,function*(){try{return{data:yield rv(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return rT(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,i,s){return rd(this,void 0,void 0,function*(){return rb(e,"PUT",t,i,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}emptyBucket(e){return rT(this,void 0,void 0,function*(){try{return{data:yield rv(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}deleteBucket(e){return rT(this,void 0,void 0,function*(){try{return{data:yield ry(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(ra(e))return{data:null,error:e};throw e}})}}class rP extends rR{constructor(e,t={},r){super(e,t,r)}from(e){return new rS(this.url,this.headers,e,this.fetch)}}let rk="";rk="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let rI={headers:{"X-Client-Info":`supabase-js-${rk}/2.50.0`}},rC={schema:"public"},rj={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},rA={};var rN=r(6003);let r$=e=>{let t;return t=e||("undefined"==typeof fetch?rN.default:fetch),(...e)=>t(...e)},rL=()=>"undefined"==typeof Headers?rN.Headers:Headers,rM=(e,t,r)=>{let i=r$(r),s=rL();return(r,n)=>(function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!=(a=yield t())?a:e,l=new s(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),i(r,Object.assign(Object.assign({},n),{headers:l}))})},rD="2.70.0",rU={"X-Client-Info":`gotrue-js/${rD}`},rB="X-Supabase-Api-Version",rq={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},rV=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class rG extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function rF(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class rz extends rG{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class rH extends rG{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class rW extends rG{constructor(e,t,r,i){super(e,r,i),this.name=t,this.status=r}}class rK extends rW{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class rX extends rW{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class rJ extends rW{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class rY extends rW{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rQ extends rW{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rZ extends rW{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function r0(e){return rF(e)&&"AuthRetryableFetchError"===e.name}class r1 extends rW{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class r2 extends rW{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let r5="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),r6=" 	\n\r=".split(""),r4=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<r6.length;t+=1)e[r6[t].charCodeAt(0)]=-2;for(let t=0;t<r5.length;t+=1)e[r5[t].charCodeAt(0)]=t;return e})();function r3(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)r(r5[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)r(r5[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function r8(e,t,r){let i=r4[e];if(i>-1)for(t.queue=t.queue<<6|i,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===i)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function r9(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},n=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,i,r)};for(let t=0;t<e.length;t+=1)r8(e.charCodeAt(t),s,n);return t.join("")}let r7=()=>"undefined"!=typeof window&&"undefined"!=typeof document,ie={tested:!1,writable:!1},it=()=>{if(!r7())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(ie.tested)return ie.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),ie.tested=!0,ie.writable=!0}catch(e){ie.tested=!0,ie.writable=!1}return ie.writable},ir=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,6003)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},ii=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,is=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},ia=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},io=async(e,t)=>{await e.removeItem(t)};class il{constructor(){this.promise=new il.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function ic(e){let t=e.split(".");if(3!==t.length)throw new r2("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!rV.test(t[e]))throw new r2("JWT not in base64url format");return{header:JSON.parse(r9(t[0])),payload:JSON.parse(r9(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)r8(e.charCodeAt(t),r,i);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function iu(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function ih(e){return("0"+e.toString(16)).substr(-2)}async function id(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function ip(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await id(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function ig(e,t,r=!1){let i=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let i=0;i<56;i++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,ih).join("")}(),s=i;r&&(s+="/PASSWORD_RECOVERY"),await is(e,`${t}-code-verifier`,s);let n=await ip(i),a=i===n?"plain":"s256";return[n,a]}il.promiseConstructor=Promise;let ib=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,im=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function iv(e){if(!im.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var iy=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};let ix=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),iw=[502,503,504];async function i_(e){var t;let r,i;if(!ii(e))throw new rZ(ix(e),0);if(iw.includes(e.status))throw new rZ(ix(e),e.status);try{r=await e.json()}catch(e){throw new rH(ix(e),e)}let s=function(e){let t=e.headers.get(rB);if(!t||!t.match(ib))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(s&&s.getTime()>=rq["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?i=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(i=r.error_code),i){if("weak_password"===i)throw new r1(ix(r),e.status,(null==(t=r.weak_password)?void 0:t.reasons)||[]);else if("session_not_found"===i)throw new rK}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new r1(ix(r),e.status,r.weak_password.reasons);throw new rz(ix(r),e.status||500,i)}let iE=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),s.body=JSON.stringify(i),Object.assign(Object.assign({},s),r))};async function iS(e,t,r,i){var s;let n=Object.assign({},null==i?void 0:i.headers);n[rB]||(n[rB]=rq["2024-01-01"].name),(null==i?void 0:i.jwt)&&(n.Authorization=`Bearer ${i.jwt}`);let a=null!=(s=null==i?void 0:i.query)?s:{};(null==i?void 0:i.redirectTo)&&(a.redirect_to=i.redirectTo);let o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await iO(e,t,r+o,{headers:n,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(l):{data:Object.assign({},l),error:null}}async function iO(e,t,r,i,s,n){let a,o=iE(t,i,s,n);try{a=await e(r,Object.assign({},o))}catch(e){throw console.error(e),new rZ(ix(e),0)}if(a.ok||await i_(a),null==i?void 0:i.noResolveJson)return a;try{return await a.json()}catch(e){await i_(e)}}function iT(e){var t,r,i;let s=null;(i=e).access_token&&i.refresh_token&&i.expires_in&&(s=Object.assign({},e),e.expires_at||(s.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:s,user:null!=(t=e.user)?t:e},error:null}}function iR(e){let t=iT(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function iP(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function ik(e){return{data:e,error:null}}function iI(e){let{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n},user:Object.assign({},iy(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function iC(e){return e}let ij=["global","local","others"];var iA=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};class iN{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=ir(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=ij[0]){if(0>ij.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${ij.join(", ")}`);try{return await iS(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(rF(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await iS(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:iP})}catch(e){if(rF(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=iA(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=null==r?void 0:r.newEmail,delete i.newEmail),await iS(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:iI,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(rF(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await iS(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:iP})}catch(e){if(rF(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,i,s,n,a,o;try{let l={nextPage:null,lastPage:0,total:0},c=await iS(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(r=null==(t=null==e?void 0:e.page)?void 0:t.toString())?r:"",per_page:null!=(s=null==(i=null==e?void 0:e.perPage)?void 0:i.toString())?s:""},xform:iC});if(c.error)throw c.error;let u=await c.json(),h=null!=(n=c.headers.get("x-total-count"))?n:0,d=null!=(o=null==(a=c.headers.get("link"))?void 0:a.split(","))?o:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(e){if(rF(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){iv(e);try{return await iS(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:iP})}catch(e){if(rF(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){iv(e);try{return await iS(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:iP})}catch(e){if(rF(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){iv(e);try{return await iS(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:iP})}catch(e){if(rF(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){iv(e.userId);try{let{data:t,error:r}=await iS(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(rF(e))return{data:null,error:e};throw e}}async _deleteFactor(e){iv(e.userId),iv(e.id);try{return{data:await iS(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(rF(e))return{data:null,error:e};throw e}}}let i$={getItem:e=>it()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{it()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{it()&&globalThis.localStorage.removeItem(e)}};function iL(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let iM={debug:!!(globalThis&&it()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class iD extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class iU extends iD{}async function iB(e,t,r){iM.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let i=new globalThis.AbortController;return t>0&&setTimeout(()=>{i.abort(),iM.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){iM.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await r()}finally{iM.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}if(0===t)throw iM.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new iU(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(iM.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}))}if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}let iq={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:rU,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function iV(e,t,r){return await r()}class iG{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=iG.nextInstanceID,iG.nextInstanceID+=1,this.instanceID>0&&r7()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let i=Object.assign(Object.assign({},iq),e);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new iN({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=ir(i.fetch),this.lock=i.lock||iV,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:r7()&&(null==(t=null==globalThis?void 0:globalThis.navigator)?void 0:t.locks)?this.lock=iB:this.lock=iV,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:it()?this.storage=i$:(this.memoryStorage={},this.storage=iL(this.memoryStorage)):(this.memoryStorage={},this.storage=iL(this.memoryStorage)),r7()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(r=this.broadcastChannel)||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${rD}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),r7()&&this.detectSessionInUrl&&"none"!==r){let{data:i,error:s}=await this._getSessionFromURL(t,r);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),rF(s)&&"AuthImplicitGrantRedirectError"===s.name){let t=null==(e=s.details)?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:s}}return await this._removeSession(),{error:s}}let{session:n,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",n,"redirect type",a),await this._saveSession(n),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(rF(e))return{error:e};return{error:new rH("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,i;try{let{data:s,error:n}=await iS(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(r=null==(t=null==e?void 0:e.options)?void 0:t.data)?r:{},gotrue_meta_security:{captcha_token:null==(i=null==e?void 0:e.options)?void 0:i.captchaToken}},xform:iT});if(n||!s)return{data:{user:null,session:null},error:n};let a=s.session,o=s.user;return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:o,session:a},error:null}}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,i;try{let s;if("email"in e){let{email:r,password:i,options:n}=e,a=null,o=null;"pkce"===this.flowType&&([a,o]=await ig(this.storage,this.storageKey)),s=await iS(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:r,password:i,data:null!=(t=null==n?void 0:n.data)?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:a,code_challenge_method:o},xform:iT})}else if("phone"in e){let{phone:t,password:n,options:a}=e;s=await iS(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!=(r=null==a?void 0:a.data)?r:{},channel:null!=(i=null==a?void 0:a.channel)?i:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:iT})}else throw new rJ("You must provide either an email or phone number and a password");let{data:n,error:a}=s;if(a||!n)return{data:{user:null,session:null},error:a};let o=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:i,options:s}=e;t=await iS(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:iR})}else if("phone"in e){let{phone:r,password:i,options:s}=e;t=await iS(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:iR})}else throw new rJ("You must provide either an email or phone number and a password");let{data:r,error:i}=t;if(i)return{data:{user:null,session:null},error:i};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new rX};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i}}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,i,s;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(t=e.options)?void 0:t.redirectTo,scopes:null==(r=e.options)?void 0:r.scopes,queryParams:null==(i=e.options)?void 0:i.queryParams,skipBrowserRedirect:null==(s=e.options)?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,i,s,n,a,o,l,c,u,h,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{let h,{chain:d,wallet:g,statement:b,options:m}=e;if(r7())if("object"==typeof g)h=g;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))h=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if("object"!=typeof g||!(null==m?void 0:m.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");h=g}let v=new URL(null!=(t=null==m?void 0:m.url)?t:window.location.href);if("signIn"in h&&h.signIn){let e,t=await h.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==m?void 0:m.signInWithSolana),{version:"1",domain:v.host,uri:v.href}),b?{statement:b}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)f="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),p=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in h)||"function"!=typeof h.signMessage||!("publicKey"in h)||"object"!=typeof h||!h.publicKey||!("toBase58"in h.publicKey)||"function"!=typeof h.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${v.host} wants you to sign in with your Solana account:`,h.publicKey.toBase58(),...b?["",b,""]:[""],"Version: 1",`URI: ${v.href}`,`Issued At: ${null!=(i=null==(r=null==m?void 0:m.signInWithSolana)?void 0:r.issuedAt)?i:new Date().toISOString()}`,...(null==(s=null==m?void 0:m.signInWithSolana)?void 0:s.notBefore)?[`Not Before: ${m.signInWithSolana.notBefore}`]:[],...(null==(n=null==m?void 0:m.signInWithSolana)?void 0:n.expirationTime)?[`Expiration Time: ${m.signInWithSolana.expirationTime}`]:[],...(null==(a=null==m?void 0:m.signInWithSolana)?void 0:a.chainId)?[`Chain ID: ${m.signInWithSolana.chainId}`]:[],...(null==(o=null==m?void 0:m.signInWithSolana)?void 0:o.nonce)?[`Nonce: ${m.signInWithSolana.nonce}`]:[],...(null==(l=null==m?void 0:m.signInWithSolana)?void 0:l.requestId)?[`Request ID: ${m.signInWithSolana.requestId}`]:[],...(null==(u=null==(c=null==m?void 0:m.signInWithSolana)?void 0:c.resources)?void 0:u.length)?["Resources",...m.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await h.signMessage(new TextEncoder().encode(f),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{let{data:t,error:r}=await iS(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};return e.forEach(e=>r3(e,r,i)),r3(null,r,i),t.join("")}(p)},(null==(h=e.options)?void 0:h.captchaToken)?{gotrue_meta_security:{captcha_token:null==(d=e.options)?void 0:d.captchaToken}}:null),xform:iT});if(r)throw r;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new rX};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await ia(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(null!=t?t:"").split("/");try{let{data:t,error:s}=await iS(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:iT});if(await io(this.storage,`${this.storageKey}-code-verifier`),s)throw s;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new rX};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=i?i:null}),error:s}}catch(e){if(rF(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:i,access_token:s,nonce:n}=e,{data:a,error:o}=await iS(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:s,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:iT});if(o)return{data:{user:null,session:null},error:o};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new rX};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:o}}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,i,s,n;try{if("email"in e){let{email:i,options:s}=e,n=null,a=null;"pkce"===this.flowType&&([n,a]=await ig(this.storage,this.storageKey));let{error:o}=await iS(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:i,data:null!=(t=null==s?void 0:s.data)?t:{},create_user:null==(r=null==s?void 0:s.shouldCreateUser)||r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:n,code_challenge_method:a},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:r}=e,{data:a,error:o}=await iS(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!=(i=null==r?void 0:r.data)?i:{},create_user:null==(s=null==r?void 0:r.shouldCreateUser)||s,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!=(n=null==r?void 0:r.channel)?n:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new rJ("You must provide either an email or phone number.")}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let i,s;"options"in e&&(i=null==(t=e.options)?void 0:t.redirectTo,s=null==(r=e.options)?void 0:r.captchaToken);let{data:n,error:a}=await iS(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:s}}),redirectTo:i,xform:iT});if(a)throw a;if(!n)throw Error("An error occurred on token verification.");let o=n.session,l=n.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,i;try{let s=null,n=null;return"pkce"===this.flowType&&([s,n]=await ig(this.storage,this.storageKey)),await iS(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(r=null==(t=e.options)?void 0:t.redirectTo)?r:void 0}),(null==(i=null==e?void 0:e.options)?void 0:i.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:n}),headers:this.headers,xform:ik})}catch(e){if(rF(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new rK;let{error:i}=await iS(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:i}})}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:i,options:s}=e,{error:n}=await iS(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){let{phone:r,type:i,options:s}=e,{data:n,error:a}=await iS(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:a}}throw new rJ("You must provide either an email or phone number and a type")}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await ia(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,i)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,i))})}return{data:{session:e},error:null}}let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{session:null},error:s};return{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await iS(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:iP});return await this._useSession(async e=>{var t,r,i;let{data:s,error:n}=e;if(n)throw n;return(null==(t=s.session)?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await iS(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(i=null==(r=s.session)?void 0:r.access_token)?i:void 0,xform:iP}):{data:{user:null},error:new rK}})}catch(e){if(rF(e))return rF(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await io(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:i,error:s}=r;if(s)throw s;if(!i.session)throw new rK;let n=i.session,a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await ig(this.storage,this.storageKey));let{data:l,error:c}=await iS(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:n.access_token,xform:iP});if(c)throw c;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}})}catch(e){if(rF(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new rK;let t=Date.now()/1e3,r=t,i=!0,s=null,{payload:n}=ic(e.access_token);if(n.exp&&(i=(r=n.exp)<=t),i){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};s=t}else{let{data:i,error:n}=await this._getUser(e.access_token);if(n)throw n;s={access_token:e.access_token,refresh_token:e.refresh_token,user:i.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(e){if(rF(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:i,error:s}=t;if(s)throw s;e=null!=(r=i.session)?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new rK;let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);return s?{data:{user:null,session:null},error:s}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(rF(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!r7())throw new rY("No browser detected.");if(e.error||e.error_description||e.error_code)throw new rY(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new rQ("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new rY("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new rQ("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let i=new URL(window.location.href);return i.searchParams.delete("code"),window.history.replaceState(window.history.state,"",i.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:i,access_token:s,refresh_token:n,expires_in:a,expires_at:o,token_type:l}=e;if(!s||!a||!n||!l)throw new rY("No session defined in URL");let c=Math.round(Date.now()/1e3),u=parseInt(a),h=c+u;o&&(h=parseInt(o));let d=h-c;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${u}s`);let f=h-u;c-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,h,c):c-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,h,c);let{data:p,error:g}=await this._getUser(s);if(g)throw g;let b={provider_token:r,provider_refresh_token:i,access_token:s,expires_in:u,expires_at:h,refresh_token:n,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:b,redirectType:e.type},error:null}}catch(e){if(rF(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await ia(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{error:s};let n=null==(r=i.session)?void 0:r.access_token;if(n){let{error:t}=await this.admin.signOut(n,e);if(t&&!(rF(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await io(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,i;try{let{data:{session:i},error:s}=t;if(s)throw s;await (null==(r=this.stateChangeEmitters.get(e))?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(t){await (null==(i=this.stateChangeEmitters.get(e))?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,i=null;"pkce"===this.flowType&&([r,i]=await ig(this.storage,this.storageKey,!0));try{return await iS(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(rF(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!=(e=t.user.identities)?e:[]},error:null}}catch(e){if(rF(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:i}=await this._useSession(async t=>{var r,i,s,n,a;let{data:o,error:l}=t;if(l)throw l;let c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(r=e.options)?void 0:r.redirectTo,scopes:null==(i=e.options)?void 0:i.scopes,queryParams:null==(s=e.options)?void 0:s.queryParams,skipBrowserRedirect:!0});return await iS(this.fetch,"GET",c,{headers:this.headers,jwt:null!=(a=null==(n=o.session)?void 0:n.access_token)?a:void 0})});if(i)throw i;return!r7()||(null==(t=e.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(rF(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)throw n;return await iS(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(i=null==(r=s.session)?void 0:r.access_token)?i:void 0})})}catch(e){if(rF(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,i;let s=Date.now();return await (r=async r=>(r>0&&await iu(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await iS(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:iT})),i=(e,t)=>{let r=200*Math.pow(2,e);return t&&r0(t)&&Date.now()+r-s<3e4},new Promise((e,t)=>{(async()=>{for(let s=0;s<1/0;s++)try{let t=await r(s);if(!i(s,null,t))return void e(t)}catch(e){if(!i(s,e))return void t(e)}})()}))}catch(e){if(this._debug(t,"error",e),rF(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),r7()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await ia(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let i=(null!=(e=r.expires_at)?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),r0(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new rK;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let i=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new il;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new rK;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let i={session:t.session,error:null};return this.refreshingDeferred.resolve(i),i}catch(e){if(this._debug(i,"error",e),rF(e)){let r={session:null,error:e};return r0(e)||await this._removeSession(),null==(t=this.refreshingDeferred)||t.resolve(r),r}throw null==(r=this.refreshingDeferred)||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(e,t,r=!0){let i=`#_notifyAllSubscribers(${e})`;this._debug(i,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let i=[],s=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){i.push(e)}});if(await Promise.all(s),i.length>0){for(let e=0;e<i.length;e+=1)console.error(i[e]);throw i[0]}}finally{this._debug(i,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await is(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await io(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&r7()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let i=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),i<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof iD)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!r7()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let i=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await ig(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});i.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);i.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${i.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await iS(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token})})}catch(e){if(rF(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)return{data:null,error:n};let a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await iS(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null==(r=null==s?void 0:s.session)?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null==(i=null==o?void 0:o.totp)?void 0:i.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(rF(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{data:null,error:s};let{data:n,error:a}=await iS(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:a})})}catch(e){if(rF(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await iS(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token})})}catch(e){if(rF(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],i=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),s=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:i,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:i},error:s}=e;if(s)return{data:null,error:s};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:n}=ic(i.access_token),a=null;n.aal&&(a=n.aal);let o=a;return(null!=(r=null==(t=i.user.factors)?void 0:t.filter(e=>"verified"===e.status))?r:[]).length>0&&(o="aal2"),{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:n.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:i,error:s}=await iS(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(s)throw s;if(!i.keys||0===i.keys.length)throw new r2("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),!(r=i.keys.find(t=>t.kid===e)))throw new r2("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let i=e;if(!i){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};i=e.session.access_token}let{header:s,payload:n,signature:a,raw:{header:o,payload:l}}=ic(i);var r=n.exp;if(!r)throw Error("Missing exp claim");if(r<=Math.floor(Date.now()/1e3))throw Error("JWT has expired");if(!s.kid||"HS256"===s.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(i);if(e)throw e;return{data:{claims:n,header:s,signature:a},error:null}}let c=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(s.alg),u=await this.fetchJwk(s.kid,t),h=await crypto.subtle.importKey("jwk",u,c,!0,["verify"]);if(!await crypto.subtle.verify(c,h,a,function(e){let t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${o}.${l}`)))throw new r2("Invalid JWT signature");return{data:{claims:n,header:s,signature:a},error:null}}catch(e){if(rF(e))return{data:null,error:e};throw e}}}iG.nextInstanceID=0;let iF=iG;class iz extends iF{constructor(e){super(e)}}class iH{constructor(e,t,r){var i,s,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o=`sb-${a.hostname.split(".")[0]}-auth-token`,l=function(e,t){var r,i;let{db:s,auth:n,realtime:a,global:o}=e,{db:l,auth:c,realtime:u,global:h}=t,d={db:Object.assign(Object.assign({},l),s),auth:Object.assign(Object.assign({},c),n),realtime:Object.assign(Object.assign({},u),a),global:Object.assign(Object.assign(Object.assign({},h),o),{headers:Object.assign(Object.assign({},null!=(r=null==h?void 0:h.headers)?r:{}),null!=(i=null==o?void 0:o.headers)?i:{})}),accessToken:()=>{var e,t,r,i;return e=this,t=void 0,i=function*(){return""},new(r=void 0,r=Promise)(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:rC,realtime:rA,auth:Object.assign(Object.assign({},rj),{storageKey:o}),global:rI});this.storageKey=null!=(i=l.auth.storageKey)?i:"",this.headers=null!=(s=l.global.headers)?s:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(n=l.auth)?n:{},this.headers,l.global.fetch),this.fetch=rM(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new tV(new URL("rest/v1",a).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new tq(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new rP(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,i,s,n;return r=this,i=void 0,s=void 0,n=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!=(t=null==(e=r.session)?void 0:e.access_token)?t:null},new(s||(s=Promise))(function(e,t){function a(e){try{l(n.next(e))}catch(e){t(e)}}function o(e){try{l(n.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof s?r:new s(function(e){e(r)})).then(a,o)}l((n=n.apply(r,i||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,storageKey:s,flowType:n,lock:a,debug:o},l,c){let u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new iz({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),l),storageKey:s,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,flowType:n,lock:a,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new ri(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let iW=(e,t,r)=>new iH(e,t,r),iK=process.env.NEXT_PUBLIC_SUPABASE_URL;iW(iK,process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY),iW(iK,process.env.SUPABASE_SERVICE_ROLE_KEY);let iX=process.env.JWT_SECRET||"your-secret-key-change-in-production";function iJ(e){try{return t$().verify(e,iX)}catch(e){return null}}let iY=["/admin"],iQ=["/login"];function iZ(e){let{pathname:t}=e.nextUrl,r=iY.some(e=>t.startsWith(e)),i=iQ.some(e=>t.startsWith(e)),s=e.cookies.get("auth-token")?.value;if(r&&!s){let r=new URL("/login",e.url);return r.searchParams.set("redirect",t),et.redirect(r)}if(r&&s&&!iJ(s)){let r=new URL("/login",e.url);r.searchParams.set("redirect",t);let i=et.redirect(r);return i.cookies.delete("auth-token"),i}return i&&s&&iJ(s)?et.redirect(new URL("/admin",e.url)):et.next()}let i0={matcher:["/((?!api|_next/static|_next/image|favicon.ico).*)"]},i1=(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401}),{...b}),i2=i1.middleware||i1.default,i5="/src/middleware";if("function"!=typeof i2)throw Object.defineProperty(Error(`The Middleware "${i5}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function i6(e){return tb({...e,page:i5,handler:async(...e)=>{try{return await i2(...e)}catch(s){let t=e[0],r=new URL(t.url),i=r.pathname+r.search;throw await x(s,{path:i,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),s}}})}},9694:(e,t,r)=>{"use strict";let i=r(4966);e.exports=(e,t,r)=>i(e,t,r)>0},9825:()=>{},9879:(e,t,r)=>{"use strict";let i=r(7814);e.exports=(e,t,r,s,n)=>{"string"==typeof r&&(n=s,s=r,r=void 0);try{return new i(e instanceof i?e.version:e,r).inc(t,s,n).version}catch(e){return null}}}},e=>{var t=e(e.s=9623);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=t}]);
//# sourceMappingURL=middleware.js.map