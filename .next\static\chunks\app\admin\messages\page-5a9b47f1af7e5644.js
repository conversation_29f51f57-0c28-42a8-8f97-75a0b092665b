(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[543],{18403:(e,s,r)=>{"use strict";r.d(s,{Card:()=>g});var t=r(95155),o=r(12115),i=r(56204),n=r(68918),a=r(43664),l=r(53791),d=r(64511),c=r(97287),h=r(50020),p=r(18832),x=r(42942);let m={},u=(0,n.createVarsResolver)((e,s)=>{let{padding:r}=s;return{root:{"--card-padding":(0,i.getSpacing)(r)}}}),g=(0,d.polymorphicFactory)((e,s)=>{let r=(0,a.useProps)("Card",m,e),{classNames:i,className:n,style:d,styles:g,unstyled:j,vars:y,children:f,padding:v,...S}=r,b=(0,l.useStyles)({name:"Card",props:r,classes:x.A,className:n,style:d,classNames:i,styles:g,unstyled:j,vars:y,varsResolver:u}),_=o.Children.toArray(f),w=_.map((e,s)=>"object"==typeof e&&e&&"type"in e&&e.type===p.CardSection?(0,o.cloneElement)(e,{"data-first-section":0===s||void 0,"data-last-section":s===_.length-1||void 0}):e);return(0,t.jsx)(h.u,{value:{getStyles:b},children:(0,t.jsx)(c.Paper,{ref:s,unstyled:j,...b("root"),...S,children:w})})});g.classes=x.A,g.displayName="@mantine/core/Card",g.Section=p.CardSection},18832:(e,s,r)=>{"use strict";r.d(s,{CardSection:()=>c});var t=r(95155);r(12115);var o=r(43664),i=r(69604),n=r(64511),a=r(50020),l=r(42942);let d={},c=(0,n.polymorphicFactory)((e,s)=>{let{classNames:r,className:n,style:l,styles:c,vars:h,withBorder:p,inheritPadding:x,mod:m,...u}=(0,o.useProps)("CardSection",d,e),g=(0,a.f)();return(0,t.jsx)(i.Box,{ref:s,mod:[{"with-border":p,"inherit-padding":x},m],...g.getStyles("section",{className:n,style:l,styles:c,classNames:r}),...u})});c.classes=l.A,c.displayName="@mantine/core/CardSection"},42942:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});var t={root:"m_e615b15f",section:"m_599a2148"}},50020:(e,s,r)=>{"use strict";r.d(s,{f:()=>i,u:()=>o}),r(12115);var t=r(56970);r(95155);let[o,i]=(0,t.createSafeContext)("Card component was not found in tree")},51675:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>z});var t=r(95155),o=r(12115),i=r(26029),n=r(8141),a=r(83347),l=r(21220),d=r(70112),c=r(93751),h=r(26903),p=r(74634),x=r(18403),m=r(58887),u=r(53776),g=r(74271),j=r(63617),y=r(41954),f=r(86467),v=(0,f.A)("outline","refresh","IconRefresh",[["path",{d:"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4",key:"svg-0"}],["path",{d:"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4",key:"svg-1"}]]),S=r(78977),b=(0,f.A)("outline","message","IconMessage",[["path",{d:"M8 9h8",key:"svg-0"}],["path",{d:"M8 13h6",key:"svg-1"}],["path",{d:"M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z",key:"svg-2"}]]),_=(0,f.A)("outline","send","IconSend",[["path",{d:"M10 14l11 -11",key:"svg-0"}],["path",{d:"M21 3l-6.5 18a.55 .55 0 0 1 -1 0l-3.5 -7l-7 -3.5a.55 .55 0 0 1 0 -1l18 -6.5",key:"svg-1"}]]),w=r(11681),C=r(11659),k=r(6942);function z(){let[e,s]=(0,o.useState)([]),[r,f]=(0,o.useState)(!0),[z,B]=(0,o.useState)(null),[T,{open:M,close:A}]=(0,w.useDisclosure)(!1),[R,E]=(0,o.useState)(!1),N=(0,C.m)({initialValues:{response_text:""},validate:{response_text:e=>e.trim()?null:"Response text is required"}});(0,o.useEffect)(()=>{D()},[]);let D=async()=>{try{f(!0);let e=await fetch("/api/messages"),r=await e.json();e.ok?s(r.messages||[]):k.notifications.show({title:"Error",message:r.error||"Failed to load messages",color:"red"})}catch(e){console.error("Error loading messages:",e),k.notifications.show({title:"Error",message:"Failed to load messages",color:"red"})}finally{f(!1)}},F=e=>{B(e),N.reset(),M()},P=async e=>{if(z){E(!0);try{let s=await fetch("/api/responses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message_id:z.id,response_text:e.response_text})}),r=await s.json();s.ok?(k.notifications.show({title:"Success",message:"Response sent successfully",color:"green"}),A(),D()):k.notifications.show({title:"Error",message:r.error||"Failed to send response",color:"red"})}catch(e){console.error("Error sending response:",e),k.notifications.show({title:"Error",message:"Failed to send response",color:"red"})}finally{E(!1)}}},G=e=>0===e.length?(0,t.jsx)(i.Badge,{color:"yellow",variant:"light",children:"No Response"}):e.some(e=>"manual"===e.response_type)?(0,t.jsx)(i.Badge,{color:"blue",variant:"light",children:"Manual Response"}):(0,t.jsx)(i.Badge,{color:"green",variant:"light",children:"Auto Response"});return r?(0,t.jsx)(n.Center,{h:400,children:(0,t.jsx)(a.Loader,{size:"lg"})}):(0,t.jsxs)(l.Stack,{children:[(0,t.jsxs)(d.Group,{justify:"space-between",children:[(0,t.jsx)(c.Title,{order:1,children:"Messages"}),(0,t.jsx)(h.Button,{leftSection:(0,t.jsx)(v,{size:"1rem"}),onClick:D,loading:r,children:"Refresh"})]}),0===e.length?(0,t.jsx)(p.Alert,{icon:(0,t.jsx)(S.A,{size:"1rem"}),title:"No messages found",color:"blue",children:"No customer messages have been received yet."}):(0,t.jsx)(l.Stack,{children:e.map(e=>(0,t.jsx)(x.Card,{withBorder:!0,children:(0,t.jsxs)(l.Stack,{children:[(0,t.jsxs)(d.Group,{justify:"space-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(m.Text,{fw:500,children:e.sender_name||"User ".concat(e.sender_id)}),(0,t.jsx)(m.Text,{size:"sm",c:"dimmed",children:new Date(e.created_at).toLocaleString()})]}),(0,t.jsxs)(d.Group,{children:[G(e.responses),(0,t.jsx)(i.Badge,{variant:"light",color:"gray",children:e.platform})]})]}),(0,t.jsx)(m.Text,{children:e.message_text}),e.responses.length>0&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.Divider,{}),(0,t.jsx)(m.Text,{size:"sm",fw:500,c:"dimmed",children:"Responses:"}),(0,t.jsx)(g.ScrollArea.Autosize,{mah:200,children:(0,t.jsx)(l.Stack,{gap:"xs",children:e.responses.map(e=>(0,t.jsxs)(x.Card,{withBorder:!0,bg:"gray.0",children:[(0,t.jsxs)(d.Group,{justify:"space-between",mb:"xs",children:[(0,t.jsx)(i.Badge,{size:"sm",color:"manual"===e.response_type?"blue":"green",children:"manual"===e.response_type?"Manual":"Auto"}),(0,t.jsx)(m.Text,{size:"xs",c:"dimmed",children:new Date(e.created_at).toLocaleString()})]}),(0,t.jsx)(m.Text,{size:"sm",children:e.response_text}),e.created_by&&(0,t.jsxs)(m.Text,{size:"xs",c:"dimmed",mt:"xs",children:["By: ",e.created_by]})]},e.id))})})]}),(0,t.jsx)(d.Group,{justify:"flex-end",children:(0,t.jsx)(h.Button,{size:"sm",leftSection:(0,t.jsx)(b,{size:"1rem"}),onClick:()=>F(e),children:"Send Manual Response"})})]})},e.id))}),(0,t.jsx)(j.Modal,{opened:T,onClose:A,title:"Send Manual Response",size:"md",children:z&&(0,t.jsxs)(l.Stack,{children:[(0,t.jsxs)(x.Card,{withBorder:!0,bg:"gray.0",children:[(0,t.jsx)(m.Text,{size:"sm",fw:500,mb:"xs",children:"Original Message:"}),(0,t.jsx)(m.Text,{size:"sm",children:z.message_text}),(0,t.jsxs)(m.Text,{size:"xs",c:"dimmed",mt:"xs",children:["From: ",z.sender_name||z.sender_id]})]}),(0,t.jsx)("form",{onSubmit:N.onSubmit(P),children:(0,t.jsxs)(l.Stack,{children:[(0,t.jsx)(y.Textarea,{label:"Your Response",placeholder:"Type your response here...",required:!0,rows:4,...N.getInputProps("response_text")}),(0,t.jsxs)(d.Group,{justify:"flex-end",children:[(0,t.jsx)(h.Button,{variant:"subtle",onClick:A,children:"Cancel"}),(0,t.jsx)(h.Button,{type:"submit",leftSection:(0,t.jsx)(_,{size:"1rem"}),loading:R,children:"Send Response"})]})]})})]})})]})}},53776:(e,s,r)=>{"use strict";r.d(s,{Divider:()=>m});var t=r(95155);r(12115);var o=r(56204),i=r(68918),n=r(71180),a=r(43664),l=r(53791),d=r(69604),c=r(36960),h={root:"m_3eebeb36",label:"m_9e365f20"};let p={orientation:"horizontal"},x=(0,i.createVarsResolver)((e,s)=>{let{color:r,variant:t,size:i}=s;return{root:{"--divider-color":r?(0,n.getThemeColor)(r,e):void 0,"--divider-border-style":t,"--divider-size":(0,o.getSize)(i,"divider-size")}}}),m=(0,c.factory)((e,s)=>{let r=(0,a.useProps)("Divider",p,e),{classNames:o,className:i,style:n,styles:c,unstyled:m,vars:u,color:g,orientation:j,label:y,labelPosition:f,mod:v,...S}=r,b=(0,l.useStyles)({name:"Divider",classes:h,props:r,className:i,style:n,classNames:o,styles:c,unstyled:m,vars:u,varsResolver:x});return(0,t.jsx)(d.Box,{ref:s,mod:[{orientation:j,"with-label":!!y},v],...b("root"),...S,role:"separator",children:y&&(0,t.jsx)(d.Box,{component:"span",mod:{position:f},...b("label"),children:y})})});m.classes=h,m.displayName="@mantine/core/Divider"},75893:(e,s,r)=>{Promise.resolve().then(r.bind(r,51675))}},e=>{var s=s=>e(e.s=s);e.O(0,[545,865,903,913,299,621,191,441,684,358],()=>s(75893)),_N_E=e.O()}]);