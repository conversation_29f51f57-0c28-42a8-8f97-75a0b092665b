(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{8141:(e,t,s)=>{"use strict";s.d(t,{Center:()=>c});var r=s(95155);s(12115);var a=s(43664),o=s(53791),l=s(69604),i=s(64511),n={root:"m_4451eb3a"};let d={},c=(0,i.polymorphicFactory)((e,t)=>{let s=(0,a.useProps)("Center",d,e),{classNames:i,className:c,style:h,styles:u,unstyled:g,vars:p,inline:m,mod:v,...x}=s,f=(0,o.useStyles)({name:"Center",props:s,classes:n,className:c,style:h,classNames:i,styles:u,unstyled:g,vars:p});return(0,r.jsx)(l.Box,{ref:t,mod:[{inline:m},v],...f("root"),...x})});c.classes=n,c.displayName="@mantine/core/Center"},15011:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","package","IconPackage",[["path",{d:"M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5",key:"svg-0"}],["path",{d:"M12 12l8 -4.5",key:"svg-1"}],["path",{d:"M12 12l0 9",key:"svg-2"}],["path",{d:"M12 12l-8 -4.5",key:"svg-3"}],["path",{d:"M16 5.25l-8 4.5",key:"svg-4"}]])},20579:(e,t,s)=>{Promise.resolve().then(s.bind(s,71812))},21220:(e,t,s)=>{"use strict";s.d(t,{Stack:()=>g});var r=s(95155);s(12115);var a=s(56204),o=s(68918),l=s(43664),i=s(53791),n=s(69604),d=s(36960),c={root:"m_6d731127"};let h={gap:"md",align:"stretch",justify:"flex-start"},u=(0,o.createVarsResolver)((e,t)=>{let{gap:s,align:r,justify:o}=t;return{root:{"--stack-gap":(0,a.getSpacing)(s),"--stack-align":r,"--stack-justify":o}}}),g=(0,d.factory)((e,t)=>{let s=(0,l.useProps)("Stack",h,e),{classNames:a,className:o,style:d,styles:g,unstyled:p,vars:m,align:v,justify:x,gap:f,variant:j,...w}=s,y=(0,i.useStyles)({name:"Stack",props:s,classes:c,className:o,style:d,classNames:a,styles:g,unstyled:p,vars:m,varsResolver:u});return(0,r.jsx)(n.Box,{ref:t,...y("root"),variant:j,...w})});g.classes=c,g.displayName="@mantine/core/Stack"},26029:(e,t,s)=>{"use strict";s.d(t,{Badge:()=>p});var r=s(95155);s(12115);var a=s(56204),o=s(68918),l=s(71180),i=s(43664),n=s(53791),d=s(69604),c=s(64511),h={root:"m_347db0ec","root--dot":"m_fbd81e3d",label:"m_5add502a",section:"m_91fdda9b"};let u={},g=(0,o.createVarsResolver)((e,t)=>{let{radius:s,color:r,gradient:o,variant:i,size:n,autoContrast:d}=t,c=e.variantColorResolver({color:r||e.primaryColor,theme:e,gradient:o,variant:i||"filled",autoContrast:d});return{root:{"--badge-height":(0,a.getSize)(n,"badge-height"),"--badge-padding-x":(0,a.getSize)(n,"badge-padding-x"),"--badge-fz":(0,a.getSize)(n,"badge-fz"),"--badge-radius":void 0===s?void 0:(0,a.getRadius)(s),"--badge-bg":r||i?c.background:void 0,"--badge-color":r||i?c.color:void 0,"--badge-bd":r||i?c.border:void 0,"--badge-dot-color":"dot"===i?(0,l.getThemeColor)(r,e):void 0}}}),p=(0,c.polymorphicFactory)((e,t)=>{let s=(0,i.useProps)("Badge",u,e),{classNames:a,className:o,style:l,styles:c,unstyled:p,vars:m,radius:v,color:x,gradient:f,leftSection:j,rightSection:w,children:y,variant:b,fullWidth:k,autoContrast:S,circle:C,mod:z,...T}=s,_=(0,n.useStyles)({name:"Badge",props:s,classes:h,className:o,style:l,classNames:a,styles:c,unstyled:p,vars:m,varsResolver:g});return(0,r.jsxs)(d.Box,{variant:b,mod:[{block:k,circle:C,"with-right-section":!!w,"with-left-section":!!j},z],..._("root",{variant:b}),ref:t,...T,children:[j&&(0,r.jsx)("span",{..._("section"),"data-position":"left",children:j}),(0,r.jsx)("span",{..._("label"),children:y}),w&&(0,r.jsx)("span",{..._("section"),"data-position":"right",children:w})]})});p.classes=h,p.displayName="@mantine/core/Badge"},26743:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","messages","IconMessages",[["path",{d:"M21 14l-3 -3h-7a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1h9a1 1 0 0 1 1 1v10",key:"svg-0"}],["path",{d:"M14 15v2a1 1 0 0 1 -1 1h-7l-3 3v-10a1 1 0 0 1 1 -1h2",key:"svg-1"}]])},56970:(e,t,s)=>{"use strict";s.d(t,{createSafeContext:()=>o});var r=s(95155),a=s(12115);function o(e){let t=(0,a.createContext)(null);return[e=>{let{children:s,value:a}=e;return(0,r.jsx)(t.Provider,{value:a,children:s})},()=>{let s=(0,a.useContext)(t);if(null===s)throw Error(e);return s}]}},70112:(e,t,s)=>{"use strict";s.d(t,{Group:()=>p});var r=s(95155),a=s(12115),o=s(56204),l=s(68918),i=s(43664),n=s(53791),d=s(69604),c=s(36960),h={root:"m_4081bf90"};let u={preventGrowOverflow:!0,gap:"md",align:"center",justify:"flex-start",wrap:"wrap"},g=(0,l.createVarsResolver)((e,t,s)=>{let{grow:r,preventGrowOverflow:a,gap:l,align:i,justify:n,wrap:d}=t,{childWidth:c}=s;return{root:{"--group-child-width":r&&a?c:void 0,"--group-gap":(0,o.getSpacing)(l),"--group-align":i,"--group-justify":n,"--group-wrap":d}}}),p=(0,c.factory)((e,t)=>{let s=(0,i.useProps)("Group",u,e),{classNames:l,className:c,style:p,styles:m,unstyled:v,children:x,gap:f,align:j,justify:w,wrap:y,grow:b,preventGrowOverflow:k,vars:S,variant:C,__size:z,mod:T,..._}=s,B=a.Children.toArray(x).filter(Boolean),M=B.length,P=(0,o.getSpacing)(null!=f?f:"md"),R="calc(".concat(100/M,"% - (").concat(P," - ").concat(P," / ").concat(M,"))"),G=(0,n.useStyles)({name:"Group",props:s,stylesCtx:{childWidth:R},className:c,style:p,classes:h,classNames:l,styles:m,unstyled:v,vars:S,varsResolver:g});return(0,r.jsx)(d.Box,{...G("root"),ref:t,variant:C,mod:[{grow:b},T],size:z,..._,children:B})});p.classes=h,p.displayName="@mantine/core/Group"},71812:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(95155),a=s(12115),o=s(8141),l=s(83347),i=s(21220),n=s(93751),d=s(89047),c=s(18403),h=s(70112),u=s(58887),g=s(26029),p=s(15011),m=s(26743),v=(0,s(86467).A)("outline","message-circle","IconMessageCircle",[["path",{d:"M3 20l1.3 -3.9c-2.324 -3.437 -1.426 -7.872 2.1 -10.374c3.526 -2.501 8.59 -2.296 11.845 .48c3.255 2.777 3.695 7.266 1.029 10.501c-2.666 3.235 -7.615 4.215 -11.574 2.293l-4.7 1",key:"svg-0"}]]);function x(){let[e,t]=(0,a.useState)(null),[s,x]=(0,a.useState)(!0);(0,a.useEffect)(()=>{f()},[]);let f=async()=>{try{var e,s,r,a;let o=await fetch("/api/products?limit=1"),l=await o.json(),i=await fetch("/api/messages?limit=10"),n=await i.json();t({totalProducts:(null==(e=l.products)?void 0:e.length)||0,totalMessages:(null==(s=n.messages)?void 0:s.length)||0,totalResponses:(null==(r=n.messages)?void 0:r.reduce((e,t)=>{var s;return e+((null==(s=t.responses)?void 0:s.length)||0)},0))||0,recentMessages:(null==(a=n.messages)?void 0:a.slice(0,5))||[]})}catch(e){console.error("Error loading dashboard stats:",e)}finally{x(!1)}};return s?(0,r.jsx)(o.Center,{h:400,children:(0,r.jsx)(l.Loader,{size:"lg"})}):(0,r.jsxs)(i.Stack,{children:[(0,r.jsx)(n.Title,{order:1,children:"Dashboard Overview"}),(0,r.jsxs)(d.Grid,{children:[(0,r.jsx)(d.Grid.Col,{span:{base:12,md:4},children:(0,r.jsx)(c.Card,{withBorder:!0,children:(0,r.jsxs)(h.Group,{justify:"space-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(u.Text,{c:"dimmed",size:"sm",tt:"uppercase",fw:700,children:"Total Products"}),(0,r.jsx)(u.Text,{fw:700,size:"xl",children:(null==e?void 0:e.totalProducts)||0})]}),(0,r.jsx)(p.A,{size:"2rem",color:"blue"})]})})}),(0,r.jsx)(d.Grid.Col,{span:{base:12,md:4},children:(0,r.jsx)(c.Card,{withBorder:!0,children:(0,r.jsxs)(h.Group,{justify:"space-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(u.Text,{c:"dimmed",size:"sm",tt:"uppercase",fw:700,children:"Total Messages"}),(0,r.jsx)(u.Text,{fw:700,size:"xl",children:(null==e?void 0:e.totalMessages)||0})]}),(0,r.jsx)(m.A,{size:"2rem",color:"green"})]})})}),(0,r.jsx)(d.Grid.Col,{span:{base:12,md:4},children:(0,r.jsx)(c.Card,{withBorder:!0,children:(0,r.jsxs)(h.Group,{justify:"space-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(u.Text,{c:"dimmed",size:"sm",tt:"uppercase",fw:700,children:"Total Responses"}),(0,r.jsx)(u.Text,{fw:700,size:"xl",children:(null==e?void 0:e.totalResponses)||0})]}),(0,r.jsx)(v,{size:"2rem",color:"orange"})]})})})]}),(0,r.jsxs)(c.Card,{withBorder:!0,children:[(0,r.jsx)(n.Title,{order:3,mb:"md",children:"Recent Messages"}),(null==e?void 0:e.recentMessages.length)===0?(0,r.jsx)(u.Text,{c:"dimmed",children:"No messages yet"}):(0,r.jsx)(i.Stack,{children:null==e?void 0:e.recentMessages.map(e=>{var t,s;return(0,r.jsxs)(h.Group,{justify:"space-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(u.Text,{fw:500,children:e.sender_name||e.sender_id}),(0,r.jsx)(u.Text,{size:"sm",c:"dimmed",truncate:!0,children:e.message_text})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g.Badge,{color:(null==(t=e.responses)?void 0:t.length)>0?"green":"yellow",variant:"light",children:(null==(s=e.responses)?void 0:s.length)>0?"Responded":"Pending"}),(0,r.jsx)(u.Text,{size:"xs",c:"dimmed",children:new Date(e.created_at).toLocaleDateString()})]})]},e.id)})})]})]})}},86467:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(12115),a={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let o=(e,t,s,o)=>{let l=(0,r.forwardRef)((s,l)=>{let{color:i="currentColor",size:n=24,stroke:d=2,title:c,className:h,children:u,...g}=s;return(0,r.createElement)("svg",{ref:l,...a[e],width:n,height:n,className:["tabler-icon","tabler-icon-".concat(t),h].join(" "),..."filled"===e?{fill:i}:{strokeWidth:d,stroke:i},...g},[c&&(0,r.createElement)("title",{key:"svg-title"},c),...o.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(u)?u:[u]])});return l.displayName="".concat(s),l}},93751:(e,t,s)=>{"use strict";s.d(t,{Title:()=>m});var r=s(95155);s(12115);var a=s(68918),o=s(43664),l=s(53791),i=s(69604),n=s(36960),d=s(5903);let c=["h1","h2","h3","h4","h5","h6"],h=["xs","sm","md","lg","xl"];var u={root:"m_8a5d1357"};let g={order:1},p=(0,a.createVarsResolver)((e,t)=>{let{order:s,size:r,lineClamp:a,textWrap:o}=t,l=function(e,t){let s=void 0!==t?t:"h".concat(e);return c.includes(s)?{fontSize:"var(--mantine-".concat(s,"-font-size)"),fontWeight:"var(--mantine-".concat(s,"-font-weight)"),lineHeight:"var(--mantine-".concat(s,"-line-height)")}:h.includes(s)?{fontSize:"var(--mantine-font-size-".concat(s,")"),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}:{fontSize:(0,d.D)(s),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}}(s||1,r);return{root:{"--title-fw":l.fontWeight,"--title-lh":l.lineHeight,"--title-fz":l.fontSize,"--title-line-clamp":"number"==typeof a?a.toString():void 0,"--title-text-wrap":o}}}),m=(0,n.factory)((e,t)=>{let s=(0,o.useProps)("Title",g,e),{classNames:a,className:n,style:d,styles:c,unstyled:h,order:m,vars:v,size:x,variant:f,lineClamp:j,textWrap:w,mod:y,...b}=s,k=(0,l.useStyles)({name:"Title",props:s,classes:u,className:n,style:d,classNames:a,styles:c,unstyled:h,vars:v,varsResolver:p});return[1,2,3,4,5,6].includes(m)?(0,r.jsx)(i.Box,{...k("root"),component:"h".concat(m),variant:f,ref:t,mod:[{order:m,"data-line-clamp":"number"==typeof j},y],size:x,...b}):null});m.classes=u,m.displayName="@mantine/core/Title"},97287:(e,t,s)=>{"use strict";s.d(t,{Paper:()=>g});var r=s(95155);s(12115);var a=s(56204),o=s(68918),l=s(43664),i=s(53791),n=s(69604),d=s(64511),c={root:"m_1b7284a3"};let h={},u=(0,o.createVarsResolver)((e,t)=>{let{radius:s,shadow:r}=t;return{root:{"--paper-radius":void 0===s?void 0:(0,a.getRadius)(s),"--paper-shadow":(0,a.getShadow)(r)}}}),g=(0,d.polymorphicFactory)((e,t)=>{let s=(0,l.useProps)("Paper",h,e),{classNames:a,className:o,style:d,styles:g,unstyled:p,withBorder:m,vars:v,radius:x,shadow:f,variant:j,mod:w,...y}=s,b=(0,i.useStyles)({name:"Paper",props:s,classes:c,className:o,style:d,classNames:a,styles:g,unstyled:p,vars:v,varsResolver:u});return(0,r.jsx)(n.Box,{ref:t,mod:[{"data-with-border":m},w],...b("root"),variant:j,...y})});g.classes=c,g.displayName="@mantine/core/Paper"}},e=>{var t=t=>e(e.s=t);e.O(0,[865,849,441,684,358],()=>t(20579)),_N_E=e.O()}]);