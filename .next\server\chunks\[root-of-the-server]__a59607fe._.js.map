{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/types/database';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);\n\n// Server-side client with service role key for admin operations\nexport const supabaseAdmin = createClient<Database>(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAGrD,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EACtC,aACA,QAAQ,GAAG,CAAC,yBAAyB", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { supabase } from './supabase';\nimport { AdminUser } from '@/types/database';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';\nconst JWT_EXPIRES_IN = '7d';\n\nexport interface AuthUser {\n  id: string;\n  username: string;\n  email: string | null;\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\n// Hash password for storage\nexport async function hashPassword(password: string): Promise<string> {\n  const saltRounds = 12;\n  return bcrypt.hash(password, saltRounds);\n}\n\n// Verify password against hash\nexport async function verifyPassword(password: string, hash: string): Promise<boolean> {\n  return bcrypt.compare(password, hash);\n}\n\n// Generate JWT token\nexport function generateToken(user: AuthUser): string {\n  return jwt.sign(\n    {\n      id: user.id,\n      username: user.username,\n      email: user.email,\n    },\n    JWT_SECRET,\n    { expiresIn: JWT_EXPIRES_IN }\n  );\n}\n\n// Verify JWT token\nexport function verifyToken(token: string): AuthUser | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as AuthUser;\n    return decoded;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Authenticate user with username and password\nexport async function authenticateUser(credentials: LoginCredentials): Promise<{\n  success: boolean;\n  user?: AuthUser;\n  token?: string;\n  error?: string;\n}> {\n  try {\n    const { data: user, error } = await supabase\n      .from('admin_users')\n      .select('*')\n      .eq('username', credentials.username)\n      .eq('is_active', true)\n      .single();\n\n    if (error || !user) {\n      return {\n        success: false,\n        error: 'Invalid username or password',\n      };\n    }\n\n    const isValidPassword = await verifyPassword(credentials.password, user.password_hash);\n\n    if (!isValidPassword) {\n      return {\n        success: false,\n        error: 'Invalid username or password',\n      };\n    }\n\n    // Update last login\n    await supabase\n      .from('admin_users')\n      .update({ last_login: new Date().toISOString() })\n      .eq('id', user.id);\n\n    const authUser: AuthUser = {\n      id: user.id,\n      username: user.username,\n      email: user.email,\n    };\n\n    const token = generateToken(authUser);\n\n    return {\n      success: true,\n      user: authUser,\n      token,\n    };\n  } catch (error) {\n    console.error('Authentication error:', error);\n    return {\n      success: false,\n      error: 'Authentication failed',\n    };\n  }\n}\n\n// Get user from token\nexport async function getUserFromToken(token: string): Promise<AuthUser | null> {\n  const decoded = verifyToken(token);\n  if (!decoded) return null;\n\n  try {\n    const { data: user, error } = await supabase\n      .from('admin_users')\n      .select('id, username, email')\n      .eq('id', decoded.id)\n      .eq('is_active', true)\n      .single();\n\n    if (error || !user) return null;\n\n    return {\n      id: user.id,\n      username: user.username,\n      email: user.email,\n    };\n  } catch (error) {\n    console.error('Get user from token error:', error);\n    return null;\n  }\n}\n\n// Create new admin user (for setup/registration)\nexport async function createAdminUser(\n  username: string,\n  password: string,\n  email?: string\n): Promise<{ success: boolean; error?: string }> {\n  try {\n    const passwordHash = await hashPassword(password);\n\n    const { error } = await supabase\n      .from('admin_users')\n      .insert({\n        username,\n        password_hash: passwordHash,\n        email,\n      });\n\n    if (error) {\n      if (error.code === '23505') { // Unique constraint violation\n        return {\n          success: false,\n          error: 'Username already exists',\n        };\n      }\n      return {\n        success: false,\n        error: 'Failed to create user',\n      };\n    }\n\n    return { success: true };\n  } catch (error) {\n    console.error('Create admin user error:', error);\n    return {\n      success: false,\n      error: 'Failed to create user',\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB;AAchB,eAAe,aAAa,QAAgB;IACjD,MAAM,aAAa;IACnB,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,IAAY;IACjE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,IAAc;IAC1C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CACb;QACE,IAAI,KAAK,EAAE;QACX,UAAU,KAAK,QAAQ;QACvB,OAAO,KAAK,KAAK;IACnB,GACA,YACA;QAAE,WAAW;IAAe;AAEhC;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,iBAAiB,WAA6B;IAMlE,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACzC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,YAAY,QAAQ,EACnC,EAAE,CAAC,aAAa,MAChB,MAAM;QAET,IAAI,SAAS,CAAC,MAAM;YAClB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,kBAAkB,MAAM,eAAe,YAAY,QAAQ,EAAE,KAAK,aAAa;QAErF,IAAI,CAAC,iBAAiB;YACpB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,oBAAoB;QACpB,MAAM,wHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,eACL,MAAM,CAAC;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC9C,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,MAAM,WAAqB;YACzB,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;QACnB;QAEA,MAAM,QAAQ,cAAc;QAE5B,OAAO;YACL,SAAS;YACT,MAAM;YACN;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAGO,eAAe,iBAAiB,KAAa;IAClD,MAAM,UAAU,YAAY;IAC5B,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACzC,IAAI,CAAC,eACL,MAAM,CAAC,uBACP,EAAE,CAAC,MAAM,QAAQ,EAAE,EACnB,EAAE,CAAC,aAAa,MAChB,MAAM;QAET,IAAI,SAAS,CAAC,MAAM,OAAO;QAE3B,OAAO;YACL,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,eAAe,gBACpB,QAAgB,EAChB,QAAgB,EAChB,KAAc;IAEd,IAAI;QACF,MAAM,eAAe,MAAM,aAAa;QAExC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,eACL,MAAM,CAAC;YACN;YACA,eAAe;YACf;QACF;QAEF,IAAI,OAAO;YACT,IAAI,MAAM,IAAI,KAAK,SAAS;gBAC1B,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YACA,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { authenticateUser } from '@/lib/auth';\nimport { cookies } from 'next/headers';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { username, password } = body;\n\n    if (!username || !password) {\n      return NextResponse.json(\n        { error: 'Username and password are required' },\n        { status: 400 }\n      );\n    }\n\n    const result = await authenticateUser({ username, password });\n\n    if (!result.success) {\n      return NextResponse.json(\n        { error: result.error },\n        { status: 401 }\n      );\n    }\n\n    // Set HTTP-only cookie with the token\n    const cookieStore = await cookies();\n    cookieStore.set('auth-token', result.token!, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'lax',\n      maxAge: 60 * 60 * 24 * 7, // 7 days\n      path: '/',\n    });\n\n    return NextResponse.json({\n      success: true,\n      user: result.user,\n    });\n  } catch (error) {\n    console.error('Login API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;QAE/B,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE;YAAE;YAAU;QAAS;QAE3D,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,OAAO,KAAK;YAAC,GACtB;gBAAE,QAAQ;YAAI;QAElB;QAEA,sCAAsC;QACtC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,YAAY,GAAG,CAAC,cAAc,OAAO,KAAK,EAAG;YAC3C,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,KAAK,KAAK,KAAK;YACvB,MAAM;QACR;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM,OAAO,IAAI;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}