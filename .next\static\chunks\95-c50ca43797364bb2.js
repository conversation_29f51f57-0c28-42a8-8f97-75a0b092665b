"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[95],{1218:(e,t,r)=>{r.d(t,{$:()=>o,u:()=>a}),r(12115);var n=r(56970);r(95155);let[a,o]=(0,n.createSafeContext)("Table component was not found in the tree")},8593:(e,t,r)=>{r.d(t,{TextInput:()=>u});var n=r(95155);r(12115);var a=r(43664),o=r(36960),l=r(24225);let i={},u=(0,o.factory)((e,t)=>{let r=(0,a.useProps)("TextInput",i,e);return(0,n.jsx)(l.InputBase,{component:"input",ref:t,...r,__staticSelector:"TextInput"})});u.classes=l.InputBase.classes,u.displayName="@mantine/core/TextInput"},9680:(e,t,r)=>{r.d(t,{A:()=>n});var n={root:"m_8d3f4000",icon:"m_8d3afb97",loader:"m_302b9fb1",group:"m_1a0f1b21",groupSection:"m_437b6484"}},30934:(e,t,r)=>{r.d(t,{Table:()=>S});var n=r(95155),a=r(5903);r(12115);var o=r(56204),l=r(68918),i=r(71180),u=r(43664),c=r(53791),s=r(69604),d=r(36960),f=r(41767),v=r(1218);function m(e){let{data:t}=e;return(0,n.jsxs)(n.Fragment,{children:[t.caption&&(0,n.jsx)(f.TableCaption,{children:t.caption}),t.head&&(0,n.jsx)(f.TableThead,{children:(0,n.jsx)(f.TableTr,{children:t.head.map((e,t)=>(0,n.jsx)(f.TableTh,{children:e},t))})}),t.body&&(0,n.jsx)(f.TableTbody,{children:t.body.map((e,t)=>(0,n.jsx)(f.TableTr,{children:e.map((e,t)=>(0,n.jsx)(f.TableTd,{children:e},t))},t))}),t.foot&&(0,n.jsx)(f.TableTfoot,{children:(0,n.jsx)(f.TableTr,{children:t.foot.map((e,t)=>(0,n.jsx)(f.TableTh,{children:e},t))})})]})}m.displayName="@mantine/core/TableDataRenderer";var p=r(50421),g=r(35496);let h={withRowBorders:!0,verticalSpacing:7},b=(0,l.createVarsResolver)((e,t)=>{let{layout:r,captionSide:n,horizontalSpacing:l,verticalSpacing:u,borderColor:c,stripedColor:s,highlightOnHoverColor:d,striped:f,highlightOnHover:v,stickyHeaderOffset:m,stickyHeader:p}=t;return{table:{"--table-layout":r,"--table-caption-side":n,"--table-horizontal-spacing":(0,o.getSpacing)(l),"--table-vertical-spacing":(0,o.getSpacing)(u),"--table-border-color":c?(0,i.getThemeColor)(c,e):void 0,"--table-striped-color":f&&s?(0,i.getThemeColor)(s,e):void 0,"--table-highlight-on-hover-color":v&&d?(0,i.getThemeColor)(d,e):void 0,"--table-sticky-header-offset":p?(0,a.D)(m):void 0}}}),S=(0,d.factory)((e,t)=>{let r=(0,u.useProps)("Table",h,e),{classNames:a,className:o,style:l,styles:i,unstyled:d,vars:f,horizontalSpacing:p,verticalSpacing:S,captionSide:y,stripedColor:x,highlightOnHoverColor:T,striped:w,highlightOnHover:N,withColumnBorders:I,withRowBorders:A,withTableBorder:V,borderColor:C,layout:E,variant:j,data:D,children:M,stickyHeader:R,stickyHeaderOffset:B,mod:O,tabularNums:_,...F}=r,k=(0,c.useStyles)({name:"Table",props:r,className:o,style:l,classes:g.A,classNames:a,styles:i,unstyled:d,rootSelector:"table",vars:f,varsResolver:b});return(0,n.jsx)(v.u,{value:{getStyles:k,stickyHeader:R,striped:!0===w?"odd":w||void 0,highlightOnHover:N,withColumnBorders:I,withRowBorders:A,captionSide:y||"bottom"},children:(0,n.jsx)(s.Box,{component:"table",variant:j,ref:t,mod:[{"data-with-table-border":V,"data-tabular-nums":_},O],...k("table"),...F,children:M||!!D&&(0,n.jsx)(m,{data:D})})})});S.classes=g.A,S.displayName="@mantine/core/Table",S.Td=f.TableTd,S.Th=f.TableTh,S.Tr=f.TableTr,S.Thead=f.TableThead,S.Tbody=f.TableTbody,S.Tfoot=f.TableTfoot,S.Caption=f.TableCaption,S.ScrollContainer=p.TableScrollContainer,S.DataRenderer=m},35496:(e,t,r)=>{r.d(t,{A:()=>n});var n={table:"m_b23fa0ef",th:"m_4e7aa4f3",tr:"m_4e7aa4fd",td:"m_4e7aa4ef",tbody:"m_b2404537",thead:"m_b242d975",caption:"m_9e5a3ac7",scrollContainer:"m_a100c15",scrollContainerInner:"m_62259741"}},41767:(e,t,r)=>{r.d(t,{TableCaption:()=>g,TableTbody:()=>m,TableTd:()=>d,TableTfoot:()=>p,TableTh:()=>s,TableThead:()=>v,TableTr:()=>f});var n=r(95155);r(12115);var a=r(43664),o=r(69604),l=r(36960),i=r(1218),u=r(35496);function c(e,t){let r="Table".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),c=(0,l.factory)((l,u)=>{let c=(0,a.useProps)(r,{},l),{classNames:s,className:d,style:f,styles:v,...m}=c,p=(0,i.$)();return(0,n.jsx)(o.Box,{component:e,ref:u,...function(e,t){if(!t)return;let r={};return t.columnBorder&&e.withColumnBorders&&(r["data-with-column-border"]=!0),t.rowBorder&&e.withRowBorders&&(r["data-with-row-border"]=!0),t.striped&&e.striped&&(r["data-striped"]=e.striped),t.highlightOnHover&&e.highlightOnHover&&(r["data-hover"]=!0),t.captionSide&&e.captionSide&&(r["data-side"]=e.captionSide),t.stickyHeader&&e.stickyHeader&&(r["data-sticky"]=!0),r}(p,t),...p.getStyles(e,{className:d,classNames:s,style:f,styles:v,props:c}),...m})});return c.displayName="@mantine/core/".concat(r),c.classes=u.A,c}let s=c("th",{columnBorder:!0}),d=c("td",{columnBorder:!0}),f=c("tr",{rowBorder:!0,striped:!0,highlightOnHover:!0}),v=c("thead",{stickyHeader:!0}),m=c("tbody"),p=c("tfoot"),g=c("caption",{captionSide:!0})},44644:(e,t,r)=>{r.d(t,{NumberInput:()=>C});var n=r(95155),a=r(12115),o=r(52596),l=r(91555),i=r(96963),u=r(57613),c=r(88551),s=r(83204),d=r(56204),f=r(68918),v=r(86028),m=r(53791),p=r(43664),g=r(36960),h=r(24225),b=r(43608);function S(e){let{direction:t,style:r,...a}=e;return(0,n.jsx)("svg",{style:{width:"var(--ni-chevron-size)",height:"var(--ni-chevron-size)",transform:"up"===t?"rotate(180deg)":void 0,...r},viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...a,children:(0,n.jsx)("path",{d:"M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})}var y={root:"m_e2f5cd4e",controls:"m_95e17d22",control:"m_80b4b171"};let x=/^(0\.0*|-0(\.0*)?)$/,T=/^-?0\d+(\.\d+)?\.?$/;function w(e){return"string"==typeof e&&""!==e&&!Number.isNaN(Number(e))}function N(e){return"number"==typeof e?e<Number.MAX_SAFE_INTEGER:""===e||w(e)&&Number(e)<Number.MAX_SAFE_INTEGER}function I(e,t,r){return void 0===e||(void 0===t||e>=t)&&(void 0===r||e<=r)}let A={step:1,clampBehavior:"blur",allowDecimal:!0,allowNegative:!0,withKeyboardEvents:!0,allowLeadingZeros:!0,trimLeadingZeroesOnBlur:!0,startValue:0},V=(0,f.createVarsResolver)((e,t)=>{let{size:r}=t;return{controls:{"--ni-chevron-size":(0,d.getSize)(r,"ni-chevron-size")}}}),C=(0,g.factory)((e,t)=>{let r=(0,p.useProps)("NumberInput",A,e),{className:d,classNames:f,styles:g,unstyled:C,vars:E,onChange:j,onValueChange:D,value:M,defaultValue:R,max:B,min:O,step:_,hideControls:F,rightSection:k,isAllowed:G,clampBehavior:P,onBlur:z,allowDecimal:L,decimalScale:U,onKeyDown:K,onKeyDownCapture:W,handlersRef:H,startValue:$,disabled:J,rightSectionPointerEvents:Z,allowNegative:X,readOnly:q,size:Q,rightSectionWidth:Y,stepHoldInterval:ee,stepHoldDelay:et,allowLeadingZeros:er,withKeyboardEvents:en,trimLeadingZeroesOnBlur:ea,...eo}=r,el=(0,m.useStyles)({name:"NumberInput",classes:y,props:r,classNames:f,styles:g,unstyled:C,vars:E,varsResolver:V}),{resolvedClassNames:ei,resolvedStyles:eu}=(0,v.useResolvedStylesApi)({classNames:f,styles:g,props:r}),[ec,es]=(0,u.useUncontrolled)({value:M,defaultValue:R,finalValue:"",onChange:j}),ed=void 0!==et&&void 0!==ee,ef=(0,a.useRef)(null),ev=(0,a.useRef)(null),em=(0,a.useRef)(0),ep=e=>{let t=String(e).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0},eg=e=>{ef.current&&void 0!==e&&ef.current.setSelectionRange(e,e)},eh=(0,a.useRef)(s.noop);eh.current=()=>{let e;if(!N(ec))return;let t=Math.max(ep(ec),ep(_)),r=10**t;if(!w(ec)&&("number"!=typeof ec||Number.isNaN(ec)))e=(0,i.clamp)($,O,B);else if(void 0!==B){let t=(Math.round(Number(ec)*r)+Math.round(_*r))/r;e=t<=B?t:B}else e=(Math.round(Number(ec)*r)+Math.round(_*r))/r;let n=e.toFixed(t);es(parseFloat(n)),null==D||D({floatValue:parseFloat(n),formattedValue:n,value:n},{source:"increment"}),setTimeout(()=>{var e;return eg(null==(e=ef.current)?void 0:e.value.length)},0)};let eb=(0,a.useRef)(s.noop);eb.current=()=>{let e;if(!N(ec))return;let t=void 0!==O?O:X?Number.MIN_SAFE_INTEGER:0,r=Math.max(ep(ec),ep(_)),n=10**r;if(!w(ec)&&"number"!=typeof ec||Number.isNaN(ec))e=(0,i.clamp)($,t,B);else{let r=(Math.round(Number(ec)*n)-Math.round(_*n))/n;e=void 0!==t&&r<t?t:r}let a=e.toFixed(r);es(parseFloat(a)),null==D||D({floatValue:parseFloat(a),formattedValue:a,value:a},{source:"decrement"}),setTimeout(()=>{var e;return eg(null==(e=ef.current)?void 0:e.value.length)},0)},(0,c.assignRef)(H,{increment:eh.current,decrement:eb.current});let eS=e=>{var t,r;e?null==(t=eh.current)||t.call(eh):null==(r=eb.current)||r.call(eb),em.current+=1},ey=e=>{if(eS(e),ed){let t="number"==typeof ee?ee:ee(em.current);ev.current=window.setTimeout(()=>ey(e),t)}},ex=(e,t)=>{var r;e.preventDefault(),null==(r=ef.current)||r.focus(),eS(t),ed&&(ev.current=window.setTimeout(()=>ey(t),et))},eT=()=>{ev.current&&window.clearTimeout(ev.current),ev.current=null,em.current=0},ew=(0,n.jsxs)("div",{...el("controls"),children:[(0,n.jsx)(b.UnstyledButton,{...el("control"),tabIndex:-1,"aria-hidden":!0,disabled:J||"number"==typeof ec&&void 0!==B&&ec>=B,mod:{direction:"up"},onMouseDown:e=>e.preventDefault(),onPointerDown:e=>{ex(e,!0)},onPointerUp:eT,onPointerLeave:eT,children:(0,n.jsx)(S,{direction:"up"})}),(0,n.jsx)(b.UnstyledButton,{...el("control"),tabIndex:-1,"aria-hidden":!0,disabled:J||"number"==typeof ec&&void 0!==O&&ec<=O,mod:{direction:"down"},onMouseDown:e=>e.preventDefault(),onPointerDown:e=>{ex(e,!1)},onPointerUp:eT,onPointerLeave:eT,children:(0,n.jsx)(S,{direction:"down"})})]});return(0,n.jsx)(h.InputBase,{component:l.HG,allowNegative:X,className:(0,o.A)(y.root,d),size:Q,...eo,readOnly:q,disabled:J,value:ec,getInputRef:(0,c.useMergedRef)(t,ef),onValueChange:(e,t)=>{"event"===t.source&&es(!function(e,t){return("number"==typeof e?e<Number.MAX_SAFE_INTEGER:!Number.isNaN(Number(e)))&&!Number.isNaN(e)&&14>t.toString().replace(".","").length&&""!==t}(e.floatValue,e.value)||x.test(e.value)||er&&T.test(e.value)?e.value:e.floatValue),null==D||D(e,t)},rightSection:F||q||!N(ec)?k:k||ew,classNames:ei,styles:eu,unstyled:C,__staticSelector:"NumberInput",decimalScale:L?U:0,onKeyDown:e=>{var t,r;null==K||K(e),!q&&en&&("ArrowUp"===e.key&&(e.preventDefault(),null==(t=eh.current)||t.call(eh)),"ArrowDown"===e.key&&(e.preventDefault(),null==(r=eb.current)||r.call(eb)))},onKeyDownCapture:e=>{if(null==W||W(e),"Backspace"===e.key){let t=ef.current;t&&0===t.selectionStart&&t.selectionStart===t.selectionEnd&&(e.preventDefault(),window.setTimeout(()=>eg(0),0))}},rightSectionPointerEvents:null!=Z?Z:J?"none":void 0,rightSectionWidth:null!=Y?Y:"var(--ni-right-section-width-".concat(Q||"sm",")"),allowLeadingZeros:er,onBlur:e=>{let t=ec;"blur"===P&&"number"==typeof t&&(t=(0,i.clamp)(t,O,B)),ea&&"string"==typeof t&&15>ep(t)&&(t=function(e,t,r){let n=e.toString().replace(/^0+/,""),a=parseFloat(n);return Number.isNaN(a)?n:a>Number.MAX_SAFE_INTEGER?void 0!==t?String(t):n:(0,i.clamp)(a,r,t)}(t,B,O)),ec!==t&&es(t),null==z||z(e)},isAllowed:e=>"strict"===P?G?G(e)&&I(e.floatValue,O,B):I(e.floatValue,O,B):!G||G(e)})});C.classes={...h.InputBase.classes,...y},C.displayName="@mantine/core/NumberInput"},50421:(e,t,r)=>{r.d(t,{TableScrollContainer:()=>m});var n=r(95155),a=r(5903);r(12115);var o=r(68918),l=r(43664),i=r(53791),u=r(69604),c=r(36960),s=r(74271),d=r(35496);let f={type:"scrollarea"},v=(0,o.createVarsResolver)((e,t)=>{let{minWidth:r,maxHeight:n,type:o}=t;return{scrollContainer:{"--table-min-width":(0,a.D)(r),"--table-max-height":(0,a.D)(n),"--table-overflow":"native"===o?"auto":void 0}}}),m=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("TableScrollContainer",f,e),{classNames:a,className:o,style:c,styles:m,unstyled:p,vars:g,children:h,minWidth:b,maxHeight:S,type:y,scrollAreaProps:x,...T}=r,w=(0,i.useStyles)({name:"TableScrollContainer",classes:d.A,props:r,className:o,style:c,classNames:a,styles:m,unstyled:p,vars:g,varsResolver:v,rootSelector:"scrollContainer"});return(0,n.jsx)(u.Box,{component:"scrollarea"===y?s.ScrollArea:"div",..."scrollarea"===y?S?{offsetScrollbars:"xy",...x}:{offsetScrollbars:"x",...x}:{},ref:t,...w("scrollContainer"),...T,children:(0,n.jsx)("div",{...w("scrollContainerInner"),children:h})})});m.classes=d.A,m.displayName="@mantine/core/TableScrollContainer"},57613:(e,t,r)=>{r.d(t,{useUncontrolled:()=>a});var n=r(12115);function a(e){let{value:t,defaultValue:r,finalValue:a,onChange:o=()=>{}}=e,[l,i]=(0,n.useState)(void 0!==r?r:a);return void 0!==t?[t,o,!0]:[l,function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];i(e),null==o||o(e,...r)},!1]}},67147:(e,t,r)=>{r.d(t,{ActionIconGroupSection:()=>v});var n=r(95155);r(12115);var a=r(56204),o=r(68918),l=r(43664),i=r(53791),u=r(69604),c=r(36960),s=r(9680);let d={},f=(0,o.createVarsResolver)((e,t)=>{let{radius:r,color:n,gradient:o,variant:l,autoContrast:i,size:u}=t,c=e.variantColorResolver({color:n||e.primaryColor,theme:e,gradient:o,variant:l||"filled",autoContrast:i});return{groupSection:{"--section-height":(0,a.getSize)(u,"section-height"),"--section-padding-x":(0,a.getSize)(u,"section-padding-x"),"--section-fz":(0,a.getFontSize)(u),"--section-radius":void 0===r?void 0:(0,a.getRadius)(r),"--section-bg":n||l?c.background:void 0,"--section-color":c.color,"--section-bd":n||l?c.border:void 0}}}),v=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("ActionIconGroupSection",d,e),{className:a,style:o,classNames:c,styles:v,unstyled:m,vars:p,variant:g,gradient:h,radius:b,autoContrast:S,...y}=(0,l.useProps)("ActionIconGroupSection",d,e),x=(0,i.useStyles)({name:"ActionIconGroupSection",props:r,classes:s.A,className:a,style:o,classNames:c,styles:v,unstyled:m,vars:p,varsResolver:f,rootSelector:"groupSection"});return(0,n.jsx)(u.Box,{...x("groupSection"),ref:t,variant:g,...y})});v.classes=s.A,v.displayName="@mantine/core/ActionIconGroupSection"},79085:(e,t,r)=>{r.d(t,{JsonInput:()=>f});var n=r(95155),a=r(12115),o=r(57613),l=r(43664),i=r(36960),u=r(24225),c=r(41954);function s(e,t){if("string"==typeof e&&0===e.trim().length)return!0;try{return t(e),!0}catch(e){return!1}}let d={serialize:JSON.stringify,deserialize:JSON.parse},f=(0,i.factory)((e,t)=>{let{value:r,defaultValue:i,onChange:u,formatOnBlur:f,validationError:v,serialize:m,deserialize:p,onFocus:g,onBlur:h,readOnly:b,error:S,...y}=(0,l.useProps)("JsonInput",d,e),[x,T]=(0,o.useUncontrolled)({value:r,defaultValue:i,finalValue:"",onChange:u}),[w,N]=(0,a.useState)(s(x,p));return(0,n.jsx)(c.Textarea,{value:x,onChange:e=>T(e.currentTarget.value),onFocus:e=>{null==g||g(e),N(!0)},onBlur:e=>{"function"==typeof h&&h(e);let t=s(e.currentTarget.value,p);f&&!b&&t&&""!==e.currentTarget.value.trim()&&T(m(p(e.currentTarget.value),null,2)),N(t)},ref:t,readOnly:b,...y,autoComplete:"off",__staticSelector:"JsonInput",error:w?S:v||!0,"data-monospace":!0})});f.classes=u.InputBase.classes,f.displayName="@mantine/core/JsonInput"},81001:(e,t,r)=>{r.d(t,{ActionIcon:()=>b});var n=r(95155);r(12115);var a=r(56204),o=r(68918),l=r(43664),i=r(53791),u=r(69604),c=r(64511),s=r(83347),d=r(60384),f=r(43608),v=r(92131),m=r(67147),p=r(9680);let g={},h=(0,o.createVarsResolver)((e,t)=>{let{size:r,radius:n,variant:o,gradient:l,color:i,autoContrast:u}=t,c=e.variantColorResolver({color:i||e.primaryColor,theme:e,gradient:l,variant:o||"filled",autoContrast:u});return{root:{"--ai-size":(0,a.getSize)(r,"ai-size"),"--ai-radius":void 0===n?void 0:(0,a.getRadius)(n),"--ai-bg":i||o?c.background:void 0,"--ai-hover":i||o?c.hover:void 0,"--ai-hover-color":i||o?c.hoverColor:void 0,"--ai-color":c.color,"--ai-bd":i||o?c.border:void 0}}}),b=(0,c.polymorphicFactory)((e,t)=>{let r=(0,l.useProps)("ActionIcon",g,e),{className:a,unstyled:o,variant:c,classNames:v,styles:m,style:b,loading:S,loaderProps:y,size:x,color:T,radius:w,__staticSelector:N,gradient:I,vars:A,children:V,disabled:C,"data-disabled":E,autoContrast:j,mod:D,...M}=r,R=(0,i.useStyles)({name:["ActionIcon",N],props:r,className:a,style:b,classes:p.A,classNames:v,styles:m,unstyled:o,vars:A,varsResolver:h});return(0,n.jsxs)(f.UnstyledButton,{...R("root",{active:!C&&!S&&!E}),...M,unstyled:o,variant:c,size:x,disabled:C||S,ref:t,mod:[{loading:S,disabled:C||E},D],children:[(0,n.jsx)(d.Transition,{mounted:!!S,transition:"slide-down",duration:150,children:e=>(0,n.jsx)(u.Box,{component:"span",...R("loader",{style:e}),"aria-hidden":!0,children:(0,n.jsx)(s.Loader,{color:"var(--ai-color)",size:"calc(var(--ai-size) * 0.55)",...y})})}),(0,n.jsx)(u.Box,{component:"span",mod:{loading:S},...R("icon"),children:V})]})});b.classes=p.A,b.displayName="@mantine/core/ActionIcon",b.Group=v.ActionIconGroup,b.GroupSection=m.ActionIconGroupSection},83204:(e,t,r)=>{r.d(t,{noop:()=>n});let n=()=>{}},87376:(e,t,r)=>{r.d(t,{createUseExternalEvents:()=>a});var n=r(73141);function a(e){return[function(t){let r=Object.keys(t).reduce((r,n)=>(r["".concat(e,":").concat(n)]=e=>t[n](e.detail),r),{});(0,n.useIsomorphicEffect)(()=>(Object.keys(r).forEach(e=>{window.removeEventListener(e,r[e]),window.addEventListener(e,r[e])}),()=>Object.keys(r).forEach(e=>{window.removeEventListener(e,r[e])})),[r])},function(t){return function(){for(var r,n,a=arguments.length,o=Array(a),l=0;l<a;l++)o[l]=arguments[l];return r="".concat(e,":").concat(String(t)),n=o[0],void window.dispatchEvent(new CustomEvent(r,{detail:n}))}}]}},91423:(e,t,r)=>{r.d(t,{F:()=>o,closeAllModals:()=>d,closeModal:()=>s,modals:()=>m,openConfirmModal:()=>u,openContextModal:()=>c,openModal:()=>i,updateContextModal:()=>v,updateModal:()=>f});var n=r(87376),a=r(74275);let[o,l]=(0,n.createUseExternalEvents)("mantine-modals"),i=e=>{let t=e.modalId||(0,a.randomId)();return l("openModal")({...e,modalId:t}),t},u=e=>{let t=e.modalId||(0,a.randomId)();return l("openConfirmModal")({...e,modalId:t}),t},c=e=>{let t=e.modalId||(0,a.randomId)();return l("openContextModal")({...e,modalId:t}),t},s=l("closeModal"),d=l("closeAllModals"),f=e=>l("updateModal")(e),v=e=>l("updateContextModal")(e),m={open:i,close:s,closeAll:d,openConfirmModal:u,openContextModal:c,updateModal:f,updateContextModal:v}},91555:(e,t,r)=>{r.d(t,{HG:()=>E});var n,a=r(12115);function o(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r}function l(){}function i(e){return!!(e||"").match(/\d/)}function u(e){return null==e}function c(e){return u(e)||"number"==typeof e&&isNaN(e)||"number"==typeof e&&!isFinite(e)}function s(e){return e.replace(/[-[\]/{}()*+?.\\^$|]/g,"\\$&")}function d(e,t){void 0===t&&(t=!0);var r="-"===e[0],n=r&&t,a=(e=e.replace("-","")).split(".");return{beforeDecimal:a[0],afterDecimal:a[1]||"",hasNegation:r,addNegation:n}}function f(e,t,r){for(var n="",a=r?"0":"",o=0;o<=t-1;o++)n+=e[o]||a;return n}function v(e,t){return Array(t+1).join(e)}function m(e){var t=e+"",r="-"===t[0]?"-":"";r&&(t=t.substring(1));var n=t.split(/[eE]/g),a=n[0],o=n[1];if(!(o=Number(o)))return r+a;a=a.replace(".","");var l=1+o,i=a.length;return l<0?a="0."+v("0",Math.abs(l))+a:l>=i?a+=v("0",l-i):a=(a.substring(0,l)||"0")+"."+a.substring(l),r+a}function p(e,t,r){if(-1!==["","-"].indexOf(e))return e;var n=(-1!==e.indexOf(".")||r)&&t,a=d(e),o=a.beforeDecimal,l=a.afterDecimal,i=a.hasNegation,u=parseFloat("0."+(l||"0")),c=(l.length<=t?"0."+l:u.toFixed(t)).split("."),s=o;return o&&Number(c[0])&&(s=o.split("").reverse().reduce(function(e,t,r){return e.length>r?(Number(e[0])+Number(t)).toString()+e.substring(1,e.length):t+e},c[0])),(i?"-":"")+s+(n?".":"")+f(c[1]||"",t,r)}function g(e,t){if(e.value=e.value,null!==e){if(e.createTextRange){var r=e.createTextRange();return r.move("character",t),r.select(),!0}return e.selectionStart||0===e.selectionStart?(e.focus(),e.setSelectionRange(t,t),!0):(e.focus(),!1)}}!function(e){e.event="event",e.props="prop"}(n||(n={}));var h=function(e){var t,r=void 0;return function(){for(var n=[],a=arguments.length;a--;)n[a]=arguments[a];return t&&n.length===t.length&&n.every(function(e,r){return e===t[r]})?r:(t=n,r=e.apply(void 0,n))}}(function(e,t){for(var r=0,n=0,a=e.length,o=t.length;e[r]===t[r]&&r<a;)r++;for(;e[a-1-n]===t[o-1-n]&&o-n>r&&a-n>r;)n++;return{from:{start:r,end:a-n},to:{start:r,end:o-n}}}),b=function(e,t){var r=Math.min(e.selectionStart,t);return{from:{start:r,end:e.selectionEnd},to:{start:r,end:t}}};function S(e){return Math.max(e.selectionStart,e.selectionEnd)}function y(e){var t=e.currentValue,r=e.formattedValue,n=e.currentValueIndex,a=e.formattedValueIndex;return t[n]===r[a]}function x(e,t,r,n){var a=e.length;if(t=Math.min(Math.max(t,0),a),"left"===n){for(;t>=0&&!r[t];)t--;-1===t&&(t=r.indexOf(!0))}else{for(;t<=a&&!r[t];)t++;t>a&&(t=r.lastIndexOf(!0))}return -1===t&&(t=a),t}function T(e){for(var t=Array.from({length:e.length+1}).map(function(){return!0}),r=0,n=t.length;r<n;r++)t[r]=!!(i(e[r])||i(e[r-1]));return t}function w(e,t,r,n,o,i){void 0===i&&(i=l);var s,d,f=(s=function(e,t){var r,a;return c(e)?(a="",r=""):r="number"==typeof e||t?n(a="number"==typeof e?m(e):e):n(a=o(e,void 0)),{formattedValue:r,numAsString:a}},(d=(0,a.useRef)(s)).current=s,(0,a.useRef)(function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];return d.current.apply(d,e)}).current),v=(0,a.useState)(function(){return f(u(e)?t:e,r)}),p=v[0],g=v[1],h=e,b=r;u(e)&&(h=p.numAsString,b=!0);var S=f(h,b);return(0,a.useMemo)(function(){g(S)},[S.formattedValue]),[p,function(e,t){e.formattedValue!==p.formattedValue&&g({formattedValue:e.formattedValue,numAsString:e.value}),i(e,t)}]}function N(e){return e.replace(/[^0-9]/g,"")}function I(e){return e}function A(e){var t=e.type;void 0===t&&(t="text");var r=e.displayType;void 0===r&&(r="input");var u=e.customInput,c=e.renderText,s=e.getInputRef,d=e.format;void 0===d&&(d=I);var f=e.removeFormatting;void 0===f&&(f=N);var v=e.defaultValue,m=e.valueIsNumericString,p=e.onValueChange,A=e.isAllowed,V=e.onChange;void 0===V&&(V=l);var C=e.onKeyDown;void 0===C&&(C=l);var E=e.onMouseUp;void 0===E&&(E=l);var j=e.onFocus;void 0===j&&(j=l);var D=e.onBlur;void 0===D&&(D=l);var M=e.value,R=e.getCaretBoundary;void 0===R&&(R=T);var B=e.isValidInputCharacter;void 0===B&&(B=i);var O=e.isCharacterSame,_=o(e,["type","displayType","customInput","renderText","getInputRef","format","removeFormatting","defaultValue","valueIsNumericString","onValueChange","isAllowed","onChange","onKeyDown","onMouseUp","onFocus","onBlur","value","getCaretBoundary","isValidInputCharacter","isCharacterSame"]),F=w(M,v,!!m,d,f,p),k=F[0],G=k.formattedValue,P=k.numAsString,z=F[1],L=(0,a.useRef)(),U=(0,a.useRef)({formattedValue:G,numAsString:P}),K=function(e,t){U.current={formattedValue:e.formattedValue,numAsString:e.value},z(e,t)},W=(0,a.useState)(!1),H=W[0],$=W[1],J=(0,a.useRef)(null),Z=(0,a.useRef)({setCaretTimeout:null,focusTimeout:null});(0,a.useEffect)(function(){return $(!0),function(){clearTimeout(Z.current.setCaretTimeout),clearTimeout(Z.current.focusTimeout)}},[]);var X=d,q=function(e,t){var r=parseFloat(t);return{formattedValue:e,value:t,floatValue:isNaN(r)?void 0:r}},Q=function(e,t,r){(0!==e.selectionStart||e.selectionEnd!==e.value.length)&&(g(e,t),Z.current.setCaretTimeout=setTimeout(function(){e.value===r&&e.selectionStart!==t&&g(e,t)},0))},Y=function(e,t,r){return x(e,t,R(e),r)},ee=function(e,t,r){var n=R(t),a=function(e,t,r,n,a,o,l){void 0===l&&(l=y);var i=a.findIndex(function(e){return e}),u=e.slice(0,i);t||r.startsWith(u)||(t=u,r=u+r,n+=u.length);for(var c=r.length,s=e.length,d={},f=Array(c),v=0;v<c;v++){f[v]=-1;for(var m=0;m<s;m++)if(l({currentValue:r,lastValue:t,formattedValue:e,currentValueIndex:v,formattedValueIndex:m})&&!0!==d[m]){f[v]=m,d[m]=!0;break}}for(var p=n;p<c&&(-1===f[p]||!o(r[p]));)p++;var g=p===c||-1===f[p]?s:f[p];for(p=n-1;p>0&&-1===f[p];)p--;var h=-1===p||-1===f[p]?0:f[p]+1;return h>g?g:n-h<g-n?h:g}(t,G,e,r,n,B,O);return x(t,a,n)},et=function(e){var t,r=e.formattedValue;void 0===r&&(r="");var n=e.input,a=e.source,o=e.event,l=e.numAsString;if(n){var i=e.inputValue||n.value,u=S(n);n.value=r,void 0!==(t=ee(i,r,u))&&Q(n,t,r)}r!==G&&K(q(r,l),{event:o,source:a})};(0,a.useEffect)(function(){var e=U.current,t=e.formattedValue,r=e.numAsString;(G!==t||P!==r)&&K(q(G,P),{event:void 0,source:n.props})},[G,P]);var er=J.current?S(J.current):void 0;("undefined"!=typeof window?a.useLayoutEffect:a.useEffect)(function(){var e=J.current;if(G!==U.current.formattedValue&&e){var t=ee(U.current.formattedValue,G,er);e.value=G,Q(e,t,G)}},[G]);var en=function(e,t,r){var n=t.target,a=Object.assign(Object.assign({},L.current?b(L.current,n.selectionEnd):h(G,e)),{lastValue:G}),o=f(e,a),l=X(o);if(o=f(l,void 0),A&&!A(q(l,o))){var i=t.target,u=ee(e,G,S(i));return i.value=G,Q(i,u,G),!1}return et({formattedValue:l,numAsString:o,inputValue:e,event:t,source:r,input:t.target}),!0},ea=function(e,t){void 0===t&&(t=0),L.current={selectionStart:e.selectionStart,selectionEnd:e.selectionEnd+t}},eo=Object.assign({inputMode:H&&"undefined"!=typeof navigator&&!(navigator.platform&&/iPhone|iPod/.test(navigator.platform))?"numeric":void 0},_,{type:t,value:G,onChange:function(e){en(e.target.value,e,n.event)&&V(e),L.current=void 0},onKeyDown:function(e){var t,r=e.target,n=e.key,a=r.selectionStart,o=r.selectionEnd,l=r.value;void 0===l&&(l=""),"ArrowLeft"===n||"Backspace"===n?t=Math.max(a-1,0):"ArrowRight"===n?t=Math.min(a+1,l.length):"Delete"===n&&(t=a);var i=0;"Delete"===n&&a===o&&(i=1);var u="ArrowLeft"===n||"ArrowRight"===n;if(void 0===t||a!==o&&!u){C(e),ea(r,i);return}var c=t;u?(c=Y(l,t,"ArrowLeft"===n?"left":"right"))!==t&&e.preventDefault():"Delete"!==n||B(l[t])?"Backspace"!==n||B(l[t])||(c=Y(l,t,"left")):c=Y(l,t,"right"),c!==t&&Q(r,c,l),C(e),ea(r,i)},onMouseUp:function(e){var t=e.target,r=function(){var e=t.selectionStart,r=t.selectionEnd,n=t.value;if(void 0===n&&(n=""),e===r){var a=Y(n,e);a!==e&&Q(t,a,n)}};r(),requestAnimationFrame(function(){r()}),E(e),ea(t)},onFocus:function(e){e.persist&&e.persist();var t=e.target,r=e.currentTarget;J.current=t,Z.current.focusTimeout=setTimeout(function(){var n=t.selectionStart,a=t.selectionEnd,o=t.value;void 0===o&&(o="");var l=Y(o,n);l!==n&&(0!==n||a!==o.length)&&Q(t,l,o),j(Object.assign(Object.assign({},e),{currentTarget:r}))},0)},onBlur:function(e){J.current=null,clearTimeout(Z.current.focusTimeout),clearTimeout(Z.current.setCaretTimeout),D(e)}});return"text"===r?c?a.createElement(a.Fragment,null,c(G,_)||null):a.createElement("span",Object.assign({},_,{ref:s}),G):u?a.createElement(u,Object.assign({},eo,{ref:s})):a.createElement("input",Object.assign({},eo,{ref:s}))}function V(e,t){var r,n,a,o=t.decimalScale,l=t.fixedDecimalScale,i=t.prefix;void 0===i&&(i="");var u=t.suffix;void 0===u&&(u="");var c=t.allowNegative,s=t.thousandsGroupStyle;if(void 0===s&&(s="thousand"),""===e||"-"===e)return e;var v=C(t),m=v.thousandSeparator,p=v.decimalSeparator,g=0!==o&&-1!==e.indexOf(".")||o&&l,h=d(e,c),b=h.beforeDecimal,S=h.afterDecimal,y=h.addNegation;return void 0!==o&&(S=f(S,o,!!l)),m&&(r=b,n=function(e){switch(e){case"lakh":return/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/g;case"wan":return/(\d)(?=(\d{4})+(?!\d))/g;default:return/(\d)(?=(\d{3})+(?!\d))/g}}(s),a=-1===(a=r.search(/[1-9]/))?r.length:a,b=r.substring(0,a)+r.substring(a,r.length).replace(n,"$1"+m)),i&&(b=i+b),u&&(S+=u),y&&(b="-"+b),e=b+(g&&p||"")+S}function C(e){var t=e.decimalSeparator;void 0===t&&(t=".");var r=e.thousandSeparator,n=e.allowedDecimalSeparators;return!0===r&&(r=","),n||(n=[t,"."]),{decimalSeparator:t,thousandSeparator:r,allowedDecimalSeparators:n}}function E(e){var t,r,f,v,b,S,y,x,T,N,I,E,j,D,M,R,B,O,_,F,k,G,P,z,L,U,K,W,H,$=((t=function(e){var t=C(e),r=t.thousandSeparator,n=t.decimalSeparator,a=e.prefix;void 0===a&&(a="");var o=e.allowNegative;if(void 0===o&&(o=!0),r===n)throw Error("\n        Decimal separator can't be same as thousand separator.\n        thousandSeparator: "+r+' (thousandSeparator = {true} is same as thousandSeparator = ",")\n        decimalSeparator: '+n+" (default value for decimalSeparator is .)\n     ");return a.startsWith("-")&&o&&(console.error("\n      Prefix can't start with '-' when allowNegative is true.\n      prefix: "+a+"\n      allowNegative: "+o+"\n    "),o=!1),Object.assign(Object.assign({},e),{allowNegative:o})}(t=e)).decimalSeparator,t.allowedDecimalSeparators,t.thousandsGroupStyle,r=t.suffix,f=t.allowNegative,v=t.allowLeadingZeros,void 0===(b=t.onKeyDown)&&(b=l),void 0===(S=t.onBlur)&&(S=l),y=t.thousandSeparator,x=t.decimalScale,T=t.fixedDecimalScale,void 0===(N=t.prefix)&&(N=""),I=t.defaultValue,E=t.value,j=t.valueIsNumericString,D=t.onValueChange,M=o(t,["decimalSeparator","allowedDecimalSeparators","thousandsGroupStyle","suffix","allowNegative","allowLeadingZeros","onKeyDown","onBlur","thousandSeparator","decimalScale","fixedDecimalScale","prefix","defaultValue","value","valueIsNumericString","onValueChange"]),B=(R=C(t)).decimalSeparator,O=R.allowedDecimalSeparators,_=function(e){return V(e,t)},F=function(e,r){return function(e,t,r){void 0===t&&(t={from:{start:0,end:0},to:{start:0,end:e.length},lastValue:""});var n,a,o,l,u=r.allowNegative,c=r.prefix;void 0===c&&(c="");var f=r.suffix;void 0===f&&(f="");var v=r.decimalScale,m=t.from,p=t.to,g=p.start,h=p.end,b=C(r),S=b.allowedDecimalSeparators,y=b.decimalSeparator,x=e[h]===y;if(i(e)&&(e===c||e===f)&&""===t.lastValue)return e;if(h-g==1&&-1!==S.indexOf(e[g])){var T=0===v?"":y;e=e.substring(0,g)+T+e.substring(g+1,e.length)}var w=function(e,t,r){var n=!1,a=!1;c.startsWith("-")?n=!1:e.startsWith("--")?(n=!1,a=!0):f.startsWith("-")&&e.length===f.length?n=!1:"-"===e[0]&&(n=!0);var o=+!!n;return a&&(o=2),o&&(e=e.substring(o),t-=o,r-=o),{value:e,start:t,end:r,hasNegation:n}},N=w(e,g,h),I=N.hasNegation;e=N.value,g=N.start,h=N.end;var A=w(t.lastValue,m.start,m.end),V=A.start,E=A.end,j=A.value,D=e.substring(g,h);e.length&&j.length&&(V>j.length-f.length||E<c.length)&&!(D&&f.startsWith(D))&&(e=j);var M=0;e.startsWith(c)?M+=c.length:g<c.length&&(M=g),e=e.substring(M),h-=M;var R=e.length,B=e.length-f.length;e.endsWith(f)?R=B:h>B?R=h:h>e.length-f.length&&(R=h),e=e.substring(0,R),void 0===(n=I?"-"+e:e)&&(n=""),a=RegExp("(-)(.)*(-)"),o=/(-)/.test(n),l=a.test(n),n=n.replace(/-/g,""),o&&!l&&u&&(n="-"+n);var O=(e=((e=n).match(RegExp("(^-)|[0-9]|"+s(y),"g"))||[]).join("")).indexOf(y),_=d(e=e.replace(RegExp(s(y),"g"),function(e,t){return t===O?".":""}),u),F=_.beforeDecimal,k=_.afterDecimal,G=_.addNegation;return p.end-p.start<m.end-m.start&&""===F&&x&&!parseFloat(k)&&(e=G?"-":""),e}(e,r,t)},k=u(E)?I:E,P=null!=j?j:(G=N,""===k||!(null==G?void 0:G.match(/\d/))&&!(null==r?void 0:r.match(/\d/))&&"string"==typeof k&&!isNaN(Number(k))),u(E)?u(I)||(P=P||"number"==typeof I):P=P||"number"==typeof E,K=(U=(L=w((z=function(e){return c(e)?e:("number"==typeof e&&(e=m(e)),P&&"number"==typeof x)?p(e,x,!!T):e})(E),z(I),!!P,_,F,D))[0]).numAsString,W=U.formattedValue,H=L[1],Object.assign(Object.assign({},M),{value:W,valueIsNumericString:!1,isValidInputCharacter:function(e){return e===B||i(e)},isCharacterSame:function(e){var t=e.currentValue,r=e.lastValue,n=e.formattedValue,a=e.currentValueIndex,o=e.formattedValueIndex,l=t[a],i=n[o],u=h(r,t).to,c=function(e){return F(e).indexOf(".")+N.length};return!(0===E&&T&&x&&t[u.start]===B&&c(t)<a&&c(n)>o)&&(!!(a>=u.start&&a<u.end&&O&&O.includes(l))&&i===B||l===i)},onValueChange:H,format:_,removeFormatting:F,getCaretBoundary:function(e){var r,n,a,o,l,i;return void 0===(n=(r=t).prefix)&&(n=""),void 0===(a=r.suffix)&&(a=""),o=Array.from({length:e.length+1}).map(function(){return!0}),l="-"===e[0],o.fill(!1,0,n.length+ +!!l),i=e.length,o.fill(!1,i-a.length+1,i+1),o},onKeyDown:function(e){var t=e.target,r=e.key,n=t.selectionStart,a=t.selectionEnd,o=t.value;if(void 0===o&&(o=""),("Backspace"===r||"Delete"===r)&&a<N.length)return void e.preventDefault();if(n!==a)return void b(e);"Backspace"===r&&"-"===o[0]&&n===N.length+1&&f&&g(t,1),x&&T&&("Backspace"===r&&o[n-1]===B?(g(t,n-1),e.preventDefault()):"Delete"===r&&o[n]===B&&e.preventDefault()),(null==O?void 0:O.includes(r))&&o[n]===B&&g(t,n+1);var l=!0===y?",":y;"Backspace"===r&&o[n-1]===l&&g(t,n-1),"Delete"===r&&o[n]===l&&g(t,n+1),b(e)},onBlur:function(e){var r=K;r.match(/\d/g)||(r=""),v||(r=function(e){if(!e)return e;var t="-"===e[0];t&&(e=e.substring(1,e.length));var r=e.split("."),n=r[0].replace(/^0+/,"")||"0",a=r[1]||"";return(t?"-":"")+n+(a?"."+a:"")}(r)),T&&x&&(r=p(r,x,T)),r!==K&&H({formattedValue:V(r,t),value:r,floatValue:parseFloat(r)},{event:e,source:n.event}),S(e)}}));return a.createElement(A,Object.assign({},$))}},92131:(e,t,r)=>{r.d(t,{ActionIconGroup:()=>v});var n=r(95155),a=r(5903);r(12115);var o=r(68918),l=r(43664),i=r(53791),u=r(69604),c=r(36960),s=r(9680);let d={orientation:"horizontal"},f=(0,o.createVarsResolver)((e,t)=>{let{borderWidth:r}=t;return{group:{"--ai-border-width":(0,a.D)(r)}}}),v=(0,c.factory)((e,t)=>{let r=(0,l.useProps)("ActionIconGroup",d,e),{className:a,style:o,classNames:c,styles:v,unstyled:m,orientation:p,vars:g,borderWidth:h,variant:b,mod:S,...y}=(0,l.useProps)("ActionIconGroup",d,e),x=(0,i.useStyles)({name:"ActionIconGroup",props:r,classes:s.A,className:a,style:o,classNames:c,styles:v,unstyled:m,vars:g,varsResolver:f,rootSelector:"group"});return(0,n.jsx)(u.Box,{...x("group"),ref:t,variant:b,mod:[{"data-orientation":p},S],role:"group",...y})});v.classes=s.A,v.displayName="@mantine/core/ActionIconGroup"},96963:(e,t,r)=>{r.d(t,{clamp:()=>n});function n(e,t,r){return void 0===t&&void 0===r?e:void 0!==t&&void 0===r?Math.max(e,t):void 0===t&&void 0!==r?Math.min(e,r):Math.min(Math.max(e,t),r)}}}]);