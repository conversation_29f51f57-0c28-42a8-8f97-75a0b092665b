/* [project]/node_modules/@mantine/dates/styles.css [app-client] (css) */
.m_468e7eda {
  appearance: none;
  padding-top: 0;
  padding-bottom: 0;
}

.m_468e7eda::-webkit-calendar-picker-indicator {
  display: none;
}

.m_468e7eda::-webkit-clear-button {
  display: none;
}

.m_468e7eda::-webkit-datetime-edit-hour-field, .m_468e7eda::-webkit-datetime-edit-minute-field, .m_468e7eda::-webkit-datetime-edit-second-field, .m_468e7eda::-webkit-datetime-edit-ampm-field {
  max-height: calc(1.875rem * var(--mantine-scale));
  padding-top: 0;
  display: inline;
}

.m_468e7eda::-webkit-datetime-edit-hour-field:focus, .m_468e7eda::-webkit-datetime-edit-minute-field:focus, .m_468e7eda::-webkit-datetime-edit-second-field:focus, .m_468e7eda::-webkit-datetime-edit-ampm-field:focus {
  background-color: var(--mantine-primary-color-filled);
  color: var(--mantine-color-white);
}

.m_7a8f1e6d {
  align-items: center;
  height: 100%;
  display: flex;
  overflow: hidden;
}

:where([dir="rtl"]) .m_7a8f1e6d {
  flex-direction: row-reverse;
}

.m_d6bb0a54 {
  height: calc(var(--input-height)  - 15px);
  align-items: center;
  display: flex;
}

.m_b97ecb26 {
  flex-direction: column;
  display: flex;
}

.m_31fe42f9 {
  gap: calc(.25rem * var(--mantine-scale));
  display: flex;
}

.m_9c4817c3 {
  padding: calc(.25rem * var(--mantine-scale));
}

.m_154c536b {
  text-align: center;
  border-radius: var(--mantine-radius-default);
  width: 2.5em;
  height: 2em;
  font-size: var(--control-font-size, var(--mantine-font-size-sm));
}

.m_154c536b:where([data-active]) {
  background-color: var(--mantine-primary-color-filled);
  color: var(--mantine-color-white);
}

@media (hover: hover) {
  .m_154c536b:hover:where(:not([data-active])) {
    color: var(--mantine-color-bright);
  }

  :where([data-mantine-color-scheme="dark"]) .m_154c536b:hover:where(:not([data-active])) {
    background-color: var(--mantine-color-dark-5);
  }

  :where([data-mantine-color-scheme="light"]) .m_154c536b:hover:where(:not([data-active])) {
    background-color: var(--mantine-color-gray-1);
  }
}

@media (hover: none) {
  .m_154c536b:active:where(:not([data-active])) {
    color: var(--mantine-color-bright);
  }

  :where([data-mantine-color-scheme="dark"]) .m_154c536b:active:where(:not([data-active])) {
    background-color: var(--mantine-color-dark-5);
  }

  :where([data-mantine-color-scheme="light"]) .m_154c536b:active:where(:not([data-active])) {
    background-color: var(--mantine-color-gray-1);
  }
}

.m_7be09d0c {
  text-align: center;
  border-radius: var(--mantine-radius-default);
  height: 2em;
  font-size: var(--control-font-size, var(--mantine-font-size-sm));
  padding-inline: .5em;
}

.m_7be09d0c:where([data-active]) {
  background-color: var(--mantine-primary-color-filled);
  color: var(--mantine-color-white);
}

@media (hover: hover) {
  .m_7be09d0c:hover:where(:not([data-active])) {
    color: var(--mantine-color-bright);
  }

  :where([data-mantine-color-scheme="dark"]) .m_7be09d0c:hover:where(:not([data-active])) {
    background-color: var(--mantine-color-dark-5);
  }

  :where([data-mantine-color-scheme="light"]) .m_7be09d0c:hover:where(:not([data-active])) {
    background-color: var(--mantine-color-gray-1);
  }
}

@media (hover: none) {
  .m_7be09d0c:active:where(:not([data-active])) {
    color: var(--mantine-color-bright);
  }

  :where([data-mantine-color-scheme="dark"]) .m_7be09d0c:active:where(:not([data-active])) {
    background-color: var(--mantine-color-dark-5);
  }

  :where([data-mantine-color-scheme="light"]) .m_7be09d0c:active:where(:not([data-active])) {
    background-color: var(--mantine-color-gray-1);
  }
}

.m_7d00001d + .m_7d00001d {
  margin-top: var(--mantine-spacing-sm);
}

.m_d8d918d7 {
  margin-bottom: calc(.25rem * var(--mantine-scale));
  color: var(--mantine-color-dimmed);
  font-size: calc(var(--control-font-size, var(--mantine-font-size-sm))  - 2px);
  align-items: center;
  padding-inline-start: calc(.4375rem * var(--mantine-scale));
  font-weight: 500;
  display: flex;
}

.m_d8d918d7:after {
  content: "";
  width: 100%;
  height: calc(.0625rem * var(--mantine-scale));
  flex: 1;
  margin-inline-start: var(--mantine-spacing-xs);
}

:where([data-mantine-color-scheme="light"]) .m_d8d918d7:after {
  background-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="dark"]) .m_d8d918d7:after {
  background-color: var(--mantine-color-dark-4);
}

.m_6b43ba88 {
  caret-color: #0000;
  font-variant-numeric: tabular-nums;
  text-align: center;
  text-align-last: center;
  width: calc(2ch + .3em);
  height: 100%;
  color: var(--input-color);
  border-radius: calc(.125rem * var(--mantine-scale));
  appearance: none;
  background-color: #0000;
  border: 0;
  padding-inline: .15em;
  line-height: 1;
  position: relative;
}

.m_6b43ba88:where([data-am-pm]) {
  width: calc(3ch + .3em);
}

.m_6b43ba88:where(:disabled) {
  cursor: not-allowed;
}

.m_6b43ba88::selection {
  background-color: #0000;
}

.m_6b43ba88::placeholder {
  opacity: 1;
  color: inherit;
}

.m_6b43ba88:focus {
  background-color: var(--mantine-primary-color-filled);
  color: var(--mantine-color-white);
  outline: 0;
}

.m_6b43ba88:focus::placeholder {
  color: var(--mantine-color-white);
}

.m_396ce5cb {
  --day-size-xs: calc(1.875rem * var(--mantine-scale));
  --day-size-sm: calc(2.25rem * var(--mantine-scale));
  --day-size-md: calc(2.625rem * var(--mantine-scale));
  --day-size-lg: calc(3rem * var(--mantine-scale));
  --day-size-xl: calc(3.375rem * var(--mantine-scale));
  --day-size: var(--day-size-sm);
  width: var(--day-size, var(--day-size-sm));
  height: var(--day-size, var(--day-size-sm));
  font-size: calc(var(--day-size) / 2.8);
  user-select: none;
  cursor: pointer;
  border-radius: var(--mantine-radius-default);
  color: var(--mantine-color-text);
  opacity: 1;
  background-color: #0000;
  justify-content: center;
  align-items: center;
  display: inline-flex;
}

@media (hover: hover) {
  [data-mantine-color-scheme="light"] .m_396ce5cb:hover:where(:not([data-static], [data-disabled], [data-selected], [data-in-range])) {
    background-color: var(--mantine-color-gray-0);
  }

  [data-mantine-color-scheme="dark"] .m_396ce5cb:hover:where(:not([data-static], [data-disabled], [data-selected], [data-in-range])) {
    background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {
  [data-mantine-color-scheme="light"] .m_396ce5cb:active:where(:not([data-static], [data-disabled], [data-selected], [data-in-range])) {
    background-color: var(--mantine-color-gray-0);
  }

  [data-mantine-color-scheme="dark"] .m_396ce5cb:active:where(:not([data-static], [data-disabled], [data-selected], [data-in-range])) {
    background-color: var(--mantine-color-dark-5);
  }
}

.m_396ce5cb:where([data-static]) {
  user-select: auto;
  cursor: default;
}

.m_396ce5cb:where([data-weekend]) {
  color: var(--mantine-color-red-6);
}

.m_396ce5cb:where([data-outside]) {
  color: var(--mantine-color-dimmed);
  opacity: .5;
}

.m_396ce5cb:where(:disabled, [data-disabled]) {
  color: var(--mantine-color-disabled-color);
  cursor: not-allowed;
  opacity: .5;
}

.m_396ce5cb:where([data-hidden]) {
  display: none;
}

:where([data-mantine-color-scheme="light"]) .m_396ce5cb:where([data-today][data-highlight-today]:not([data-selected], [data-in-range])) {
  border: 1px solid var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme="dark"]) .m_396ce5cb:where([data-today][data-highlight-today]:not([data-selected], [data-in-range])) {
  border: 1px solid var(--mantine-color-dark-4);
}

.m_396ce5cb:where([data-in-range]) {
  background-color: var(--mantine-primary-color-light-hover);
  border-radius: 0;
}

@media (hover: hover) {
  .m_396ce5cb:where([data-in-range]):hover:where(:not([data-disabled], [data-static])) {
    background-color: var(--mantine-primary-color-light);
  }
}

@media (hover: none) {
  .m_396ce5cb:where([data-in-range]):active:where(:not([data-disabled], [data-static])) {
    background-color: var(--mantine-primary-color-light);
  }
}

.m_396ce5cb:where([data-first-in-range]) {
  border-radius: 0;
  border-start-start-radius: var(--mantine-radius-default);
  border-end-start-radius: var(--mantine-radius-default);
}

.m_396ce5cb:where([data-last-in-range]) {
  border-radius: 0;
  border-start-end-radius: var(--mantine-radius-default);
  border-end-end-radius: var(--mantine-radius-default);
}

.m_396ce5cb:where([data-last-in-range][data-first-in-range]) {
  border-radius: var(--mantine-radius-default);
}

.m_396ce5cb:where([data-selected]) {
  background-color: var(--mantine-primary-color-filled);
  color: var(--mantine-primary-color-contrast);
}

@media (hover: hover) {
  .m_396ce5cb:where([data-selected]):hover:where(:not([data-disabled], [data-static])) {
    background-color: var(--mantine-primary-color-filled-hover);
  }
}

@media (hover: none) {
  .m_396ce5cb:where([data-selected]):active:where(:not([data-disabled], [data-static])) {
    background-color: var(--mantine-primary-color-filled-hover);
  }
}

.m_18a3eca {
  color: var(--mantine-color-dimmed);
  font-weight: normal;
  font-size: var(--wr-fz, var(--mantine-font-size-sm));
  text-transform: capitalize;
  padding-bottom: calc(var(--wr-spacing, var(--mantine-spacing-sm)) / 2);
}

.m_cc9820d3 {
  border-collapse: collapse;
  table-layout: fixed;
}

.m_8f457cd5 {
  padding: 0;
}

.m_8f457cd5:where([data-with-spacing]) {
  padding: calc(.03125rem * var(--mantine-scale));
}

.m_6cff9dea {
  --wn-size-xs: calc(1.875rem * var(--mantine-scale));
  --wn-size-sm: calc(2.25rem * var(--mantine-scale));
  --wn-size-md: calc(2.625rem * var(--mantine-scale));
  --wn-size-lg: calc(3rem * var(--mantine-scale));
  --wn-size-xl: calc(3.375rem * var(--mantine-scale));
  color: var(--mantine-color-dimmed);
  font-weight: normal;
  font-size: calc(var(--wn-size, var(--wn-size-sm)) / 2.8);
  text-align: center;
  width: var(--wn-size, var(--wn-size-sm));
}

.m_dc6a3c71 {
  --dpc-size-xs: calc(1.875rem * var(--mantine-scale));
  --dpc-size-sm: calc(2.25rem * var(--mantine-scale));
  --dpc-size-md: calc(2.625rem * var(--mantine-scale));
  --dpc-size-lg: calc(3rem * var(--mantine-scale));
  --dpc-size-xl: calc(3.375rem * var(--mantine-scale));
  --dpc-size: var(--dpc-size-sm);
  font-size: var(--dpc-fz, var(--mantine-font-size-sm));
  height: var(--dpc-size);
  width: calc((var(--dpc-size) * 7) / 3 + calc(.09375rem * var(--mantine-scale)));
  user-select: none;
  cursor: pointer;
  color: var(--mantine-color-text);
  opacity: 1;
  border-radius: var(--mantine-radius-default);
  background-color: #0000;
  justify-content: center;
  align-items: center;
  display: flex;
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_dc6a3c71:hover:where(:not([data-disabled], :disabled)) {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_dc6a3c71:hover:where(:not([data-disabled], :disabled)) {
    background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_dc6a3c71:active:where(:not([data-disabled], :disabled)) {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_dc6a3c71:active:where(:not([data-disabled], :disabled)) {
    background-color: var(--mantine-color-dark-5);
  }
}

.m_dc6a3c71:where(:disabled, [data-disabled]) {
  color: var(--mantine-color-disabled-color);
  cursor: not-allowed;
  opacity: .5;
}

.m_dc6a3c71:where([data-selected]) {
  background-color: var(--mantine-primary-color-filled);
  color: var(--mantine-primary-color-contrast, var(--mantine-color-white));
}

@media (hover: hover) {
  .m_dc6a3c71:where([data-selected]):hover {
    background-color: var(--mantine-primary-color-filled-hover);
  }
}

@media (hover: none) {
  .m_dc6a3c71:where([data-selected]):active {
    background-color: var(--mantine-primary-color-filled-hover);
  }
}

.m_dc6a3c71:where([data-in-range]) {
  background-color: var(--mantine-primary-color-light-hover);
  border-radius: 0;
}

@media (hover: hover) {
  .m_dc6a3c71:where([data-in-range]):hover {
    background-color: var(--mantine-primary-color-light);
  }
}

@media (hover: none) {
  .m_dc6a3c71:where([data-in-range]):active {
    background-color: var(--mantine-primary-color-light);
  }
}

.m_dc6a3c71:where([data-first-in-range]) {
  border-radius: 0;
  border-start-start-radius: var(--mantine-radius-default);
  border-end-start-radius: var(--mantine-radius-default);
}

.m_dc6a3c71:where([data-last-in-range]) {
  border-radius: 0;
  border-start-end-radius: var(--mantine-radius-default);
  border-end-end-radius: var(--mantine-radius-default);
}

.m_dc6a3c71:where([data-first-in-range][data-last-in-range]) {
  border-radius: var(--mantine-radius-default);
}

.m_9206547b {
  border-collapse: collapse;
  border-width: 0;
}

.m_c5a19c7d {
  padding: 0;
}

.m_c5a19c7d:where([data-with-spacing]) {
  padding: calc(.03125rem * var(--mantine-scale));
}

.m_2a6c32d {
  border-collapse: collapse;
  cursor: pointer;
  border-width: 0;
}

.m_fe27622f {
  padding: 0;
}

.m_fe27622f:where([data-with-spacing]) {
  padding: calc(.03125rem * var(--mantine-scale));
}

.m_730a79ed {
  --dch-control-size-xs: calc(1.875rem * var(--mantine-scale));
  --dch-control-size-sm: calc(2.25rem * var(--mantine-scale));
  --dch-control-size-md: calc(2.625rem * var(--mantine-scale));
  --dch-control-size-lg: calc(3rem * var(--mantine-scale));
  --dch-control-size-xl: calc(3.375rem * var(--mantine-scale));
  --dch-control-size: var(--dch-control-size-sm);
  max-width: calc(var(--dch-control-size) * 8 + calc(.4375rem * var(--mantine-scale)));
  margin-bottom: var(--mantine-spacing-xs);
  display: flex;
}

.m_f6645d97, .m_2351eeb0 {
  height: var(--dch-control-size);
  border-radius: var(--mantine-radius-default);
  user-select: none;
  opacity: 1;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  display: flex;
}

.m_f6645d97:where([data-static]), .m_2351eeb0:where([data-static]) {
  cursor: default;
}

@media (hover: hover) {
  [data-mantine-color-scheme="light"] .m_f6645d97:hover:where(:not([data-disabled], [data-static], :disabled)), [data-mantine-color-scheme="light"] .m_2351eeb0:hover:where(:not([data-disabled], [data-static], :disabled)) {
    background-color: var(--mantine-color-gray-0);
  }

  [data-mantine-color-scheme="dark"] .m_f6645d97:hover:where(:not([data-disabled], [data-static], :disabled)), [data-mantine-color-scheme="dark"] .m_2351eeb0:hover:where(:not([data-disabled], [data-static], :disabled)) {
    background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {
  [data-mantine-color-scheme="light"] .m_f6645d97:active:where(:not([data-disabled], [data-static], :disabled)), [data-mantine-color-scheme="light"] .m_2351eeb0:active:where(:not([data-disabled], [data-static], :disabled)) {
    background-color: var(--mantine-color-gray-0);
  }

  [data-mantine-color-scheme="dark"] .m_f6645d97:active:where(:not([data-disabled], [data-static], :disabled)), [data-mantine-color-scheme="dark"] .m_2351eeb0:active:where(:not([data-disabled], [data-static], :disabled)) {
    background-color: var(--mantine-color-dark-5);
  }
}

.m_f6645d97:where(:disabled, [data-disabled]), .m_2351eeb0:where(:disabled, [data-disabled]) {
  opacity: .2;
  cursor: not-allowed;
}

.m_2351eeb0 {
  width: var(--dch-control-size);
}

.m_f6645d97 {
  font-size: var(--dch-fz, var(--mantine-font-size-sm));
  text-transform: capitalize;
  flex: 1;
  font-weight: 500;
}

.m_367dc749 {
  width: 60%;
  height: 60%;
}

.m_367dc749:where([data-direction="next"]) {
  transform: rotate(270deg);
}

:where([dir="rtl"]) .m_367dc749:where([data-direction="next"]), .m_367dc749:where([data-direction="previous"]) {
  transform: rotate(90deg);
}

:where([dir="rtl"]) .m_367dc749:where([data-direction="previous"]) {
  transform: rotate(270deg);
}

.m_30b26e33 {
  gap: var(--mantine-spacing-md);
  display: flex;
}

.m_6fa5e2aa {
  cursor: pointer;
  line-height: unset;
}

.m_6fa5e2aa:where([data-read-only]) {
  cursor: default;
}

.m_765a40cf {
  font-size: var(--preset-font-size);
  display: flex;
}

.m_d6a681e1 {
  border-inline-end: calc(.0625rem * var(--mantine-scale)) solid;
  flex-direction: column;
  margin-inline-end: .5em;
  padding-inline-end: .5em;
  display: flex;
}

:where([data-mantine-color-scheme="light"]) .m_d6a681e1 {
  border-color: var(--mantine-color-gray-2);
}

:where([data-mantine-color-scheme="dark"]) .m_d6a681e1 {
  border-color: var(--mantine-color-dark-5);
}

.m_acd30b22 {
  border-radius: var(--mantine-radius-default);
  font-size: var(--preset-font-size);
  white-space: nowrap;
  padding: .52em .8em;
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_acd30b22:hover {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_acd30b22:hover {
    background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_acd30b22:active {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_acd30b22:active {
    background-color: var(--mantine-color-dark-5);
  }
}

.m_208d2562 {
  margin-top: var(--mantine-spacing-md);
  align-items: stretch;
  display: flex;
}

.m_62ee059 {
  flex: 1;
  margin-inline-end: var(--mantine-spacing-md);
}

.m_ac3f4d63 {
  text-align: center;
  border: 1px solid var(--mantine-color-default-border);
  background-color: var(--mantine-color-default);
  color: var(--mantine-color-default-color);
  border-radius: var(--time-grid-radius, var(--mantine-radius-default));
  font-size: var(--time-grid-fz, var(--mantine-font-size-sm));
  padding-block: .25em;
  padding-inline: 1em;
}

@media (hover: hover) {
  .m_ac3f4d63:hover:where(:not([data-disabled])) {
    background-color: var(--mantine-color-default-hover);
  }
}

@media (hover: none) {
  .m_ac3f4d63:active:where(:not([data-disabled])) {
    background-color: var(--mantine-color-default-hover);
  }
}

.m_ac3f4d63:where([data-active]) {
  background-color: var(--mantine-primary-color-filled);
  color: var(--mantine-color-white);
  border-color: #0000;
}

@media (hover: hover) {
  .m_ac3f4d63:where([data-active]):hover:where(:not([data-disabled])) {
    background-color: var(--mantine-primary-color-filled-hover);
  }
}

@media (hover: none) {
  .m_ac3f4d63:where([data-active]):active:where(:not([data-disabled])) {
    background-color: var(--mantine-primary-color-filled-hover);
  }
}

.m_ac3f4d63:where(:disabled, [data-disabled]) {
  opacity: .5;
  cursor: not-allowed;
}

/*# sourceMappingURL=node_modules_%40mantine_dates_styles_css_f9ee138c._.single.css.map*/