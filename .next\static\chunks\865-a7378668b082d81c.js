"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[865],{1526:(e,t,r)=>{r.d(t,{filterProps:()=>n});function n(e){return Object.keys(e).reduce((t,r)=>(void 0!==e[r]&&(t[r]=e[r]),t),{})}},2270:(e,t,r)=>{r.d(t,{camelToKebabCase:()=>n});function n(e){return e.replace(/[A-Z]/g,e=>"-".concat(e.toLowerCase()))}},3097:(e,t,r)=>{r.d(t,{K:()=>n});function n(e){return e}},3131:(e,t,r)=>{r.d(t,{MantineThemeContext:()=>c,MantineThemeProvider:()=>u,useMantineTheme:()=>f,useSafeMantineTheme:()=>d});var n=r(95155),a=r(12115),o=r(67118),i=r(41750);function s(e){return!(e<0)&&!(e>9)&&parseInt(e.toString(),10)===e}function l(e){if(!(e.primaryColor in e.colors))throw Error("[@mantine/core] MantineProvider: Invalid theme.primaryColor, it accepts only key of theme.colors, learn more – https://mantine.dev/theming/colors/#primary-color");if("object"==typeof e.primaryShade&&(!s(e.primaryShade.dark)||!s(e.primaryShade.light))||"number"==typeof e.primaryShade&&!s(e.primaryShade))throw Error("[@mantine/core] MantineProvider: Invalid theme.primaryShade, it accepts only 0-9 integers or an object { light: 0-9, dark: 0-9 }")}let c=(0,a.createContext)(null),d=()=>(0,a.useContext)(c)||o.S;function f(){let e=(0,a.useContext)(c);if(!e)throw Error("@mantine/core: MantineProvider was not found in component tree, make sure you have it in your app");return e}function u(e){let{theme:t,children:r,inherit:s=!0}=e,f=d(),u=(0,a.useMemo)(()=>(function(e,t){if(!t)return l(e),e;let r=(0,i.$)(e,t);return t.fontFamily&&!t.headings?.fontFamily&&(r.headings.fontFamily=t.fontFamily),l(r),r})(s?f:o.S,t),[t,f,s]);return(0,n.jsx)(c.Provider,{value:u,children:r})}u.displayName="@mantine/core/MantineThemeProvider"},5903:(e,t,r)=>{function n(e){return"0rem"===e?"0rem":`calc(${e} * var(--mantine-scale))`}function a(e,{shouldScale:t=!1}={}){return function r(a){if(0===a||"0"===a)return`0${e}`;if("number"==typeof a){let r=`${a/16}${e}`;return t?n(r):r}if("string"==typeof a){if(""===a||a.startsWith("calc(")||a.startsWith("clamp(")||a.includes("rgba("))return a;if(a.includes(","))return a.split(",").map(e=>r(e)).join(",");if(a.includes(" "))return a.split(" ").map(e=>r(e)).join(" ");let o=a.replace("px","");if(!Number.isNaN(Number(o))){let r=`${Number(o)/16}${e}`;return t?n(r):r}}return a}}r.d(t,{D:()=>o,em:()=>i});let o=a("rem",{shouldScale:!0}),i=a("em")},13656:(e,t,r)=>{r.d(t,{MantineContext:()=>a,useMantineClassNamesPrefix:()=>s,useMantineContext:()=>o,useMantineCssVariablesResolver:()=>i,useMantineEnv:()=>p,useMantineIsHeadless:()=>d,useMantineStyleNonce:()=>l,useMantineStylesTransform:()=>u,useMantineSxTransform:()=>f,useMantineWithStaticClasses:()=>c});var n=r(12115);let a=(0,n.createContext)(null);function o(){let e=(0,n.useContext)(a);if(!e)throw Error("[@mantine/core] MantineProvider was not found in tree");return e}function i(){return o().cssVariablesResolver}function s(){return o().classNamesPrefix}function l(){return o().getStyleNonce}function c(){return o().withStaticClasses}function d(){return o().headless}function f(){var e;return null==(e=o().stylesTransform)?void 0:e.sx}function u(){var e;return null==(e=o().stylesTransform)?void 0:e.styles}function p(){return o().env||"default"}},18512:(e,t,r)=>{r.d(t,{getGradient:()=>a});var n=r(71180);function a(e,t){var r,a;let o={from:(null==e?void 0:e.from)||t.defaultGradient.from,to:(null==e?void 0:e.to)||t.defaultGradient.to,deg:null!=(a=null!=(r=null==e?void 0:e.deg)?r:t.defaultGradient.deg)?a:0},i=(0,n.getThemeColor)(o.from,t),s=(0,n.getThemeColor)(o.to,t);return"linear-gradient(".concat(o.deg,"deg, ").concat(i," 0%, ").concat(s," 100%)")}},19224:(e,t,r)=>{r.d(t,{keys:()=>n});function n(e){return Object.keys(e)}},19787:(e,t,r)=>{r.d(t,{resolveClassNames:()=>o});var n=r(52596);let a={};function o(e){let{theme:t,classNames:r,props:o,stylesCtx:i}=e;var s=(Array.isArray(r)?r:[r]).map(e=>"function"==typeof e?e(t,o,i):e||a);let l={};return s.forEach(e=>{Object.entries(e).forEach(e=>{let[t,r]=e;l[t]?l[t]=(0,n.A)(l[t],r):l[t]=r})}),l}},29235:(e,t,r)=>{r.d(t,{parseStyleProps:()=>u});var n=r(19224);r(12115),r(95155);var a=r(5903),o=r(98271);function i(e,t){let r=(0,o.parseThemeColor)({color:e,theme:t});return"dimmed"===r.color?"var(--mantine-color-dimmed)":"bright"===r.color?"var(--mantine-color-bright)":r.variable?"var(".concat(r.variable,")"):r.color}let s={text:"var(--mantine-font-family)",mono:"var(--mantine-font-family-monospace)",monospace:"var(--mantine-font-family-monospace)",heading:"var(--mantine-font-family-headings)",headings:"var(--mantine-font-family-headings)"},l=["h1","h2","h3","h4","h5","h6"],c=["h1","h2","h3","h4","h5","h6"],d={color:i,textColor:function(e,t){let r=(0,o.parseThemeColor)({color:e,theme:t});return r.isThemeColor&&void 0===r.shade?"var(--mantine-color-".concat(r.color,"-text)"):i(e,t)},fontSize:function(e,t){return"string"==typeof e&&e in t.fontSizes?"var(--mantine-font-size-".concat(e,")"):"string"==typeof e&&l.includes(e)?"var(--mantine-".concat(e,"-font-size)"):"number"==typeof e||"string"==typeof e?(0,a.D)(e):e},spacing:function(e,t){if("number"==typeof e)return(0,a.D)(e);if("string"==typeof e){let r=e.replace("-","");if(!(r in t.spacing))return(0,a.D)(e);let n="--mantine-spacing-".concat(r);return e.startsWith("-")?"calc(var(".concat(n,") * -1)"):"var(".concat(n,")")}return e},radius:function(e,t){return"string"==typeof e&&e in t.radius?"var(--mantine-radius-".concat(e,")"):"number"==typeof e||"string"==typeof e?(0,a.D)(e):e},identity:function(e){return e},size:function(e){return"number"==typeof e?(0,a.D)(e):e},lineHeight:function(e,t){return"string"==typeof e&&e in t.lineHeights?"var(--mantine-line-height-".concat(e,")"):"string"==typeof e&&c.includes(e)?"var(--mantine-".concat(e,"-line-height)"):e},fontFamily:function(e){return"string"==typeof e&&e in s?s[e]:e},border:function(e,t){if("number"==typeof e)return(0,a.D)(e);if("string"==typeof e){let[r,n,...o]=e.split(" ").filter(e=>""!==e.trim()),s="".concat((0,a.D)(r));return n&&(s+=" ".concat(n)),o.length>0&&(s+=" ".concat(i(o.join(" "),t))),s.trim()}return e}};function f(e){return e.replace("(min-width: ","").replace("em)","")}function u(e){let{styleProps:t,data:r,theme:a}=e;return function(e){let{media:t,...r}=e,n=Object.keys(t).sort((e,t)=>Number(f(e))-Number(f(t))).map(e=>({query:e,styles:t[e]}));return{...r,media:n}}((0,n.keys)(t).reduce((e,o)=>{var i,s;if("hiddenFrom"===o||"visibleFrom"===o||"sx"===o)return e;let l=r[o],c=Array.isArray(l.property)?l.property:[l.property],f="object"==typeof(s=t[o])&&null!==s?"base"in s?s.base:void 0:s;if(!function(e){if("object"!=typeof e||null===e)return!1;let t=Object.keys(e);return 1!==t.length||"base"!==t[0]}(t[o]))return c.forEach(t=>{e.inlineStyles[t]=d[l.type](f,a)}),e;e.hasResponsiveStyles=!0;let u="object"==typeof(i=t[o])&&null!==i?(0,n.keys)(i).filter(e=>"base"!==e):[];return c.forEach(r=>{f&&(e.styles[r]=d[l.type](f,a)),u.forEach(n=>{var i;let s="(min-width: ".concat(a.breakpoints[n],")");e.media[s]={...e.media[s],[r]:d[l.type]((i=t[o],"object"==typeof i&&null!==i&&n in i?i[n]:i),a)}})}),e},{hasResponsiveStyles:!1,styles:{},inlineStyles:{},media:{}}))}},30128:(e,t,r)=>{r.d(t,{getPrimaryShade:()=>n});function n(e,t){return"number"==typeof e.primaryShade?e.primaryShade:"dark"===t?e.primaryShade.dark:e.primaryShade.light}},34034:(e,t,r)=>{r.d(t,{stylesToString:()=>i});var n=r(19224),a=r(2270);function o(e){return(0,n.keys)(e).reduce((t,r)=>void 0!==e[r]?"".concat(t).concat((0,a.camelToKebabCase)(r),":").concat(e[r],";"):t,"").trim()}function i(e){let{selector:t,styles:r,media:n,container:a}=e,i=r?o(r):"",s=Array.isArray(n)?n.map(e=>"@media".concat(e.query,"{").concat(t,"{").concat(o(e.styles),"}}")):[],l=Array.isArray(a)?a.map(e=>"@container ".concat(e.query,"{").concat(t,"{").concat(o(e.styles),"}}")):[];return"".concat(i?"".concat(t,"{").concat(i,"}"):"").concat(s.join("")).concat(l.join("")).trim()}r(12115),r(95155)},36960:(e,t,r)=>{r.d(t,{D:()=>o,factory:()=>s,getWithProps:()=>i});var n=r(95155),a=r(12115);function o(e){return e}function i(e){return t=>{let r=(0,a.forwardRef)((r,a)=>(0,n.jsx)(e,{...t,...r,ref:a}));return r.extend=e.extend,r.displayName="WithProps(".concat(e.displayName,")"),r}}function s(e){let t=(0,a.forwardRef)(e);return t.extend=o,t.withProps=e=>{let r=(0,a.forwardRef)((r,a)=>(0,n.jsx)(t,{...e,...r,ref:a}));return r.extend=t.extend,r.displayName="WithProps(".concat(t.displayName,")"),r},t}},38792:(e,t,r)=>{r.d(t,{K:()=>n});function n(e){if(/^#?([0-9A-F]{3}){1,2}([0-9A-F]{2})?$/i.test(e)){let t=e.replace("#","");if(3===t.length){let e=t.split("");t=[e[0],e[0],e[1],e[1],e[2],e[2]].join("")}if(8===t.length){let e=parseInt(t.slice(6,8),16)/255;return{r:parseInt(t.slice(0,2),16),g:parseInt(t.slice(2,4),16),b:parseInt(t.slice(4,6),16),a:e}}let r=parseInt(t,16);return{r:r>>16&255,g:r>>8&255,b:255&r,a:1}}if(e.startsWith("rgb")){let[t,r,n,a]=e.replace(/[^0-9,./]/g,"").split(/[/,]/).map(Number);return{r:t,g:r,b:n,a:void 0===a?1:a}}return e.startsWith("hsl")?function(e){let t,r,n,a=e.match(/^hsla?\(\s*(\d+)\s*,\s*(\d+%)\s*,\s*(\d+%)\s*(,\s*(0?\.\d+|\d+(\.\d+)?))?\s*\)$/i);if(!a)return{r:0,g:0,b:0,a:1};let o=parseInt(a[1],10),i=parseInt(a[2],10)/100,s=parseInt(a[3],10)/100,l=a[5]?parseFloat(a[5]):void 0,c=(1-Math.abs(2*s-1))*i,d=o/60,f=c*(1-Math.abs(d%2-1)),u=s-c/2;return d>=0&&d<1?(t=c,r=f,n=0):d>=1&&d<2?(t=f,r=c,n=0):d>=2&&d<3?(t=0,r=c,n=f):d>=3&&d<4?(t=0,r=f,n=c):d>=4&&d<5?(t=f,r=0,n=c):(t=c,r=0,n=f),{r:Math.round((t+u)*255),g:Math.round((r+u)*255),b:Math.round((n+u)*255),a:l||1}}(e):{r:0,g:0,b:0,a:1}}},41750:(e,t,r)=>{function n(e){return e&&"object"==typeof e&&!Array.isArray(e)}r.d(t,{$:()=>function e(t,r){let a={...t};return n(t)&&n(r)&&Object.keys(r).forEach(o=>{n(r[o])&&o in t?a[o]=e(a[o],r[o]):a[o]=r[o]}),a}})},43664:(e,t,r)=>{r.d(t,{useProps:()=>o});var n=r(1526);r(12115),r(95155);var a=r(3131);function o(e,t,r){var o;let i=(0,a.useMantineTheme)(),s=null==(o=i.components[e])?void 0:o.defaultProps,l="function"==typeof s?s(i):s;return{...t,...l,...(0,n.filterProps)(r)}}},46390:(e,t,r)=>{r.d(t,{useRandomClassName:()=>a});var n=r(12115);function a(){let e=(0,n.useId)().replace(/:/g,"");return"__m__-".concat(e)}},52596:(e,t,r)=>{r.d(t,{A:()=>n});let n=function(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=function e(t){var r,n,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(a&&(a+=" "),a+=n)}else for(n in t)t[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=t);return n}},53791:(e,t,r)=>{r.d(t,{useStyles:()=>u}),r(12115),r(95155);var n=r(13656),a=r(3131),o=r(52596),i=r(97677),s=r(19787);function l(e){let{selector:t,stylesCtx:r,theme:n,classNames:a,props:o}=e;return(0,s.resolveClassNames)({theme:n,classNames:a,props:o,stylesCtx:r})[t]}var c=r(84092);function d(e){let{style:t,theme:r}=e;return Array.isArray(t)?[...t].reduce((e,t)=>({...e,...d({style:t,theme:r})}),{}):"function"==typeof t?t(r):null==t?{}:t}var f=r(1526);function u(e){let{name:t,classes:r,props:u,stylesCtx:p,className:m,style:y,rootSelector:h="root",unstyled:v,classNames:g,styles:b,vars:x,varsResolver:S}=e,D=(0,a.useMantineTheme)(),k=(0,n.useMantineClassNamesPrefix)(),C=(0,n.useMantineWithStaticClasses)(),w=(0,n.useMantineIsHeadless)(),N=(Array.isArray(t)?t:[t]).filter(e=>e),{withStylesTransform:$,getTransformedStyles:T}=function(e){var t;let{props:r,stylesCtx:o,themeName:i}=e,s=(0,a.useMantineTheme)(),l=null==(t=(0,n.useMantineStylesTransform)())?void 0:t();return{getTransformedStyles:e=>l?[...e.map(e=>l(e,{props:r,theme:s,ctx:o})),...i.map(e=>{var t;return l(null==(t=s.components[e])?void 0:t.styles,{props:r,theme:s,ctx:o})})].filter(Boolean):[],withStylesTransform:!!l}}({props:u,stylesCtx:p,themeName:N});return(e,t)=>({className:function(e){let{theme:t,options:r,themeName:n,selector:a,classNamesPrefix:c,classNames:d,classes:f,unstyled:u,className:p,rootSelector:m,props:y,stylesCtx:h,withStaticClasses:v,headless:g,transformedStyles:b}=e;return(0,o.A)((0,i.K)({theme:t,options:r,unstyled:u||g}),function(e){let{themeName:t,theme:r,selector:n,props:a,stylesCtx:o}=e;return t.map(e=>{var t,i;return null==(t=(0,s.resolveClassNames)({theme:r,classNames:null==(i=r.components[e])?void 0:i.classNames,props:a,stylesCtx:o}))?void 0:t[n]})}({theme:t,themeName:n,selector:a,props:y,stylesCtx:h}),function(e){let{options:t,classes:r,selector:n,unstyled:a}=e;return(null==t?void 0:t.variant)&&!a?r["".concat(n,"--").concat(t.variant)]:void 0}({options:r,classes:f,selector:a,unstyled:u}),l({selector:a,stylesCtx:h,theme:t,classNames:d,props:y}),l({selector:a,stylesCtx:h,theme:t,classNames:b,props:y}),function(e){let{selector:t,stylesCtx:r,options:n,props:a,theme:o}=e;return(0,s.resolveClassNames)({theme:o,classNames:null==n?void 0:n.classNames,props:(null==n?void 0:n.props)||a,stylesCtx:r})[t]}({selector:a,stylesCtx:h,options:r,props:y,theme:t}),function(e){let{rootSelector:t,selector:r,className:n}=e;return t===r?n:void 0}({rootSelector:m,selector:a,className:p}),function(e){let{selector:t,classes:r,unstyled:n}=e;return n?void 0:r[t]}({selector:a,classes:f,unstyled:u||g}),v&&!g&&function(e){let{themeName:t,classNamesPrefix:r,selector:n,withStaticClass:a}=e;return!1===a?[]:t.map(e=>"".concat(r,"-").concat(e,"-").concat(n))}({themeName:n,classNamesPrefix:c,selector:a,withStaticClass:null==r?void 0:r.withStaticClass}),null==r?void 0:r.className)}({theme:D,options:t,themeName:N,selector:e,classNamesPrefix:k,classNames:g,classes:r,unstyled:v,className:m,rootSelector:h,props:u,stylesCtx:p,withStaticClasses:C,headless:w,transformedStyles:T([null==t?void 0:t.styles,b])}),style:function(e){let{theme:t,themeName:r,selector:n,options:a,props:o,stylesCtx:i,rootSelector:s,styles:l,style:u,vars:p,varsResolver:m,headless:y,withStylesTransform:h}=e;return{...!h&&function(e){let{theme:t,themeName:r,props:n,stylesCtx:a,selector:o}=e;return r.map(e=>{var r;return(0,c.resolveStyles)({theme:t,styles:null==(r=t.components[e])?void 0:r.styles,props:n,stylesCtx:a})[o]}).reduce((e,t)=>({...e,...t}),{})}({theme:t,themeName:r,props:o,stylesCtx:i,selector:n}),...!h&&(0,c.resolveStyles)({theme:t,styles:l,props:o,stylesCtx:i})[n],...!h&&(0,c.resolveStyles)({theme:t,styles:null==a?void 0:a.styles,props:(null==a?void 0:a.props)||o,stylesCtx:i})[n],...function(e){var t;let{vars:r,varsResolver:n,theme:a,props:o,stylesCtx:i,selector:s,themeName:l,headless:c}=e;return null==(t=[c?{}:null==n?void 0:n(a,o,i),...l.map(e=>{var t,r,n;return null==(n=a.components)||null==(r=n[e])||null==(t=r.vars)?void 0:t.call(r,a,o,i)}),null==r?void 0:r(a,o,i)].reduce((e,t)=>(t&&Object.keys(t).forEach(r=>{e[r]={...e[r],...(0,f.filterProps)(t[r])}}),e),{}))?void 0:t[s]}({theme:t,props:o,stylesCtx:i,vars:p,varsResolver:m,selector:n,themeName:r,headless:y}),...s===n?d({style:u,theme:t}):null,...d({style:null==a?void 0:a.style,theme:t})}}({theme:D,themeName:N,selector:e,options:t,props:u,stylesCtx:p,rootSelector:h,styles:b,style:y,vars:x,varsResolver:S,headless:w,withStylesTransform:$})})}},56204:(e,t,r)=>{r.d(t,{getFontSize:()=>l,getLineHeight:()=>c,getRadius:()=>s,getShadow:()=>d,getSize:()=>o,getSpacing:()=>i});var n=r(78772),a=r(5903);function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"size",r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(void 0!==e)return(0,n.isNumberLike)(e)?r?(0,a.D)(e):e:"var(--".concat(t,"-").concat(e,")")}function i(e){return o(e,"mantine-spacing")}function s(e){return void 0===e?"var(--mantine-radius-default)":o(e,"mantine-radius")}function l(e){return o(e,"mantine-font-size")}function c(e){return o(e,"mantine-line-height",!1)}function d(e){if(e)return o(e,"mantine-shadow",!1)}},58887:(e,t,r)=>{r.d(t,{Text:()=>y});var n=r(95155);r(12115);var a=r(56204),o=r(68918),i=r(71180),s=r(18512),l=r(43664),c=r(53791),d=r(69604),f=r(64511),u={root:"m_b6d8b162"};let p={inherit:!1},m=(0,o.createVarsResolver)((e,t)=>{let{variant:r,lineClamp:n,gradient:o,size:l,color:c}=t;return{root:{"--text-fz":(0,a.getFontSize)(l),"--text-lh":(0,a.getLineHeight)(l),"--text-gradient":"gradient"===r?(0,s.getGradient)(o,e):void 0,"--text-line-clamp":"number"==typeof n?n.toString():void 0,"--text-color":c?(0,i.getThemeColor)(c,e):void 0}}}),y=(0,f.polymorphicFactory)((e,t)=>{let r=(0,l.useProps)("Text",p,e),{lineClamp:a,truncate:o,inline:i,inherit:s,gradient:f,span:y,__staticSelector:h,vars:v,className:g,style:b,classNames:x,styles:S,unstyled:D,variant:k,mod:C,size:w,...N}=r,$=(0,c.useStyles)({name:["Text",h],props:r,classes:u,className:g,style:b,classNames:x,styles:S,unstyled:D,vars:v,varsResolver:m});return(0,n.jsx)(d.Box,{...$("root",{focusable:!0}),ref:t,component:y?"span":"p",variant:k,mod:[{"data-truncate":function(e){return"start"===e?"start":"end"===e||e?"end":void 0}(o),"data-line-clamp":"number"==typeof a,"data-inline":i,"data-inherit":s},C],size:w,...N})});y.classes=u,y.displayName="@mantine/core/Text"},58976:(e,t,r)=>{r.d(t,{InlineStyles:()=>i});var n=r(95155);r(12115);var a=r(13656),o=r(34034);function i(e){let t=(0,a.useMantineStyleNonce)();return(0,n.jsx)("style",{"data-mantine-styles":"inline",nonce:null==t?void 0:t(),dangerouslySetInnerHTML:{__html:(0,o.stylesToString)(e)}})}},64511:(e,t,r)=>{r.d(t,{polymorphicFactory:()=>i});var n=r(95155),a=r(12115),o=r(36960);function i(e){let t=(0,a.forwardRef)(e);return t.withProps=e=>{let r=(0,a.forwardRef)((r,a)=>(0,n.jsx)(t,{...e,...r,ref:a}));return r.extend=t.extend,r.displayName="WithProps(".concat(t.displayName,")"),r},t.extend=o.D,t}},67118:(e,t,r)=>{r.d(t,{S:()=>i});var n=r(5903);r(12115),r(95155);var a=r(84022);let o="-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji",i={scale:1,fontSmoothing:!0,focusRing:"auto",white:"#fff",black:"#000",colors:{dark:["#C9C9C9","#b8b8b8","#828282","#696969","#424242","#3b3b3b","#2e2e2e","#242424","#1f1f1f","#141414"],gray:["#f8f9fa","#f1f3f5","#e9ecef","#dee2e6","#ced4da","#adb5bd","#868e96","#495057","#343a40","#212529"],red:["#fff5f5","#ffe3e3","#ffc9c9","#ffa8a8","#ff8787","#ff6b6b","#fa5252","#f03e3e","#e03131","#c92a2a"],pink:["#fff0f6","#ffdeeb","#fcc2d7","#faa2c1","#f783ac","#f06595","#e64980","#d6336c","#c2255c","#a61e4d"],grape:["#f8f0fc","#f3d9fa","#eebefa","#e599f7","#da77f2","#cc5de8","#be4bdb","#ae3ec9","#9c36b5","#862e9c"],violet:["#f3f0ff","#e5dbff","#d0bfff","#b197fc","#9775fa","#845ef7","#7950f2","#7048e8","#6741d9","#5f3dc4"],indigo:["#edf2ff","#dbe4ff","#bac8ff","#91a7ff","#748ffc","#5c7cfa","#4c6ef5","#4263eb","#3b5bdb","#364fc7"],blue:["#e7f5ff","#d0ebff","#a5d8ff","#74c0fc","#4dabf7","#339af0","#228be6","#1c7ed6","#1971c2","#1864ab"],cyan:["#e3fafc","#c5f6fa","#99e9f2","#66d9e8","#3bc9db","#22b8cf","#15aabf","#1098ad","#0c8599","#0b7285"],teal:["#e6fcf5","#c3fae8","#96f2d7","#63e6be","#38d9a9","#20c997","#12b886","#0ca678","#099268","#087f5b"],green:["#ebfbee","#d3f9d8","#b2f2bb","#8ce99a","#69db7c","#51cf66","#40c057","#37b24d","#2f9e44","#2b8a3e"],lime:["#f4fce3","#e9fac8","#d8f5a2","#c0eb75","#a9e34b","#94d82d","#82c91e","#74b816","#66a80f","#5c940d"],yellow:["#fff9db","#fff3bf","#ffec99","#ffe066","#ffd43b","#fcc419","#fab005","#f59f00","#f08c00","#e67700"],orange:["#fff4e6","#ffe8cc","#ffd8a8","#ffc078","#ffa94d","#ff922b","#fd7e14","#f76707","#e8590c","#d9480f"]},primaryShade:{light:6,dark:8},primaryColor:"blue",variantColorResolver:a.defaultVariantColorsResolver,autoContrast:!1,luminanceThreshold:.3,fontFamily:o,fontFamilyMonospace:"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace",respectReducedMotion:!1,cursorType:"default",defaultGradient:{from:"blue",to:"cyan",deg:45},defaultRadius:"sm",activeClassName:"mantine-active",focusClassName:"",headings:{fontFamily:o,fontWeight:"700",textWrap:"wrap",sizes:{h1:{fontSize:(0,n.D)(34),lineHeight:"1.3"},h2:{fontSize:(0,n.D)(26),lineHeight:"1.35"},h3:{fontSize:(0,n.D)(22),lineHeight:"1.4"},h4:{fontSize:(0,n.D)(18),lineHeight:"1.45"},h5:{fontSize:(0,n.D)(16),lineHeight:"1.5"},h6:{fontSize:(0,n.D)(14),lineHeight:"1.5"}}},fontSizes:{xs:(0,n.D)(12),sm:(0,n.D)(14),md:(0,n.D)(16),lg:(0,n.D)(18),xl:(0,n.D)(20)},lineHeights:{xs:"1.4",sm:"1.45",md:"1.55",lg:"1.6",xl:"1.65"},radius:{xs:(0,n.D)(2),sm:(0,n.D)(4),md:(0,n.D)(8),lg:(0,n.D)(16),xl:(0,n.D)(32)},spacing:{xs:(0,n.D)(10),sm:(0,n.D)(12),md:(0,n.D)(16),lg:(0,n.D)(20),xl:(0,n.D)(32)},breakpoints:{xs:"36em",sm:"48em",md:"62em",lg:"75em",xl:"88em"},shadows:{xs:`0 ${(0,n.D)(1)} ${(0,n.D)(3)} rgba(0, 0, 0, 0.05), 0 ${(0,n.D)(1)} ${(0,n.D)(2)} rgba(0, 0, 0, 0.1)`,sm:`0 ${(0,n.D)(1)} ${(0,n.D)(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${(0,n.D)(10)} ${(0,n.D)(15)} ${(0,n.D)(-5)}, rgba(0, 0, 0, 0.04) 0 ${(0,n.D)(7)} ${(0,n.D)(7)} ${(0,n.D)(-5)}`,md:`0 ${(0,n.D)(1)} ${(0,n.D)(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${(0,n.D)(20)} ${(0,n.D)(25)} ${(0,n.D)(-5)}, rgba(0, 0, 0, 0.04) 0 ${(0,n.D)(10)} ${(0,n.D)(10)} ${(0,n.D)(-5)}`,lg:`0 ${(0,n.D)(1)} ${(0,n.D)(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${(0,n.D)(28)} ${(0,n.D)(23)} ${(0,n.D)(-7)}, rgba(0, 0, 0, 0.04) 0 ${(0,n.D)(12)} ${(0,n.D)(12)} ${(0,n.D)(-7)}`,xl:`0 ${(0,n.D)(1)} ${(0,n.D)(3)} rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 ${(0,n.D)(36)} ${(0,n.D)(28)} ${(0,n.D)(-7)}, rgba(0, 0, 0, 0.04) 0 ${(0,n.D)(17)} ${(0,n.D)(17)} ${(0,n.D)(-7)}`},other:{},components:{}}},68918:(e,t,r)=>{r.d(t,{createVarsResolver:()=>n});function n(e){return e}},69604:(e,t,r)=>{r.d(t,{Box:()=>g});var n=r(95155),a=r(12115),o=r(52596),i=r(3097),s=r(58976),l=r(78772),c=r(13656),d=r(3131);function f(e){return e.startsWith("data-")?e:"data-".concat(e)}function u(e,t){return Array.isArray(e)?[...e].reduce((e,r)=>({...e,...u(r,t)}),{}):"function"==typeof e?e(t):null==e?{}:e}var p=r(99537),m=r(74689),y=r(29235),h=r(46390);let v=(0,a.forwardRef)((e,t)=>{var r;let{component:a,style:i,__vars:v,className:g,variant:b,mod:x,size:S,hiddenFrom:D,visibleFrom:k,lightHidden:C,darkHidden:w,renderRoot:N,__size:$,...T}=e,M=(0,d.useMantineTheme)(),{styleProps:j,rest:A}=(0,p.extractStyleProps)(T),P=(0,c.useMantineSxTransform)(),z=null==P||null==(r=P())?void 0:r(j.sx),R=(0,h.useRandomClassName)(),L=(0,y.parseStyleProps)({styleProps:j,theme:M,data:m.STYlE_PROPS_DATA}),_={ref:t,style:function(e){let{theme:t,style:r,vars:n,styleProps:a}=e,o=u(r,t),i=u(n,t);return{...o,...i,...a}}({theme:M,style:i,vars:v,styleProps:L.inlineStyles}),className:(0,o.A)(g,z,{[R]:L.hasResponsiveStyles,"mantine-light-hidden":C,"mantine-dark-hidden":w,["mantine-hidden-from-".concat(D)]:D,["mantine-visible-from-".concat(k)]:k}),"data-variant":b,"data-size":(0,l.isNumberLike)(S)?void 0:S||void 0,size:$,...function e(t){return t?"string"==typeof t?{[f(t)]:!0}:Array.isArray(t)?[...t].reduce((t,r)=>({...t,...e(r)}),{}):Object.keys(t).reduce((e,r)=>{let n=t[r];return void 0===n||""===n||!1===n||null===n||(e[f(r)]=t[r]),e},{}):null}(x),...A};return(0,n.jsxs)(n.Fragment,{children:[L.hasResponsiveStyles&&(0,n.jsx)(s.InlineStyles,{selector:".".concat(R),styles:L.styles,media:L.media}),"function"==typeof N?N(_):(0,n.jsx)(a||"div",{..._})]})});v.displayName="@mantine/core/Box";let g=(0,i.K)(v)},70714:(e,t,r)=>{r.d(t,{B:()=>a,X:()=>o});var n=r(38792);function a(e,t){if("string"!=typeof e||t>1||t<0)return"rgba(0, 0, 0, 1)";if(e.startsWith("var("))return`color-mix(in srgb, ${e}, transparent ${(1-t)*100}%)`;if(e.startsWith("oklch"))return e.includes("/")?e.replace(/\/\s*[\d.]+\s*\)/,`/ ${t})`):e.replace(")",` / ${t})`);let{r,g:a,b:o}=(0,n.K)(e);return`rgba(${r}, ${a}, ${o}, ${t})`}let o=a},71180:(e,t,r)=>{r.d(t,{getThemeColor:()=>a});var n=r(98271);function a(e,t){let r=(0,n.parseThemeColor)({color:e||t.primaryColor,theme:t});return r.variable?"var(".concat(r.variable,")"):e}},74689:(e,t,r)=>{r.d(t,{STYlE_PROPS_DATA:()=>n});let n={m:{type:"spacing",property:"margin"},mt:{type:"spacing",property:"marginTop"},mb:{type:"spacing",property:"marginBottom"},ml:{type:"spacing",property:"marginLeft"},mr:{type:"spacing",property:"marginRight"},ms:{type:"spacing",property:"marginInlineStart"},me:{type:"spacing",property:"marginInlineEnd"},mx:{type:"spacing",property:"marginInline"},my:{type:"spacing",property:"marginBlock"},p:{type:"spacing",property:"padding"},pt:{type:"spacing",property:"paddingTop"},pb:{type:"spacing",property:"paddingBottom"},pl:{type:"spacing",property:"paddingLeft"},pr:{type:"spacing",property:"paddingRight"},ps:{type:"spacing",property:"paddingInlineStart"},pe:{type:"spacing",property:"paddingInlineEnd"},px:{type:"spacing",property:"paddingInline"},py:{type:"spacing",property:"paddingBlock"},bd:{type:"border",property:"border"},bdrs:{type:"radius",property:"borderRadius"},bg:{type:"color",property:"background"},c:{type:"textColor",property:"color"},opacity:{type:"identity",property:"opacity"},ff:{type:"fontFamily",property:"fontFamily"},fz:{type:"fontSize",property:"fontSize"},fw:{type:"identity",property:"fontWeight"},lts:{type:"size",property:"letterSpacing"},ta:{type:"identity",property:"textAlign"},lh:{type:"lineHeight",property:"lineHeight"},fs:{type:"identity",property:"fontStyle"},tt:{type:"identity",property:"textTransform"},td:{type:"identity",property:"textDecoration"},w:{type:"spacing",property:"width"},miw:{type:"spacing",property:"minWidth"},maw:{type:"spacing",property:"maxWidth"},h:{type:"spacing",property:"height"},mih:{type:"spacing",property:"minHeight"},mah:{type:"spacing",property:"maxHeight"},bgsz:{type:"size",property:"backgroundSize"},bgp:{type:"identity",property:"backgroundPosition"},bgr:{type:"identity",property:"backgroundRepeat"},bga:{type:"identity",property:"backgroundAttachment"},pos:{type:"identity",property:"position"},top:{type:"size",property:"top"},left:{type:"size",property:"left"},bottom:{type:"size",property:"bottom"},right:{type:"size",property:"right"},inset:{type:"size",property:"inset"},display:{type:"identity",property:"display"},flex:{type:"identity",property:"flex"}}},78772:(e,t,r)=>{r.d(t,{isNumberLike:()=>n});function n(e){if("number"==typeof e)return!0;if("string"==typeof e){if(e.startsWith("calc(")||e.startsWith("var(")||e.includes(" ")&&""!==e.trim())return!0;let t=/^[+-]?[0-9]+(\.[0-9]+)?(px|em|rem|ex|ch|lh|rlh|vw|vh|vmin|vmax|vb|vi|svw|svh|lvw|lvh|dvw|dvh|cm|mm|in|pt|pc|q|cqw|cqh|cqi|cqb|cqmin|cqmax|%)?$/;return e.trim().split(/\s+/).every(e=>t.test(e))}return!1}},83347:(e,t,r)=>{r.d(t,{Loader:()=>x,defaultLoaders:()=>v});var n=r(95155),a=r(12115),o=r(56204),i=r(68918),s=r(71180),l=r(43664),c=r(53791),d=r(69604),f=r(36960),u=r(52596),p={root:"m_5ae2e3c",barsLoader:"m_7a2bd4cd",bar:"m_870bb79","bars-loader-animation":"m_5d2b3b9d",dotsLoader:"m_4e3f22d7",dot:"m_870c4af","loader-dots-animation":"m_aac34a1",ovalLoader:"m_b34414df","oval-loader-animation":"m_f8e89c4b"};let m=(0,a.forwardRef)((e,t)=>{let{className:r,...a}=e;return(0,n.jsxs)(d.Box,{component:"span",className:(0,u.A)(p.barsLoader,r),...a,ref:t,children:[(0,n.jsx)("span",{className:p.bar}),(0,n.jsx)("span",{className:p.bar}),(0,n.jsx)("span",{className:p.bar})]})});m.displayName="@mantine/core/Bars";let y=(0,a.forwardRef)((e,t)=>{let{className:r,...a}=e;return(0,n.jsxs)(d.Box,{component:"span",className:(0,u.A)(p.dotsLoader,r),...a,ref:t,children:[(0,n.jsx)("span",{className:p.dot}),(0,n.jsx)("span",{className:p.dot}),(0,n.jsx)("span",{className:p.dot})]})});y.displayName="@mantine/core/Dots";let h=(0,a.forwardRef)((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)(d.Box,{component:"span",className:(0,u.A)(p.ovalLoader,r),...a,ref:t})});h.displayName="@mantine/core/Oval";let v={bars:m,oval:h,dots:y},g={loaders:v,type:"oval"},b=(0,i.createVarsResolver)((e,t)=>{let{size:r,color:n}=t;return{root:{"--loader-size":(0,o.getSize)(r,"loader-size"),"--loader-color":n?(0,s.getThemeColor)(n,e):void 0}}}),x=(0,f.factory)((e,t)=>{let r=(0,l.useProps)("Loader",g,e),{size:a,color:o,type:i,vars:s,className:f,style:u,classNames:m,styles:y,unstyled:h,loaders:v,variant:x,children:S,...D}=r,k=(0,c.useStyles)({name:"Loader",props:r,classes:p,className:f,style:u,classNames:m,styles:y,unstyled:h,vars:s,varsResolver:b});return S?(0,n.jsx)(d.Box,{...k("root"),ref:t,...D,children:S}):(0,n.jsx)(d.Box,{...k("root"),ref:t,component:v[i],variant:x,size:a,...D})});x.defaultLoaders=v,x.classes=p,x.displayName="@mantine/core/Loader"},84022:(e,t,r)=>{r.r(t),r.d(t,{defaultVariantColorsResolver:()=>c});var n=r(5903);r(12115),r(95155);var a=r(38792);function o(e,t){if(e.startsWith("var("))return`color-mix(in srgb, ${e}, black ${100*t}%)`;let{r,g:n,b:o,a:i}=(0,a.K)(e),s=1-t,l=e=>Math.round(e*s);return`rgba(${l(r)}, ${l(n)}, ${l(o)}, ${i})`}var i=r(18512),s=r(98271),l=r(70714);let c=e=>{let{color:t,theme:r,variant:a,gradient:c,autoContrast:d}=e,f=(0,s.parseThemeColor)({color:t,theme:r}),u="boolean"==typeof d?d:r.autoContrast;if("none"===a)return{background:"transparent",hover:"transparent",color:"inherit",border:"none"};if("filled"===a){let e=u&&f.isLight?"var(--mantine-color-black)":"var(--mantine-color-white)";return f.isThemeColor?void 0===f.shade?{background:"var(--mantine-color-".concat(t,"-filled)"),hover:"var(--mantine-color-".concat(t,"-filled-hover)"),color:e,border:"".concat((0,n.D)(1)," solid transparent")}:{background:"var(--mantine-color-".concat(f.color,"-").concat(f.shade,")"),hover:"var(--mantine-color-".concat(f.color,"-").concat(9===f.shade?8:f.shade+1,")"),color:e,border:"".concat((0,n.D)(1)," solid transparent")}:{background:t,hover:o(t,.1),color:e,border:"".concat((0,n.D)(1)," solid transparent")}}if("light"===a){if(f.isThemeColor){if(void 0===f.shade)return{background:"var(--mantine-color-".concat(t,"-light)"),hover:"var(--mantine-color-".concat(t,"-light-hover)"),color:"var(--mantine-color-".concat(t,"-light-color)"),border:"".concat((0,n.D)(1)," solid transparent")};let e=r.colors[f.color][f.shade];return{background:(0,l.B)(e,.1),hover:(0,l.B)(e,.12),color:"var(--mantine-color-".concat(f.color,"-").concat(Math.min(f.shade,6),")"),border:"".concat((0,n.D)(1)," solid transparent")}}return{background:(0,l.B)(t,.1),hover:(0,l.B)(t,.12),color:t,border:"".concat((0,n.D)(1)," solid transparent")}}if("outline"===a)return f.isThemeColor?void 0===f.shade?{background:"transparent",hover:"var(--mantine-color-".concat(t,"-outline-hover)"),color:"var(--mantine-color-".concat(t,"-outline)"),border:"".concat((0,n.D)(1)," solid var(--mantine-color-").concat(t,"-outline)")}:{background:"transparent",hover:(0,l.B)(r.colors[f.color][f.shade],.05),color:"var(--mantine-color-".concat(f.color,"-").concat(f.shade,")"),border:"".concat((0,n.D)(1)," solid var(--mantine-color-").concat(f.color,"-").concat(f.shade,")")}:{background:"transparent",hover:(0,l.B)(t,.05),color:t,border:"".concat((0,n.D)(1)," solid ").concat(t)};if("subtle"===a){if(f.isThemeColor){if(void 0===f.shade)return{background:"transparent",hover:"var(--mantine-color-".concat(t,"-light-hover)"),color:"var(--mantine-color-".concat(t,"-light-color)"),border:"".concat((0,n.D)(1)," solid transparent")};let e=r.colors[f.color][f.shade];return{background:"transparent",hover:(0,l.B)(e,.12),color:"var(--mantine-color-".concat(f.color,"-").concat(Math.min(f.shade,6),")"),border:"".concat((0,n.D)(1)," solid transparent")}}return{background:"transparent",hover:(0,l.B)(t,.12),color:t,border:"".concat((0,n.D)(1)," solid transparent")}}if("transparent"===a)return f.isThemeColor?void 0===f.shade?{background:"transparent",hover:"transparent",color:"var(--mantine-color-".concat(t,"-light-color)"),border:"".concat((0,n.D)(1)," solid transparent")}:{background:"transparent",hover:"transparent",color:"var(--mantine-color-".concat(f.color,"-").concat(Math.min(f.shade,6),")"),border:"".concat((0,n.D)(1)," solid transparent")}:{background:"transparent",hover:"transparent",color:t,border:"".concat((0,n.D)(1)," solid transparent")};if("white"===a)return f.isThemeColor?void 0===f.shade?{background:"var(--mantine-color-white)",hover:o(r.white,.01),color:"var(--mantine-color-".concat(t,"-filled)"),border:"".concat((0,n.D)(1)," solid transparent")}:{background:"var(--mantine-color-white)",hover:o(r.white,.01),color:"var(--mantine-color-".concat(f.color,"-").concat(f.shade,")"),border:"".concat((0,n.D)(1)," solid transparent")}:{background:"var(--mantine-color-white)",hover:o(r.white,.01),color:t,border:"".concat((0,n.D)(1)," solid transparent")};return"gradient"===a?{background:(0,i.getGradient)(c,r),hover:(0,i.getGradient)(c,r),color:"var(--mantine-color-white)",border:"none"}:"default"===a?{background:"var(--mantine-color-default)",hover:"var(--mantine-color-default-hover)",color:"var(--mantine-color-default-color)",border:"".concat((0,n.D)(1)," solid var(--mantine-color-default-border)")}:{}}},84092:(e,t,r)=>{r.d(t,{resolveStyles:()=>n});function n(e){let{theme:t,styles:r,props:n,stylesCtx:a}=e;return(Array.isArray(r)?r:[r]).reduce((e,r)=>"function"==typeof r?{...e,...r(t,n,a)}:{...e,...r},{})}},97677:(e,t,r)=>{r.d(t,{FOCUS_CLASS_NAMES:()=>a,K:()=>o});var n=r(52596);let a={always:"mantine-focus-always",auto:"mantine-focus-auto",never:"mantine-focus-never"};function o(e){let{theme:t,options:r,unstyled:o}=e;return(0,n.A)((null==r?void 0:r.focusable)&&!o&&(t.focusClassName||a[t.focusRing]),(null==r?void 0:r.active)&&!o&&t.activeClassName)}},98271:(e,t,r)=>{r.d(t,{parseThemeColor:()=>s});var n=r(30128),a=r(38792);function o(e){return e<=.03928?e/12.92:((e+.055)/1.055)**2.4}function i(e,t=.179){return!e.startsWith("var(")&&function(e){if(e.startsWith("oklch("))return(function(e){let t=e.match(/oklch\((.*?)%\s/);return t?parseFloat(t[1]):null}(e)||0)/100;let{r:t,g:r,b:n}=(0,a.K)(e),i=o(t/255);return .2126*i+.7152*o(r/255)+.0722*o(n/255)}(e)>t}function s(e){let{color:t,theme:r,colorScheme:a}=e;if("string"!=typeof t)throw Error("[@mantine/core] Failed to parse color. Expected color to be a string, instead got ".concat(typeof t));if("bright"===t)return{color:t,value:"dark"===a?r.white:r.black,shade:void 0,isThemeColor:!1,isLight:i("dark"===a?r.white:r.black,r.luminanceThreshold),variable:"--mantine-color-bright"};if("dimmed"===t)return{color:t,value:"dark"===a?r.colors.dark[2]:r.colors.gray[7],shade:void 0,isThemeColor:!1,isLight:i("dark"===a?r.colors.dark[2]:r.colors.gray[6],r.luminanceThreshold),variable:"--mantine-color-dimmed"};if("white"===t||"black"===t)return{color:t,value:"white"===t?r.white:r.black,shade:void 0,isThemeColor:!1,isLight:i("white"===t?r.white:r.black,r.luminanceThreshold),variable:"--mantine-color-".concat(t)};let[o,s]=t.split("."),l=s?Number(s):void 0,c=o in r.colors;if(c){let e=void 0!==l?r.colors[o][l]:r.colors[o][(0,n.getPrimaryShade)(r,a||"light")];return{color:o,value:e,shade:l,isThemeColor:c,isLight:i(e,r.luminanceThreshold),variable:s?"--mantine-color-".concat(o,"-").concat(l):"--mantine-color-".concat(o,"-filled")}}return{color:t,value:t,isThemeColor:c,isLight:i(t,r.luminanceThreshold),shade:l,variable:void 0}}},99537:(e,t,r)=>{r.d(t,{extractStyleProps:()=>a});var n=r(1526);function a(e){let{m:t,mx:r,my:a,mt:o,mb:i,ml:s,mr:l,me:c,ms:d,p:f,px:u,py:p,pt:m,pb:y,pl:h,pr:v,pe:g,ps:b,bd:x,bg:S,c:D,opacity:k,ff:C,fz:w,fw:N,lts:$,ta:T,lh:M,fs:j,tt:A,td:P,w:z,miw:R,maw:L,h:_,mih:F,mah:I,bgsz:B,bgp:W,bgr:E,bga:H,pos:O,top:K,left:q,bottom:G,right:V,inset:U,display:Y,flex:X,hiddenFrom:Z,visibleFrom:J,lightHidden:Q,darkHidden:ee,sx:et,...er}=e;return{styleProps:(0,n.filterProps)({m:t,mx:r,my:a,mt:o,mb:i,ml:s,mr:l,me:c,ms:d,p:f,px:u,py:p,pt:m,pb:y,pl:h,pr:v,pe:g,ps:b,bd:x,bg:S,c:D,opacity:k,ff:C,fz:w,fw:N,lts:$,ta:T,lh:M,fs:j,tt:A,td:P,w:z,miw:R,maw:L,h:_,mih:F,mah:I,bgsz:B,bgp:W,bgr:E,bga:H,pos:O,top:K,left:q,bottom:G,right:V,inset:U,display:Y,flex:X,hiddenFrom:Z,visibleFrom:J,lightHidden:Q,darkHidden:ee,sx:et}),rest:er}}r(12115),r(95155)}}]);