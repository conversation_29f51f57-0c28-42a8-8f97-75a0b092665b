export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          price: number;
          image_url: string | null;
          specs: Record<string, any> | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          price: number;
          image_url?: string | null;
          specs?: Record<string, any> | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          price?: number;
          image_url?: string | null;
          specs?: Record<string, any> | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      messages: {
        Row: {
          id: string;
          sender_id: string;
          sender_name: string | null;
          message_text: string;
          message_type: string;
          platform: string;
          metadata: Record<string, any> | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          sender_id: string;
          sender_name?: string | null;
          message_text: string;
          message_type?: string;
          platform?: string;
          metadata?: Record<string, any> | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          sender_id?: string;
          sender_name?: string | null;
          message_text?: string;
          message_type?: string;
          platform?: string;
          metadata?: Record<string, any> | null;
          created_at?: string;
        };
      };
      responses: {
        Row: {
          id: string;
          message_id: string | null;
          response_text: string;
          response_type: string;
          confidence_score: number | null;
          llm_provider: string | null;
          created_by: string | null;
          sent_at: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          message_id?: string | null;
          response_text: string;
          response_type?: string;
          confidence_score?: number | null;
          llm_provider?: string | null;
          created_by?: string | null;
          sent_at?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          message_id?: string | null;
          response_text?: string;
          response_type?: string;
          confidence_score?: number | null;
          llm_provider?: string | null;
          created_by?: string | null;
          sent_at?: string | null;
          created_at?: string;
        };
      };
      memory: {
        Row: {
          id: string;
          question: string;
          answer: string;
          keywords: string[];
          category: string | null;
          confidence_threshold: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          question: string;
          answer: string;
          keywords: string[];
          category?: string | null;
          confidence_threshold?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          question?: string;
          answer?: string;
          keywords?: string[];
          category?: string | null;
          confidence_threshold?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      admin_users: {
        Row: {
          id: string;
          username: string;
          password_hash: string;
          email: string | null;
          is_active: boolean;
          last_login: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          username: string;
          password_hash: string;
          email?: string | null;
          is_active?: boolean;
          last_login?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          username?: string;
          password_hash?: string;
          email?: string | null;
          is_active?: boolean;
          last_login?: string | null;
          created_at?: string;
        };
      };
    };
  };
}

// Additional type definitions for the application
export type Product = Database['public']['Tables']['products']['Row'];
export type ProductInsert = Database['public']['Tables']['products']['Insert'];
export type ProductUpdate = Database['public']['Tables']['products']['Update'];

export type Message = Database['public']['Tables']['messages']['Row'];
export type MessageInsert = Database['public']['Tables']['messages']['Insert'];
export type MessageUpdate = Database['public']['Tables']['messages']['Update'];

export type Response = Database['public']['Tables']['responses']['Row'];
export type ResponseInsert = Database['public']['Tables']['responses']['Insert'];
export type ResponseUpdate = Database['public']['Tables']['responses']['Update'];

export type Memory = Database['public']['Tables']['memory']['Row'];
export type MemoryInsert = Database['public']['Tables']['memory']['Insert'];
export type MemoryUpdate = Database['public']['Tables']['memory']['Update'];

export type AdminUser = Database['public']['Tables']['admin_users']['Row'];
export type AdminUserInsert = Database['public']['Tables']['admin_users']['Insert'];
export type AdminUserUpdate = Database['public']['Tables']['admin_users']['Update'];
