(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\cozy\\\\nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\cozy\\nextjs\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4591:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(60687),n=t(43210),a=t(46521),i=t(6099),o=t(68704),d=t(23376),l=t(65168),c=t(40859),p=t(99445),h=t(16537),u=t(61279),x=t(44543),m=t(40437),g=(0,t(6445).A)("outline","message-circle","IconMessageCircle",[["path",{d:"M3 20l1.3 -3.9c-2.324 -3.437 -1.426 -7.872 2.1 -10.374c3.526 -2.501 8.59 -2.296 11.845 .48c3.255 2.777 3.695 7.266 1.029 10.501c-2.666 3.235 -7.615 4.215 -11.574 2.293l-4.7 1",key:"svg-0"}]]);function v(){let[e,s]=(0,n.useState)(null),[t,v]=(0,n.useState)(!0);return t?(0,r.jsx)(a.Center,{h:400,children:(0,r.jsx)(i.Loader,{size:"lg"})}):(0,r.jsxs)(o.Stack,{children:[(0,r.jsx)(d.Title,{order:1,children:"Dashboard Overview"}),(0,r.jsxs)(l.Grid,{children:[(0,r.jsx)(l.Grid.Col,{span:{base:12,md:4},children:(0,r.jsx)(c.Card,{withBorder:!0,children:(0,r.jsxs)(p.Group,{justify:"space-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(h.Text,{c:"dimmed",size:"sm",tt:"uppercase",fw:700,children:"Total Products"}),(0,r.jsx)(h.Text,{fw:700,size:"xl",children:e?.totalProducts||0})]}),(0,r.jsx)(x.A,{size:"2rem",color:"blue"})]})})}),(0,r.jsx)(l.Grid.Col,{span:{base:12,md:4},children:(0,r.jsx)(c.Card,{withBorder:!0,children:(0,r.jsxs)(p.Group,{justify:"space-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(h.Text,{c:"dimmed",size:"sm",tt:"uppercase",fw:700,children:"Total Messages"}),(0,r.jsx)(h.Text,{fw:700,size:"xl",children:e?.totalMessages||0})]}),(0,r.jsx)(m.A,{size:"2rem",color:"green"})]})})}),(0,r.jsx)(l.Grid.Col,{span:{base:12,md:4},children:(0,r.jsx)(c.Card,{withBorder:!0,children:(0,r.jsxs)(p.Group,{justify:"space-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(h.Text,{c:"dimmed",size:"sm",tt:"uppercase",fw:700,children:"Total Responses"}),(0,r.jsx)(h.Text,{fw:700,size:"xl",children:e?.totalResponses||0})]}),(0,r.jsx)(g,{size:"2rem",color:"orange"})]})})})]}),(0,r.jsxs)(c.Card,{withBorder:!0,children:[(0,r.jsx)(d.Title,{order:3,mb:"md",children:"Recent Messages"}),e?.recentMessages.length===0?(0,r.jsx)(h.Text,{c:"dimmed",children:"No messages yet"}):(0,r.jsx)(o.Stack,{children:e?.recentMessages.map(e=>(0,r.jsxs)(p.Group,{justify:"space-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(h.Text,{fw:500,children:e.sender_name||e.sender_id}),(0,r.jsx)(h.Text,{size:"sm",c:"dimmed",truncate:!0,children:e.message_text})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(u.Badge,{color:e.responses?.length>0?"green":"yellow",variant:"light",children:e.responses?.length>0?"Responded":"Pending"}),(0,r.jsx)(h.Text,{size:"xs",c:"dimmed",children:new Date(e.created_at).toLocaleDateString()})]})]},e.id))})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12913:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(60687),n=t(43210),a=t(16189),i=t(46521),o=t(6099),d=t(74610),l=t(99445),c=t(16537),p=t(10507),h=t(35096),u=t(6445),x=(0,u.A)("outline","logout","IconLogout",[["path",{d:"M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M9 12h12l-3 -3",key:"svg-1"}],["path",{d:"M18 15l3 -3",key:"svg-2"}]]),m=t(76887),g=t(44543),v=t(40437),j=(0,u.A)("outline","settings","IconSettings",[["path",{d:"M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]]),f=t(47874);function y({children:e}){let[s,t]=(0,n.useState)(null),[u,y]=(0,n.useState)(!0),b=(0,a.useRouter)(),k=async()=>{try{await fetch("/api/auth/logout",{method:"POST"}),f.notifications.show({title:"Success",message:"Logged out successfully",color:"green"}),b.push("/login")}catch(e){console.error("Logout error:",e)}};return u?(0,r.jsx)(i.Center,{h:"100vh",children:(0,r.jsx)(o.Loader,{size:"lg"})}):s?(0,r.jsxs)(d.AppShell,{navbar:{width:250,breakpoint:"sm"},header:{height:60},padding:"md",children:[(0,r.jsx)(d.AppShell.Header,{children:(0,r.jsxs)(l.Group,{h:"100%",px:"md",justify:"space-between",children:[(0,r.jsx)(c.Text,{size:"lg",fw:600,children:"Admin Dashboard"}),(0,r.jsxs)(l.Group,{children:[(0,r.jsxs)(c.Text,{size:"sm",c:"dimmed",children:["Welcome, ",s.username]}),(0,r.jsx)(p.Button,{variant:"subtle",leftSection:(0,r.jsx)(x,{size:"1rem"}),onClick:k,children:"Logout"})]})]})}),(0,r.jsxs)(d.AppShell.Navbar,{p:"md",children:[(0,r.jsx)(h.NavLink,{href:"/admin",label:"Dashboard",leftSection:(0,r.jsx)(m.A,{size:"1rem"})}),(0,r.jsx)(h.NavLink,{href:"/admin/products",label:"Products",leftSection:(0,r.jsx)(g.A,{size:"1rem"})}),(0,r.jsx)(h.NavLink,{href:"/admin/messages",label:"Messages",leftSection:(0,r.jsx)(v.A,{size:"1rem"})}),(0,r.jsx)(h.NavLink,{href:"/admin/settings",label:"Settings",leftSection:(0,r.jsx)(j,{size:"1rem"})})]}),(0,r.jsx)(d.AppShell.Main,{children:e})]}):null}},16189:(e,s,t)=>{"use strict";var r=t(65773);t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},16757:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24684:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>l});var r=t(65239),n=t(48088),a=t(88170),i=t.n(a),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(s,d);let l={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"E:\\cozy\\nextjs\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"E:\\cozy\\nextjs\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\cozy\\nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\cozy\\nextjs\\src\\app\\admin\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30325:(e,s,t)=>{Promise.resolve().then(t.bind(t,4591))},33873:e=>{"use strict";e.exports=require("path")},40437:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var r=(0,t(6445).A)("outline","messages","IconMessages",[["path",{d:"M21 14l-3 -3h-7a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1h9a1 1 0 0 1 1 1v10",key:"svg-0"}],["path",{d:"M14 15v2a1 1 0 0 1 -1 1h-7l-3 3v-10a1 1 0 0 1 1 -1h2",key:"svg-1"}]])},44543:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var r=(0,t(6445).A)("outline","package","IconPackage",[["path",{d:"M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5",key:"svg-0"}],["path",{d:"M12 12l8 -4.5",key:"svg-1"}],["path",{d:"M12 12l0 9",key:"svg-2"}],["path",{d:"M12 12l-8 -4.5",key:"svg-3"}],["path",{d:"M16 5.25l-8 4.5",key:"svg-4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76887:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var r=(0,t(6445).A)("outline","dashboard","IconDashboard",[["path",{d:"M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M13.45 11.55l2.05 -2.05",key:"svg-1"}],["path",{d:"M6.4 20a9 9 0 1 1 11.2 0z",key:"svg-2"}]])},79551:e=>{"use strict";e.exports=require("url")},86454:(e,s,t)=>{Promise.resolve().then(t.bind(t,99111))},98078:(e,s,t)=>{Promise.resolve().then(t.bind(t,12913))},99111:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\cozy\\\\nextjs\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\cozy\\nextjs\\src\\app\\admin\\layout.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,405,762,995],()=>t(24684));module.exports=r})();