{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40floating-ui/react/dist/floating-ui.react.utils.mjs"], "sourcesContent": ["import { isShadowRoot, isHTMLElement } from '@floating-ui/utils/dom';\n\nfunction activeElement(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null || (_activeElement = _activeElement.shadowRoot) == null ? void 0 : _activeElement.activeElement) != null) {\n    var _activeElement;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  // FIXME: Firefox is now emitting a deprecation warning for `mozInputSource`.\n  // Try to find a workaround for this. `react-aria` source still has the check.\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  if (isAndroid() && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  if (isJSDOM()) return false;\n  return !isAndroid() && event.width === 0 && event.height === 0 || isAndroid() && event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'touch';\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isAndroid() {\n  const re = /android/i;\n  return re.test(getPlatform()) || re.test(getUserAgent());\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isJSDOM() {\n  return getUserAgent().includes('jsdom/');\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\nfunction isReactEvent(event) {\n  return 'nativeEvent' in event;\n}\nfunction isRootElement(element) {\n  return element.matches('html,body');\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\nfunction isTypeableCombobox(element) {\n  if (!element) return false;\n  return element.getAttribute('role') === 'combobox' && isTypeableElement(element);\n}\n\nexport { TYPEABLE_SELECTOR, activeElement, contains, getDocument, getPlatform, getTarget, getUserAgent, isAndroid, isEventTargetWithin, isJSDOM, isMac, isMouseLikePointerType, isReactEvent, isRootElement, isSafari, isTypeableCombobox, isTypeableElement, isVirtualClick, isVirtualPointerEvent, stopEvent };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA,SAAS,cAAc,GAAG;IACxB,IAAI,gBAAgB,IAAI,aAAa;IACrC,MAAO,CAAC,CAAC,iBAAiB,aAAa,KAAK,QAAQ,CAAC,iBAAiB,eAAe,UAAU,KAAK,OAAO,KAAK,IAAI,eAAe,aAAa,KAAK,KAAM;QACzJ,IAAI;QACJ,gBAAgB,cAAc,UAAU,CAAC,aAAa;IACxD;IACA,OAAO;AACT;AACA,SAAS,SAAS,MAAM,EAAE,KAAK;IAC7B,IAAI,CAAC,UAAU,CAAC,OAAO;QACrB,OAAO;IACT;IACA,MAAM,WAAW,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,MAAM,WAAW;IAEvE,2CAA2C;IAC3C,IAAI,OAAO,QAAQ,CAAC,QAAQ;QAC1B,OAAO;IACT;IAEA,iEAAiE;IACjE,IAAI,YAAY,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,WAAW;QACtC,IAAI,OAAO;QACX,MAAO,KAAM;YACX,IAAI,WAAW,MAAM;gBACnB,OAAO;YACT;YACA,aAAa;YACb,OAAO,KAAK,UAAU,IAAI,KAAK,IAAI;QACrC;IACF;IAEA,+BAA+B;IAC/B,OAAO;AACT;AACA,sCAAsC;AACtC,SAAS;IACP,MAAM,SAAS,UAAU,aAAa;IACtC,IAAI,UAAU,QAAQ,OAAO,QAAQ,EAAE;QACrC,OAAO,OAAO,QAAQ;IACxB;IACA,OAAO,UAAU,QAAQ;AAC3B;AACA,SAAS;IACP,MAAM,SAAS,UAAU,aAAa;IACtC,IAAI,UAAU,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;QAC1C,OAAO,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA;YACvB,IAAI,EACF,KAAK,EACL,OAAO,EACR,GAAG;YACJ,OAAO,QAAQ,MAAM;QACvB,GAAG,IAAI,CAAC;IACV;IACA,OAAO,UAAU,SAAS;AAC5B;AAEA,kJAAkJ;AAClJ,SAAS,eAAe,KAAK;IAC3B,6EAA6E;IAC7E,8EAA8E;IAC9E,IAAI,MAAM,cAAc,KAAK,KAAK,MAAM,SAAS,EAAE;QACjD,OAAO;IACT;IACA,IAAI,eAAe,MAAM,WAAW,EAAE;QACpC,OAAO,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,KAAK;IACrD;IACA,OAAO,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM,WAAW;AACjD;AACA,SAAS,sBAAsB,KAAK;IAClC,IAAI,WAAW,OAAO;IACtB,OAAO,CAAC,eAAe,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,eAAe,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,WAAW,KAAK,WAChM,iDAAiD;IACjD,MAAM,KAAK,GAAG,KAAK,MAAM,MAAM,GAAG,KAAK,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,WAAW,KAAK;AAC7G;AACA,SAAS;IACP,2DAA2D;IAC3D,OAAO,SAAS,IAAI,CAAC,UAAU,MAAM;AACvC;AACA,SAAS;IACP,MAAM,KAAK;IACX,OAAO,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAC3C;AACA,SAAS;IACP,OAAO,cAAc,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC,UAAU,cAAc;AACnF;AACA,SAAS;IACP,OAAO,eAAe,QAAQ,CAAC;AACjC;AACA,SAAS,uBAAuB,WAAW,EAAE,MAAM;IACjD,4EAA4E;IAC5E,mEAAmE;IACnE,MAAM,SAAS;QAAC;QAAS;KAAM;IAC/B,IAAI,CAAC,QAAQ;QACX,OAAO,IAAI,CAAC,IAAI;IAClB;IACA,OAAO,OAAO,QAAQ,CAAC;AACzB;AACA,SAAS,aAAa,KAAK;IACzB,OAAO,iBAAiB;AAC1B;AACA,SAAS,cAAc,OAAO;IAC5B,OAAO,QAAQ,OAAO,CAAC;AACzB;AACA,SAAS,YAAY,IAAI;IACvB,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,aAAa,KAAK;AACzD;AACA,SAAS,oBAAoB,KAAK,EAAE,IAAI;IACtC,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IACA,IAAI,kBAAkB,OAAO;QAC3B,OAAO,MAAM,YAAY,GAAG,QAAQ,CAAC;IACvC;IAEA,4HAA4H;IAC5H,MAAM,IAAI;IACV,OAAO,EAAE,MAAM,IAAI,QAAQ,KAAK,QAAQ,CAAC,EAAE,MAAM;AACnD;AACA,SAAS,UAAU,KAAK;IACtB,IAAI,kBAAkB,OAAO;QAC3B,OAAO,MAAM,YAAY,EAAE,CAAC,EAAE;IAChC;IAEA,wEAAwE;IACxE,2DAA2D;IAC3D,OAAO,MAAM,MAAM;AACrB;AACA,MAAM,oBAAoB,gDAAgD;AAC1E,SAAS,kBAAkB,OAAO;IAChC,OAAO,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,QAAQ,OAAO,CAAC;AACnD;AACA,SAAS,UAAU,KAAK;IACtB,MAAM,cAAc;IACpB,MAAM,eAAe;AACvB;AACA,SAAS,mBAAmB,OAAO;IACjC,IAAI,CAAC,SAAS,OAAO;IACrB,OAAO,QAAQ,YAAY,CAAC,YAAY,cAAc,kBAAkB;AAC1E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40floating-ui/react/dist/floating-ui.react.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect, useEffect, useRef } from 'react';\nimport { stopEvent, getDocument, isMouseLikePointerType, contains, activeElement, isSafari, isTypeableCombobox, isVirtualClick, isVirtualPointerEvent, getTarget, getPlatform, isTypeableElement, isReactEvent, isRootElement, isEventTargetWithin, isMac, getUserAgent } from '@floating-ui/react/utils';\nimport { floor, evaluate, max, min, round } from '@floating-ui/utils';\nimport { getComputedStyle, isElement, getNodeName, isHTMLElement, getWindow, isLastTraversableNode, getParentNode, isWebKit } from '@floating-ui/utils/dom';\nimport { tabbable, isTabbable } from 'tabbable';\nimport * as ReactDOM from 'react-dom';\nimport { getOverflowAncestors, useFloating as useFloating$1, offset, detectOverflow } from '@floating-ui/react-dom';\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/react-dom';\n\n/**\n * Merges an array of refs into a single memoized callback ref or `null`.\n * @see https://floating-ui.com/docs/react-utils#usemergerefs\n */\nfunction useMergeRefs(refs) {\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      refs.forEach(ref => {\n        if (typeof ref === 'function') {\n          ref(value);\n        } else if (ref != null) {\n          ref.current = value;\n        }\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}\n\n// https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379\nconst SafeReact = {\n  ...React\n};\n\nconst useInsertionEffect = SafeReact.useInsertionEffect;\nconst useSafeInsertionEffect = useInsertionEffect || (fn => fn());\nfunction useEffectEvent(callback) {\n  const ref = React.useRef(() => {\n    if (process.env.NODE_ENV !== \"production\") {\n      throw new Error('Cannot call an event handler while rendering.');\n    }\n  });\n  useSafeInsertionEffect(() => {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current == null ? void 0 : ref.current(...args);\n  }, []);\n}\n\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nfunction isDifferentRow(index, cols, prevRow) {\n  return Math.floor(index / cols) !== prevRow;\n}\nfunction isIndexOutOfBounds(listRef, index) {\n  return index < 0 || index >= listRef.current.length;\n}\nfunction getMinIndex(listRef, disabledIndices) {\n  return findNonDisabledIndex(listRef, {\n    disabledIndices\n  });\n}\nfunction getMaxIndex(listRef, disabledIndices) {\n  return findNonDisabledIndex(listRef, {\n    decrement: true,\n    startingIndex: listRef.current.length,\n    disabledIndices\n  });\n}\nfunction findNonDisabledIndex(listRef, _temp) {\n  let {\n    startingIndex = -1,\n    decrement = false,\n    disabledIndices,\n    amount = 1\n  } = _temp === void 0 ? {} : _temp;\n  const list = listRef.current;\n  let index = startingIndex;\n  do {\n    index += decrement ? -amount : amount;\n  } while (index >= 0 && index <= list.length - 1 && isDisabled(list, index, disabledIndices));\n  return index;\n}\nfunction getGridNavigatedIndex(elementsRef, _ref) {\n  let {\n    event,\n    orientation,\n    loop,\n    rtl,\n    cols,\n    disabledIndices,\n    minIndex,\n    maxIndex,\n    prevIndex,\n    stopEvent: stop = false\n  } = _ref;\n  let nextIndex = prevIndex;\n  if (event.key === ARROW_UP) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = maxIndex;\n    } else {\n      nextIndex = findNonDisabledIndex(elementsRef, {\n        startingIndex: nextIndex,\n        amount: cols,\n        decrement: true,\n        disabledIndices\n      });\n      if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {\n        const col = prevIndex % cols;\n        const maxCol = maxIndex % cols;\n        const offset = maxIndex - (maxCol - col);\n        if (maxCol === col) {\n          nextIndex = maxIndex;\n        } else {\n          nextIndex = maxCol > col ? offset : offset - cols;\n        }\n      }\n    }\n    if (isIndexOutOfBounds(elementsRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n  if (event.key === ARROW_DOWN) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = minIndex;\n    } else {\n      nextIndex = findNonDisabledIndex(elementsRef, {\n        startingIndex: prevIndex,\n        amount: cols,\n        disabledIndices\n      });\n      if (loop && prevIndex + cols > maxIndex) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex % cols - cols,\n          amount: cols,\n          disabledIndices\n        });\n      }\n    }\n    if (isIndexOutOfBounds(elementsRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n\n  // Remains on the same row/column.\n  if (orientation === 'both') {\n    const prevRow = floor(prevIndex / cols);\n    if (event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== cols - 1) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex,\n          disabledIndices\n        });\n        if (loop && isDifferentRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledIndex(elementsRef, {\n            startingIndex: prevIndex - prevIndex % cols - 1,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      }\n      if (isDifferentRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    if (event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== 0) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex,\n          decrement: true,\n          disabledIndices\n        });\n        if (loop && isDifferentRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledIndex(elementsRef, {\n            startingIndex: prevIndex + (cols - prevIndex % cols),\n            decrement: true,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex + (cols - prevIndex % cols),\n          decrement: true,\n          disabledIndices\n        });\n      }\n      if (isDifferentRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    const lastRow = floor(maxIndex / cols) === prevRow;\n    if (isIndexOutOfBounds(elementsRef, nextIndex)) {\n      if (loop && lastRow) {\n        nextIndex = event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT) ? maxIndex : findNonDisabledIndex(elementsRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      } else {\n        nextIndex = prevIndex;\n      }\n    }\n  }\n  return nextIndex;\n}\n\n/** For each cell index, gets the item index that occupies that cell */\nfunction buildCellMap(sizes, cols, dense) {\n  const cellMap = [];\n  let startIndex = 0;\n  sizes.forEach((_ref2, index) => {\n    let {\n      width,\n      height\n    } = _ref2;\n    if (width > cols) {\n      if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[Floating UI]: Invalid grid - item width at index \" + index + \" is greater than grid columns\");\n      }\n    }\n    let itemPlaced = false;\n    if (dense) {\n      startIndex = 0;\n    }\n    while (!itemPlaced) {\n      const targetCells = [];\n      for (let i = 0; i < width; i++) {\n        for (let j = 0; j < height; j++) {\n          targetCells.push(startIndex + i + j * cols);\n        }\n      }\n      if (startIndex % cols + width <= cols && targetCells.every(cell => cellMap[cell] == null)) {\n        targetCells.forEach(cell => {\n          cellMap[cell] = index;\n        });\n        itemPlaced = true;\n      } else {\n        startIndex++;\n      }\n    }\n  });\n\n  // convert into a non-sparse array\n  return [...cellMap];\n}\n\n/** Gets cell index of an item's corner or -1 when index is -1. */\nfunction getCellIndexOfCorner(index, sizes, cellMap, cols, corner) {\n  if (index === -1) return -1;\n  const firstCellIndex = cellMap.indexOf(index);\n  const sizeItem = sizes[index];\n  switch (corner) {\n    case 'tl':\n      return firstCellIndex;\n    case 'tr':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + sizeItem.width - 1;\n    case 'bl':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + (sizeItem.height - 1) * cols;\n    case 'br':\n      return cellMap.lastIndexOf(index);\n  }\n}\n\n/** Gets all cell indices that correspond to the specified indices */\nfunction getCellIndices(indices, cellMap) {\n  return cellMap.flatMap((index, cellIndex) => indices.includes(index) ? [cellIndex] : []);\n}\nfunction isDisabled(list, index, disabledIndices) {\n  if (disabledIndices) {\n    return disabledIndices.includes(index);\n  }\n  const element = list[index];\n  return element == null || element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true';\n}\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\nfunction sortByDocumentPosition(a, b) {\n  const position = a.compareDocumentPosition(b);\n  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n    return -1;\n  }\n  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {\n    return 1;\n  }\n  return 0;\n}\nfunction areMapsEqual(map1, map2) {\n  if (map1.size !== map2.size) {\n    return false;\n  }\n  for (const [key, value] of map1.entries()) {\n    if (value !== map2.get(key)) {\n      return false;\n    }\n  }\n  return true;\n}\nconst FloatingListContext = /*#__PURE__*/React.createContext({\n  register: () => {},\n  unregister: () => {},\n  map: /*#__PURE__*/new Map(),\n  elementsRef: {\n    current: []\n  }\n});\n/**\n * Provides context for a list of items within the floating element.\n * @see https://floating-ui.com/docs/FloatingList\n */\nfunction FloatingList(props) {\n  const {\n    children,\n    elementsRef,\n    labelsRef\n  } = props;\n  const [map, setMap] = React.useState(() => new Map());\n  const register = React.useCallback(node => {\n    setMap(prevMap => new Map(prevMap).set(node, null));\n  }, []);\n  const unregister = React.useCallback(node => {\n    setMap(prevMap => {\n      const map = new Map(prevMap);\n      map.delete(node);\n      return map;\n    });\n  }, []);\n  index(() => {\n    const newMap = new Map(map);\n    const nodes = Array.from(newMap.keys()).sort(sortByDocumentPosition);\n    nodes.forEach((node, index) => {\n      newMap.set(node, index);\n    });\n    if (!areMapsEqual(map, newMap)) {\n      setMap(newMap);\n    }\n  }, [map]);\n  return /*#__PURE__*/React.createElement(FloatingListContext.Provider, {\n    value: React.useMemo(() => ({\n      register,\n      unregister,\n      map,\n      elementsRef,\n      labelsRef\n    }), [register, unregister, map, elementsRef, labelsRef])\n  }, children);\n}\n/**\n * Used to register a list item and its index (DOM position) in the\n * `FloatingList`.\n * @see https://floating-ui.com/docs/FloatingList#uselistitem\n */\nfunction useListItem(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    label\n  } = props;\n  const {\n    register,\n    unregister,\n    map,\n    elementsRef,\n    labelsRef\n  } = React.useContext(FloatingListContext);\n  const [index$1, setIndex] = React.useState(null);\n  const componentRef = React.useRef(null);\n  const ref = React.useCallback(node => {\n    componentRef.current = node;\n    if (index$1 !== null) {\n      elementsRef.current[index$1] = node;\n      if (labelsRef) {\n        var _node$textContent;\n        const isLabelDefined = label !== undefined;\n        labelsRef.current[index$1] = isLabelDefined ? label : (_node$textContent = node == null ? void 0 : node.textContent) != null ? _node$textContent : null;\n      }\n    }\n  }, [index$1, elementsRef, labelsRef, label]);\n  index(() => {\n    const node = componentRef.current;\n    if (node) {\n      register(node);\n      return () => {\n        unregister(node);\n      };\n    }\n  }, [register, unregister]);\n  index(() => {\n    const index = componentRef.current ? map.get(componentRef.current) : null;\n    if (index != null) {\n      setIndex(index);\n    }\n  }, [map]);\n  return React.useMemo(() => ({\n    ref,\n    index: index$1 == null ? -1 : index$1\n  }), [index$1, ref]);\n}\n\nfunction renderJsx(render, computedProps) {\n  if (typeof render === 'function') {\n    return render(computedProps);\n  }\n  if (render) {\n    return /*#__PURE__*/React.cloneElement(render, computedProps);\n  }\n  return /*#__PURE__*/React.createElement(\"div\", computedProps);\n}\nconst CompositeContext = /*#__PURE__*/React.createContext({\n  activeIndex: 0,\n  onNavigate: () => {}\n});\nconst horizontalKeys = [ARROW_LEFT, ARROW_RIGHT];\nconst verticalKeys = [ARROW_UP, ARROW_DOWN];\nconst allKeys = [...horizontalKeys, ...verticalKeys];\n\n/**\n * Creates a single tab stop whose items are navigated by arrow keys, which\n * provides list navigation outside of floating element contexts.\n *\n * This is useful to enable navigation of a list of items that aren’t part of a\n * floating element. A menubar is an example of a composite, with each reference\n * element being an item.\n * @see https://floating-ui.com/docs/Composite\n */\nconst Composite = /*#__PURE__*/React.forwardRef(function Composite(props, forwardedRef) {\n  const {\n    render,\n    orientation = 'both',\n    loop = true,\n    rtl = false,\n    cols = 1,\n    disabledIndices,\n    activeIndex: externalActiveIndex,\n    onNavigate: externalSetActiveIndex,\n    itemSizes,\n    dense = false,\n    ...domProps\n  } = props;\n  const [internalActiveIndex, internalSetActiveIndex] = React.useState(0);\n  const activeIndex = externalActiveIndex != null ? externalActiveIndex : internalActiveIndex;\n  const onNavigate = useEffectEvent(externalSetActiveIndex != null ? externalSetActiveIndex : internalSetActiveIndex);\n  const elementsRef = React.useRef([]);\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const contextValue = React.useMemo(() => ({\n    activeIndex,\n    onNavigate\n  }), [activeIndex, onNavigate]);\n  const isGrid = cols > 1;\n  function handleKeyDown(event) {\n    if (!allKeys.includes(event.key)) return;\n    let nextIndex = activeIndex;\n    const minIndex = getMinIndex(elementsRef, disabledIndices);\n    const maxIndex = getMaxIndex(elementsRef, disabledIndices);\n    const horizontalEndKey = rtl ? ARROW_LEFT : ARROW_RIGHT;\n    const horizontalStartKey = rtl ? ARROW_RIGHT : ARROW_LEFT;\n    if (isGrid) {\n      const sizes = itemSizes || Array.from({\n        length: elementsRef.current.length\n      }, () => ({\n        width: 1,\n        height: 1\n      }));\n      // To calculate movements on the grid, we use hypothetical cell indices\n      // as if every item was 1x1, then convert back to real indices.\n      const cellMap = buildCellMap(sizes, cols, dense);\n      const minGridIndex = cellMap.findIndex(index => index != null && !isDisabled(elementsRef.current, index, disabledIndices));\n      // last enabled index\n      const maxGridIndex = cellMap.reduce((foundIndex, index, cellIndex) => index != null && !isDisabled(elementsRef.current, index, disabledIndices) ? cellIndex : foundIndex, -1);\n      const maybeNextIndex = cellMap[getGridNavigatedIndex({\n        current: cellMap.map(itemIndex => itemIndex ? elementsRef.current[itemIndex] : null)\n      }, {\n        event,\n        orientation,\n        loop,\n        rtl,\n        cols,\n        // treat undefined (empty grid spaces) as disabled indices so we\n        // don't end up in them\n        disabledIndices: getCellIndices([...(disabledIndices || elementsRef.current.map((_, index) => isDisabled(elementsRef.current, index) ? index : undefined)), undefined], cellMap),\n        minIndex: minGridIndex,\n        maxIndex: maxGridIndex,\n        prevIndex: getCellIndexOfCorner(activeIndex > maxIndex ? minIndex : activeIndex, sizes, cellMap, cols,\n        // use a corner matching the edge closest to the direction we're\n        // moving in so we don't end up in the same item. Prefer\n        // top/left over bottom/right.\n        event.key === ARROW_DOWN ? 'bl' : event.key === horizontalEndKey ? 'tr' : 'tl')\n      })];\n      if (maybeNextIndex != null) {\n        nextIndex = maybeNextIndex;\n      }\n    }\n    const toEndKeys = {\n      horizontal: [horizontalEndKey],\n      vertical: [ARROW_DOWN],\n      both: [horizontalEndKey, ARROW_DOWN]\n    }[orientation];\n    const toStartKeys = {\n      horizontal: [horizontalStartKey],\n      vertical: [ARROW_UP],\n      both: [horizontalStartKey, ARROW_UP]\n    }[orientation];\n    const preventedKeys = isGrid ? allKeys : {\n      horizontal: horizontalKeys,\n      vertical: verticalKeys,\n      both: allKeys\n    }[orientation];\n    if (nextIndex === activeIndex && [...toEndKeys, ...toStartKeys].includes(event.key)) {\n      if (loop && nextIndex === maxIndex && toEndKeys.includes(event.key)) {\n        nextIndex = minIndex;\n      } else if (loop && nextIndex === minIndex && toStartKeys.includes(event.key)) {\n        nextIndex = maxIndex;\n      } else {\n        nextIndex = findNonDisabledIndex(elementsRef, {\n          startingIndex: nextIndex,\n          decrement: toStartKeys.includes(event.key),\n          disabledIndices\n        });\n      }\n    }\n    if (nextIndex !== activeIndex && !isIndexOutOfBounds(elementsRef, nextIndex)) {\n      var _elementsRef$current$;\n      event.stopPropagation();\n      if (preventedKeys.includes(event.key)) {\n        event.preventDefault();\n      }\n      onNavigate(nextIndex);\n      (_elementsRef$current$ = elementsRef.current[nextIndex]) == null || _elementsRef$current$.focus();\n    }\n  }\n  const computedProps = {\n    ...domProps,\n    ...renderElementProps,\n    ref: forwardedRef,\n    'aria-orientation': orientation === 'both' ? undefined : orientation,\n    onKeyDown(e) {\n      domProps.onKeyDown == null || domProps.onKeyDown(e);\n      renderElementProps.onKeyDown == null || renderElementProps.onKeyDown(e);\n      handleKeyDown(e);\n    }\n  };\n  return /*#__PURE__*/React.createElement(CompositeContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(FloatingList, {\n    elementsRef: elementsRef\n  }, renderJsx(render, computedProps)));\n});\n/**\n * @see https://floating-ui.com/docs/Composite\n */\nconst CompositeItem = /*#__PURE__*/React.forwardRef(function CompositeItem(props, forwardedRef) {\n  const {\n    render,\n    ...domProps\n  } = props;\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const {\n    activeIndex,\n    onNavigate\n  } = React.useContext(CompositeContext);\n  const {\n    ref,\n    index\n  } = useListItem();\n  const mergedRef = useMergeRefs([ref, forwardedRef, renderElementProps.ref]);\n  const isActive = activeIndex === index;\n  const computedProps = {\n    ...domProps,\n    ...renderElementProps,\n    ref: mergedRef,\n    tabIndex: isActive ? 0 : -1,\n    'data-active': isActive ? '' : undefined,\n    onFocus(e) {\n      domProps.onFocus == null || domProps.onFocus(e);\n      renderElementProps.onFocus == null || renderElementProps.onFocus(e);\n      onNavigate(index);\n    }\n  };\n  return renderJsx(render, computedProps);\n});\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nlet serverHandoffComplete = false;\nlet count = 0;\nconst genId = () => // Ensure the id is unique with multiple independent versions of Floating UI\n// on <React 18\n\"floating-ui-\" + Math.random().toString(36).slice(2, 6) + count++;\nfunction useFloatingId() {\n  const [id, setId] = React.useState(() => serverHandoffComplete ? genId() : undefined);\n  index(() => {\n    if (id == null) {\n      setId(genId());\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  React.useEffect(() => {\n    serverHandoffComplete = true;\n  }, []);\n  return id;\n}\nconst useReactId = SafeReact.useId;\n\n/**\n * Uses React 18's built-in `useId()` when available, or falls back to a\n * slightly less performant (requiring a double render) implementation for\n * earlier React versions.\n * @see https://floating-ui.com/docs/react-utils#useid\n */\nconst useId = useReactId || useFloatingId;\n\nlet devMessageSet;\nif (process.env.NODE_ENV !== \"production\") {\n  devMessageSet = /*#__PURE__*/new Set();\n}\nfunction warn() {\n  var _devMessageSet;\n  for (var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++) {\n    messages[_key] = arguments[_key];\n  }\n  const message = \"Floating UI: \" + messages.join(' ');\n  if (!((_devMessageSet = devMessageSet) != null && _devMessageSet.has(message))) {\n    var _devMessageSet2;\n    (_devMessageSet2 = devMessageSet) == null || _devMessageSet2.add(message);\n    console.warn(message);\n  }\n}\nfunction error() {\n  var _devMessageSet3;\n  for (var _len2 = arguments.length, messages = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    messages[_key2] = arguments[_key2];\n  }\n  const message = \"Floating UI: \" + messages.join(' ');\n  if (!((_devMessageSet3 = devMessageSet) != null && _devMessageSet3.has(message))) {\n    var _devMessageSet4;\n    (_devMessageSet4 = devMessageSet) == null || _devMessageSet4.add(message);\n    console.error(message);\n  }\n}\n\n/**\n * Renders a pointing arrow triangle.\n * @see https://floating-ui.com/docs/FloatingArrow\n */\nconst FloatingArrow = /*#__PURE__*/React.forwardRef(function FloatingArrow(props, ref) {\n  const {\n    context: {\n      placement,\n      elements: {\n        floating\n      },\n      middlewareData: {\n        arrow,\n        shift\n      }\n    },\n    width = 14,\n    height = 7,\n    tipRadius = 0,\n    strokeWidth = 0,\n    staticOffset,\n    stroke,\n    d,\n    style: {\n      transform,\n      ...restStyle\n    } = {},\n    ...rest\n  } = props;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!ref) {\n      warn('The `ref` prop is required for `FloatingArrow`.');\n    }\n  }\n  const clipPathId = useId();\n  const [isRTL, setIsRTL] = React.useState(false);\n\n  // https://github.com/floating-ui/floating-ui/issues/2932\n  index(() => {\n    if (!floating) return;\n    const isRTL = getComputedStyle(floating).direction === 'rtl';\n    if (isRTL) {\n      setIsRTL(true);\n    }\n  }, [floating]);\n  if (!floating) {\n    return null;\n  }\n  const [side, alignment] = placement.split('-');\n  const isVerticalSide = side === 'top' || side === 'bottom';\n  let computedStaticOffset = staticOffset;\n  if (isVerticalSide && shift != null && shift.x || !isVerticalSide && shift != null && shift.y) {\n    computedStaticOffset = null;\n  }\n\n  // Strokes must be double the border width, this ensures the stroke's width\n  // works as you'd expect.\n  const computedStrokeWidth = strokeWidth * 2;\n  const halfStrokeWidth = computedStrokeWidth / 2;\n  const svgX = width / 2 * (tipRadius / -8 + 1);\n  const svgY = height / 2 * tipRadius / 4;\n  const isCustomShape = !!d;\n  const yOffsetProp = computedStaticOffset && alignment === 'end' ? 'bottom' : 'top';\n  let xOffsetProp = computedStaticOffset && alignment === 'end' ? 'right' : 'left';\n  if (computedStaticOffset && isRTL) {\n    xOffsetProp = alignment === 'end' ? 'left' : 'right';\n  }\n  const arrowX = (arrow == null ? void 0 : arrow.x) != null ? computedStaticOffset || arrow.x : '';\n  const arrowY = (arrow == null ? void 0 : arrow.y) != null ? computedStaticOffset || arrow.y : '';\n  const dValue = d || 'M0,0' + (\" H\" + width) + (\" L\" + (width - svgX) + \",\" + (height - svgY)) + (\" Q\" + width / 2 + \",\" + height + \" \" + svgX + \",\" + (height - svgY)) + ' Z';\n  const rotation = {\n    top: isCustomShape ? 'rotate(180deg)' : '',\n    left: isCustomShape ? 'rotate(90deg)' : 'rotate(-90deg)',\n    bottom: isCustomShape ? '' : 'rotate(180deg)',\n    right: isCustomShape ? 'rotate(-90deg)' : 'rotate(90deg)'\n  }[side];\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({}, rest, {\n    \"aria-hidden\": true,\n    ref: ref,\n    width: isCustomShape ? width : width + computedStrokeWidth,\n    height: width,\n    viewBox: \"0 0 \" + width + \" \" + (height > width ? height : width),\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      [xOffsetProp]: arrowX,\n      [yOffsetProp]: arrowY,\n      [side]: isVerticalSide || isCustomShape ? '100%' : \"calc(100% - \" + computedStrokeWidth / 2 + \"px)\",\n      transform: [rotation, transform].filter(t => !!t).join(' '),\n      ...restStyle\n    }\n  }), computedStrokeWidth > 0 && /*#__PURE__*/React.createElement(\"path\", {\n    clipPath: \"url(#\" + clipPathId + \")\",\n    fill: \"none\",\n    stroke: stroke\n    // Account for the stroke on the fill path rendered below.\n    ,\n    strokeWidth: computedStrokeWidth + (d ? 0 : 1),\n    d: dValue\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    stroke: computedStrokeWidth && !d ? rest.fill : 'none',\n    d: dValue\n  }), /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: clipPathId\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: -halfStrokeWidth,\n    y: halfStrokeWidth * (isCustomShape ? -1 : 1),\n    width: width + computedStrokeWidth,\n    height: width\n  })));\n});\n\nfunction createPubSub() {\n  const map = new Map();\n  return {\n    emit(event, data) {\n      var _map$get;\n      (_map$get = map.get(event)) == null || _map$get.forEach(handler => handler(data));\n    },\n    on(event, listener) {\n      map.set(event, [...(map.get(event) || []), listener]);\n    },\n    off(event, listener) {\n      var _map$get2;\n      map.set(event, ((_map$get2 = map.get(event)) == null ? void 0 : _map$get2.filter(l => l !== listener)) || []);\n    }\n  };\n}\n\nconst FloatingNodeContext = /*#__PURE__*/React.createContext(null);\nconst FloatingTreeContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the parent node id for nested floating elements, if available.\n * Returns `null` for top-level floating elements.\n */\nconst useFloatingParentNodeId = () => {\n  var _React$useContext;\n  return ((_React$useContext = React.useContext(FloatingNodeContext)) == null ? void 0 : _React$useContext.id) || null;\n};\n\n/**\n * Returns the nearest floating tree context, if available.\n */\nconst useFloatingTree = () => React.useContext(FloatingTreeContext);\n\n/**\n * Registers a node into the `FloatingTree`, returning its id.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction useFloatingNodeId(customParentId) {\n  const id = useId();\n  const tree = useFloatingTree();\n  const reactParentId = useFloatingParentNodeId();\n  const parentId = customParentId || reactParentId;\n  index(() => {\n    const node = {\n      id,\n      parentId\n    };\n    tree == null || tree.addNode(node);\n    return () => {\n      tree == null || tree.removeNode(node);\n    };\n  }, [tree, id, parentId]);\n  return id;\n}\n/**\n * Provides parent node context for nested floating elements.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingNode(props) {\n  const {\n    children,\n    id\n  } = props;\n  const parentId = useFloatingParentNodeId();\n  return /*#__PURE__*/React.createElement(FloatingNodeContext.Provider, {\n    value: React.useMemo(() => ({\n      id,\n      parentId\n    }), [id, parentId])\n  }, children);\n}\n/**\n * Provides context for nested floating elements when they are not children of\n * each other on the DOM.\n * This is not necessary in all cases, except when there must be explicit communication between parent and child floating elements. It is necessary for:\n * - The `bubbles` option in the `useDismiss()` Hook\n * - Nested virtual list navigation\n * - Nested floating elements that each open on hover\n * - Custom communication between parent and child floating elements\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingTree(props) {\n  const {\n    children\n  } = props;\n  const nodesRef = React.useRef([]);\n  const addNode = React.useCallback(node => {\n    nodesRef.current = [...nodesRef.current, node];\n  }, []);\n  const removeNode = React.useCallback(node => {\n    nodesRef.current = nodesRef.current.filter(n => n !== node);\n  }, []);\n  const events = React.useState(() => createPubSub())[0];\n  return /*#__PURE__*/React.createElement(FloatingTreeContext.Provider, {\n    value: React.useMemo(() => ({\n      nodesRef,\n      addNode,\n      removeNode,\n      events\n    }), [addNode, removeNode, events])\n  }, children);\n}\n\nfunction createAttribute(name) {\n  return \"data-floating-ui-\" + name;\n}\n\nfunction useLatestRef(value) {\n  const ref = useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\nconst safePolygonIdentifier = /*#__PURE__*/createAttribute('safe-polygon');\nfunction getDelay(value, prop, pointerType) {\n  if (pointerType && !isMouseLikePointerType(pointerType)) {\n    return 0;\n  }\n  if (typeof value === 'number') {\n    return value;\n  }\n  return value == null ? void 0 : value[prop];\n}\n/**\n * Opens the floating element while hovering over the reference element, like\n * CSS `:hover`.\n * @see https://floating-ui.com/docs/useHover\n */\nfunction useHover(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    events,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    delay = 0,\n    handleClose = null,\n    mouseOnly = false,\n    restMs = 0,\n    move = true\n  } = props;\n  const tree = useFloatingTree();\n  const parentId = useFloatingParentNodeId();\n  const handleCloseRef = useLatestRef(handleClose);\n  const delayRef = useLatestRef(delay);\n  const openRef = useLatestRef(open);\n  const pointerTypeRef = React.useRef();\n  const timeoutRef = React.useRef(-1);\n  const handlerRef = React.useRef();\n  const restTimeoutRef = React.useRef(-1);\n  const blockMouseMoveRef = React.useRef(true);\n  const performedPointerEventsMutationRef = React.useRef(false);\n  const unbindMouseMoveRef = React.useRef(() => {});\n  const restTimeoutPendingRef = React.useRef(false);\n  const isHoverOpen = React.useCallback(() => {\n    var _dataRef$current$open;\n    const type = (_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type;\n    return (type == null ? void 0 : type.includes('mouse')) && type !== 'mousedown';\n  }, [dataRef]);\n\n  // When closing before opening, clear the delay timeouts to cancel it\n  // from showing.\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onOpenChange(_ref) {\n      let {\n        open\n      } = _ref;\n      if (!open) {\n        clearTimeout(timeoutRef.current);\n        clearTimeout(restTimeoutRef.current);\n        blockMouseMoveRef.current = true;\n        restTimeoutPendingRef.current = false;\n      }\n    }\n    events.on('openchange', onOpenChange);\n    return () => {\n      events.off('openchange', onOpenChange);\n    };\n  }, [enabled, events]);\n  React.useEffect(() => {\n    if (!enabled) return;\n    if (!handleCloseRef.current) return;\n    if (!open) return;\n    function onLeave(event) {\n      if (isHoverOpen()) {\n        onOpenChange(false, event, 'hover');\n      }\n    }\n    const html = getDocument(elements.floating).documentElement;\n    html.addEventListener('mouseleave', onLeave);\n    return () => {\n      html.removeEventListener('mouseleave', onLeave);\n    };\n  }, [elements.floating, open, onOpenChange, enabled, handleCloseRef, isHoverOpen]);\n  const closeWithDelay = React.useCallback(function (event, runElseBranch, reason) {\n    if (runElseBranch === void 0) {\n      runElseBranch = true;\n    }\n    if (reason === void 0) {\n      reason = 'hover';\n    }\n    const closeDelay = getDelay(delayRef.current, 'close', pointerTypeRef.current);\n    if (closeDelay && !handlerRef.current) {\n      clearTimeout(timeoutRef.current);\n      timeoutRef.current = window.setTimeout(() => onOpenChange(false, event, reason), closeDelay);\n    } else if (runElseBranch) {\n      clearTimeout(timeoutRef.current);\n      onOpenChange(false, event, reason);\n    }\n  }, [delayRef, onOpenChange]);\n  const cleanupMouseMoveHandler = useEffectEvent(() => {\n    unbindMouseMoveRef.current();\n    handlerRef.current = undefined;\n  });\n  const clearPointerEvents = useEffectEvent(() => {\n    if (performedPointerEventsMutationRef.current) {\n      const body = getDocument(elements.floating).body;\n      body.style.pointerEvents = '';\n      body.removeAttribute(safePolygonIdentifier);\n      performedPointerEventsMutationRef.current = false;\n    }\n  });\n  const isClickLikeOpenEvent = useEffectEvent(() => {\n    return dataRef.current.openEvent ? ['click', 'mousedown'].includes(dataRef.current.openEvent.type) : false;\n  });\n\n  // Registering the mouse events on the reference directly to bypass React's\n  // delegation system. If the cursor was on a disabled element and then entered\n  // the reference (no gap), `mouseenter` doesn't fire in the delegation system.\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onMouseEnter(event) {\n      clearTimeout(timeoutRef.current);\n      blockMouseMoveRef.current = false;\n      if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current) || restMs > 0 && !getDelay(delayRef.current, 'open')) {\n        return;\n      }\n      const openDelay = getDelay(delayRef.current, 'open', pointerTypeRef.current);\n      if (openDelay) {\n        timeoutRef.current = window.setTimeout(() => {\n          if (!openRef.current) {\n            onOpenChange(true, event, 'hover');\n          }\n        }, openDelay);\n      } else if (!open) {\n        onOpenChange(true, event, 'hover');\n      }\n    }\n    function onMouseLeave(event) {\n      if (isClickLikeOpenEvent()) return;\n      unbindMouseMoveRef.current();\n      const doc = getDocument(elements.floating);\n      clearTimeout(restTimeoutRef.current);\n      restTimeoutPendingRef.current = false;\n      if (handleCloseRef.current && dataRef.current.floatingContext) {\n        // Prevent clearing `onScrollMouseLeave` timeout.\n        if (!open) {\n          clearTimeout(timeoutRef.current);\n        }\n        handlerRef.current = handleCloseRef.current({\n          ...dataRef.current.floatingContext,\n          tree,\n          x: event.clientX,\n          y: event.clientY,\n          onClose() {\n            clearPointerEvents();\n            cleanupMouseMoveHandler();\n            if (!isClickLikeOpenEvent()) {\n              closeWithDelay(event, true, 'safe-polygon');\n            }\n          }\n        });\n        const handler = handlerRef.current;\n        doc.addEventListener('mousemove', handler);\n        unbindMouseMoveRef.current = () => {\n          doc.removeEventListener('mousemove', handler);\n        };\n        return;\n      }\n\n      // Allow interactivity without `safePolygon` on touch devices. With a\n      // pointer, a short close delay is an alternative, so it should work\n      // consistently.\n      const shouldClose = pointerTypeRef.current === 'touch' ? !contains(elements.floating, event.relatedTarget) : true;\n      if (shouldClose) {\n        closeWithDelay(event);\n      }\n    }\n\n    // Ensure the floating element closes after scrolling even if the pointer\n    // did not move.\n    // https://github.com/floating-ui/floating-ui/discussions/1692\n    function onScrollMouseLeave(event) {\n      if (isClickLikeOpenEvent()) return;\n      if (!dataRef.current.floatingContext) return;\n      handleCloseRef.current == null || handleCloseRef.current({\n        ...dataRef.current.floatingContext,\n        tree,\n        x: event.clientX,\n        y: event.clientY,\n        onClose() {\n          clearPointerEvents();\n          cleanupMouseMoveHandler();\n          if (!isClickLikeOpenEvent()) {\n            closeWithDelay(event);\n          }\n        }\n      })(event);\n    }\n    if (isElement(elements.domReference)) {\n      var _elements$floating;\n      const ref = elements.domReference;\n      open && ref.addEventListener('mouseleave', onScrollMouseLeave);\n      (_elements$floating = elements.floating) == null || _elements$floating.addEventListener('mouseleave', onScrollMouseLeave);\n      move && ref.addEventListener('mousemove', onMouseEnter, {\n        once: true\n      });\n      ref.addEventListener('mouseenter', onMouseEnter);\n      ref.addEventListener('mouseleave', onMouseLeave);\n      return () => {\n        var _elements$floating2;\n        open && ref.removeEventListener('mouseleave', onScrollMouseLeave);\n        (_elements$floating2 = elements.floating) == null || _elements$floating2.removeEventListener('mouseleave', onScrollMouseLeave);\n        move && ref.removeEventListener('mousemove', onMouseEnter);\n        ref.removeEventListener('mouseenter', onMouseEnter);\n        ref.removeEventListener('mouseleave', onMouseLeave);\n      };\n    }\n  }, [elements, enabled, context, mouseOnly, restMs, move, closeWithDelay, cleanupMouseMoveHandler, clearPointerEvents, onOpenChange, open, openRef, tree, delayRef, handleCloseRef, dataRef, isClickLikeOpenEvent]);\n\n  // Block pointer-events of every element other than the reference and floating\n  // while the floating element is open and has a `handleClose` handler. Also\n  // handles nested floating elements.\n  // https://github.com/floating-ui/floating-ui/issues/1722\n  index(() => {\n    var _handleCloseRef$curre;\n    if (!enabled) return;\n    if (open && (_handleCloseRef$curre = handleCloseRef.current) != null && _handleCloseRef$curre.__options.blockPointerEvents && isHoverOpen()) {\n      performedPointerEventsMutationRef.current = true;\n      const floatingEl = elements.floating;\n      if (isElement(elements.domReference) && floatingEl) {\n        var _tree$nodesRef$curren;\n        const body = getDocument(elements.floating).body;\n        body.setAttribute(safePolygonIdentifier, '');\n        const ref = elements.domReference;\n        const parentFloating = tree == null || (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null || (_tree$nodesRef$curren = _tree$nodesRef$curren.context) == null ? void 0 : _tree$nodesRef$curren.elements.floating;\n        if (parentFloating) {\n          parentFloating.style.pointerEvents = '';\n        }\n        body.style.pointerEvents = 'none';\n        ref.style.pointerEvents = 'auto';\n        floatingEl.style.pointerEvents = 'auto';\n        return () => {\n          body.style.pointerEvents = '';\n          ref.style.pointerEvents = '';\n          floatingEl.style.pointerEvents = '';\n        };\n      }\n    }\n  }, [enabled, open, parentId, elements, tree, handleCloseRef, isHoverOpen]);\n  index(() => {\n    if (!open) {\n      pointerTypeRef.current = undefined;\n      restTimeoutPendingRef.current = false;\n      cleanupMouseMoveHandler();\n      clearPointerEvents();\n    }\n  }, [open, cleanupMouseMoveHandler, clearPointerEvents]);\n  React.useEffect(() => {\n    return () => {\n      cleanupMouseMoveHandler();\n      clearTimeout(timeoutRef.current);\n      clearTimeout(restTimeoutRef.current);\n      clearPointerEvents();\n    };\n  }, [enabled, elements.domReference, cleanupMouseMoveHandler, clearPointerEvents]);\n  const reference = React.useMemo(() => {\n    function setPointerRef(event) {\n      pointerTypeRef.current = event.pointerType;\n    }\n    return {\n      onPointerDown: setPointerRef,\n      onPointerEnter: setPointerRef,\n      onMouseMove(event) {\n        const {\n          nativeEvent\n        } = event;\n        function handleMouseMove() {\n          if (!blockMouseMoveRef.current && !openRef.current) {\n            onOpenChange(true, nativeEvent, 'hover');\n          }\n        }\n        if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current)) {\n          return;\n        }\n        if (open || restMs === 0) {\n          return;\n        }\n\n        // Ignore insignificant movements to account for tremors.\n        if (restTimeoutPendingRef.current && event.movementX ** 2 + event.movementY ** 2 < 2) {\n          return;\n        }\n        clearTimeout(restTimeoutRef.current);\n        if (pointerTypeRef.current === 'touch') {\n          handleMouseMove();\n        } else {\n          restTimeoutPendingRef.current = true;\n          restTimeoutRef.current = window.setTimeout(handleMouseMove, restMs);\n        }\n      }\n    };\n  }, [mouseOnly, onOpenChange, open, openRef, restMs]);\n  const floating = React.useMemo(() => ({\n    onMouseEnter() {\n      clearTimeout(timeoutRef.current);\n    },\n    onMouseLeave(event) {\n      if (!isClickLikeOpenEvent()) {\n        closeWithDelay(event.nativeEvent, false);\n      }\n    }\n  }), [closeWithDelay, isClickLikeOpenEvent]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nconst NOOP = () => {};\nconst FloatingDelayGroupContext = /*#__PURE__*/React.createContext({\n  delay: 0,\n  initialDelay: 0,\n  timeoutMs: 0,\n  currentId: null,\n  setCurrentId: NOOP,\n  setState: NOOP,\n  isInstantPhase: false\n});\n\n/**\n * @deprecated\n * Use the return value of `useDelayGroup()` instead.\n */\nconst useDelayGroupContext = () => React.useContext(FloatingDelayGroupContext);\n/**\n * Provides context for a group of floating elements that should share a\n * `delay`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction FloatingDelayGroup(props) {\n  const {\n    children,\n    delay,\n    timeoutMs = 0\n  } = props;\n  const [state, setState] = React.useReducer((prev, next) => ({\n    ...prev,\n    ...next\n  }), {\n    delay,\n    timeoutMs,\n    initialDelay: delay,\n    currentId: null,\n    isInstantPhase: false\n  });\n  const initialCurrentIdRef = React.useRef(null);\n  const setCurrentId = React.useCallback(currentId => {\n    setState({\n      currentId\n    });\n  }, []);\n  index(() => {\n    if (state.currentId) {\n      if (initialCurrentIdRef.current === null) {\n        initialCurrentIdRef.current = state.currentId;\n      } else if (!state.isInstantPhase) {\n        setState({\n          isInstantPhase: true\n        });\n      }\n    } else {\n      if (state.isInstantPhase) {\n        setState({\n          isInstantPhase: false\n        });\n      }\n      initialCurrentIdRef.current = null;\n    }\n  }, [state.currentId, state.isInstantPhase]);\n  return /*#__PURE__*/React.createElement(FloatingDelayGroupContext.Provider, {\n    value: React.useMemo(() => ({\n      ...state,\n      setState,\n      setCurrentId\n    }), [state, setCurrentId])\n  }, children);\n}\n/**\n * Enables grouping when called inside a component that's a child of a\n * `FloatingDelayGroup`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction useDelayGroup(context, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    floatingId\n  } = context;\n  const {\n    id: optionId,\n    enabled = true\n  } = options;\n  const id = optionId != null ? optionId : floatingId;\n  const groupContext = useDelayGroupContext();\n  const {\n    currentId,\n    setCurrentId,\n    initialDelay,\n    setState,\n    timeoutMs\n  } = groupContext;\n  index(() => {\n    if (!enabled) return;\n    if (!currentId) return;\n    setState({\n      delay: {\n        open: 1,\n        close: getDelay(initialDelay, 'close')\n      }\n    });\n    if (currentId !== id) {\n      onOpenChange(false);\n    }\n  }, [enabled, id, onOpenChange, setState, currentId, initialDelay]);\n  index(() => {\n    function unset() {\n      onOpenChange(false);\n      setState({\n        delay: initialDelay,\n        currentId: null\n      });\n    }\n    if (!enabled) return;\n    if (!currentId) return;\n    if (!open && currentId === id) {\n      if (timeoutMs) {\n        const timeout = window.setTimeout(unset, timeoutMs);\n        return () => {\n          clearTimeout(timeout);\n        };\n      }\n      unset();\n    }\n  }, [enabled, open, setState, currentId, id, onOpenChange, initialDelay, timeoutMs]);\n  index(() => {\n    if (!enabled) return;\n    if (setCurrentId === NOOP || !open) return;\n    setCurrentId(id);\n  }, [enabled, open, setCurrentId, id]);\n  return groupContext;\n}\n\nlet rafId = 0;\nfunction enqueueFocus(el, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    preventScroll = false,\n    cancelPrevious = true,\n    sync = false\n  } = options;\n  cancelPrevious && cancelAnimationFrame(rafId);\n  const exec = () => el == null ? void 0 : el.focus({\n    preventScroll\n  });\n  if (sync) {\n    exec();\n  } else {\n    rafId = requestAnimationFrame(exec);\n  }\n}\n\nfunction getAncestors(nodes, id) {\n  var _nodes$find;\n  let allAncestors = [];\n  let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;\n  while (currentParentId) {\n    const currentNode = nodes.find(node => node.id === currentParentId);\n    currentParentId = currentNode == null ? void 0 : currentNode.parentId;\n    if (currentNode) {\n      allAncestors = allAncestors.concat(currentNode);\n    }\n  }\n  return allAncestors;\n}\n\nfunction getChildren(nodes, id) {\n  let allChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);\n  });\n  let currentChildren = allChildren;\n  while (currentChildren.length) {\n    currentChildren = nodes.filter(node => {\n      var _currentChildren;\n      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {\n        var _node$context2;\n        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);\n      });\n    });\n    allChildren = allChildren.concat(currentChildren);\n  }\n  return allChildren;\n}\nfunction getDeepestNode(nodes, id) {\n  let deepestNodeId;\n  let maxDepth = -1;\n  function findDeepest(nodeId, depth) {\n    if (depth > maxDepth) {\n      deepestNodeId = nodeId;\n      maxDepth = depth;\n    }\n    const children = getChildren(nodes, nodeId);\n    children.forEach(child => {\n      findDeepest(child.id, depth + 1);\n    });\n  }\n  findDeepest(id, 0);\n  return nodes.find(node => node.id === deepestNodeId);\n}\n\n// Modified to add conditional `aria-hidden` support:\n// https://github.com/theKashey/aria-hidden/blob/9220c8f4a4fd35f63bee5510a9f41a37264382d4/src/index.ts\nlet counterMap = /*#__PURE__*/new WeakMap();\nlet uncontrolledElementsSet = /*#__PURE__*/new WeakSet();\nlet markerMap = {};\nlet lockCount$1 = 0;\nconst supportsInert = () => typeof HTMLElement !== 'undefined' && 'inert' in HTMLElement.prototype;\nconst unwrapHost = node => node && (node.host || unwrapHost(node.parentNode));\nconst correctElements = (parent, targets) => targets.map(target => {\n  if (parent.contains(target)) {\n    return target;\n  }\n  const correctedTarget = unwrapHost(target);\n  if (parent.contains(correctedTarget)) {\n    return correctedTarget;\n  }\n  return null;\n}).filter(x => x != null);\nfunction applyAttributeToOthers(uncorrectedAvoidElements, body, ariaHidden, inert) {\n  const markerName = 'data-floating-ui-inert';\n  const controlAttribute = inert ? 'inert' : ariaHidden ? 'aria-hidden' : null;\n  const avoidElements = correctElements(body, uncorrectedAvoidElements);\n  const elementsToKeep = new Set();\n  const elementsToStop = new Set(avoidElements);\n  const hiddenElements = [];\n  if (!markerMap[markerName]) {\n    markerMap[markerName] = new WeakMap();\n  }\n  const markerCounter = markerMap[markerName];\n  avoidElements.forEach(keep);\n  deep(body);\n  elementsToKeep.clear();\n  function keep(el) {\n    if (!el || elementsToKeep.has(el)) {\n      return;\n    }\n    elementsToKeep.add(el);\n    el.parentNode && keep(el.parentNode);\n  }\n  function deep(parent) {\n    if (!parent || elementsToStop.has(parent)) {\n      return;\n    }\n    [].forEach.call(parent.children, node => {\n      if (getNodeName(node) === 'script') return;\n      if (elementsToKeep.has(node)) {\n        deep(node);\n      } else {\n        const attr = controlAttribute ? node.getAttribute(controlAttribute) : null;\n        const alreadyHidden = attr !== null && attr !== 'false';\n        const counterValue = (counterMap.get(node) || 0) + 1;\n        const markerValue = (markerCounter.get(node) || 0) + 1;\n        counterMap.set(node, counterValue);\n        markerCounter.set(node, markerValue);\n        hiddenElements.push(node);\n        if (counterValue === 1 && alreadyHidden) {\n          uncontrolledElementsSet.add(node);\n        }\n        if (markerValue === 1) {\n          node.setAttribute(markerName, '');\n        }\n        if (!alreadyHidden && controlAttribute) {\n          node.setAttribute(controlAttribute, 'true');\n        }\n      }\n    });\n  }\n  lockCount$1++;\n  return () => {\n    hiddenElements.forEach(element => {\n      const counterValue = (counterMap.get(element) || 0) - 1;\n      const markerValue = (markerCounter.get(element) || 0) - 1;\n      counterMap.set(element, counterValue);\n      markerCounter.set(element, markerValue);\n      if (!counterValue) {\n        if (!uncontrolledElementsSet.has(element) && controlAttribute) {\n          element.removeAttribute(controlAttribute);\n        }\n        uncontrolledElementsSet.delete(element);\n      }\n      if (!markerValue) {\n        element.removeAttribute(markerName);\n      }\n    });\n    lockCount$1--;\n    if (!lockCount$1) {\n      counterMap = new WeakMap();\n      counterMap = new WeakMap();\n      uncontrolledElementsSet = new WeakSet();\n      markerMap = {};\n    }\n  };\n}\nfunction markOthers(avoidElements, ariaHidden, inert) {\n  if (ariaHidden === void 0) {\n    ariaHidden = false;\n  }\n  if (inert === void 0) {\n    inert = false;\n  }\n  const body = getDocument(avoidElements[0]).body;\n  return applyAttributeToOthers(avoidElements.concat(Array.from(body.querySelectorAll('[aria-live]'))), body, ariaHidden, inert);\n}\n\nconst getTabbableOptions = () => ({\n  getShadowRoot: true,\n  displayCheck:\n  // JSDOM does not support the `tabbable` library. To solve this we can\n  // check if `ResizeObserver` is a real function (not polyfilled), which\n  // determines if the current environment is JSDOM-like.\n  typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'\n});\nfunction getTabbableIn(container, direction) {\n  const allTabbable = tabbable(container, getTabbableOptions());\n  if (direction === 'prev') {\n    allTabbable.reverse();\n  }\n  const activeIndex = allTabbable.indexOf(activeElement(getDocument(container)));\n  const nextTabbableElements = allTabbable.slice(activeIndex + 1);\n  return nextTabbableElements[0];\n}\nfunction getNextTabbable() {\n  return getTabbableIn(document.body, 'next');\n}\nfunction getPreviousTabbable() {\n  return getTabbableIn(document.body, 'prev');\n}\nfunction isOutsideEvent(event, container) {\n  const containerElement = container || event.currentTarget;\n  const relatedTarget = event.relatedTarget;\n  return !relatedTarget || !contains(containerElement, relatedTarget);\n}\nfunction disableFocusInside(container) {\n  const tabbableElements = tabbable(container, getTabbableOptions());\n  tabbableElements.forEach(element => {\n    element.dataset.tabindex = element.getAttribute('tabindex') || '';\n    element.setAttribute('tabindex', '-1');\n  });\n}\nfunction enableFocusInside(container) {\n  const elements = container.querySelectorAll('[data-tabindex]');\n  elements.forEach(element => {\n    const tabindex = element.dataset.tabindex;\n    delete element.dataset.tabindex;\n    if (tabindex) {\n      element.setAttribute('tabindex', tabindex);\n    } else {\n      element.removeAttribute('tabindex');\n    }\n  });\n}\n\n// See Diego Haz's Sandbox for making this logic work well on Safari/iOS:\n// https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/FocusTrap.tsx\n\nconst HIDDEN_STYLES = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'fixed',\n  whiteSpace: 'nowrap',\n  width: '1px',\n  top: 0,\n  left: 0\n};\nlet timeoutId;\nfunction setActiveElementOnTab(event) {\n  if (event.key === 'Tab') {\n    event.target;\n    clearTimeout(timeoutId);\n  }\n}\nconst FocusGuard = /*#__PURE__*/React.forwardRef(function FocusGuard(props, ref) {\n  const [role, setRole] = React.useState();\n  index(() => {\n    if (isSafari()) {\n      // Unlike other screen readers such as NVDA and JAWS, the virtual cursor\n      // on VoiceOver does trigger the onFocus event, so we can use the focus\n      // trap element. On Safari, only buttons trigger the onFocus event.\n      // NB: \"group\" role in the Sandbox no longer appears to work, must be a\n      // button role.\n      setRole('button');\n    }\n    document.addEventListener('keydown', setActiveElementOnTab);\n    return () => {\n      document.removeEventListener('keydown', setActiveElementOnTab);\n    };\n  }, []);\n  const restProps = {\n    ref,\n    tabIndex: 0,\n    // Role is only for VoiceOver\n    role,\n    'aria-hidden': role ? undefined : true,\n    [createAttribute('focus-guard')]: '',\n    style: HIDDEN_STYLES\n  };\n  return /*#__PURE__*/React.createElement(\"span\", _extends({}, props, restProps));\n});\n\nconst PortalContext = /*#__PURE__*/React.createContext(null);\nconst attr = /*#__PURE__*/createAttribute('portal');\n/**\n * @see https://floating-ui.com/docs/FloatingPortal#usefloatingportalnode\n */\nfunction useFloatingPortalNode(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    id,\n    root\n  } = props;\n  const uniqueId = useId();\n  const portalContext = usePortalContext();\n  const [portalNode, setPortalNode] = React.useState(null);\n  const portalNodeRef = React.useRef(null);\n  index(() => {\n    return () => {\n      portalNode == null || portalNode.remove();\n      // Allow the subsequent layout effects to create a new node on updates.\n      // The portal node will still be cleaned up on unmount.\n      // https://github.com/floating-ui/floating-ui/issues/2454\n      queueMicrotask(() => {\n        portalNodeRef.current = null;\n      });\n    };\n  }, [portalNode]);\n  index(() => {\n    // Wait for the uniqueId to be generated before creating the portal node in\n    // React <18 (using `useFloatingId` instead of the native `useId`).\n    // https://github.com/floating-ui/floating-ui/issues/2778\n    if (!uniqueId) return;\n    if (portalNodeRef.current) return;\n    const existingIdRoot = id ? document.getElementById(id) : null;\n    if (!existingIdRoot) return;\n    const subRoot = document.createElement('div');\n    subRoot.id = uniqueId;\n    subRoot.setAttribute(attr, '');\n    existingIdRoot.appendChild(subRoot);\n    portalNodeRef.current = subRoot;\n    setPortalNode(subRoot);\n  }, [id, uniqueId]);\n  index(() => {\n    // Wait for the root to exist before creating the portal node. The root must\n    // be stored in state, not a ref, for this to work reactively.\n    if (root === null) return;\n    if (!uniqueId) return;\n    if (portalNodeRef.current) return;\n    let container = root || (portalContext == null ? void 0 : portalContext.portalNode);\n    if (container && !isElement(container)) container = container.current;\n    container = container || document.body;\n    let idWrapper = null;\n    if (id) {\n      idWrapper = document.createElement('div');\n      idWrapper.id = id;\n      container.appendChild(idWrapper);\n    }\n    const subRoot = document.createElement('div');\n    subRoot.id = uniqueId;\n    subRoot.setAttribute(attr, '');\n    container = idWrapper || container;\n    container.appendChild(subRoot);\n    portalNodeRef.current = subRoot;\n    setPortalNode(subRoot);\n  }, [id, root, uniqueId, portalContext]);\n  return portalNode;\n}\n/**\n * Portals the floating element into a given container element — by default,\n * outside of the app root and into the body.\n * This is necessary to ensure the floating element can appear outside any\n * potential parent containers that cause clipping (such as `overflow: hidden`),\n * while retaining its location in the React tree.\n * @see https://floating-ui.com/docs/FloatingPortal\n */\nfunction FloatingPortal(props) {\n  const {\n    children,\n    id,\n    root,\n    preserveTabOrder = true\n  } = props;\n  const portalNode = useFloatingPortalNode({\n    id,\n    root\n  });\n  const [focusManagerState, setFocusManagerState] = React.useState(null);\n  const beforeOutsideRef = React.useRef(null);\n  const afterOutsideRef = React.useRef(null);\n  const beforeInsideRef = React.useRef(null);\n  const afterInsideRef = React.useRef(null);\n  const modal = focusManagerState == null ? void 0 : focusManagerState.modal;\n  const open = focusManagerState == null ? void 0 : focusManagerState.open;\n  const shouldRenderGuards =\n  // The FocusManager and therefore floating element are currently open/\n  // rendered.\n  !!focusManagerState &&\n  // Guards are only for non-modal focus management.\n  !focusManagerState.modal &&\n  // Don't render if unmount is transitioning.\n  focusManagerState.open && preserveTabOrder && !!(root || portalNode);\n\n  // https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/TabbablePortal.tsx\n  React.useEffect(() => {\n    if (!portalNode || !preserveTabOrder || modal) {\n      return;\n    }\n\n    // Make sure elements inside the portal element are tabbable only when the\n    // portal has already been focused, either by tabbing into a focus trap\n    // element outside or using the mouse.\n    function onFocus(event) {\n      if (portalNode && isOutsideEvent(event)) {\n        const focusing = event.type === 'focusin';\n        const manageFocus = focusing ? enableFocusInside : disableFocusInside;\n        manageFocus(portalNode);\n      }\n    }\n    // Listen to the event on the capture phase so they run before the focus\n    // trap elements onFocus prop is called.\n    portalNode.addEventListener('focusin', onFocus, true);\n    portalNode.addEventListener('focusout', onFocus, true);\n    return () => {\n      portalNode.removeEventListener('focusin', onFocus, true);\n      portalNode.removeEventListener('focusout', onFocus, true);\n    };\n  }, [portalNode, preserveTabOrder, modal]);\n  React.useEffect(() => {\n    if (!portalNode) return;\n    if (open) return;\n    enableFocusInside(portalNode);\n  }, [open, portalNode]);\n  return /*#__PURE__*/React.createElement(PortalContext.Provider, {\n    value: React.useMemo(() => ({\n      preserveTabOrder,\n      beforeOutsideRef,\n      afterOutsideRef,\n      beforeInsideRef,\n      afterInsideRef,\n      portalNode,\n      setFocusManagerState\n    }), [preserveTabOrder, portalNode])\n  }, shouldRenderGuards && portalNode && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"outside\",\n    ref: beforeOutsideRef,\n    onFocus: event => {\n      if (isOutsideEvent(event, portalNode)) {\n        var _beforeInsideRef$curr;\n        (_beforeInsideRef$curr = beforeInsideRef.current) == null || _beforeInsideRef$curr.focus();\n      } else {\n        const prevTabbable = getPreviousTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);\n        prevTabbable == null || prevTabbable.focus();\n      }\n    }\n  }), shouldRenderGuards && portalNode && /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-owns\": portalNode.id,\n    style: HIDDEN_STYLES\n  }), portalNode && /*#__PURE__*/ReactDOM.createPortal(children, portalNode), shouldRenderGuards && portalNode && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"outside\",\n    ref: afterOutsideRef,\n    onFocus: event => {\n      if (isOutsideEvent(event, portalNode)) {\n        var _afterInsideRef$curre;\n        (_afterInsideRef$curre = afterInsideRef.current) == null || _afterInsideRef$curre.focus();\n      } else {\n        const nextTabbable = getNextTabbable() || (focusManagerState == null ? void 0 : focusManagerState.refs.domReference.current);\n        nextTabbable == null || nextTabbable.focus();\n        (focusManagerState == null ? void 0 : focusManagerState.closeOnFocusOut) && (focusManagerState == null ? void 0 : focusManagerState.onOpenChange(false, event.nativeEvent, 'focus-out'));\n      }\n    }\n  }));\n}\nconst usePortalContext = () => React.useContext(PortalContext);\n\nconst FOCUSABLE_ATTRIBUTE = 'data-floating-ui-focusable';\nfunction getFloatingFocusElement(floatingElement) {\n  if (!floatingElement) {\n    return null;\n  }\n  // Try to find the element that has `{...getFloatingProps()}` spread on it.\n  // This indicates the floating element is acting as a positioning wrapper, and\n  // so focus should be managed on the child element with the event handlers and\n  // aria props.\n  return floatingElement.hasAttribute(FOCUSABLE_ATTRIBUTE) ? floatingElement : floatingElement.querySelector(\"[\" + FOCUSABLE_ATTRIBUTE + \"]\") || floatingElement;\n}\n\nconst LIST_LIMIT = 20;\nlet previouslyFocusedElements = [];\nfunction addPreviouslyFocusedElement(element) {\n  previouslyFocusedElements = previouslyFocusedElements.filter(el => el.isConnected);\n  let tabbableEl = element;\n  if (!tabbableEl || getNodeName(tabbableEl) === 'body') return;\n  if (!isTabbable(tabbableEl, getTabbableOptions())) {\n    const tabbableChild = tabbable(tabbableEl, getTabbableOptions())[0];\n    if (tabbableChild) {\n      tabbableEl = tabbableChild;\n    }\n  }\n  previouslyFocusedElements.push(tabbableEl);\n  if (previouslyFocusedElements.length > LIST_LIMIT) {\n    previouslyFocusedElements = previouslyFocusedElements.slice(-LIST_LIMIT);\n  }\n}\nfunction getPreviouslyFocusedElement() {\n  return previouslyFocusedElements.slice().reverse().find(el => el.isConnected);\n}\nconst VisuallyHiddenDismiss = /*#__PURE__*/React.forwardRef(function VisuallyHiddenDismiss(props, ref) {\n  return /*#__PURE__*/React.createElement(\"button\", _extends({}, props, {\n    type: \"button\",\n    ref: ref,\n    tabIndex: -1,\n    style: HIDDEN_STYLES\n  }));\n});\n/**\n * Provides focus management for the floating element.\n * @see https://floating-ui.com/docs/FloatingFocusManager\n */\nfunction FloatingFocusManager(props) {\n  const {\n    context,\n    children,\n    disabled = false,\n    order = ['content'],\n    guards: _guards = true,\n    initialFocus = 0,\n    returnFocus = true,\n    restoreFocus = false,\n    modal = true,\n    visuallyHiddenDismiss = false,\n    closeOnFocusOut = true\n  } = props;\n  const {\n    open,\n    refs,\n    nodeId,\n    onOpenChange,\n    events,\n    dataRef,\n    floatingId,\n    elements: {\n      domReference,\n      floating\n    }\n  } = context;\n  const ignoreInitialFocus = typeof initialFocus === 'number' && initialFocus < 0;\n  // If the reference is a combobox and is typeable (e.g. input/textarea),\n  // there are different focus semantics. The guards should not be rendered, but\n  // aria-hidden should be applied to all nodes still. Further, the visually\n  // hidden dismiss button should only appear at the end of the list, not the\n  // start.\n  const isUntrappedTypeableCombobox = isTypeableCombobox(domReference) && ignoreInitialFocus;\n\n  // Force the guards to be rendered if the `inert` attribute is not supported.\n  const guards = supportsInert() ? _guards : true;\n  const orderRef = useLatestRef(order);\n  const initialFocusRef = useLatestRef(initialFocus);\n  const returnFocusRef = useLatestRef(returnFocus);\n  const tree = useFloatingTree();\n  const portalContext = usePortalContext();\n  const startDismissButtonRef = React.useRef(null);\n  const endDismissButtonRef = React.useRef(null);\n  const preventReturnFocusRef = React.useRef(false);\n  const isPointerDownRef = React.useRef(false);\n  const tabbableIndexRef = React.useRef(-1);\n  const isInsidePortal = portalContext != null;\n  const floatingFocusElement = getFloatingFocusElement(floating);\n  const getTabbableContent = useEffectEvent(function (container) {\n    if (container === void 0) {\n      container = floatingFocusElement;\n    }\n    return container ? tabbable(container, getTabbableOptions()) : [];\n  });\n  const getTabbableElements = useEffectEvent(container => {\n    const content = getTabbableContent(container);\n    return orderRef.current.map(type => {\n      if (domReference && type === 'reference') {\n        return domReference;\n      }\n      if (floatingFocusElement && type === 'floating') {\n        return floatingFocusElement;\n      }\n      return content;\n    }).filter(Boolean).flat();\n  });\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!modal) return;\n    function onKeyDown(event) {\n      if (event.key === 'Tab') {\n        // The focus guards have nothing to focus, so we need to stop the event.\n        if (contains(floatingFocusElement, activeElement(getDocument(floatingFocusElement))) && getTabbableContent().length === 0 && !isUntrappedTypeableCombobox) {\n          stopEvent(event);\n        }\n        const els = getTabbableElements();\n        const target = getTarget(event);\n        if (orderRef.current[0] === 'reference' && target === domReference) {\n          stopEvent(event);\n          if (event.shiftKey) {\n            enqueueFocus(els[els.length - 1]);\n          } else {\n            enqueueFocus(els[1]);\n          }\n        }\n        if (orderRef.current[1] === 'floating' && target === floatingFocusElement && event.shiftKey) {\n          stopEvent(event);\n          enqueueFocus(els[0]);\n        }\n      }\n    }\n    const doc = getDocument(floatingFocusElement);\n    doc.addEventListener('keydown', onKeyDown);\n    return () => {\n      doc.removeEventListener('keydown', onKeyDown);\n    };\n  }, [disabled, domReference, floatingFocusElement, modal, orderRef, isUntrappedTypeableCombobox, getTabbableContent, getTabbableElements]);\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!floating) return;\n    function handleFocusIn(event) {\n      const target = getTarget(event);\n      const tabbableContent = getTabbableContent();\n      const tabbableIndex = tabbableContent.indexOf(target);\n      if (tabbableIndex !== -1) {\n        tabbableIndexRef.current = tabbableIndex;\n      }\n    }\n    floating.addEventListener('focusin', handleFocusIn);\n    return () => {\n      floating.removeEventListener('focusin', handleFocusIn);\n    };\n  }, [disabled, floating, getTabbableContent]);\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!closeOnFocusOut) return;\n\n    // In Safari, buttons lose focus when pressing them.\n    function handlePointerDown() {\n      isPointerDownRef.current = true;\n      setTimeout(() => {\n        isPointerDownRef.current = false;\n      });\n    }\n    function handleFocusOutside(event) {\n      const relatedTarget = event.relatedTarget;\n      queueMicrotask(() => {\n        const movedToUnrelatedNode = !(contains(domReference, relatedTarget) || contains(floating, relatedTarget) || contains(relatedTarget, floating) || contains(portalContext == null ? void 0 : portalContext.portalNode, relatedTarget) || relatedTarget != null && relatedTarget.hasAttribute(createAttribute('focus-guard')) || tree && (getChildren(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context, _node$context2;\n          return contains((_node$context = node.context) == null ? void 0 : _node$context.elements.floating, relatedTarget) || contains((_node$context2 = node.context) == null ? void 0 : _node$context2.elements.domReference, relatedTarget);\n        }) || getAncestors(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context3, _node$context4;\n          return ((_node$context3 = node.context) == null ? void 0 : _node$context3.elements.floating) === relatedTarget || ((_node$context4 = node.context) == null ? void 0 : _node$context4.elements.domReference) === relatedTarget;\n        })));\n\n        // Restore focus to the previous tabbable element index to prevent\n        // focus from being lost outside the floating tree.\n        if (restoreFocus && movedToUnrelatedNode && activeElement(getDocument(floatingFocusElement)) === getDocument(floatingFocusElement).body) {\n          // Let `FloatingPortal` effect knows that focus is still inside the\n          // floating tree.\n          if (isHTMLElement(floatingFocusElement)) {\n            floatingFocusElement.focus();\n          }\n          const prevTabbableIndex = tabbableIndexRef.current;\n          const tabbableContent = getTabbableContent();\n          const nodeToFocus = tabbableContent[prevTabbableIndex] || tabbableContent[tabbableContent.length - 1] || floatingFocusElement;\n          if (isHTMLElement(nodeToFocus)) {\n            nodeToFocus.focus();\n          }\n        }\n\n        // Focus did not move inside the floating tree, and there are no tabbable\n        // portal guards to handle closing.\n        if ((isUntrappedTypeableCombobox ? true : !modal) && relatedTarget && movedToUnrelatedNode && !isPointerDownRef.current &&\n        // Fix React 18 Strict Mode returnFocus due to double rendering.\n        relatedTarget !== getPreviouslyFocusedElement()) {\n          preventReturnFocusRef.current = true;\n          onOpenChange(false, event, 'focus-out');\n        }\n      });\n    }\n    if (floating && isHTMLElement(domReference)) {\n      domReference.addEventListener('focusout', handleFocusOutside);\n      domReference.addEventListener('pointerdown', handlePointerDown);\n      floating.addEventListener('focusout', handleFocusOutside);\n      return () => {\n        domReference.removeEventListener('focusout', handleFocusOutside);\n        domReference.removeEventListener('pointerdown', handlePointerDown);\n        floating.removeEventListener('focusout', handleFocusOutside);\n      };\n    }\n  }, [disabled, domReference, floating, floatingFocusElement, modal, nodeId, tree, portalContext, onOpenChange, closeOnFocusOut, restoreFocus, getTabbableContent, isUntrappedTypeableCombobox]);\n  React.useEffect(() => {\n    var _portalContext$portal;\n    if (disabled) return;\n\n    // Don't hide portals nested within the parent portal.\n    const portalNodes = Array.from((portalContext == null || (_portalContext$portal = portalContext.portalNode) == null ? void 0 : _portalContext$portal.querySelectorAll(\"[\" + createAttribute('portal') + \"]\")) || []);\n    if (floating) {\n      const insideElements = [floating, ...portalNodes, startDismissButtonRef.current, endDismissButtonRef.current, orderRef.current.includes('reference') || isUntrappedTypeableCombobox ? domReference : null].filter(x => x != null);\n      const cleanup = modal || isUntrappedTypeableCombobox ? markOthers(insideElements, guards, !guards) : markOthers(insideElements);\n      return () => {\n        cleanup();\n      };\n    }\n  }, [disabled, domReference, floating, modal, orderRef, portalContext, isUntrappedTypeableCombobox, guards]);\n  index(() => {\n    if (disabled || !isHTMLElement(floatingFocusElement)) return;\n    const doc = getDocument(floatingFocusElement);\n    const previouslyFocusedElement = activeElement(doc);\n\n    // Wait for any layout effect state setters to execute to set `tabIndex`.\n    queueMicrotask(() => {\n      const focusableElements = getTabbableElements(floatingFocusElement);\n      const initialFocusValue = initialFocusRef.current;\n      const elToFocus = (typeof initialFocusValue === 'number' ? focusableElements[initialFocusValue] : initialFocusValue.current) || floatingFocusElement;\n      const focusAlreadyInsideFloatingEl = contains(floatingFocusElement, previouslyFocusedElement);\n      if (!ignoreInitialFocus && !focusAlreadyInsideFloatingEl && open) {\n        enqueueFocus(elToFocus, {\n          preventScroll: elToFocus === floatingFocusElement\n        });\n      }\n    });\n  }, [disabled, open, floatingFocusElement, ignoreInitialFocus, getTabbableElements, initialFocusRef]);\n  index(() => {\n    if (disabled || !floatingFocusElement) return;\n    let preventReturnFocusScroll = false;\n    const doc = getDocument(floatingFocusElement);\n    const previouslyFocusedElement = activeElement(doc);\n    const contextData = dataRef.current;\n    let openEvent = contextData.openEvent;\n    addPreviouslyFocusedElement(previouslyFocusedElement);\n\n    // Dismissing via outside press should always ignore `returnFocus` to\n    // prevent unwanted scrolling.\n    function onOpenChange(_ref) {\n      let {\n        open,\n        reason,\n        event,\n        nested\n      } = _ref;\n      if (open) {\n        openEvent = event;\n      }\n      if (reason === 'escape-key' && refs.domReference.current) {\n        addPreviouslyFocusedElement(refs.domReference.current);\n      }\n      if (reason === 'hover' && event.type === 'mouseleave') {\n        preventReturnFocusRef.current = true;\n      }\n      if (reason !== 'outside-press') return;\n      if (nested) {\n        preventReturnFocusRef.current = false;\n        preventReturnFocusScroll = true;\n      } else {\n        preventReturnFocusRef.current = !(isVirtualClick(event) || isVirtualPointerEvent(event));\n      }\n    }\n    events.on('openchange', onOpenChange);\n    const fallbackEl = doc.createElement('span');\n    fallbackEl.setAttribute('tabindex', '-1');\n    fallbackEl.setAttribute('aria-hidden', 'true');\n    Object.assign(fallbackEl.style, HIDDEN_STYLES);\n    if (isInsidePortal && domReference) {\n      domReference.insertAdjacentElement('afterend', fallbackEl);\n    }\n    function getReturnElement() {\n      if (typeof returnFocusRef.current === 'boolean') {\n        return getPreviouslyFocusedElement() || fallbackEl;\n      }\n      return returnFocusRef.current.current || fallbackEl;\n    }\n    return () => {\n      events.off('openchange', onOpenChange);\n      const activeEl = activeElement(doc);\n      const isFocusInsideFloatingTree = contains(floating, activeEl) || tree && getChildren(tree.nodesRef.current, nodeId).some(node => {\n        var _node$context5;\n        return contains((_node$context5 = node.context) == null ? void 0 : _node$context5.elements.floating, activeEl);\n      });\n      const shouldFocusReference = isFocusInsideFloatingTree || openEvent && ['click', 'mousedown'].includes(openEvent.type);\n      if (shouldFocusReference && refs.domReference.current) {\n        addPreviouslyFocusedElement(refs.domReference.current);\n      }\n      const returnElement = getReturnElement();\n      queueMicrotask(() => {\n        if (\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        returnFocusRef.current && !preventReturnFocusRef.current && isHTMLElement(returnElement) && (\n        // If the focus moved somewhere else after mount, avoid returning focus\n        // since it likely entered a different element which should be\n        // respected: https://github.com/floating-ui/floating-ui/issues/2607\n        returnElement !== activeEl && activeEl !== doc.body ? isFocusInsideFloatingTree : true)) {\n          returnElement.focus({\n            preventScroll: preventReturnFocusScroll\n          });\n        }\n        fallbackEl.remove();\n      });\n    };\n  }, [disabled, floating, floatingFocusElement, returnFocusRef, dataRef, refs, events, tree, nodeId, isInsidePortal, domReference]);\n  React.useEffect(() => {\n    // The `returnFocus` cleanup behavior is inside a microtask; ensure we\n    // wait for it to complete before resetting the flag.\n    queueMicrotask(() => {\n      preventReturnFocusRef.current = false;\n    });\n  }, [disabled]);\n\n  // Synchronize the `context` & `modal` value to the FloatingPortal context.\n  // It will decide whether or not it needs to render its own guards.\n  index(() => {\n    if (disabled) return;\n    if (!portalContext) return;\n    portalContext.setFocusManagerState({\n      modal,\n      closeOnFocusOut,\n      open,\n      onOpenChange,\n      refs\n    });\n    return () => {\n      portalContext.setFocusManagerState(null);\n    };\n  }, [disabled, portalContext, modal, open, onOpenChange, refs, closeOnFocusOut]);\n  index(() => {\n    if (disabled) return;\n    if (!floatingFocusElement) return;\n    if (typeof MutationObserver !== 'function') return;\n    if (ignoreInitialFocus) return;\n    const handleMutation = () => {\n      const tabIndex = floatingFocusElement.getAttribute('tabindex');\n      const tabbableContent = getTabbableContent();\n      const activeEl = activeElement(getDocument(floating));\n      const tabbableIndex = tabbableContent.indexOf(activeEl);\n      if (tabbableIndex !== -1) {\n        tabbableIndexRef.current = tabbableIndex;\n      }\n      if (orderRef.current.includes('floating') || activeEl !== refs.domReference.current && tabbableContent.length === 0) {\n        if (tabIndex !== '0') {\n          floatingFocusElement.setAttribute('tabindex', '0');\n        }\n      } else if (tabIndex !== '-1') {\n        floatingFocusElement.setAttribute('tabindex', '-1');\n      }\n    };\n    handleMutation();\n    const observer = new MutationObserver(handleMutation);\n    observer.observe(floatingFocusElement, {\n      childList: true,\n      subtree: true,\n      attributes: true\n    });\n    return () => {\n      observer.disconnect();\n    };\n  }, [disabled, floating, floatingFocusElement, refs, orderRef, getTabbableContent, ignoreInitialFocus]);\n  function renderDismissButton(location) {\n    if (disabled || !visuallyHiddenDismiss || !modal) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(VisuallyHiddenDismiss, {\n      ref: location === 'start' ? startDismissButtonRef : endDismissButtonRef,\n      onClick: event => onOpenChange(false, event.nativeEvent)\n    }, typeof visuallyHiddenDismiss === 'string' ? visuallyHiddenDismiss : 'Dismiss');\n  }\n  const shouldRenderGuards = !disabled && guards && (modal ? !isUntrappedTypeableCombobox : true) && (isInsidePortal || modal);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, shouldRenderGuards && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"inside\",\n    ref: portalContext == null ? void 0 : portalContext.beforeInsideRef,\n    onFocus: event => {\n      if (modal) {\n        const els = getTabbableElements();\n        enqueueFocus(order[0] === 'reference' ? els[0] : els[els.length - 1]);\n      } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n        preventReturnFocusRef.current = false;\n        if (isOutsideEvent(event, portalContext.portalNode)) {\n          const nextTabbable = getNextTabbable() || domReference;\n          nextTabbable == null || nextTabbable.focus();\n        } else {\n          var _portalContext$before;\n          (_portalContext$before = portalContext.beforeOutsideRef.current) == null || _portalContext$before.focus();\n        }\n      }\n    }\n  }), !isUntrappedTypeableCombobox && renderDismissButton('start'), children, renderDismissButton('end'), shouldRenderGuards && /*#__PURE__*/React.createElement(FocusGuard, {\n    \"data-type\": \"inside\",\n    ref: portalContext == null ? void 0 : portalContext.afterInsideRef,\n    onFocus: event => {\n      if (modal) {\n        enqueueFocus(getTabbableElements()[0]);\n      } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n        if (closeOnFocusOut) {\n          preventReturnFocusRef.current = true;\n        }\n        if (isOutsideEvent(event, portalContext.portalNode)) {\n          const prevTabbable = getPreviousTabbable() || domReference;\n          prevTabbable == null || prevTabbable.focus();\n        } else {\n          var _portalContext$afterO;\n          (_portalContext$afterO = portalContext.afterOutsideRef.current) == null || _portalContext$afterO.focus();\n        }\n      }\n    }\n  }));\n}\n\nlet lockCount = 0;\nfunction enableScrollLock() {\n  const isIOS = /iP(hone|ad|od)|iOS/.test(getPlatform());\n  const bodyStyle = document.body.style;\n  // RTL <body> scrollbar\n  const scrollbarX = Math.round(document.documentElement.getBoundingClientRect().left) + document.documentElement.scrollLeft;\n  const paddingProp = scrollbarX ? 'paddingLeft' : 'paddingRight';\n  const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n  const scrollX = bodyStyle.left ? parseFloat(bodyStyle.left) : window.scrollX;\n  const scrollY = bodyStyle.top ? parseFloat(bodyStyle.top) : window.scrollY;\n  bodyStyle.overflow = 'hidden';\n  if (scrollbarWidth) {\n    bodyStyle[paddingProp] = scrollbarWidth + \"px\";\n  }\n\n  // Only iOS doesn't respect `overflow: hidden` on document.body, and this\n  // technique has fewer side effects.\n  if (isIOS) {\n    var _window$visualViewpor, _window$visualViewpor2;\n    // iOS 12 does not support `visualViewport`.\n    const offsetLeft = ((_window$visualViewpor = window.visualViewport) == null ? void 0 : _window$visualViewpor.offsetLeft) || 0;\n    const offsetTop = ((_window$visualViewpor2 = window.visualViewport) == null ? void 0 : _window$visualViewpor2.offsetTop) || 0;\n    Object.assign(bodyStyle, {\n      position: 'fixed',\n      top: -(scrollY - Math.floor(offsetTop)) + \"px\",\n      left: -(scrollX - Math.floor(offsetLeft)) + \"px\",\n      right: '0'\n    });\n  }\n  return () => {\n    Object.assign(bodyStyle, {\n      overflow: '',\n      [paddingProp]: ''\n    });\n    if (isIOS) {\n      Object.assign(bodyStyle, {\n        position: '',\n        top: '',\n        left: '',\n        right: ''\n      });\n      window.scrollTo(scrollX, scrollY);\n    }\n  };\n}\nlet cleanup = () => {};\n\n/**\n * Provides base styling for a fixed overlay element to dim content or block\n * pointer events behind a floating element.\n * It's a regular `<div>`, so it can be styled via any CSS solution you prefer.\n * @see https://floating-ui.com/docs/FloatingOverlay\n */\nconst FloatingOverlay = /*#__PURE__*/React.forwardRef(function FloatingOverlay(props, ref) {\n  const {\n    lockScroll = false,\n    ...rest\n  } = props;\n  index(() => {\n    if (!lockScroll) return;\n    lockCount++;\n    if (lockCount === 1) {\n      cleanup = enableScrollLock();\n    }\n    return () => {\n      lockCount--;\n      if (lockCount === 0) {\n        cleanup();\n      }\n    };\n  }, [lockScroll]);\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref\n  }, rest, {\n    style: {\n      position: 'fixed',\n      overflow: 'auto',\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      ...rest.style\n    }\n  }));\n});\n\nfunction isButtonTarget(event) {\n  return isHTMLElement(event.target) && event.target.tagName === 'BUTTON';\n}\nfunction isSpaceIgnored(element) {\n  return isTypeableElement(element);\n}\n/**\n * Opens or closes the floating element when clicking the reference element.\n * @see https://floating-ui.com/docs/useClick\n */\nfunction useClick(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    elements: {\n      domReference\n    }\n  } = context;\n  const {\n    enabled = true,\n    event: eventOption = 'click',\n    toggle = true,\n    ignoreMouse = false,\n    keyboardHandlers = true,\n    stickIfOpen = true\n  } = props;\n  const pointerTypeRef = React.useRef();\n  const didKeyDownRef = React.useRef(false);\n  const reference = React.useMemo(() => ({\n    onPointerDown(event) {\n      pointerTypeRef.current = event.pointerType;\n    },\n    onMouseDown(event) {\n      const pointerType = pointerTypeRef.current;\n\n      // Ignore all buttons except for the \"main\" button.\n      // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/button\n      if (event.button !== 0) return;\n      if (eventOption === 'click') return;\n      if (isMouseLikePointerType(pointerType, true) && ignoreMouse) return;\n      if (open && toggle && (dataRef.current.openEvent && stickIfOpen ? dataRef.current.openEvent.type === 'mousedown' : true)) {\n        onOpenChange(false, event.nativeEvent, 'click');\n      } else {\n        // Prevent stealing focus from the floating element\n        event.preventDefault();\n        onOpenChange(true, event.nativeEvent, 'click');\n      }\n    },\n    onClick(event) {\n      const pointerType = pointerTypeRef.current;\n      if (eventOption === 'mousedown' && pointerTypeRef.current) {\n        pointerTypeRef.current = undefined;\n        return;\n      }\n      if (isMouseLikePointerType(pointerType, true) && ignoreMouse) return;\n      if (open && toggle && (dataRef.current.openEvent && stickIfOpen ? dataRef.current.openEvent.type === 'click' : true)) {\n        onOpenChange(false, event.nativeEvent, 'click');\n      } else {\n        onOpenChange(true, event.nativeEvent, 'click');\n      }\n    },\n    onKeyDown(event) {\n      pointerTypeRef.current = undefined;\n      if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event)) {\n        return;\n      }\n      if (event.key === ' ' && !isSpaceIgnored(domReference)) {\n        // Prevent scrolling\n        event.preventDefault();\n        didKeyDownRef.current = true;\n      }\n      if (event.key === 'Enter') {\n        if (open && toggle) {\n          onOpenChange(false, event.nativeEvent, 'click');\n        } else {\n          onOpenChange(true, event.nativeEvent, 'click');\n        }\n      }\n    },\n    onKeyUp(event) {\n      if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event) || isSpaceIgnored(domReference)) {\n        return;\n      }\n      if (event.key === ' ' && didKeyDownRef.current) {\n        didKeyDownRef.current = false;\n        if (open && toggle) {\n          onOpenChange(false, event.nativeEvent, 'click');\n        } else {\n          onOpenChange(true, event.nativeEvent, 'click');\n        }\n      }\n    }\n  }), [dataRef, domReference, eventOption, ignoreMouse, keyboardHandlers, onOpenChange, open, stickIfOpen, toggle]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nfunction createVirtualElement(domElement, data) {\n  let offsetX = null;\n  let offsetY = null;\n  let isAutoUpdateEvent = false;\n  return {\n    contextElement: domElement || undefined,\n    getBoundingClientRect() {\n      var _data$dataRef$current;\n      const domRect = (domElement == null ? void 0 : domElement.getBoundingClientRect()) || {\n        width: 0,\n        height: 0,\n        x: 0,\n        y: 0\n      };\n      const isXAxis = data.axis === 'x' || data.axis === 'both';\n      const isYAxis = data.axis === 'y' || data.axis === 'both';\n      const canTrackCursorOnAutoUpdate = ['mouseenter', 'mousemove'].includes(((_data$dataRef$current = data.dataRef.current.openEvent) == null ? void 0 : _data$dataRef$current.type) || '') && data.pointerType !== 'touch';\n      let width = domRect.width;\n      let height = domRect.height;\n      let x = domRect.x;\n      let y = domRect.y;\n      if (offsetX == null && data.x && isXAxis) {\n        offsetX = domRect.x - data.x;\n      }\n      if (offsetY == null && data.y && isYAxis) {\n        offsetY = domRect.y - data.y;\n      }\n      x -= offsetX || 0;\n      y -= offsetY || 0;\n      width = 0;\n      height = 0;\n      if (!isAutoUpdateEvent || canTrackCursorOnAutoUpdate) {\n        width = data.axis === 'y' ? domRect.width : 0;\n        height = data.axis === 'x' ? domRect.height : 0;\n        x = isXAxis && data.x != null ? data.x : x;\n        y = isYAxis && data.y != null ? data.y : y;\n      } else if (isAutoUpdateEvent && !canTrackCursorOnAutoUpdate) {\n        height = data.axis === 'x' ? domRect.height : height;\n        width = data.axis === 'y' ? domRect.width : width;\n      }\n      isAutoUpdateEvent = true;\n      return {\n        width,\n        height,\n        x,\n        y,\n        top: y,\n        right: x + width,\n        bottom: y + height,\n        left: x\n      };\n    }\n  };\n}\nfunction isMouseBasedEvent(event) {\n  return event != null && event.clientX != null;\n}\n/**\n * Positions the floating element relative to a client point (in the viewport),\n * such as the mouse position. By default, it follows the mouse cursor.\n * @see https://floating-ui.com/docs/useClientPoint\n */\nfunction useClientPoint(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    dataRef,\n    elements: {\n      floating,\n      domReference\n    },\n    refs\n  } = context;\n  const {\n    enabled = true,\n    axis = 'both',\n    x = null,\n    y = null\n  } = props;\n  const initialRef = React.useRef(false);\n  const cleanupListenerRef = React.useRef(null);\n  const [pointerType, setPointerType] = React.useState();\n  const [reactive, setReactive] = React.useState([]);\n  const setReference = useEffectEvent((x, y) => {\n    if (initialRef.current) return;\n\n    // Prevent setting if the open event was not a mouse-like one\n    // (e.g. focus to open, then hover over the reference element).\n    // Only apply if the event exists.\n    if (dataRef.current.openEvent && !isMouseBasedEvent(dataRef.current.openEvent)) {\n      return;\n    }\n    refs.setPositionReference(createVirtualElement(domReference, {\n      x,\n      y,\n      axis,\n      dataRef,\n      pointerType\n    }));\n  });\n  const handleReferenceEnterOrMove = useEffectEvent(event => {\n    if (x != null || y != null) return;\n    if (!open) {\n      setReference(event.clientX, event.clientY);\n    } else if (!cleanupListenerRef.current) {\n      // If there's no cleanup, there's no listener, but we want to ensure\n      // we add the listener if the cursor landed on the floating element and\n      // then back on the reference (i.e. it's interactive).\n      setReactive([]);\n    }\n  });\n\n  // If the pointer is a mouse-like pointer, we want to continue following the\n  // mouse even if the floating element is transitioning out. On touch\n  // devices, this is undesirable because the floating element will move to\n  // the dismissal touch point.\n  const openCheck = isMouseLikePointerType(pointerType) ? floating : open;\n  const addListener = React.useCallback(() => {\n    // Explicitly specified `x`/`y` coordinates shouldn't add a listener.\n    if (!openCheck || !enabled || x != null || y != null) return;\n    const win = getWindow(floating);\n    function handleMouseMove(event) {\n      const target = getTarget(event);\n      if (!contains(floating, target)) {\n        setReference(event.clientX, event.clientY);\n      } else {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      }\n    }\n    if (!dataRef.current.openEvent || isMouseBasedEvent(dataRef.current.openEvent)) {\n      win.addEventListener('mousemove', handleMouseMove);\n      const cleanup = () => {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      };\n      cleanupListenerRef.current = cleanup;\n      return cleanup;\n    }\n    refs.setPositionReference(domReference);\n  }, [openCheck, enabled, x, y, floating, dataRef, refs, domReference, setReference]);\n  React.useEffect(() => {\n    return addListener();\n  }, [addListener, reactive]);\n  React.useEffect(() => {\n    if (enabled && !floating) {\n      initialRef.current = false;\n    }\n  }, [enabled, floating]);\n  React.useEffect(() => {\n    if (!enabled && open) {\n      initialRef.current = true;\n    }\n  }, [enabled, open]);\n  index(() => {\n    if (enabled && (x != null || y != null)) {\n      initialRef.current = false;\n      setReference(x, y);\n    }\n  }, [enabled, x, y, setReference]);\n  const reference = React.useMemo(() => {\n    function setPointerTypeRef(_ref) {\n      let {\n        pointerType\n      } = _ref;\n      setPointerType(pointerType);\n    }\n    return {\n      onPointerDown: setPointerTypeRef,\n      onPointerEnter: setPointerTypeRef,\n      onMouseMove: handleReferenceEnterOrMove,\n      onMouseEnter: handleReferenceEnterOrMove\n    };\n  }, [handleReferenceEnterOrMove]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nconst bubbleHandlerKeys = {\n  pointerdown: 'onPointerDown',\n  mousedown: 'onMouseDown',\n  click: 'onClick'\n};\nconst captureHandlerKeys = {\n  pointerdown: 'onPointerDownCapture',\n  mousedown: 'onMouseDownCapture',\n  click: 'onClickCapture'\n};\nconst normalizeProp = normalizable => {\n  var _normalizable$escapeK, _normalizable$outside;\n  return {\n    escapeKey: typeof normalizable === 'boolean' ? normalizable : (_normalizable$escapeK = normalizable == null ? void 0 : normalizable.escapeKey) != null ? _normalizable$escapeK : false,\n    outsidePress: typeof normalizable === 'boolean' ? normalizable : (_normalizable$outside = normalizable == null ? void 0 : normalizable.outsidePress) != null ? _normalizable$outside : true\n  };\n};\n/**\n * Closes the floating element when a dismissal is requested — by default, when\n * the user presses the `escape` key or outside of the floating element.\n * @see https://floating-ui.com/docs/useDismiss\n */\nfunction useDismiss(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    elements,\n    dataRef\n  } = context;\n  const {\n    enabled = true,\n    escapeKey = true,\n    outsidePress: unstable_outsidePress = true,\n    outsidePressEvent = 'pointerdown',\n    referencePress = false,\n    referencePressEvent = 'pointerdown',\n    ancestorScroll = false,\n    bubbles,\n    capture\n  } = props;\n  const tree = useFloatingTree();\n  const outsidePressFn = useEffectEvent(typeof unstable_outsidePress === 'function' ? unstable_outsidePress : () => false);\n  const outsidePress = typeof unstable_outsidePress === 'function' ? outsidePressFn : unstable_outsidePress;\n  const insideReactTreeRef = React.useRef(false);\n  const endedOrStartedInsideRef = React.useRef(false);\n  const {\n    escapeKey: escapeKeyBubbles,\n    outsidePress: outsidePressBubbles\n  } = normalizeProp(bubbles);\n  const {\n    escapeKey: escapeKeyCapture,\n    outsidePress: outsidePressCapture\n  } = normalizeProp(capture);\n  const isComposingRef = React.useRef(false);\n  const closeOnEscapeKeyDown = useEffectEvent(event => {\n    var _dataRef$current$floa;\n    if (!open || !enabled || !escapeKey || event.key !== 'Escape') {\n      return;\n    }\n\n    // Wait until IME is settled. Pressing `Escape` while composing should\n    // close the compose menu, but not the floating element.\n    if (isComposingRef.current) {\n      return;\n    }\n    const nodeId = (_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.nodeId;\n    const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];\n    if (!escapeKeyBubbles) {\n      event.stopPropagation();\n      if (children.length > 0) {\n        let shouldDismiss = true;\n        children.forEach(child => {\n          var _child$context;\n          if ((_child$context = child.context) != null && _child$context.open && !child.context.dataRef.current.__escapeKeyBubbles) {\n            shouldDismiss = false;\n            return;\n          }\n        });\n        if (!shouldDismiss) {\n          return;\n        }\n      }\n    }\n    onOpenChange(false, isReactEvent(event) ? event.nativeEvent : event, 'escape-key');\n  });\n  const closeOnEscapeKeyDownCapture = useEffectEvent(event => {\n    var _getTarget2;\n    const callback = () => {\n      var _getTarget;\n      closeOnEscapeKeyDown(event);\n      (_getTarget = getTarget(event)) == null || _getTarget.removeEventListener('keydown', callback);\n    };\n    (_getTarget2 = getTarget(event)) == null || _getTarget2.addEventListener('keydown', callback);\n  });\n  const closeOnPressOutside = useEffectEvent(event => {\n    var _dataRef$current$floa2;\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = insideReactTreeRef.current;\n    insideReactTreeRef.current = false;\n\n    // When click outside is lazy (`click` event), handle dragging.\n    // Don't close if:\n    // - The click started inside the floating element.\n    // - The click ended inside the floating element.\n    const endedOrStartedInside = endedOrStartedInsideRef.current;\n    endedOrStartedInsideRef.current = false;\n    if (outsidePressEvent === 'click' && endedOrStartedInside) {\n      return;\n    }\n    if (insideReactTree) {\n      return;\n    }\n    if (typeof outsidePress === 'function' && !outsidePress(event)) {\n      return;\n    }\n    const target = getTarget(event);\n    const inertSelector = \"[\" + createAttribute('inert') + \"]\";\n    const markers = getDocument(elements.floating).querySelectorAll(inertSelector);\n    let targetRootAncestor = isElement(target) ? target : null;\n    while (targetRootAncestor && !isLastTraversableNode(targetRootAncestor)) {\n      const nextParent = getParentNode(targetRootAncestor);\n      if (isLastTraversableNode(nextParent) || !isElement(nextParent)) {\n        break;\n      }\n      targetRootAncestor = nextParent;\n    }\n\n    // Check if the click occurred on a third-party element injected after the\n    // floating element rendered.\n    if (markers.length && isElement(target) && !isRootElement(target) &&\n    // Clicked on a direct ancestor (e.g. FloatingOverlay).\n    !contains(target, elements.floating) &&\n    // If the target root element contains none of the markers, then the\n    // element was injected after the floating element rendered.\n    Array.from(markers).every(marker => !contains(targetRootAncestor, marker))) {\n      return;\n    }\n\n    // Check if the click occurred on the scrollbar\n    if (isHTMLElement(target) && floating) {\n      // In Firefox, `target.scrollWidth > target.clientWidth` for inline\n      // elements.\n      const canScrollX = target.clientWidth > 0 && target.scrollWidth > target.clientWidth;\n      const canScrollY = target.clientHeight > 0 && target.scrollHeight > target.clientHeight;\n      let xCond = canScrollY && event.offsetX > target.clientWidth;\n\n      // In some browsers it is possible to change the <body> (or window)\n      // scrollbar to the left side, but is very rare and is difficult to\n      // check for. Plus, for modal dialogs with backdrops, it is more\n      // important that the backdrop is checked but not so much the window.\n      if (canScrollY) {\n        const isRTL = getComputedStyle(target).direction === 'rtl';\n        if (isRTL) {\n          xCond = event.offsetX <= target.offsetWidth - target.clientWidth;\n        }\n      }\n      if (xCond || canScrollX && event.offsetY > target.clientHeight) {\n        return;\n      }\n    }\n    const nodeId = (_dataRef$current$floa2 = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa2.nodeId;\n    const targetIsInsideChildren = tree && getChildren(tree.nodesRef.current, nodeId).some(node => {\n      var _node$context;\n      return isEventTargetWithin(event, (_node$context = node.context) == null ? void 0 : _node$context.elements.floating);\n    });\n    if (isEventTargetWithin(event, elements.floating) || isEventTargetWithin(event, elements.domReference) || targetIsInsideChildren) {\n      return;\n    }\n    const children = tree ? getChildren(tree.nodesRef.current, nodeId) : [];\n    if (children.length > 0) {\n      let shouldDismiss = true;\n      children.forEach(child => {\n        var _child$context2;\n        if ((_child$context2 = child.context) != null && _child$context2.open && !child.context.dataRef.current.__outsidePressBubbles) {\n          shouldDismiss = false;\n          return;\n        }\n      });\n      if (!shouldDismiss) {\n        return;\n      }\n    }\n    onOpenChange(false, event, 'outside-press');\n  });\n  const closeOnPressOutsideCapture = useEffectEvent(event => {\n    var _getTarget4;\n    const callback = () => {\n      var _getTarget3;\n      closeOnPressOutside(event);\n      (_getTarget3 = getTarget(event)) == null || _getTarget3.removeEventListener(outsidePressEvent, callback);\n    };\n    (_getTarget4 = getTarget(event)) == null || _getTarget4.addEventListener(outsidePressEvent, callback);\n  });\n  React.useEffect(() => {\n    if (!open || !enabled) {\n      return;\n    }\n    dataRef.current.__escapeKeyBubbles = escapeKeyBubbles;\n    dataRef.current.__outsidePressBubbles = outsidePressBubbles;\n    let compositionTimeout = -1;\n    function onScroll(event) {\n      onOpenChange(false, event, 'ancestor-scroll');\n    }\n    function handleCompositionStart() {\n      window.clearTimeout(compositionTimeout);\n      isComposingRef.current = true;\n    }\n    function handleCompositionEnd() {\n      // Safari fires `compositionend` before `keydown`, so we need to wait\n      // until the next tick to set `isComposing` to `false`.\n      // https://bugs.webkit.org/show_bug.cgi?id=165004\n      compositionTimeout = window.setTimeout(() => {\n        isComposingRef.current = false;\n      },\n      // 0ms or 1ms don't work in Safari. 5ms appears to consistently work.\n      // Only apply to WebKit for the test to remain 0ms.\n      isWebKit() ? 5 : 0);\n    }\n    const doc = getDocument(elements.floating);\n    if (escapeKey) {\n      doc.addEventListener('keydown', escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);\n      doc.addEventListener('compositionstart', handleCompositionStart);\n      doc.addEventListener('compositionend', handleCompositionEnd);\n    }\n    outsidePress && doc.addEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);\n    let ancestors = [];\n    if (ancestorScroll) {\n      if (isElement(elements.domReference)) {\n        ancestors = getOverflowAncestors(elements.domReference);\n      }\n      if (isElement(elements.floating)) {\n        ancestors = ancestors.concat(getOverflowAncestors(elements.floating));\n      }\n      if (!isElement(elements.reference) && elements.reference && elements.reference.contextElement) {\n        ancestors = ancestors.concat(getOverflowAncestors(elements.reference.contextElement));\n      }\n    }\n\n    // Ignore the visual viewport for scrolling dismissal (allow pinch-zoom)\n    ancestors = ancestors.filter(ancestor => {\n      var _doc$defaultView;\n      return ancestor !== ((_doc$defaultView = doc.defaultView) == null ? void 0 : _doc$defaultView.visualViewport);\n    });\n    ancestors.forEach(ancestor => {\n      ancestor.addEventListener('scroll', onScroll, {\n        passive: true\n      });\n    });\n    return () => {\n      if (escapeKey) {\n        doc.removeEventListener('keydown', escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);\n        doc.removeEventListener('compositionstart', handleCompositionStart);\n        doc.removeEventListener('compositionend', handleCompositionEnd);\n      }\n      outsidePress && doc.removeEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);\n      ancestors.forEach(ancestor => {\n        ancestor.removeEventListener('scroll', onScroll);\n      });\n      window.clearTimeout(compositionTimeout);\n    };\n  }, [dataRef, elements, escapeKey, outsidePress, outsidePressEvent, open, onOpenChange, ancestorScroll, enabled, escapeKeyBubbles, outsidePressBubbles, closeOnEscapeKeyDown, escapeKeyCapture, closeOnEscapeKeyDownCapture, closeOnPressOutside, outsidePressCapture, closeOnPressOutsideCapture]);\n  React.useEffect(() => {\n    insideReactTreeRef.current = false;\n  }, [outsidePress, outsidePressEvent]);\n  const reference = React.useMemo(() => ({\n    onKeyDown: closeOnEscapeKeyDown,\n    [bubbleHandlerKeys[referencePressEvent]]: event => {\n      if (referencePress) {\n        onOpenChange(false, event.nativeEvent, 'reference-press');\n      }\n    }\n  }), [closeOnEscapeKeyDown, onOpenChange, referencePress, referencePressEvent]);\n  const floating = React.useMemo(() => ({\n    onKeyDown: closeOnEscapeKeyDown,\n    onMouseDown() {\n      endedOrStartedInsideRef.current = true;\n    },\n    onMouseUp() {\n      endedOrStartedInsideRef.current = true;\n    },\n    [captureHandlerKeys[outsidePressEvent]]: () => {\n      insideReactTreeRef.current = true;\n    }\n  }), [closeOnEscapeKeyDown, outsidePressEvent]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nfunction useFloatingRootContext(options) {\n  const {\n    open = false,\n    onOpenChange: onOpenChangeProp,\n    elements: elementsProp\n  } = options;\n  const floatingId = useId();\n  const dataRef = React.useRef({});\n  const [events] = React.useState(() => createPubSub());\n  const nested = useFloatingParentNodeId() != null;\n  if (process.env.NODE_ENV !== \"production\") {\n    const optionDomReference = elementsProp.reference;\n    if (optionDomReference && !isElement(optionDomReference)) {\n      error('Cannot pass a virtual element to the `elements.reference` option,', 'as it must be a real DOM element. Use `refs.setPositionReference()`', 'instead.');\n    }\n  }\n  const [positionReference, setPositionReference] = React.useState(elementsProp.reference);\n  const onOpenChange = useEffectEvent((open, event, reason) => {\n    dataRef.current.openEvent = open ? event : undefined;\n    events.emit('openchange', {\n      open,\n      event,\n      reason,\n      nested\n    });\n    onOpenChangeProp == null || onOpenChangeProp(open, event, reason);\n  });\n  const refs = React.useMemo(() => ({\n    setPositionReference\n  }), []);\n  const elements = React.useMemo(() => ({\n    reference: positionReference || elementsProp.reference || null,\n    floating: elementsProp.floating || null,\n    domReference: elementsProp.reference\n  }), [positionReference, elementsProp.reference, elementsProp.floating]);\n  return React.useMemo(() => ({\n    dataRef,\n    open,\n    onOpenChange,\n    elements,\n    events,\n    floatingId,\n    refs\n  }), [open, onOpenChange, elements, events, floatingId, refs]);\n}\n\n/**\n * Provides data to position a floating element and context to add interactions.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    nodeId\n  } = options;\n  const internalRootContext = useFloatingRootContext({\n    ...options,\n    elements: {\n      reference: null,\n      floating: null,\n      ...options.elements\n    }\n  });\n  const rootContext = options.rootContext || internalRootContext;\n  const computedElements = rootContext.elements;\n  const [_domReference, setDomReference] = React.useState(null);\n  const [positionReference, _setPositionReference] = React.useState(null);\n  const optionDomReference = computedElements == null ? void 0 : computedElements.domReference;\n  const domReference = optionDomReference || _domReference;\n  const domReferenceRef = React.useRef(null);\n  const tree = useFloatingTree();\n  index(() => {\n    if (domReference) {\n      domReferenceRef.current = domReference;\n    }\n  }, [domReference]);\n  const position = useFloating$1({\n    ...options,\n    elements: {\n      ...computedElements,\n      ...(positionReference && {\n        reference: positionReference\n      })\n    }\n  });\n  const setPositionReference = React.useCallback(node => {\n    const computedPositionReference = isElement(node) ? {\n      getBoundingClientRect: () => node.getBoundingClientRect(),\n      contextElement: node\n    } : node;\n    // Store the positionReference in state if the DOM reference is specified externally via the\n    // `elements.reference` option. This ensures that it won't be overridden on future renders.\n    _setPositionReference(computedPositionReference);\n    position.refs.setReference(computedPositionReference);\n  }, [position.refs]);\n  const setReference = React.useCallback(node => {\n    if (isElement(node) || node === null) {\n      domReferenceRef.current = node;\n      setDomReference(node);\n    }\n\n    // Backwards-compatibility for passing a virtual element to `reference`\n    // after it has set the DOM reference.\n    if (isElement(position.refs.reference.current) || position.refs.reference.current === null ||\n    // Don't allow setting virtual elements using the old technique back to\n    // `null` to support `positionReference` + an unstable `reference`\n    // callback ref.\n    node !== null && !isElement(node)) {\n      position.refs.setReference(node);\n    }\n  }, [position.refs]);\n  const refs = React.useMemo(() => ({\n    ...position.refs,\n    setReference,\n    setPositionReference,\n    domReference: domReferenceRef\n  }), [position.refs, setReference, setPositionReference]);\n  const elements = React.useMemo(() => ({\n    ...position.elements,\n    domReference: domReference\n  }), [position.elements, domReference]);\n  const context = React.useMemo(() => ({\n    ...position,\n    ...rootContext,\n    refs,\n    elements,\n    nodeId\n  }), [position, refs, elements, nodeId, rootContext]);\n  index(() => {\n    rootContext.dataRef.current.floatingContext = context;\n    const node = tree == null ? void 0 : tree.nodesRef.current.find(node => node.id === nodeId);\n    if (node) {\n      node.context = context;\n    }\n  });\n  return React.useMemo(() => ({\n    ...position,\n    context,\n    refs,\n    elements\n  }), [position, refs, elements, context]);\n}\n\n/**\n * Opens the floating element while the reference element has focus, like CSS\n * `:focus`.\n * @see https://floating-ui.com/docs/useFocus\n */\nfunction useFocus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    events,\n    dataRef,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    visibleOnly = true\n  } = props;\n  const blockFocusRef = React.useRef(false);\n  const timeoutRef = React.useRef();\n  const keyboardModalityRef = React.useRef(true);\n  React.useEffect(() => {\n    if (!enabled) return;\n    const win = getWindow(elements.domReference);\n\n    // If the reference was focused and the user left the tab/window, and the\n    // floating element was not open, the focus should be blocked when they\n    // return to the tab/window.\n    function onBlur() {\n      if (!open && isHTMLElement(elements.domReference) && elements.domReference === activeElement(getDocument(elements.domReference))) {\n        blockFocusRef.current = true;\n      }\n    }\n    function onKeyDown() {\n      keyboardModalityRef.current = true;\n    }\n    win.addEventListener('blur', onBlur);\n    win.addEventListener('keydown', onKeyDown, true);\n    return () => {\n      win.removeEventListener('blur', onBlur);\n      win.removeEventListener('keydown', onKeyDown, true);\n    };\n  }, [elements.domReference, open, enabled]);\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onOpenChange(_ref) {\n      let {\n        reason\n      } = _ref;\n      if (reason === 'reference-press' || reason === 'escape-key') {\n        blockFocusRef.current = true;\n      }\n    }\n    events.on('openchange', onOpenChange);\n    return () => {\n      events.off('openchange', onOpenChange);\n    };\n  }, [events, enabled]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(timeoutRef.current);\n    };\n  }, []);\n  const reference = React.useMemo(() => ({\n    onPointerDown(event) {\n      if (isVirtualPointerEvent(event.nativeEvent)) return;\n      keyboardModalityRef.current = false;\n    },\n    onMouseLeave() {\n      blockFocusRef.current = false;\n    },\n    onFocus(event) {\n      if (blockFocusRef.current) return;\n      const target = getTarget(event.nativeEvent);\n      if (visibleOnly && isElement(target)) {\n        try {\n          // Mac Safari unreliably matches `:focus-visible` on the reference\n          // if focus was outside the page initially - use the fallback\n          // instead.\n          if (isSafari() && isMac()) throw Error();\n          if (!target.matches(':focus-visible')) return;\n        } catch (e) {\n          // Old browsers will throw an error when using `:focus-visible`.\n          if (!keyboardModalityRef.current && !isTypeableElement(target)) {\n            return;\n          }\n        }\n      }\n      onOpenChange(true, event.nativeEvent, 'focus');\n    },\n    onBlur(event) {\n      blockFocusRef.current = false;\n      const relatedTarget = event.relatedTarget;\n      const nativeEvent = event.nativeEvent;\n\n      // Hit the non-modal focus management portal guard. Focus will be\n      // moved into the floating element immediately after.\n      const movedToFocusGuard = isElement(relatedTarget) && relatedTarget.hasAttribute(createAttribute('focus-guard')) && relatedTarget.getAttribute('data-type') === 'outside';\n\n      // Wait for the window blur listener to fire.\n      timeoutRef.current = window.setTimeout(() => {\n        var _dataRef$current$floa;\n        const activeEl = activeElement(elements.domReference ? elements.domReference.ownerDocument : document);\n\n        // Focus left the page, keep it open.\n        if (!relatedTarget && activeEl === elements.domReference) return;\n\n        // When focusing the reference element (e.g. regular click), then\n        // clicking into the floating element, prevent it from hiding.\n        // Note: it must be focusable, e.g. `tabindex=\"-1\"`.\n        // We can not rely on relatedTarget to point to the correct element\n        // as it will only point to the shadow host of the newly focused element\n        // and not the element that actually has received focus if it is located\n        // inside a shadow root.\n        if (contains((_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.refs.floating.current, activeEl) || contains(elements.domReference, activeEl) || movedToFocusGuard) {\n          return;\n        }\n        onOpenChange(false, nativeEvent, 'focus');\n      });\n    }\n  }), [dataRef, elements.domReference, onOpenChange, visibleOnly]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nconst ACTIVE_KEY = 'active';\nconst SELECTED_KEY = 'selected';\nfunction mergeProps(userProps, propsList, elementKey) {\n  const map = new Map();\n  const isItem = elementKey === 'item';\n  let domUserProps = userProps;\n  if (isItem && userProps) {\n    const {\n      [ACTIVE_KEY]: _,\n      [SELECTED_KEY]: __,\n      ...validProps\n    } = userProps;\n    domUserProps = validProps;\n  }\n  return {\n    ...(elementKey === 'floating' && {\n      tabIndex: -1,\n      [FOCUSABLE_ATTRIBUTE]: ''\n    }),\n    ...domUserProps,\n    ...propsList.map(value => {\n      const propsOrGetProps = value ? value[elementKey] : null;\n      if (typeof propsOrGetProps === 'function') {\n        return userProps ? propsOrGetProps(userProps) : null;\n      }\n      return propsOrGetProps;\n    }).concat(userProps).reduce((acc, props) => {\n      if (!props) {\n        return acc;\n      }\n      Object.entries(props).forEach(_ref => {\n        let [key, value] = _ref;\n        if (isItem && [ACTIVE_KEY, SELECTED_KEY].includes(key)) {\n          return;\n        }\n        if (key.indexOf('on') === 0) {\n          if (!map.has(key)) {\n            map.set(key, []);\n          }\n          if (typeof value === 'function') {\n            var _map$get;\n            (_map$get = map.get(key)) == null || _map$get.push(value);\n            acc[key] = function () {\n              var _map$get2;\n              for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n              }\n              return (_map$get2 = map.get(key)) == null ? void 0 : _map$get2.map(fn => fn(...args)).find(val => val !== undefined);\n            };\n          }\n        } else {\n          acc[key] = value;\n        }\n      });\n      return acc;\n    }, {})\n  };\n}\n/**\n * Merges an array of interaction hooks' props into prop getters, allowing\n * event handler functions to be composed together without overwriting one\n * another.\n * @see https://floating-ui.com/docs/useInteractions\n */\nfunction useInteractions(propsList) {\n  if (propsList === void 0) {\n    propsList = [];\n  }\n  const referenceDeps = propsList.map(key => key == null ? void 0 : key.reference);\n  const floatingDeps = propsList.map(key => key == null ? void 0 : key.floating);\n  const itemDeps = propsList.map(key => key == null ? void 0 : key.item);\n  const getReferenceProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'reference'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  referenceDeps);\n  const getFloatingProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'floating'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  floatingDeps);\n  const getItemProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'item'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  itemDeps);\n  return React.useMemo(() => ({\n    getReferenceProps,\n    getFloatingProps,\n    getItemProps\n  }), [getReferenceProps, getFloatingProps, getItemProps]);\n}\n\nlet isPreventScrollSupported = false;\nfunction doSwitch(orientation, vertical, horizontal) {\n  switch (orientation) {\n    case 'vertical':\n      return vertical;\n    case 'horizontal':\n      return horizontal;\n    default:\n      return vertical || horizontal;\n  }\n}\nfunction isMainOrientationKey(key, orientation) {\n  const vertical = key === ARROW_UP || key === ARROW_DOWN;\n  const horizontal = key === ARROW_LEFT || key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isMainOrientationToEndKey(key, orientation, rtl) {\n  const vertical = key === ARROW_DOWN;\n  const horizontal = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal) || key === 'Enter' || key === ' ' || key === '';\n}\nfunction isCrossOrientationOpenKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  const horizontal = key === ARROW_DOWN;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isCrossOrientationCloseKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_RIGHT : key === ARROW_LEFT;\n  const horizontal = key === ARROW_UP;\n  return doSwitch(orientation, vertical, horizontal);\n}\n/**\n * Adds arrow key-based navigation of a list of items, either using real DOM\n * focus or virtual focus.\n * @see https://floating-ui.com/docs/useListNavigation\n */\nfunction useListNavigation(context, props) {\n  const {\n    open,\n    onOpenChange,\n    elements\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onNavigate: unstable_onNavigate = () => {},\n    enabled = true,\n    selectedIndex = null,\n    allowEscape = false,\n    loop = false,\n    nested = false,\n    rtl = false,\n    virtual = false,\n    focusItemOnOpen = 'auto',\n    focusItemOnHover = true,\n    openOnArrowKeyDown = true,\n    disabledIndices = undefined,\n    orientation = 'vertical',\n    cols = 1,\n    scrollItemIntoView = true,\n    virtualItemRef,\n    itemSizes,\n    dense = false\n  } = props;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (allowEscape) {\n      if (!loop) {\n        warn('`useListNavigation` looping must be enabled to allow escaping.');\n      }\n      if (!virtual) {\n        warn('`useListNavigation` must be virtual to allow escaping.');\n      }\n    }\n    if (orientation === 'vertical' && cols > 1) {\n      warn('In grid list navigation mode (`cols` > 1), the `orientation` should', 'be either \"horizontal\" or \"both\".');\n    }\n  }\n  const floatingFocusElement = getFloatingFocusElement(elements.floating);\n  const floatingFocusElementRef = useLatestRef(floatingFocusElement);\n  const parentId = useFloatingParentNodeId();\n  const tree = useFloatingTree();\n  const onNavigate = useEffectEvent(unstable_onNavigate);\n  const typeableComboboxReference = isTypeableCombobox(elements.domReference);\n  const focusItemOnOpenRef = React.useRef(focusItemOnOpen);\n  const indexRef = React.useRef(selectedIndex != null ? selectedIndex : -1);\n  const keyRef = React.useRef(null);\n  const isPointerModalityRef = React.useRef(true);\n  const previousOnNavigateRef = React.useRef(onNavigate);\n  const previousMountedRef = React.useRef(!!elements.floating);\n  const previousOpenRef = React.useRef(open);\n  const forceSyncFocus = React.useRef(false);\n  const forceScrollIntoViewRef = React.useRef(false);\n  const disabledIndicesRef = useLatestRef(disabledIndices);\n  const latestOpenRef = useLatestRef(open);\n  const scrollItemIntoViewRef = useLatestRef(scrollItemIntoView);\n  const selectedIndexRef = useLatestRef(selectedIndex);\n  const [activeId, setActiveId] = React.useState();\n  const [virtualId, setVirtualId] = React.useState();\n  const focusItem = useEffectEvent(function (listRef, indexRef, forceScrollIntoView) {\n    if (forceScrollIntoView === void 0) {\n      forceScrollIntoView = false;\n    }\n    function runFocus(item) {\n      if (virtual) {\n        setActiveId(item.id);\n        tree == null || tree.events.emit('virtualfocus', item);\n        if (virtualItemRef) {\n          virtualItemRef.current = item;\n        }\n      } else {\n        enqueueFocus(item, {\n          preventScroll: true,\n          // Mac Safari does not move the virtual cursor unless the focus call\n          // is sync. However, for the very first focus call, we need to wait\n          // for the position to be ready in order to prevent unwanted\n          // scrolling. This means the virtual cursor will not move to the first\n          // item when first opening the floating element, but will on\n          // subsequent calls. `preventScroll` is supported in modern Safari,\n          // so we can use that instead.\n          // iOS Safari must be async or the first item will not be focused.\n          sync: isMac() && isSafari() ? isPreventScrollSupported || forceSyncFocus.current : false\n        });\n      }\n    }\n    const initialItem = listRef.current[indexRef.current];\n    if (initialItem) {\n      runFocus(initialItem);\n    }\n    requestAnimationFrame(() => {\n      const waitedItem = listRef.current[indexRef.current] || initialItem;\n      if (!waitedItem) return;\n      if (!initialItem) {\n        runFocus(waitedItem);\n      }\n      const scrollIntoViewOptions = scrollItemIntoViewRef.current;\n      const shouldScrollIntoView = scrollIntoViewOptions && item && (forceScrollIntoView || !isPointerModalityRef.current);\n      if (shouldScrollIntoView) {\n        // JSDOM doesn't support `.scrollIntoView()` but it's widely supported\n        // by all browsers.\n        waitedItem.scrollIntoView == null || waitedItem.scrollIntoView(typeof scrollIntoViewOptions === 'boolean' ? {\n          block: 'nearest',\n          inline: 'nearest'\n        } : scrollIntoViewOptions);\n      }\n    });\n  });\n  index(() => {\n    document.createElement('div').focus({\n      get preventScroll() {\n        isPreventScrollSupported = true;\n        return false;\n      }\n    });\n  }, []);\n\n  // Sync `selectedIndex` to be the `activeIndex` upon opening the floating\n  // element. Also, reset `activeIndex` upon closing the floating element.\n  index(() => {\n    if (!enabled) return;\n    if (open && elements.floating) {\n      if (focusItemOnOpenRef.current && selectedIndex != null) {\n        // Regardless of the pointer modality, we want to ensure the selected\n        // item comes into view when the floating element is opened.\n        forceScrollIntoViewRef.current = true;\n        indexRef.current = selectedIndex;\n        onNavigate(selectedIndex);\n      }\n    } else if (previousMountedRef.current) {\n      // Since the user can specify `onNavigate` conditionally\n      // (onNavigate: open ? setActiveIndex : setSelectedIndex),\n      // we store and call the previous function.\n      indexRef.current = -1;\n      previousOnNavigateRef.current(null);\n    }\n  }, [enabled, open, elements.floating, selectedIndex, onNavigate]);\n\n  // Sync `activeIndex` to be the focused item while the floating element is\n  // open.\n  index(() => {\n    if (!enabled) return;\n    if (open && elements.floating) {\n      if (activeIndex == null) {\n        forceSyncFocus.current = false;\n        if (selectedIndexRef.current != null) {\n          return;\n        }\n\n        // Reset while the floating element was open (e.g. the list changed).\n        if (previousMountedRef.current) {\n          indexRef.current = -1;\n          focusItem(listRef, indexRef);\n        }\n\n        // Initial sync.\n        if ((!previousOpenRef.current || !previousMountedRef.current) && focusItemOnOpenRef.current && (keyRef.current != null || focusItemOnOpenRef.current === true && keyRef.current == null)) {\n          let runs = 0;\n          const waitForListPopulated = () => {\n            if (listRef.current[0] == null) {\n              // Avoid letting the browser paint if possible on the first try,\n              // otherwise use rAF. Don't try more than twice, since something\n              // is wrong otherwise.\n              if (runs < 2) {\n                const scheduler = runs ? requestAnimationFrame : queueMicrotask;\n                scheduler(waitForListPopulated);\n              }\n              runs++;\n            } else {\n              indexRef.current = keyRef.current == null || isMainOrientationToEndKey(keyRef.current, orientation, rtl) || nested ? getMinIndex(listRef, disabledIndicesRef.current) : getMaxIndex(listRef, disabledIndicesRef.current);\n              keyRef.current = null;\n              onNavigate(indexRef.current);\n            }\n          };\n          waitForListPopulated();\n        }\n      } else if (!isIndexOutOfBounds(listRef, activeIndex)) {\n        indexRef.current = activeIndex;\n        focusItem(listRef, indexRef, forceScrollIntoViewRef.current);\n        forceScrollIntoViewRef.current = false;\n      }\n    }\n  }, [enabled, open, elements.floating, activeIndex, selectedIndexRef, nested, listRef, orientation, rtl, onNavigate, focusItem, disabledIndicesRef]);\n\n  // Ensure the parent floating element has focus when a nested child closes\n  // to allow arrow key navigation to work after the pointer leaves the child.\n  index(() => {\n    var _nodes$find;\n    if (!enabled || elements.floating || !tree || virtual || !previousMountedRef.current) {\n      return;\n    }\n    const nodes = tree.nodesRef.current;\n    const parent = (_nodes$find = nodes.find(node => node.id === parentId)) == null || (_nodes$find = _nodes$find.context) == null ? void 0 : _nodes$find.elements.floating;\n    const activeEl = activeElement(getDocument(elements.floating));\n    const treeContainsActiveEl = nodes.some(node => node.context && contains(node.context.elements.floating, activeEl));\n    if (parent && !treeContainsActiveEl && isPointerModalityRef.current) {\n      parent.focus({\n        preventScroll: true\n      });\n    }\n  }, [enabled, elements.floating, tree, parentId, virtual]);\n  index(() => {\n    if (!enabled) return;\n    if (!tree) return;\n    if (!virtual) return;\n    if (parentId) return;\n    function handleVirtualFocus(item) {\n      setVirtualId(item.id);\n      if (virtualItemRef) {\n        virtualItemRef.current = item;\n      }\n    }\n    tree.events.on('virtualfocus', handleVirtualFocus);\n    return () => {\n      tree.events.off('virtualfocus', handleVirtualFocus);\n    };\n  }, [enabled, tree, virtual, parentId, virtualItemRef]);\n  index(() => {\n    previousOnNavigateRef.current = onNavigate;\n    previousMountedRef.current = !!elements.floating;\n  });\n  index(() => {\n    if (!open) {\n      keyRef.current = null;\n    }\n  }, [open]);\n  index(() => {\n    previousOpenRef.current = open;\n  }, [open]);\n  const hasActiveIndex = activeIndex != null;\n  const item = React.useMemo(() => {\n    function syncCurrentTarget(currentTarget) {\n      if (!open) return;\n      const index = listRef.current.indexOf(currentTarget);\n      if (index !== -1) {\n        onNavigate(index);\n      }\n    }\n    const props = {\n      onFocus(_ref) {\n        let {\n          currentTarget\n        } = _ref;\n        syncCurrentTarget(currentTarget);\n      },\n      onClick: _ref2 => {\n        let {\n          currentTarget\n        } = _ref2;\n        return currentTarget.focus({\n          preventScroll: true\n        });\n      },\n      // Safari\n      ...(focusItemOnHover && {\n        onMouseMove(_ref3) {\n          let {\n            currentTarget\n          } = _ref3;\n          syncCurrentTarget(currentTarget);\n        },\n        onPointerLeave(_ref4) {\n          let {\n            pointerType\n          } = _ref4;\n          if (!isPointerModalityRef.current || pointerType === 'touch') {\n            return;\n          }\n          indexRef.current = -1;\n          focusItem(listRef, indexRef);\n          onNavigate(null);\n          if (!virtual) {\n            enqueueFocus(floatingFocusElementRef.current, {\n              preventScroll: true\n            });\n          }\n        }\n      })\n    };\n    return props;\n  }, [open, floatingFocusElementRef, focusItem, focusItemOnHover, listRef, onNavigate, virtual]);\n  const commonOnKeyDown = useEffectEvent(event => {\n    isPointerModalityRef.current = false;\n    forceSyncFocus.current = true;\n\n    // When composing a character, Chrome fires ArrowDown twice. Firefox/Safari\n    // don't appear to suffer from this. `event.isComposing` is avoided due to\n    // Safari not supporting it properly (although it's not needed in the first\n    // place for Safari, just avoiding any possible issues).\n    if (event.which === 229) {\n      return;\n    }\n\n    // If the floating element is animating out, ignore navigation. Otherwise,\n    // the `activeIndex` gets set to 0 despite not being open so the next time\n    // the user ArrowDowns, the first item won't be focused.\n    if (!latestOpenRef.current && event.currentTarget === floatingFocusElementRef.current) {\n      return;\n    }\n    if (nested && isCrossOrientationCloseKey(event.key, orientation, rtl)) {\n      stopEvent(event);\n      onOpenChange(false, event.nativeEvent, 'list-navigation');\n      if (isHTMLElement(elements.domReference)) {\n        if (virtual) {\n          tree == null || tree.events.emit('virtualfocus', elements.domReference);\n        } else {\n          elements.domReference.focus();\n        }\n      }\n      return;\n    }\n    const currentIndex = indexRef.current;\n    const minIndex = getMinIndex(listRef, disabledIndices);\n    const maxIndex = getMaxIndex(listRef, disabledIndices);\n    if (!typeableComboboxReference) {\n      if (event.key === 'Home') {\n        stopEvent(event);\n        indexRef.current = minIndex;\n        onNavigate(indexRef.current);\n      }\n      if (event.key === 'End') {\n        stopEvent(event);\n        indexRef.current = maxIndex;\n        onNavigate(indexRef.current);\n      }\n    }\n\n    // Grid navigation.\n    if (cols > 1) {\n      const sizes = itemSizes || Array.from({\n        length: listRef.current.length\n      }, () => ({\n        width: 1,\n        height: 1\n      }));\n      // To calculate movements on the grid, we use hypothetical cell indices\n      // as if every item was 1x1, then convert back to real indices.\n      const cellMap = buildCellMap(sizes, cols, dense);\n      const minGridIndex = cellMap.findIndex(index => index != null && !isDisabled(listRef.current, index, disabledIndices));\n      // last enabled index\n      const maxGridIndex = cellMap.reduce((foundIndex, index, cellIndex) => index != null && !isDisabled(listRef.current, index, disabledIndices) ? cellIndex : foundIndex, -1);\n      const index = cellMap[getGridNavigatedIndex({\n        current: cellMap.map(itemIndex => itemIndex != null ? listRef.current[itemIndex] : null)\n      }, {\n        event,\n        orientation,\n        loop,\n        rtl,\n        cols,\n        // treat undefined (empty grid spaces) as disabled indices so we\n        // don't end up in them\n        disabledIndices: getCellIndices([...(disabledIndices || listRef.current.map((_, index) => isDisabled(listRef.current, index) ? index : undefined)), undefined], cellMap),\n        minIndex: minGridIndex,\n        maxIndex: maxGridIndex,\n        prevIndex: getCellIndexOfCorner(indexRef.current > maxIndex ? minIndex : indexRef.current, sizes, cellMap, cols,\n        // use a corner matching the edge closest to the direction\n        // we're moving in so we don't end up in the same item. Prefer\n        // top/left over bottom/right.\n        event.key === ARROW_DOWN ? 'bl' : event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT) ? 'tr' : 'tl'),\n        stopEvent: true\n      })];\n      if (index != null) {\n        indexRef.current = index;\n        onNavigate(indexRef.current);\n      }\n      if (orientation === 'both') {\n        return;\n      }\n    }\n    if (isMainOrientationKey(event.key, orientation)) {\n      stopEvent(event);\n\n      // Reset the index if no item is focused.\n      if (open && !virtual && activeElement(event.currentTarget.ownerDocument) === event.currentTarget) {\n        indexRef.current = isMainOrientationToEndKey(event.key, orientation, rtl) ? minIndex : maxIndex;\n        onNavigate(indexRef.current);\n        return;\n      }\n      if (isMainOrientationToEndKey(event.key, orientation, rtl)) {\n        if (loop) {\n          indexRef.current = currentIndex >= maxIndex ? allowEscape && currentIndex !== listRef.current.length ? -1 : minIndex : findNonDisabledIndex(listRef, {\n            startingIndex: currentIndex,\n            disabledIndices\n          });\n        } else {\n          indexRef.current = Math.min(maxIndex, findNonDisabledIndex(listRef, {\n            startingIndex: currentIndex,\n            disabledIndices\n          }));\n        }\n      } else {\n        if (loop) {\n          indexRef.current = currentIndex <= minIndex ? allowEscape && currentIndex !== -1 ? listRef.current.length : maxIndex : findNonDisabledIndex(listRef, {\n            startingIndex: currentIndex,\n            decrement: true,\n            disabledIndices\n          });\n        } else {\n          indexRef.current = Math.max(minIndex, findNonDisabledIndex(listRef, {\n            startingIndex: currentIndex,\n            decrement: true,\n            disabledIndices\n          }));\n        }\n      }\n      if (isIndexOutOfBounds(listRef, indexRef.current)) {\n        onNavigate(null);\n      } else {\n        onNavigate(indexRef.current);\n      }\n    }\n  });\n  const ariaActiveDescendantProp = React.useMemo(() => {\n    return virtual && open && hasActiveIndex && {\n      'aria-activedescendant': virtualId || activeId\n    };\n  }, [virtual, open, hasActiveIndex, virtualId, activeId]);\n  const floating = React.useMemo(() => {\n    return {\n      'aria-orientation': orientation === 'both' ? undefined : orientation,\n      ...(!isTypeableCombobox(elements.domReference) && ariaActiveDescendantProp),\n      onKeyDown: commonOnKeyDown,\n      onPointerMove() {\n        isPointerModalityRef.current = true;\n      }\n    };\n  }, [ariaActiveDescendantProp, commonOnKeyDown, elements.domReference, orientation]);\n  const reference = React.useMemo(() => {\n    function checkVirtualMouse(event) {\n      if (focusItemOnOpen === 'auto' && isVirtualClick(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    function checkVirtualPointer(event) {\n      // `pointerdown` fires first, reset the state then perform the checks.\n      focusItemOnOpenRef.current = focusItemOnOpen;\n      if (focusItemOnOpen === 'auto' && isVirtualPointerEvent(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    return {\n      ...ariaActiveDescendantProp,\n      onKeyDown(event) {\n        isPointerModalityRef.current = false;\n        const isArrowKey = event.key.startsWith('Arrow');\n        const isHomeOrEndKey = ['Home', 'End'].includes(event.key);\n        const isMoveKey = isArrowKey || isHomeOrEndKey;\n        const isCrossOpenKey = isCrossOrientationOpenKey(event.key, orientation, rtl);\n        const isCrossCloseKey = isCrossOrientationCloseKey(event.key, orientation, rtl);\n        const isMainKey = isMainOrientationKey(event.key, orientation);\n        const isNavigationKey = (nested ? isCrossOpenKey : isMainKey) || event.key === 'Enter' || event.key.trim() === '';\n        if (virtual && open) {\n          const rootNode = tree == null ? void 0 : tree.nodesRef.current.find(node => node.parentId == null);\n          const deepestNode = tree && rootNode ? getDeepestNode(tree.nodesRef.current, rootNode.id) : null;\n          if (isMoveKey && deepestNode && virtualItemRef) {\n            const eventObject = new KeyboardEvent('keydown', {\n              key: event.key,\n              bubbles: true\n            });\n            if (isCrossOpenKey || isCrossCloseKey) {\n              var _deepestNode$context, _deepestNode$context2;\n              const isCurrentTarget = ((_deepestNode$context = deepestNode.context) == null ? void 0 : _deepestNode$context.elements.domReference) === event.currentTarget;\n              const dispatchItem = isCrossCloseKey && !isCurrentTarget ? (_deepestNode$context2 = deepestNode.context) == null ? void 0 : _deepestNode$context2.elements.domReference : isCrossOpenKey ? listRef.current.find(item => (item == null ? void 0 : item.id) === activeId) : null;\n              if (dispatchItem) {\n                stopEvent(event);\n                dispatchItem.dispatchEvent(eventObject);\n                setVirtualId(undefined);\n              }\n            }\n            if ((isMainKey || isHomeOrEndKey) && deepestNode.context) {\n              if (deepestNode.context.open && deepestNode.parentId && event.currentTarget !== deepestNode.context.elements.domReference) {\n                var _deepestNode$context$;\n                stopEvent(event);\n                (_deepestNode$context$ = deepestNode.context.elements.domReference) == null || _deepestNode$context$.dispatchEvent(eventObject);\n                return;\n              }\n            }\n          }\n          return commonOnKeyDown(event);\n        }\n\n        // If a floating element should not open on arrow key down, avoid\n        // setting `activeIndex` while it's closed.\n        if (!open && !openOnArrowKeyDown && isArrowKey) {\n          return;\n        }\n        if (isNavigationKey) {\n          keyRef.current = nested && isMainKey ? null : event.key;\n        }\n        if (nested) {\n          if (isCrossOpenKey) {\n            stopEvent(event);\n            if (open) {\n              indexRef.current = getMinIndex(listRef, disabledIndicesRef.current);\n              onNavigate(indexRef.current);\n            } else {\n              onOpenChange(true, event.nativeEvent, 'list-navigation');\n            }\n          }\n          return;\n        }\n        if (isMainKey) {\n          if (selectedIndex != null) {\n            indexRef.current = selectedIndex;\n          }\n          stopEvent(event);\n          if (!open && openOnArrowKeyDown) {\n            onOpenChange(true, event.nativeEvent, 'list-navigation');\n          } else {\n            commonOnKeyDown(event);\n          }\n          if (open) {\n            onNavigate(indexRef.current);\n          }\n        }\n      },\n      onFocus() {\n        if (open && !virtual) {\n          onNavigate(null);\n        }\n      },\n      onPointerDown: checkVirtualPointer,\n      onMouseDown: checkVirtualMouse,\n      onClick: checkVirtualMouse\n    };\n  }, [activeId, ariaActiveDescendantProp, commonOnKeyDown, disabledIndicesRef, focusItemOnOpen, listRef, nested, onNavigate, onOpenChange, open, openOnArrowKeyDown, orientation, rtl, selectedIndex, tree, virtual, virtualItemRef]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating,\n    item\n  } : {}, [enabled, reference, floating, item]);\n}\n\nconst componentRoleToAriaRoleMap = /*#__PURE__*/new Map([['select', 'listbox'], ['combobox', 'listbox'], ['label', false]]);\n\n/**\n * Adds base screen reader props to the reference and floating elements for a\n * given floating element `role`.\n * @see https://floating-ui.com/docs/useRole\n */\nfunction useRole(context, props) {\n  var _componentRoleToAriaR;\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    floatingId\n  } = context;\n  const {\n    enabled = true,\n    role = 'dialog'\n  } = props;\n  const ariaRole = (_componentRoleToAriaR = componentRoleToAriaRoleMap.get(role)) != null ? _componentRoleToAriaR : role;\n  const referenceId = useId();\n  const parentId = useFloatingParentNodeId();\n  const isNested = parentId != null;\n  const reference = React.useMemo(() => {\n    if (ariaRole === 'tooltip' || role === 'label') {\n      return {\n        [\"aria-\" + (role === 'label' ? 'labelledby' : 'describedby')]: open ? floatingId : undefined\n      };\n    }\n    return {\n      'aria-expanded': open ? 'true' : 'false',\n      'aria-haspopup': ariaRole === 'alertdialog' ? 'dialog' : ariaRole,\n      'aria-controls': open ? floatingId : undefined,\n      ...(ariaRole === 'listbox' && {\n        role: 'combobox'\n      }),\n      ...(ariaRole === 'menu' && {\n        id: referenceId\n      }),\n      ...(ariaRole === 'menu' && isNested && {\n        role: 'menuitem'\n      }),\n      ...(role === 'select' && {\n        'aria-autocomplete': 'none'\n      }),\n      ...(role === 'combobox' && {\n        'aria-autocomplete': 'list'\n      })\n    };\n  }, [ariaRole, floatingId, isNested, open, referenceId, role]);\n  const floating = React.useMemo(() => {\n    const floatingProps = {\n      id: floatingId,\n      ...(ariaRole && {\n        role: ariaRole\n      })\n    };\n    if (ariaRole === 'tooltip' || role === 'label') {\n      return floatingProps;\n    }\n    return {\n      ...floatingProps,\n      ...(ariaRole === 'menu' && {\n        'aria-labelledby': referenceId\n      })\n    };\n  }, [ariaRole, floatingId, referenceId, role]);\n  const item = React.useCallback(_ref => {\n    let {\n      active,\n      selected\n    } = _ref;\n    const commonProps = {\n      role: 'option',\n      ...(active && {\n        id: floatingId + \"-option\"\n      })\n    };\n\n    // For `menu`, we are unable to tell if the item is a `menuitemradio`\n    // or `menuitemcheckbox`. For backwards-compatibility reasons, also\n    // avoid defaulting to `menuitem` as it may overwrite custom role props.\n    switch (role) {\n      case 'select':\n        return {\n          ...commonProps,\n          'aria-selected': active && selected\n        };\n      case 'combobox':\n        {\n          return {\n            ...commonProps,\n            ...(active && {\n              'aria-selected': true\n            })\n          };\n        }\n    }\n    return {};\n  }, [floatingId, role]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating,\n    item\n  } : {}, [enabled, reference, floating, item]);\n}\n\n// Converts a JS style key like `backgroundColor` to a CSS transition-property\n// like `background-color`.\nconst camelCaseToKebabCase = str => str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? '-' : '') + $.toLowerCase());\nfunction execWithArgsOrReturn(valueOrFn, args) {\n  return typeof valueOrFn === 'function' ? valueOrFn(args) : valueOrFn;\n}\nfunction useDelayUnmount(open, durationMs) {\n  const [isMounted, setIsMounted] = React.useState(open);\n  if (open && !isMounted) {\n    setIsMounted(true);\n  }\n  React.useEffect(() => {\n    if (!open && isMounted) {\n      const timeout = setTimeout(() => setIsMounted(false), durationMs);\n      return () => clearTimeout(timeout);\n    }\n  }, [open, isMounted, durationMs]);\n  return isMounted;\n}\n/**\n * Provides a status string to apply CSS transitions to a floating element,\n * correctly handling placement-aware transitions.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstatus\n */\nfunction useTransitionStatus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    elements: {\n      floating\n    }\n  } = context;\n  const {\n    duration = 250\n  } = props;\n  const isNumberDuration = typeof duration === 'number';\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [status, setStatus] = React.useState('unmounted');\n  const isMounted = useDelayUnmount(open, closeDuration);\n  if (!isMounted && status === 'close') {\n    setStatus('unmounted');\n  }\n  index(() => {\n    if (!floating) return;\n    if (open) {\n      setStatus('initial');\n      const frame = requestAnimationFrame(() => {\n        setStatus('open');\n      });\n      return () => {\n        cancelAnimationFrame(frame);\n      };\n    }\n    setStatus('close');\n  }, [open, floating]);\n  return {\n    isMounted,\n    status\n  };\n}\n/**\n * Provides styles to apply CSS transitions to a floating element, correctly\n * handling placement-aware transitions. Wrapper around `useTransitionStatus`.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstyles\n */\nfunction useTransitionStyles(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    initial: unstable_initial = {\n      opacity: 0\n    },\n    open: unstable_open,\n    close: unstable_close,\n    common: unstable_common,\n    duration = 250\n  } = props;\n  const placement = context.placement;\n  const side = placement.split('-')[0];\n  const fnArgs = React.useMemo(() => ({\n    side,\n    placement\n  }), [side, placement]);\n  const isNumberDuration = typeof duration === 'number';\n  const openDuration = (isNumberDuration ? duration : duration.open) || 0;\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [styles, setStyles] = React.useState(() => ({\n    ...execWithArgsOrReturn(unstable_common, fnArgs),\n    ...execWithArgsOrReturn(unstable_initial, fnArgs)\n  }));\n  const {\n    isMounted,\n    status\n  } = useTransitionStatus(context, {\n    duration\n  });\n  const initialRef = useLatestRef(unstable_initial);\n  const openRef = useLatestRef(unstable_open);\n  const closeRef = useLatestRef(unstable_close);\n  const commonRef = useLatestRef(unstable_common);\n  index(() => {\n    const initialStyles = execWithArgsOrReturn(initialRef.current, fnArgs);\n    const closeStyles = execWithArgsOrReturn(closeRef.current, fnArgs);\n    const commonStyles = execWithArgsOrReturn(commonRef.current, fnArgs);\n    const openStyles = execWithArgsOrReturn(openRef.current, fnArgs) || Object.keys(initialStyles).reduce((acc, key) => {\n      acc[key] = '';\n      return acc;\n    }, {});\n    if (status === 'initial') {\n      setStyles(styles => ({\n        transitionProperty: styles.transitionProperty,\n        ...commonStyles,\n        ...initialStyles\n      }));\n    }\n    if (status === 'open') {\n      setStyles({\n        transitionProperty: Object.keys(openStyles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: openDuration + \"ms\",\n        ...commonStyles,\n        ...openStyles\n      });\n    }\n    if (status === 'close') {\n      const styles = closeStyles || initialStyles;\n      setStyles({\n        transitionProperty: Object.keys(styles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: closeDuration + \"ms\",\n        ...commonStyles,\n        ...styles\n      });\n    }\n  }, [closeDuration, closeRef, initialRef, openRef, commonRef, openDuration, status, fnArgs]);\n  return {\n    isMounted,\n    styles\n  };\n}\n\n/**\n * Provides a matching callback that can be used to focus an item as the user\n * types, often used in tandem with `useListNavigation()`.\n * @see https://floating-ui.com/docs/useTypeahead\n */\nfunction useTypeahead(context, props) {\n  var _ref;\n  const {\n    open,\n    dataRef\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onMatch: unstable_onMatch,\n    onTypingChange: unstable_onTypingChange,\n    enabled = true,\n    findMatch = null,\n    resetMs = 750,\n    ignoreKeys = [],\n    selectedIndex = null\n  } = props;\n  const timeoutIdRef = React.useRef();\n  const stringRef = React.useRef('');\n  const prevIndexRef = React.useRef((_ref = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref : -1);\n  const matchIndexRef = React.useRef(null);\n  const onMatch = useEffectEvent(unstable_onMatch);\n  const onTypingChange = useEffectEvent(unstable_onTypingChange);\n  const findMatchRef = useLatestRef(findMatch);\n  const ignoreKeysRef = useLatestRef(ignoreKeys);\n  index(() => {\n    if (open) {\n      clearTimeout(timeoutIdRef.current);\n      matchIndexRef.current = null;\n      stringRef.current = '';\n    }\n  }, [open]);\n  index(() => {\n    // Sync arrow key navigation but not typeahead navigation.\n    if (open && stringRef.current === '') {\n      var _ref2;\n      prevIndexRef.current = (_ref2 = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref2 : -1;\n    }\n  }, [open, selectedIndex, activeIndex]);\n  const setTypingChange = useEffectEvent(value => {\n    if (value) {\n      if (!dataRef.current.typing) {\n        dataRef.current.typing = value;\n        onTypingChange(value);\n      }\n    } else {\n      if (dataRef.current.typing) {\n        dataRef.current.typing = value;\n        onTypingChange(value);\n      }\n    }\n  });\n  const onKeyDown = useEffectEvent(event => {\n    function getMatchingIndex(list, orderedList, string) {\n      const str = findMatchRef.current ? findMatchRef.current(orderedList, string) : orderedList.find(text => (text == null ? void 0 : text.toLocaleLowerCase().indexOf(string.toLocaleLowerCase())) === 0);\n      return str ? list.indexOf(str) : -1;\n    }\n    const listContent = listRef.current;\n    if (stringRef.current.length > 0 && stringRef.current[0] !== ' ') {\n      if (getMatchingIndex(listContent, listContent, stringRef.current) === -1) {\n        setTypingChange(false);\n      } else if (event.key === ' ') {\n        stopEvent(event);\n      }\n    }\n    if (listContent == null || ignoreKeysRef.current.includes(event.key) ||\n    // Character key.\n    event.key.length !== 1 ||\n    // Modifier key.\n    event.ctrlKey || event.metaKey || event.altKey) {\n      return;\n    }\n    if (open && event.key !== ' ') {\n      stopEvent(event);\n      setTypingChange(true);\n    }\n\n    // Bail out if the list contains a word like \"llama\" or \"aaron\". TODO:\n    // allow it in this case, too.\n    const allowRapidSuccessionOfFirstLetter = listContent.every(text => {\n      var _text$, _text$2;\n      return text ? ((_text$ = text[0]) == null ? void 0 : _text$.toLocaleLowerCase()) !== ((_text$2 = text[1]) == null ? void 0 : _text$2.toLocaleLowerCase()) : true;\n    });\n\n    // Allows the user to cycle through items that start with the same letter\n    // in rapid succession.\n    if (allowRapidSuccessionOfFirstLetter && stringRef.current === event.key) {\n      stringRef.current = '';\n      prevIndexRef.current = matchIndexRef.current;\n    }\n    stringRef.current += event.key;\n    clearTimeout(timeoutIdRef.current);\n    timeoutIdRef.current = setTimeout(() => {\n      stringRef.current = '';\n      prevIndexRef.current = matchIndexRef.current;\n      setTypingChange(false);\n    }, resetMs);\n    const prevIndex = prevIndexRef.current;\n    const index = getMatchingIndex(listContent, [...listContent.slice((prevIndex || 0) + 1), ...listContent.slice(0, (prevIndex || 0) + 1)], stringRef.current);\n    if (index !== -1) {\n      onMatch(index);\n      matchIndexRef.current = index;\n    } else if (event.key !== ' ') {\n      stringRef.current = '';\n      setTypingChange(false);\n    }\n  });\n  const reference = React.useMemo(() => ({\n    onKeyDown\n  }), [onKeyDown]);\n  const floating = React.useMemo(() => {\n    return {\n      onKeyDown,\n      onKeyUp(event) {\n        if (event.key === ' ') {\n          setTypingChange(false);\n        }\n      }\n    };\n  }, [onKeyDown, setTypingChange]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nfunction getArgsWithCustomFloatingHeight(state, height) {\n  return {\n    ...state,\n    rects: {\n      ...state.rects,\n      floating: {\n        ...state.rects.floating,\n        height\n      }\n    }\n  };\n}\n/**\n * Positions the floating element such that an inner element inside of it is\n * anchored to the reference element.\n * @see https://floating-ui.com/docs/inner\n */\nconst inner = props => ({\n  name: 'inner',\n  options: props,\n  async fn(state) {\n    const {\n      listRef,\n      overflowRef,\n      onFallbackChange,\n      offset: innerOffset = 0,\n      index = 0,\n      minItemsVisible = 4,\n      referenceOverflowThreshold = 0,\n      scrollRef,\n      ...detectOverflowOptions\n    } = evaluate(props, state);\n    const {\n      rects,\n      elements: {\n        floating\n      }\n    } = state;\n    const item = listRef.current[index];\n    const scrollEl = (scrollRef == null ? void 0 : scrollRef.current) || floating;\n\n    // Valid combinations:\n    // 1. Floating element is the scrollRef and has a border (default)\n    // 2. Floating element is not the scrollRef, floating element has a border\n    // 3. Floating element is not the scrollRef, scrollRef has a border\n    // Floating > {...getFloatingProps()} wrapper > scrollRef > items is not\n    // allowed as VoiceOver doesn't work.\n    const clientTop = floating.clientTop || scrollEl.clientTop;\n    const floatingIsBordered = floating.clientTop !== 0;\n    const scrollElIsBordered = scrollEl.clientTop !== 0;\n    const floatingIsScrollEl = floating === scrollEl;\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!state.placement.startsWith('bottom')) {\n        warn('`placement` side must be \"bottom\" when using the `inner`', 'middleware.');\n      }\n    }\n    if (!item) {\n      return {};\n    }\n    const nextArgs = {\n      ...state,\n      ...(await offset(-item.offsetTop - floating.clientTop - rects.reference.height / 2 - item.offsetHeight / 2 - innerOffset).fn(state))\n    };\n    const overflow = await detectOverflow(getArgsWithCustomFloatingHeight(nextArgs, scrollEl.scrollHeight + clientTop + floating.clientTop), detectOverflowOptions);\n    const refOverflow = await detectOverflow(nextArgs, {\n      ...detectOverflowOptions,\n      elementContext: 'reference'\n    });\n    const diffY = max(0, overflow.top);\n    const nextY = nextArgs.y + diffY;\n    const isScrollable = scrollEl.scrollHeight > scrollEl.clientHeight;\n    const rounder = isScrollable ? v => v : round;\n    const maxHeight = rounder(max(0, scrollEl.scrollHeight + (floatingIsBordered && floatingIsScrollEl || scrollElIsBordered ? clientTop * 2 : 0) - diffY - max(0, overflow.bottom)));\n    scrollEl.style.maxHeight = maxHeight + \"px\";\n    scrollEl.scrollTop = diffY;\n\n    // There is not enough space, fallback to standard anchored positioning\n    if (onFallbackChange) {\n      const shouldFallback = scrollEl.offsetHeight < item.offsetHeight * min(minItemsVisible, listRef.current.length) - 1 || refOverflow.top >= -referenceOverflowThreshold || refOverflow.bottom >= -referenceOverflowThreshold;\n      ReactDOM.flushSync(() => onFallbackChange(shouldFallback));\n    }\n    if (overflowRef) {\n      overflowRef.current = await detectOverflow(getArgsWithCustomFloatingHeight({\n        ...nextArgs,\n        y: nextY\n      }, scrollEl.offsetHeight + clientTop + floating.clientTop), detectOverflowOptions);\n    }\n    return {\n      y: nextY\n    };\n  }\n});\n/**\n * Changes the `inner` middleware's `offset` upon a `wheel` event to\n * expand the floating element's height, revealing more list items.\n * @see https://floating-ui.com/docs/inner\n */\nfunction useInnerOffset(context, props) {\n  const {\n    open,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    overflowRef,\n    scrollRef,\n    onChange: unstable_onChange\n  } = props;\n  const onChange = useEffectEvent(unstable_onChange);\n  const controlledScrollingRef = React.useRef(false);\n  const prevScrollTopRef = React.useRef(null);\n  const initialOverflowRef = React.useRef(null);\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onWheel(e) {\n      if (e.ctrlKey || !el || overflowRef.current == null) {\n        return;\n      }\n      const dY = e.deltaY;\n      const isAtTop = overflowRef.current.top >= -0.5;\n      const isAtBottom = overflowRef.current.bottom >= -0.5;\n      const remainingScroll = el.scrollHeight - el.clientHeight;\n      const sign = dY < 0 ? -1 : 1;\n      const method = dY < 0 ? 'max' : 'min';\n      if (el.scrollHeight <= el.clientHeight) {\n        return;\n      }\n      if (!isAtTop && dY > 0 || !isAtBottom && dY < 0) {\n        e.preventDefault();\n        ReactDOM.flushSync(() => {\n          onChange(d => d + Math[method](dY, remainingScroll * sign));\n        });\n      } else if (/firefox/i.test(getUserAgent())) {\n        // Needed to propagate scrolling during momentum scrolling phase once\n        // it gets limited by the boundary. UX improvement, not critical.\n        el.scrollTop += dY;\n      }\n    }\n    const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n    if (open && el) {\n      el.addEventListener('wheel', onWheel);\n\n      // Wait for the position to be ready.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n        if (overflowRef.current != null) {\n          initialOverflowRef.current = {\n            ...overflowRef.current\n          };\n        }\n      });\n      return () => {\n        prevScrollTopRef.current = null;\n        initialOverflowRef.current = null;\n        el.removeEventListener('wheel', onWheel);\n      };\n    }\n  }, [enabled, open, elements.floating, overflowRef, scrollRef, onChange]);\n  const floating = React.useMemo(() => ({\n    onKeyDown() {\n      controlledScrollingRef.current = true;\n    },\n    onWheel() {\n      controlledScrollingRef.current = false;\n    },\n    onPointerMove() {\n      controlledScrollingRef.current = false;\n    },\n    onScroll() {\n      const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n      if (!overflowRef.current || !el || !controlledScrollingRef.current) {\n        return;\n      }\n      if (prevScrollTopRef.current !== null) {\n        const scrollDiff = el.scrollTop - prevScrollTopRef.current;\n        if (overflowRef.current.bottom < -0.5 && scrollDiff < -1 || overflowRef.current.top < -0.5 && scrollDiff > 1) {\n          ReactDOM.flushSync(() => onChange(d => d + scrollDiff));\n        }\n      }\n\n      // [Firefox] Wait for the height change to have been applied.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n      });\n    }\n  }), [elements.floating, onChange, overflowRef, scrollRef]);\n  return React.useMemo(() => enabled ? {\n    floating\n  } : {}, [enabled, floating]);\n}\n\nfunction isPointInPolygon(point, polygon) {\n  const [x, y] = point;\n  let isInside = false;\n  const length = polygon.length;\n  for (let i = 0, j = length - 1; i < length; j = i++) {\n    const [xi, yi] = polygon[i] || [0, 0];\n    const [xj, yj] = polygon[j] || [0, 0];\n    const intersect = yi >= y !== yj >= y && x <= (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) {\n      isInside = !isInside;\n    }\n  }\n  return isInside;\n}\nfunction isInside(point, rect) {\n  return point[0] >= rect.x && point[0] <= rect.x + rect.width && point[1] >= rect.y && point[1] <= rect.y + rect.height;\n}\n/**\n * Generates a safe polygon area that the user can traverse without closing the\n * floating element once leaving the reference element.\n * @see https://floating-ui.com/docs/useHover#safepolygon\n */\nfunction safePolygon(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    buffer = 0.5,\n    blockPointerEvents = false,\n    requireIntent = true\n  } = options;\n  let timeoutId;\n  let hasLanded = false;\n  let lastX = null;\n  let lastY = null;\n  let lastCursorTime = performance.now();\n  function getCursorSpeed(x, y) {\n    const currentTime = performance.now();\n    const elapsedTime = currentTime - lastCursorTime;\n    if (lastX === null || lastY === null || elapsedTime === 0) {\n      lastX = x;\n      lastY = y;\n      lastCursorTime = currentTime;\n      return null;\n    }\n    const deltaX = x - lastX;\n    const deltaY = y - lastY;\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n    const speed = distance / elapsedTime; // px / ms\n\n    lastX = x;\n    lastY = y;\n    lastCursorTime = currentTime;\n    return speed;\n  }\n  const fn = _ref => {\n    let {\n      x,\n      y,\n      placement,\n      elements,\n      onClose,\n      nodeId,\n      tree\n    } = _ref;\n    return function onMouseMove(event) {\n      function close() {\n        clearTimeout(timeoutId);\n        onClose();\n      }\n      clearTimeout(timeoutId);\n      if (!elements.domReference || !elements.floating || placement == null || x == null || y == null) {\n        return;\n      }\n      const {\n        clientX,\n        clientY\n      } = event;\n      const clientPoint = [clientX, clientY];\n      const target = getTarget(event);\n      const isLeave = event.type === 'mouseleave';\n      const isOverFloatingEl = contains(elements.floating, target);\n      const isOverReferenceEl = contains(elements.domReference, target);\n      const refRect = elements.domReference.getBoundingClientRect();\n      const rect = elements.floating.getBoundingClientRect();\n      const side = placement.split('-')[0];\n      const cursorLeaveFromRight = x > rect.right - rect.width / 2;\n      const cursorLeaveFromBottom = y > rect.bottom - rect.height / 2;\n      const isOverReferenceRect = isInside(clientPoint, refRect);\n      const isFloatingWider = rect.width > refRect.width;\n      const isFloatingTaller = rect.height > refRect.height;\n      const left = (isFloatingWider ? refRect : rect).left;\n      const right = (isFloatingWider ? refRect : rect).right;\n      const top = (isFloatingTaller ? refRect : rect).top;\n      const bottom = (isFloatingTaller ? refRect : rect).bottom;\n      if (isOverFloatingEl) {\n        hasLanded = true;\n        if (!isLeave) {\n          return;\n        }\n      }\n      if (isOverReferenceEl) {\n        hasLanded = false;\n      }\n      if (isOverReferenceEl && !isLeave) {\n        hasLanded = true;\n        return;\n      }\n\n      // Prevent overlapping floating element from being stuck in an open-close\n      // loop: https://github.com/floating-ui/floating-ui/issues/1910\n      if (isLeave && isElement(event.relatedTarget) && contains(elements.floating, event.relatedTarget)) {\n        return;\n      }\n\n      // If any nested child is open, abort.\n      if (tree && getChildren(tree.nodesRef.current, nodeId).some(_ref2 => {\n        let {\n          context\n        } = _ref2;\n        return context == null ? void 0 : context.open;\n      })) {\n        return;\n      }\n\n      // If the pointer is leaving from the opposite side, the \"buffer\" logic\n      // creates a point where the floating element remains open, but should be\n      // ignored.\n      // A constant of 1 handles floating point rounding errors.\n      if (side === 'top' && y >= refRect.bottom - 1 || side === 'bottom' && y <= refRect.top + 1 || side === 'left' && x >= refRect.right - 1 || side === 'right' && x <= refRect.left + 1) {\n        return close();\n      }\n\n      // Ignore when the cursor is within the rectangular trough between the\n      // two elements. Since the triangle is created from the cursor point,\n      // which can start beyond the ref element's edge, traversing back and\n      // forth from the ref to the floating element can cause it to close. This\n      // ensures it always remains open in that case.\n      let rectPoly = [];\n      switch (side) {\n        case 'top':\n          rectPoly = [[left, refRect.top + 1], [left, rect.bottom - 1], [right, rect.bottom - 1], [right, refRect.top + 1]];\n          break;\n        case 'bottom':\n          rectPoly = [[left, rect.top + 1], [left, refRect.bottom - 1], [right, refRect.bottom - 1], [right, rect.top + 1]];\n          break;\n        case 'left':\n          rectPoly = [[rect.right - 1, bottom], [rect.right - 1, top], [refRect.left + 1, top], [refRect.left + 1, bottom]];\n          break;\n        case 'right':\n          rectPoly = [[refRect.right - 1, bottom], [refRect.right - 1, top], [rect.left + 1, top], [rect.left + 1, bottom]];\n          break;\n      }\n      function getPolygon(_ref3) {\n        let [x, y] = _ref3;\n        switch (side) {\n          case 'top':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.bottom - buffer : isFloatingWider ? rect.bottom - buffer : rect.top], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.bottom - buffer : rect.top : rect.bottom - buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'bottom':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.top + buffer : isFloatingWider ? rect.top + buffer : rect.bottom], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.top + buffer : rect.bottom : rect.top + buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'left':\n            {\n              const cursorPointOne = [x + buffer + 1, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x + buffer + 1, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.right - buffer : isFloatingTaller ? rect.right - buffer : rect.left, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.right - buffer : rect.left : rect.right - buffer, rect.bottom]];\n              return [...commonPoints, cursorPointOne, cursorPointTwo];\n            }\n          case 'right':\n            {\n              const cursorPointOne = [x - buffer, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x - buffer, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.left + buffer : isFloatingTaller ? rect.left + buffer : rect.right, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.left + buffer : rect.right : rect.left + buffer, rect.bottom]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n        }\n      }\n      if (isPointInPolygon([clientX, clientY], rectPoly)) {\n        return;\n      }\n      if (hasLanded && !isOverReferenceRect) {\n        return close();\n      }\n      if (!isLeave && requireIntent) {\n        const cursorSpeed = getCursorSpeed(event.clientX, event.clientY);\n        const cursorSpeedThreshold = 0.1;\n        if (cursorSpeed !== null && cursorSpeed < cursorSpeedThreshold) {\n          return close();\n        }\n      }\n      if (!isPointInPolygon([clientX, clientY], getPolygon([x, y]))) {\n        close();\n      } else if (!hasLanded && requireIntent) {\n        timeoutId = window.setTimeout(close, 40);\n      }\n    };\n  };\n  fn.__options = {\n    blockPointerEvents\n  };\n  return fn;\n}\n\nexport { Composite, CompositeItem, FloatingArrow, FloatingDelayGroup, FloatingFocusManager, FloatingList, FloatingNode, FloatingOverlay, FloatingPortal, FloatingTree, inner, safePolygon, useClick, useClientPoint, useDelayGroup, useDelayGroupContext, useDismiss, useFloating, useFloatingNodeId, useFloatingParentNodeId, useFloatingPortalNode, useFloatingRootContext, useFloatingTree, useFocus, useHover, useId, useInnerOffset, useInteractions, useListItem, useListNavigation, useMergeRefs, useRole, useTransitionStatus, useTransitionStyles, useTypeahead };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCQ;AAzCR;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;AAGA;;;CAGC,GACD,SAAS,aAAa,IAAI;IACxB,OAAO,8JAAM,OAAO;gCAAC;YACnB,IAAI,KAAK,KAAK;wCAAC,CAAA,MAAO,OAAO;wCAAO;gBAClC,OAAO;YACT;YACA;wCAAO,CAAA;oBACL,KAAK,OAAO;gDAAC,CAAA;4BACX,IAAI,OAAO,QAAQ,YAAY;gCAC7B,IAAI;4BACN,OAAO,IAAI,OAAO,MAAM;gCACtB,IAAI,OAAO,GAAG;4BAChB;wBACF;;gBACF;;QACA,uDAAuD;QACzD;+BAAG;AACL;AAEA,0EAA0E;AAC1E,MAAM,YAAY;IAChB,GAAG,6JAAK;AACV;AAEA,MAAM,qBAAqB,UAAU,kBAAkB;AACvD,MAAM,yBAAyB,sBAAsB,CAAC,CAAA,KAAM,IAAI;AAChE,SAAS,eAAe,QAAQ;IAC9B,MAAM,MAAM,8JAAM,MAAM;sCAAC;YACvB,wCAA2C;gBACzC,MAAM,IAAI,MAAM;YAClB;QACF;;IACA;iDAAuB;YACrB,IAAI,OAAO,GAAG;QAChB;;IACA,OAAO,8JAAM,WAAW;sCAAC;YACvB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;gBACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;YAC9B;YACA,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,IAAI;QACvD;qCAAG,EAAE;AACP;AAEA,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,SAAS,eAAe,KAAK,EAAE,IAAI,EAAE,OAAO;IAC1C,OAAO,KAAK,KAAK,CAAC,QAAQ,UAAU;AACtC;AACA,SAAS,mBAAmB,OAAO,EAAE,KAAK;IACxC,OAAO,QAAQ,KAAK,SAAS,QAAQ,OAAO,CAAC,MAAM;AACrD;AACA,SAAS,YAAY,OAAO,EAAE,eAAe;IAC3C,OAAO,qBAAqB,SAAS;QACnC;IACF;AACF;AACA,SAAS,YAAY,OAAO,EAAE,eAAe;IAC3C,OAAO,qBAAqB,SAAS;QACnC,WAAW;QACX,eAAe,QAAQ,OAAO,CAAC,MAAM;QACrC;IACF;AACF;AACA,SAAS,qBAAqB,OAAO,EAAE,KAAK;IAC1C,IAAI,EACF,gBAAgB,CAAC,CAAC,EAClB,YAAY,KAAK,EACjB,eAAe,EACf,SAAS,CAAC,EACX,GAAG,UAAU,KAAK,IAAI,CAAC,IAAI;IAC5B,MAAM,OAAO,QAAQ,OAAO;IAC5B,IAAI,QAAQ;IACZ,GAAG;QACD,SAAS,YAAY,CAAC,SAAS;IACjC,QAAS,SAAS,KAAK,SAAS,KAAK,MAAM,GAAG,KAAK,WAAW,MAAM,OAAO,iBAAkB;IAC7F,OAAO;AACT;AACA,SAAS,sBAAsB,WAAW,EAAE,IAAI;IAC9C,IAAI,EACF,KAAK,EACL,WAAW,EACX,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,WAAW,OAAO,KAAK,EACxB,GAAG;IACJ,IAAI,YAAY;IAChB,IAAI,MAAM,GAAG,KAAK,UAAU;QAC1B,QAAQ,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;QAClB,IAAI,cAAc,CAAC,GAAG;YACpB,YAAY;QACd,OAAO;YACL,YAAY,qBAAqB,aAAa;gBAC5C,eAAe;gBACf,QAAQ;gBACR,WAAW;gBACX;YACF;YACA,IAAI,QAAQ,CAAC,YAAY,OAAO,YAAY,YAAY,CAAC,GAAG;gBAC1D,MAAM,MAAM,YAAY;gBACxB,MAAM,SAAS,WAAW;gBAC1B,MAAM,SAAS,WAAW,CAAC,SAAS,GAAG;gBACvC,IAAI,WAAW,KAAK;oBAClB,YAAY;gBACd,OAAO;oBACL,YAAY,SAAS,MAAM,SAAS,SAAS;gBAC/C;YACF;QACF;QACA,IAAI,mBAAmB,aAAa,YAAY;YAC9C,YAAY;QACd;IACF;IACA,IAAI,MAAM,GAAG,KAAK,YAAY;QAC5B,QAAQ,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;QAClB,IAAI,cAAc,CAAC,GAAG;YACpB,YAAY;QACd,OAAO;YACL,YAAY,qBAAqB,aAAa;gBAC5C,eAAe;gBACf,QAAQ;gBACR;YACF;YACA,IAAI,QAAQ,YAAY,OAAO,UAAU;gBACvC,YAAY,qBAAqB,aAAa;oBAC5C,eAAe,YAAY,OAAO;oBAClC,QAAQ;oBACR;gBACF;YACF;QACF;QACA,IAAI,mBAAmB,aAAa,YAAY;YAC9C,YAAY;QACd;IACF;IAEA,kCAAkC;IAClC,IAAI,gBAAgB,QAAQ;QAC1B,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,YAAY;QAClC,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,aAAa,WAAW,GAAG;YAClD,QAAQ,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;YAClB,IAAI,YAAY,SAAS,OAAO,GAAG;gBACjC,YAAY,qBAAqB,aAAa;oBAC5C,eAAe;oBACf;gBACF;gBACA,IAAI,QAAQ,eAAe,WAAW,MAAM,UAAU;oBACpD,YAAY,qBAAqB,aAAa;wBAC5C,eAAe,YAAY,YAAY,OAAO;wBAC9C;oBACF;gBACF;YACF,OAAO,IAAI,MAAM;gBACf,YAAY,qBAAqB,aAAa;oBAC5C,eAAe,YAAY,YAAY,OAAO;oBAC9C;gBACF;YACF;YACA,IAAI,eAAe,WAAW,MAAM,UAAU;gBAC5C,YAAY;YACd;QACF;QACA,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,cAAc,UAAU,GAAG;YAClD,QAAQ,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;YAClB,IAAI,YAAY,SAAS,GAAG;gBAC1B,YAAY,qBAAqB,aAAa;oBAC5C,eAAe;oBACf,WAAW;oBACX;gBACF;gBACA,IAAI,QAAQ,eAAe,WAAW,MAAM,UAAU;oBACpD,YAAY,qBAAqB,aAAa;wBAC5C,eAAe,YAAY,CAAC,OAAO,YAAY,IAAI;wBACnD,WAAW;wBACX;oBACF;gBACF;YACF,OAAO,IAAI,MAAM;gBACf,YAAY,qBAAqB,aAAa;oBAC5C,eAAe,YAAY,CAAC,OAAO,YAAY,IAAI;oBACnD,WAAW;oBACX;gBACF;YACF;YACA,IAAI,eAAe,WAAW,MAAM,UAAU;gBAC5C,YAAY;YACd;QACF;QACA,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,WAAW,UAAU;QAC3C,IAAI,mBAAmB,aAAa,YAAY;YAC9C,IAAI,QAAQ,SAAS;gBACnB,YAAY,MAAM,GAAG,KAAK,CAAC,MAAM,cAAc,UAAU,IAAI,WAAW,qBAAqB,aAAa;oBACxG,eAAe,YAAY,YAAY,OAAO;oBAC9C;gBACF;YACF,OAAO;gBACL,YAAY;YACd;QACF;IACF;IACA,OAAO;AACT;AAEA,qEAAqE,GACrE,SAAS,aAAa,KAAK,EAAE,IAAI,EAAE,KAAK;IACtC,MAAM,UAAU,EAAE;IAClB,IAAI,aAAa;IACjB,MAAM,OAAO,CAAC,CAAC,OAAO;QACpB,IAAI,EACF,KAAK,EACL,MAAM,EACP,GAAG;QACJ,IAAI,QAAQ,MAAM;YAChB,wCAA2C;gBACzC,MAAM,IAAI,MAAM,uDAAuD,QAAQ;YACjF;QACF;QACA,IAAI,aAAa;QACjB,IAAI,OAAO;YACT,aAAa;QACf;QACA,MAAO,CAAC,WAAY;YAClB,MAAM,cAAc,EAAE;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;oBAC/B,YAAY,IAAI,CAAC,aAAa,IAAI,IAAI;gBACxC;YACF;YACA,IAAI,aAAa,OAAO,SAAS,QAAQ,YAAY,KAAK,CAAC,CAAA,OAAQ,OAAO,CAAC,KAAK,IAAI,OAAO;gBACzF,YAAY,OAAO,CAAC,CAAA;oBAClB,OAAO,CAAC,KAAK,GAAG;gBAClB;gBACA,aAAa;YACf,OAAO;gBACL;YACF;QACF;IACF;IAEA,kCAAkC;IAClC,OAAO;WAAI;KAAQ;AACrB;AAEA,gEAAgE,GAChE,SAAS,qBAAqB,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM;IAC/D,IAAI,UAAU,CAAC,GAAG,OAAO,CAAC;IAC1B,MAAM,iBAAiB,QAAQ,OAAO,CAAC;IACvC,MAAM,WAAW,KAAK,CAAC,MAAM;IAC7B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YACA,OAAO,iBAAiB,SAAS,KAAK,GAAG;QAC3C,KAAK;YACH,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YACA,OAAO,iBAAiB,CAAC,SAAS,MAAM,GAAG,CAAC,IAAI;QAClD,KAAK;YACH,OAAO,QAAQ,WAAW,CAAC;IAC/B;AACF;AAEA,mEAAmE,GACnE,SAAS,eAAe,OAAO,EAAE,OAAO;IACtC,OAAO,QAAQ,OAAO,CAAC,CAAC,OAAO,YAAc,QAAQ,QAAQ,CAAC,SAAS;YAAC;SAAU,GAAG,EAAE;AACzF;AACA,SAAS,WAAW,IAAI,EAAE,KAAK,EAAE,eAAe;IAC9C,IAAI,iBAAiB;QACnB,OAAO,gBAAgB,QAAQ,CAAC;IAClC;IACA,MAAM,UAAU,IAAI,CAAC,MAAM;IAC3B,OAAO,WAAW,QAAQ,QAAQ,YAAY,CAAC,eAAe,QAAQ,YAAY,CAAC,qBAAqB;AAC1G;AAEA,IAAI,QAAQ,OAAO,aAAa,cAAc,6JAAA,CAAA,kBAAe,GAAG,6JAAA,CAAA,YAAS;AAEzE,SAAS,uBAAuB,CAAC,EAAE,CAAC;IAClC,MAAM,WAAW,EAAE,uBAAuB,CAAC;IAC3C,IAAI,WAAW,KAAK,2BAA2B,IAAI,WAAW,KAAK,8BAA8B,EAAE;QACjG,OAAO,CAAC;IACV;IACA,IAAI,WAAW,KAAK,2BAA2B,IAAI,WAAW,KAAK,0BAA0B,EAAE;QAC7F,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,aAAa,IAAI,EAAE,IAAI;IAC9B,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE;QAC3B,OAAO;IACT;IACA,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,KAAK,OAAO,GAAI;QACzC,IAAI,UAAU,KAAK,GAAG,CAAC,MAAM;YAC3B,OAAO;QACT;IACF;IACA,OAAO;AACT;AACA,MAAM,sBAAsB,WAAW,GAAE,8JAAM,aAAa,CAAC;IAC3D,UAAU,KAAO;IACjB,YAAY,KAAO;IACnB,KAAK,WAAW,GAAE,IAAI;IACtB,aAAa;QACX,SAAS,EAAE;IACb;AACF;AACA;;;CAGC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,SAAS,EACV,GAAG;IACJ,MAAM,CAAC,KAAK,OAAO,GAAG,8JAAM,QAAQ;iCAAC,IAAM,IAAI;;IAC/C,MAAM,WAAW,8JAAM,WAAW;8CAAC,CAAA;YACjC;sDAAO,CAAA,UAAW,IAAI,IAAI,SAAS,GAAG,CAAC,MAAM;;QAC/C;6CAAG,EAAE;IACL,MAAM,aAAa,8JAAM,WAAW;gDAAC,CAAA;YACnC;wDAAO,CAAA;oBACL,MAAM,MAAM,IAAI,IAAI;oBACpB,IAAI,MAAM,CAAC;oBACX,OAAO;gBACT;;QACF;+CAAG,EAAE;IACL,MAAM;QACJ,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC;QAC7C,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,OAAO,GAAG,CAAC,MAAM;QACnB;QACA,IAAI,CAAC,aAAa,KAAK,SAAS;YAC9B,OAAO;QACT;IACF,GAAG;QAAC;KAAI;IACR,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,oBAAoB,QAAQ,EAAE;QACpE,OAAO,8JAAM,OAAO;oCAAC,IAAM,CAAC;oBAC1B;oBACA;oBACA;oBACA;oBACA;gBACF,CAAC;mCAAG;YAAC;YAAU;YAAY;YAAK;YAAa;SAAU;IACzD,GAAG;AACL;AACA;;;;CAIC,GACD,SAAS,YAAY,KAAK;IACxB,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,KAAK,EACN,GAAG;IACJ,MAAM,EACJ,QAAQ,EACR,UAAU,EACV,GAAG,EACH,WAAW,EACX,SAAS,EACV,GAAG,8JAAM,UAAU,CAAC;IACrB,MAAM,CAAC,SAAS,SAAS,GAAG,8JAAM,QAAQ,CAAC;IAC3C,MAAM,eAAe,8JAAM,MAAM,CAAC;IAClC,MAAM,MAAM,8JAAM,WAAW;wCAAC,CAAA;YAC5B,aAAa,OAAO,GAAG;YACvB,IAAI,YAAY,MAAM;gBACpB,YAAY,OAAO,CAAC,QAAQ,GAAG;gBAC/B,IAAI,WAAW;oBACb,IAAI;oBACJ,MAAM,iBAAiB,UAAU;oBACjC,UAAU,OAAO,CAAC,QAAQ,GAAG,iBAAiB,QAAQ,CAAC,oBAAoB,QAAQ,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK,OAAO,oBAAoB;gBACrJ;YACF;QACF;uCAAG;QAAC;QAAS;QAAa;QAAW;KAAM;IAC3C,MAAM;QACJ,MAAM,OAAO,aAAa,OAAO;QACjC,IAAI,MAAM;YACR,SAAS;YACT,OAAO;gBACL,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAU;KAAW;IACzB,MAAM;QACJ,MAAM,QAAQ,aAAa,OAAO,GAAG,IAAI,GAAG,CAAC,aAAa,OAAO,IAAI;QACrE,IAAI,SAAS,MAAM;YACjB,SAAS;QACX;IACF,GAAG;QAAC;KAAI;IACR,OAAO,8JAAM,OAAO;+BAAC,IAAM,CAAC;gBAC1B;gBACA,OAAO,WAAW,OAAO,CAAC,IAAI;YAChC,CAAC;8BAAG;QAAC;QAAS;KAAI;AACpB;AAEA,SAAS,UAAU,MAAM,EAAE,aAAa;IACtC,IAAI,OAAO,WAAW,YAAY;QAChC,OAAO,OAAO;IAChB;IACA,IAAI,QAAQ;QACV,OAAO,WAAW,GAAE,8JAAM,YAAY,CAAC,QAAQ;IACjD;IACA,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,OAAO;AACjD;AACA,MAAM,mBAAmB,WAAW,GAAE,8JAAM,aAAa,CAAC;IACxD,aAAa;IACb,YAAY,KAAO;AACrB;AACA,MAAM,iBAAiB;IAAC;IAAY;CAAY;AAChD,MAAM,eAAe;IAAC;IAAU;CAAW;AAC3C,MAAM,UAAU;OAAI;OAAmB;CAAa;AAEpD;;;;;;;;CAQC,GACD,MAAM,YAAY,WAAW,GAAE,8JAAM,UAAU,CAAC,SAAS,UAAU,KAAK,EAAE,YAAY;IACpF,MAAM,EACJ,MAAM,EACN,cAAc,MAAM,EACpB,OAAO,IAAI,EACX,MAAM,KAAK,EACX,OAAO,CAAC,EACR,eAAe,EACf,aAAa,mBAAmB,EAChC,YAAY,sBAAsB,EAClC,SAAS,EACT,QAAQ,KAAK,EACb,GAAG,UACJ,GAAG;IACJ,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,8JAAM,QAAQ,CAAC;IACrE,MAAM,cAAc,uBAAuB,OAAO,sBAAsB;IACxE,MAAM,aAAa,eAAe,0BAA0B,OAAO,yBAAyB;IAC5F,MAAM,cAAc,8JAAM,MAAM,CAAC,EAAE;IACnC,MAAM,qBAAqB,UAAU,OAAO,WAAW,aAAa,OAAO,KAAK,GAAG,CAAC;IACpF,MAAM,eAAe,8JAAM,OAAO;qDAAC,IAAM,CAAC;gBACxC;gBACA;YACF,CAAC;oDAAG;QAAC;QAAa;KAAW;IAC7B,MAAM,SAAS,OAAO;IACtB,SAAS,cAAc,KAAK;QAC1B,IAAI,CAAC,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;QAClC,IAAI,YAAY;QAChB,MAAM,WAAW,YAAY,aAAa;QAC1C,MAAM,WAAW,YAAY,aAAa;QAC1C,MAAM,mBAAmB,MAAM,aAAa;QAC5C,MAAM,qBAAqB,MAAM,cAAc;QAC/C,IAAI,QAAQ;YACV,MAAM,QAAQ,aAAa,MAAM,IAAI,CAAC;gBACpC,QAAQ,YAAY,OAAO,CAAC,MAAM;YACpC,GAAG,IAAM,CAAC;oBACR,OAAO;oBACP,QAAQ;gBACV,CAAC;YACD,uEAAuE;YACvE,+DAA+D;YAC/D,MAAM,UAAU,aAAa,OAAO,MAAM;YAC1C,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,QAAS,SAAS,QAAQ,CAAC,WAAW,YAAY,OAAO,EAAE,OAAO;YACzG,qBAAqB;YACrB,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,YAAY,OAAO,YAAc,SAAS,QAAQ,CAAC,WAAW,YAAY,OAAO,EAAE,OAAO,mBAAmB,YAAY,YAAY,CAAC;YAC3K,MAAM,iBAAiB,OAAO,CAAC,sBAAsB;gBACnD,SAAS,QAAQ,GAAG,CAAC,CAAA,YAAa,YAAY,YAAY,OAAO,CAAC,UAAU,GAAG;YACjF,GAAG;gBACD;gBACA;gBACA;gBACA;gBACA;gBACA,gEAAgE;gBAChE,uBAAuB;gBACvB,iBAAiB,eAAe;uBAAK,mBAAmB,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,QAAU,WAAW,YAAY,OAAO,EAAE,SAAS,QAAQ;oBAAa;iBAAU,EAAE;gBACxK,UAAU;gBACV,UAAU;gBACV,WAAW,qBAAqB,cAAc,WAAW,WAAW,aAAa,OAAO,SAAS,MACjG,gEAAgE;gBAChE,wDAAwD;gBACxD,8BAA8B;gBAC9B,MAAM,GAAG,KAAK,aAAa,OAAO,MAAM,GAAG,KAAK,mBAAmB,OAAO;YAC5E,GAAG;YACH,IAAI,kBAAkB,MAAM;gBAC1B,YAAY;YACd;QACF;QACA,MAAM,YAAY;YAChB,YAAY;gBAAC;aAAiB;YAC9B,UAAU;gBAAC;aAAW;YACtB,MAAM;gBAAC;gBAAkB;aAAW;QACtC,CAAC,CAAC,YAAY;QACd,MAAM,cAAc;YAClB,YAAY;gBAAC;aAAmB;YAChC,UAAU;gBAAC;aAAS;YACpB,MAAM;gBAAC;gBAAoB;aAAS;QACtC,CAAC,CAAC,YAAY;QACd,MAAM,gBAAgB,SAAS,UAAU,CAAA;YACvC,YAAY;YACZ,UAAU;YACV,MAAM;QACR,CAAA,CAAC,CAAC,YAAY;QACd,IAAI,cAAc,eAAe;eAAI;eAAc;SAAY,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;YACnF,IAAI,QAAQ,cAAc,YAAY,UAAU,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACnE,YAAY;YACd,OAAO,IAAI,QAAQ,cAAc,YAAY,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC5E,YAAY;YACd,OAAO;gBACL,YAAY,qBAAqB,aAAa;oBAC5C,eAAe;oBACf,WAAW,YAAY,QAAQ,CAAC,MAAM,GAAG;oBACzC;gBACF;YACF;QACF;QACA,IAAI,cAAc,eAAe,CAAC,mBAAmB,aAAa,YAAY;YAC5E,IAAI;YACJ,MAAM,eAAe;YACrB,IAAI,cAAc,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACrC,MAAM,cAAc;YACtB;YACA,WAAW;YACX,CAAC,wBAAwB,YAAY,OAAO,CAAC,UAAU,KAAK,QAAQ,sBAAsB,KAAK;QACjG;IACF;IACA,MAAM,gBAAgB;QACpB,GAAG,QAAQ;QACX,GAAG,kBAAkB;QACrB,KAAK;QACL,oBAAoB,gBAAgB,SAAS,YAAY;QACzD,WAAU,CAAC;YACT,SAAS,SAAS,IAAI,QAAQ,SAAS,SAAS,CAAC;YACjD,mBAAmB,SAAS,IAAI,QAAQ,mBAAmB,SAAS,CAAC;YACrE,cAAc;QAChB;IACF;IACA,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,iBAAiB,QAAQ,EAAE;QACjE,OAAO;IACT,GAAG,WAAW,GAAE,8JAAM,aAAa,CAAC,cAAc;QAChD,aAAa;IACf,GAAG,UAAU,QAAQ;AACvB;AACA;;CAEC,GACD,MAAM,gBAAgB,WAAW,GAAE,8JAAM,UAAU,CAAC,SAAS,cAAc,KAAK,EAAE,YAAY;IAC5F,MAAM,EACJ,MAAM,EACN,GAAG,UACJ,GAAG;IACJ,MAAM,qBAAqB,UAAU,OAAO,WAAW,aAAa,OAAO,KAAK,GAAG,CAAC;IACpF,MAAM,EACJ,WAAW,EACX,UAAU,EACX,GAAG,8JAAM,UAAU,CAAC;IACrB,MAAM,EACJ,GAAG,EACH,KAAK,EACN,GAAG;IACJ,MAAM,YAAY,aAAa;QAAC;QAAK;QAAc,mBAAmB,GAAG;KAAC;IAC1E,MAAM,WAAW,gBAAgB;IACjC,MAAM,gBAAgB;QACpB,GAAG,QAAQ;QACX,GAAG,kBAAkB;QACrB,KAAK;QACL,UAAU,WAAW,IAAI,CAAC;QAC1B,eAAe,WAAW,KAAK;QAC/B,SAAQ,CAAC;YACP,SAAS,OAAO,IAAI,QAAQ,SAAS,OAAO,CAAC;YAC7C,mBAAmB,OAAO,IAAI,QAAQ,mBAAmB,OAAO,CAAC;YACjE,WAAW;QACb;IACF;IACA,OAAO,UAAU,QAAQ;AAC3B;AAEA,SAAS;IACP,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI;IAW7C,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AAEA,IAAI,wBAAwB;AAC5B,IAAI,QAAQ;AACZ,MAAM,QAAQ,IACd,eAAe;IACf,iBAAiB,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK;AAC1D,SAAS;IACP,MAAM,CAAC,IAAI,MAAM,GAAG,8JAAM,QAAQ;kCAAC,IAAM,wBAAwB,UAAU;;IAC3E,MAAM;QACJ,IAAI,MAAM,MAAM;YACd,MAAM;QACR;IACA,uDAAuD;IACzD,GAAG,EAAE;IACL,8JAAM,SAAS;mCAAC;YACd,wBAAwB;QAC1B;kCAAG,EAAE;IACL,OAAO;AACT;AACA,MAAM,aAAa,UAAU,KAAK;AAElC;;;;;CAKC,GACD,MAAM,QAAQ,cAAc;AAE5B,IAAI;AACJ,wCAA2C;IACzC,gBAAgB,WAAW,GAAE,IAAI;AACnC;AACA,SAAS;IACP,IAAI;IACJ,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,WAAW,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QAC3F,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAClC;IACA,MAAM,UAAU,kBAAkB,SAAS,IAAI,CAAC;IAChD,IAAI,CAAC,CAAC,CAAC,iBAAiB,aAAa,KAAK,QAAQ,eAAe,GAAG,CAAC,QAAQ,GAAG;QAC9E,IAAI;QACJ,CAAC,kBAAkB,aAAa,KAAK,QAAQ,gBAAgB,GAAG,CAAC;QACjE,QAAQ,IAAI,CAAC;IACf;AACF;AACA,SAAS;IACP,IAAI;IACJ,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,WAAW,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;QACjG,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;IACpC;IACA,MAAM,UAAU,kBAAkB,SAAS,IAAI,CAAC;IAChD,IAAI,CAAC,CAAC,CAAC,kBAAkB,aAAa,KAAK,QAAQ,gBAAgB,GAAG,CAAC,QAAQ,GAAG;QAChF,IAAI;QACJ,CAAC,kBAAkB,aAAa,KAAK,QAAQ,gBAAgB,GAAG,CAAC;QACjE,QAAQ,KAAK,CAAC;IAChB;AACF;AAEA;;;CAGC,GACD,MAAM,gBAAgB,WAAW,GAAE,8JAAM,UAAU,CAAC,SAAS,cAAc,KAAK,EAAE,GAAG;IACnF,MAAM,EACJ,SAAS,EACP,SAAS,EACT,UAAU,EACR,QAAQ,EACT,EACD,gBAAgB,EACd,KAAK,EACL,KAAK,EACN,EACF,EACD,QAAQ,EAAE,EACV,SAAS,CAAC,EACV,YAAY,CAAC,EACb,cAAc,CAAC,EACf,YAAY,EACZ,MAAM,EACN,CAAC,EACD,OAAO,EACL,SAAS,EACT,GAAG,WACJ,GAAG,CAAC,CAAC,EACN,GAAG,MACJ,GAAG;IACJ,wCAA2C;QACzC,IAAI,CAAC,KAAK;YACR,KAAK;QACP;IACF;IACA,MAAM,aAAa;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,8JAAM,QAAQ,CAAC;IAEzC,yDAAyD;IACzD,MAAM;QACJ,IAAI,CAAC,UAAU;QACf,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,SAAS,KAAK;QACvD,IAAI,OAAO;YACT,SAAS;QACX;IACF,GAAG;QAAC;KAAS;IACb,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IACA,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,KAAK,CAAC;IAC1C,MAAM,iBAAiB,SAAS,SAAS,SAAS;IAClD,IAAI,uBAAuB;IAC3B,IAAI,kBAAkB,SAAS,QAAQ,MAAM,CAAC,IAAI,CAAC,kBAAkB,SAAS,QAAQ,MAAM,CAAC,EAAE;QAC7F,uBAAuB;IACzB;IAEA,2EAA2E;IAC3E,yBAAyB;IACzB,MAAM,sBAAsB,cAAc;IAC1C,MAAM,kBAAkB,sBAAsB;IAC9C,MAAM,OAAO,QAAQ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;IAC5C,MAAM,OAAO,SAAS,IAAI,YAAY;IACtC,MAAM,gBAAgB,CAAC,CAAC;IACxB,MAAM,cAAc,wBAAwB,cAAc,QAAQ,WAAW;IAC7E,IAAI,cAAc,wBAAwB,cAAc,QAAQ,UAAU;IAC1E,IAAI,wBAAwB,OAAO;QACjC,cAAc,cAAc,QAAQ,SAAS;IAC/C;IACA,MAAM,SAAS,CAAC,SAAS,OAAO,KAAK,IAAI,MAAM,CAAC,KAAK,OAAO,wBAAwB,MAAM,CAAC,GAAG;IAC9F,MAAM,SAAS,CAAC,SAAS,OAAO,KAAK,IAAI,MAAM,CAAC,KAAK,OAAO,wBAAwB,MAAM,CAAC,GAAG;IAC9F,MAAM,SAAS,KAAK,SAAS,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,QAAQ,IAAI,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI;IACzK,MAAM,WAAW;QACf,KAAK,gBAAgB,mBAAmB;QACxC,MAAM,gBAAgB,kBAAkB;QACxC,QAAQ,gBAAgB,KAAK;QAC7B,OAAO,gBAAgB,mBAAmB;IAC5C,CAAC,CAAC,KAAK;IACP,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,OAAO,SAAS,CAAC,GAAG,MAAM;QAChE,eAAe;QACf,KAAK;QACL,OAAO,gBAAgB,QAAQ,QAAQ;QACvC,QAAQ;QACR,SAAS,SAAS,QAAQ,MAAM,CAAC,SAAS,QAAQ,SAAS,KAAK;QAChE,OAAO;YACL,UAAU;YACV,eAAe;YACf,CAAC,YAAY,EAAE;YACf,CAAC,YAAY,EAAE;YACf,CAAC,KAAK,EAAE,kBAAkB,gBAAgB,SAAS,iBAAiB,sBAAsB,IAAI;YAC9F,WAAW;gBAAC;gBAAU;aAAU,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,GAAG,IAAI,CAAC;YACvD,GAAG,SAAS;QACd;IACF,IAAI,sBAAsB,KAAK,WAAW,GAAE,8JAAM,aAAa,CAAC,QAAQ;QACtE,UAAU,UAAU,aAAa;QACjC,MAAM;QACN,QAAQ;QAGR,aAAa,sBAAsB,CAAC,IAAI,IAAI,CAAC;QAC7C,GAAG;IACL,IAAI,WAAW,GAAE,8JAAM,aAAa,CAAC,QAAQ;QAC3C,QAAQ,uBAAuB,CAAC,IAAI,KAAK,IAAI,GAAG;QAChD,GAAG;IACL,IAAI,WAAW,GAAE,8JAAM,aAAa,CAAC,YAAY;QAC/C,IAAI;IACN,GAAG,WAAW,GAAE,8JAAM,aAAa,CAAC,QAAQ;QAC1C,GAAG,CAAC;QACJ,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAC5C,OAAO,QAAQ;QACf,QAAQ;IACV;AACF;AAEA,SAAS;IACP,MAAM,MAAM,IAAI;IAChB,OAAO;QACL,MAAK,KAAK,EAAE,IAAI;YACd,IAAI;YACJ,CAAC,WAAW,IAAI,GAAG,CAAC,MAAM,KAAK,QAAQ,SAAS,OAAO,CAAC,CAAA,UAAW,QAAQ;QAC7E;QACA,IAAG,KAAK,EAAE,QAAQ;YAChB,IAAI,GAAG,CAAC,OAAO;mBAAK,IAAI,GAAG,CAAC,UAAU,EAAE;gBAAG;aAAS;QACtD;QACA,KAAI,KAAK,EAAE,QAAQ;YACjB,IAAI;YACJ,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,YAAY,IAAI,GAAG,CAAC,MAAM,KAAK,OAAO,KAAK,IAAI,UAAU,MAAM,CAAC,CAAA,IAAK,MAAM,SAAS,KAAK,EAAE;QAC9G;IACF;AACF;AAEA,MAAM,sBAAsB,WAAW,GAAE,8JAAM,aAAa,CAAC;AAC7D,MAAM,sBAAsB,WAAW,GAAE,8JAAM,aAAa,CAAC;AAE7D;;;CAGC,GACD,MAAM,0BAA0B;IAC9B,IAAI;IACJ,OAAO,CAAC,CAAC,oBAAoB,8JAAM,UAAU,CAAC,oBAAoB,KAAK,OAAO,KAAK,IAAI,kBAAkB,EAAE,KAAK;AAClH;AAEA;;CAEC,GACD,MAAM,kBAAkB,IAAM,8JAAM,UAAU,CAAC;AAE/C;;;CAGC,GACD,SAAS,kBAAkB,cAAc;IACvC,MAAM,KAAK;IACX,MAAM,OAAO;IACb,MAAM,gBAAgB;IACtB,MAAM,WAAW,kBAAkB;IACnC,MAAM;QACJ,MAAM,OAAO;YACX;YACA;QACF;QACA,QAAQ,QAAQ,KAAK,OAAO,CAAC;QAC7B,OAAO;YACL,QAAQ,QAAQ,KAAK,UAAU,CAAC;QAClC;IACF,GAAG;QAAC;QAAM;QAAI;KAAS;IACvB,OAAO;AACT;AACA;;;CAGC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,EACJ,QAAQ,EACR,EAAE,EACH,GAAG;IACJ,MAAM,WAAW;IACjB,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,oBAAoB,QAAQ,EAAE;QACpE,OAAO,8JAAM,OAAO;oCAAC,IAAM,CAAC;oBAC1B;oBACA;gBACF,CAAC;mCAAG;YAAC;YAAI;SAAS;IACpB,GAAG;AACL;AACA;;;;;;;;;CASC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,EACJ,QAAQ,EACT,GAAG;IACJ,MAAM,WAAW,8JAAM,MAAM,CAAC,EAAE;IAChC,MAAM,UAAU,8JAAM,WAAW;6CAAC,CAAA;YAChC,SAAS,OAAO,GAAG;mBAAI,SAAS,OAAO;gBAAE;aAAK;QAChD;4CAAG,EAAE;IACL,MAAM,aAAa,8JAAM,WAAW;gDAAC,CAAA;YACnC,SAAS,OAAO,GAAG,SAAS,OAAO,CAAC,MAAM;wDAAC,CAAA,IAAK,MAAM;;QACxD;+CAAG,EAAE;IACL,MAAM,SAAS,8JAAM,QAAQ;iCAAC,IAAM;+BAAe,CAAC,EAAE;IACtD,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,oBAAoB,QAAQ,EAAE;QACpE,OAAO,8JAAM,OAAO;oCAAC,IAAM,CAAC;oBAC1B;oBACA;oBACA;oBACA;gBACF,CAAC;mCAAG;YAAC;YAAS;YAAY;SAAO;IACnC,GAAG;AACL;AAEA,SAAS,gBAAgB,IAAI;IAC3B,OAAO,sBAAsB;AAC/B;AAEA,SAAS,aAAa,KAAK;IACzB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM;QACJ,IAAI,OAAO,GAAG;IAChB;IACA,OAAO;AACT;AAEA,MAAM,wBAAwB,WAAW,GAAE,gBAAgB;AAC3D,SAAS,SAAS,KAAK,EAAE,IAAI,EAAE,WAAW;IACxC,IAAI,eAAe,CAAC,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,cAAc;QACvD,OAAO;IACT;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO,SAAS,OAAO,KAAK,IAAI,KAAK,CAAC,KAAK;AAC7C;AACA;;;;CAIC,GACD,SAAS,SAAS,OAAO,EAAE,KAAK;IAC9B,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,QAAQ,CAAC,EACT,cAAc,IAAI,EAClB,YAAY,KAAK,EACjB,SAAS,CAAC,EACV,OAAO,IAAI,EACZ,GAAG;IACJ,MAAM,OAAO;IACb,MAAM,WAAW;IACjB,MAAM,iBAAiB,aAAa;IACpC,MAAM,WAAW,aAAa;IAC9B,MAAM,UAAU,aAAa;IAC7B,MAAM,iBAAiB,8JAAM,MAAM;IACnC,MAAM,aAAa,8JAAM,MAAM,CAAC,CAAC;IACjC,MAAM,aAAa,8JAAM,MAAM;IAC/B,MAAM,iBAAiB,8JAAM,MAAM,CAAC,CAAC;IACrC,MAAM,oBAAoB,8JAAM,MAAM,CAAC;IACvC,MAAM,oCAAoC,8JAAM,MAAM,CAAC;IACvD,MAAM,qBAAqB,8JAAM,MAAM;+CAAC,KAAO;;IAC/C,MAAM,wBAAwB,8JAAM,MAAM,CAAC;IAC3C,MAAM,cAAc,8JAAM,WAAW;6CAAC;YACpC,IAAI;YACJ,MAAM,OAAO,CAAC,wBAAwB,QAAQ,OAAO,CAAC,SAAS,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI;YAC9G,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,SAAS;QACtE;4CAAG;QAAC;KAAQ;IAEZ,qEAAqE;IACrE,gBAAgB;IAChB,8JAAM,SAAS;8BAAC;YACd,IAAI,CAAC,SAAS;YACd,SAAS,aAAa,IAAI;gBACxB,IAAI,EACF,IAAI,EACL,GAAG;gBACJ,IAAI,CAAC,MAAM;oBACT,aAAa,WAAW,OAAO;oBAC/B,aAAa,eAAe,OAAO;oBACnC,kBAAkB,OAAO,GAAG;oBAC5B,sBAAsB,OAAO,GAAG;gBAClC;YACF;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;sCAAO;oBACL,OAAO,GAAG,CAAC,cAAc;gBAC3B;;QACF;6BAAG;QAAC;QAAS;KAAO;IACpB,8JAAM,SAAS;8BAAC;YACd,IAAI,CAAC,SAAS;YACd,IAAI,CAAC,eAAe,OAAO,EAAE;YAC7B,IAAI,CAAC,MAAM;YACX,SAAS,QAAQ,KAAK;gBACpB,IAAI,eAAe;oBACjB,aAAa,OAAO,OAAO;gBAC7B;YACF;YACA,MAAM,OAAO,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,SAAS,QAAQ,EAAE,eAAe;YAC3D,KAAK,gBAAgB,CAAC,cAAc;YACpC;sCAAO;oBACL,KAAK,mBAAmB,CAAC,cAAc;gBACzC;;QACF;6BAAG;QAAC,SAAS,QAAQ;QAAE;QAAM;QAAc;QAAS;QAAgB;KAAY;IAChF,MAAM,iBAAiB,8JAAM,WAAW;gDAAC,SAAU,KAAK,EAAE,aAAa,EAAE,MAAM;YAC7E,IAAI,kBAAkB,KAAK,GAAG;gBAC5B,gBAAgB;YAClB;YACA,IAAI,WAAW,KAAK,GAAG;gBACrB,SAAS;YACX;YACA,MAAM,aAAa,SAAS,SAAS,OAAO,EAAE,SAAS,eAAe,OAAO;YAC7E,IAAI,cAAc,CAAC,WAAW,OAAO,EAAE;gBACrC,aAAa,WAAW,OAAO;gBAC/B,WAAW,OAAO,GAAG,OAAO,UAAU;4DAAC,IAAM,aAAa,OAAO,OAAO;2DAAS;YACnF,OAAO,IAAI,eAAe;gBACxB,aAAa,WAAW,OAAO;gBAC/B,aAAa,OAAO,OAAO;YAC7B;QACF;+CAAG;QAAC;QAAU;KAAa;IAC3B,MAAM,0BAA0B;4DAAe;YAC7C,mBAAmB,OAAO;YAC1B,WAAW,OAAO,GAAG;QACvB;;IACA,MAAM,qBAAqB;uDAAe;YACxC,IAAI,kCAAkC,OAAO,EAAE;gBAC7C,MAAM,OAAO,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,SAAS,QAAQ,EAAE,IAAI;gBAChD,KAAK,KAAK,CAAC,aAAa,GAAG;gBAC3B,KAAK,eAAe,CAAC;gBACrB,kCAAkC,OAAO,GAAG;YAC9C;QACF;;IACA,MAAM,uBAAuB;yDAAe;YAC1C,OAAO,QAAQ,OAAO,CAAC,SAAS,GAAG;gBAAC;gBAAS;aAAY,CAAC,QAAQ,CAAC,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI;QACvG;;IAEA,2EAA2E;IAC3E,8EAA8E;IAC9E,8EAA8E;IAC9E,8JAAM,SAAS;8BAAC;YACd,IAAI,CAAC,SAAS;YACd,SAAS,aAAa,KAAK;gBACzB,aAAa,WAAW,OAAO;gBAC/B,kBAAkB,OAAO,GAAG;gBAC5B,IAAI,aAAa,CAAC,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,OAAO,KAAK,SAAS,KAAK,CAAC,SAAS,SAAS,OAAO,EAAE,SAAS;oBACrH;gBACF;gBACA,MAAM,YAAY,SAAS,SAAS,OAAO,EAAE,QAAQ,eAAe,OAAO;gBAC3E,IAAI,WAAW;oBACb,WAAW,OAAO,GAAG,OAAO,UAAU;2DAAC;4BACrC,IAAI,CAAC,QAAQ,OAAO,EAAE;gCACpB,aAAa,MAAM,OAAO;4BAC5B;wBACF;0DAAG;gBACL,OAAO,IAAI,CAAC,MAAM;oBAChB,aAAa,MAAM,OAAO;gBAC5B;YACF;YACA,SAAS,aAAa,KAAK;gBACzB,IAAI,wBAAwB;gBAC5B,mBAAmB,OAAO;gBAC1B,MAAM,MAAM,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,SAAS,QAAQ;gBACzC,aAAa,eAAe,OAAO;gBACnC,sBAAsB,OAAO,GAAG;gBAChC,IAAI,eAAe,OAAO,IAAI,QAAQ,OAAO,CAAC,eAAe,EAAE;oBAC7D,iDAAiD;oBACjD,IAAI,CAAC,MAAM;wBACT,aAAa,WAAW,OAAO;oBACjC;oBACA,WAAW,OAAO,GAAG,eAAe,OAAO,CAAC;wBAC1C,GAAG,QAAQ,OAAO,CAAC,eAAe;wBAClC;wBACA,GAAG,MAAM,OAAO;wBAChB,GAAG,MAAM,OAAO;wBAChB;4BACE;4BACA;4BACA,IAAI,CAAC,wBAAwB;gCAC3B,eAAe,OAAO,MAAM;4BAC9B;wBACF;oBACF;oBACA,MAAM,UAAU,WAAW,OAAO;oBAClC,IAAI,gBAAgB,CAAC,aAAa;oBAClC,mBAAmB,OAAO;2DAAG;4BAC3B,IAAI,mBAAmB,CAAC,aAAa;wBACvC;;oBACA;gBACF;gBAEA,qEAAqE;gBACrE,oEAAoE;gBACpE,gBAAgB;gBAChB,MAAM,cAAc,eAAe,OAAO,KAAK,UAAU,CAAC,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ,EAAE,MAAM,aAAa,IAAI;gBAC7G,IAAI,aAAa;oBACf,eAAe;gBACjB;YACF;YAEA,yEAAyE;YACzE,gBAAgB;YAChB,8DAA8D;YAC9D,SAAS,mBAAmB,KAAK;gBAC/B,IAAI,wBAAwB;gBAC5B,IAAI,CAAC,QAAQ,OAAO,CAAC,eAAe,EAAE;gBACtC,eAAe,OAAO,IAAI,QAAQ,eAAe,OAAO,CAAC;oBACvD,GAAG,QAAQ,OAAO,CAAC,eAAe;oBAClC;oBACA,GAAG,MAAM,OAAO;oBAChB,GAAG,MAAM,OAAO;oBAChB;wBACE;wBACA;wBACA,IAAI,CAAC,wBAAwB;4BAC3B,eAAe;wBACjB;oBACF;gBACF,GAAG;YACL;YACA,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY,GAAG;gBACpC,IAAI;gBACJ,MAAM,MAAM,SAAS,YAAY;gBACjC,QAAQ,IAAI,gBAAgB,CAAC,cAAc;gBAC3C,CAAC,qBAAqB,SAAS,QAAQ,KAAK,QAAQ,mBAAmB,gBAAgB,CAAC,cAAc;gBACtG,QAAQ,IAAI,gBAAgB,CAAC,aAAa,cAAc;oBACtD,MAAM;gBACR;gBACA,IAAI,gBAAgB,CAAC,cAAc;gBACnC,IAAI,gBAAgB,CAAC,cAAc;gBACnC;0CAAO;wBACL,IAAI;wBACJ,QAAQ,IAAI,mBAAmB,CAAC,cAAc;wBAC9C,CAAC,sBAAsB,SAAS,QAAQ,KAAK,QAAQ,oBAAoB,mBAAmB,CAAC,cAAc;wBAC3G,QAAQ,IAAI,mBAAmB,CAAC,aAAa;wBAC7C,IAAI,mBAAmB,CAAC,cAAc;wBACtC,IAAI,mBAAmB,CAAC,cAAc;oBACxC;;YACF;QACF;6BAAG;QAAC;QAAU;QAAS;QAAS;QAAW;QAAQ;QAAM;QAAgB;QAAyB;QAAoB;QAAc;QAAM;QAAS;QAAM;QAAU;QAAgB;QAAS;KAAqB;IAEjN,8EAA8E;IAC9E,2EAA2E;IAC3E,oCAAoC;IACpC,yDAAyD;IACzD,MAAM;QACJ,IAAI;QACJ,IAAI,CAAC,SAAS;QACd,IAAI,QAAQ,CAAC,wBAAwB,eAAe,OAAO,KAAK,QAAQ,sBAAsB,SAAS,CAAC,kBAAkB,IAAI,eAAe;YAC3I,kCAAkC,OAAO,GAAG;YAC5C,MAAM,aAAa,SAAS,QAAQ;YACpC,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY,KAAK,YAAY;gBAClD,IAAI;gBACJ,MAAM,OAAO,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,SAAS,QAAQ,EAAE,IAAI;gBAChD,KAAK,YAAY,CAAC,uBAAuB;gBACzC,MAAM,MAAM,SAAS,YAAY;gBACjC,MAAM,iBAAiB,QAAQ,QAAQ,CAAC,wBAAwB,KAAK,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,OAAO,KAAK,OAAO,KAAK,IAAI,sBAAsB,QAAQ,CAAC,QAAQ;gBACvP,IAAI,gBAAgB;oBAClB,eAAe,KAAK,CAAC,aAAa,GAAG;gBACvC;gBACA,KAAK,KAAK,CAAC,aAAa,GAAG;gBAC3B,IAAI,KAAK,CAAC,aAAa,GAAG;gBAC1B,WAAW,KAAK,CAAC,aAAa,GAAG;gBACjC,OAAO;oBACL,KAAK,KAAK,CAAC,aAAa,GAAG;oBAC3B,IAAI,KAAK,CAAC,aAAa,GAAG;oBAC1B,WAAW,KAAK,CAAC,aAAa,GAAG;gBACnC;YACF;QACF;IACF,GAAG;QAAC;QAAS;QAAM;QAAU;QAAU;QAAM;QAAgB;KAAY;IACzE,MAAM;QACJ,IAAI,CAAC,MAAM;YACT,eAAe,OAAO,GAAG;YACzB,sBAAsB,OAAO,GAAG;YAChC;YACA;QACF;IACF,GAAG;QAAC;QAAM;QAAyB;KAAmB;IACtD,8JAAM,SAAS;8BAAC;YACd;sCAAO;oBACL;oBACA,aAAa,WAAW,OAAO;oBAC/B,aAAa,eAAe,OAAO;oBACnC;gBACF;;QACF;6BAAG;QAAC;QAAS,SAAS,YAAY;QAAE;QAAyB;KAAmB;IAChF,MAAM,YAAY,8JAAM,OAAO;uCAAC;YAC9B,SAAS,cAAc,KAAK;gBAC1B,eAAe,OAAO,GAAG,MAAM,WAAW;YAC5C;YACA,OAAO;gBACL,eAAe;gBACf,gBAAgB;gBAChB,aAAY,KAAK;oBACf,MAAM,EACJ,WAAW,EACZ,GAAG;oBACJ,SAAS;wBACP,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE;4BAClD,aAAa,MAAM,aAAa;wBAClC;oBACF;oBACA,IAAI,aAAa,CAAC,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,OAAO,GAAG;wBAChE;oBACF;oBACA,IAAI,QAAQ,WAAW,GAAG;wBACxB;oBACF;oBAEA,yDAAyD;oBACzD,IAAI,sBAAsB,OAAO,IAAI,MAAM,SAAS,IAAI,IAAI,MAAM,SAAS,IAAI,IAAI,GAAG;wBACpF;oBACF;oBACA,aAAa,eAAe,OAAO;oBACnC,IAAI,eAAe,OAAO,KAAK,SAAS;wBACtC;oBACF,OAAO;wBACL,sBAAsB,OAAO,GAAG;wBAChC,eAAe,OAAO,GAAG,OAAO,UAAU,CAAC,iBAAiB;oBAC9D;gBACF;YACF;QACF;sCAAG;QAAC;QAAW;QAAc;QAAM;QAAS;KAAO;IACnD,MAAM,WAAW,8JAAM,OAAO;sCAAC,IAAM,CAAC;gBACpC;oBACE,aAAa,WAAW,OAAO;gBACjC;gBACA,cAAa,KAAK;oBAChB,IAAI,CAAC,wBAAwB;wBAC3B,eAAe,MAAM,WAAW,EAAE;oBACpC;gBACF;YACF,CAAC;qCAAG;QAAC;QAAgB;KAAqB;IAC1C,OAAO,8JAAM,OAAO;4BAAC,IAAM,UAAU;gBACnC;gBACA;YACF,IAAI,CAAC;2BAAG;QAAC;QAAS;QAAW;KAAS;AACxC;AAEA,MAAM,OAAO,KAAO;AACpB,MAAM,4BAA4B,WAAW,GAAE,8JAAM,aAAa,CAAC;IACjE,OAAO;IACP,cAAc;IACd,WAAW;IACX,WAAW;IACX,cAAc;IACd,UAAU;IACV,gBAAgB;AAClB;AAEA;;;CAGC,GACD,MAAM,uBAAuB,IAAM,8JAAM,UAAU,CAAC;AACpD;;;;CAIC,GACD,SAAS,mBAAmB,KAAK;IAC/B,MAAM,EACJ,QAAQ,EACR,KAAK,EACL,YAAY,CAAC,EACd,GAAG;IACJ,MAAM,CAAC,OAAO,SAAS,GAAG,8JAAM,UAAU;yCAAC,CAAC,MAAM,OAAS,CAAC;gBAC1D,GAAG,IAAI;gBACP,GAAG,IAAI;YACT,CAAC;wCAAG;QACF;QACA;QACA,cAAc;QACd,WAAW;QACX,gBAAgB;IAClB;IACA,MAAM,sBAAsB,8JAAM,MAAM,CAAC;IACzC,MAAM,eAAe,8JAAM,WAAW;wDAAC,CAAA;YACrC,SAAS;gBACP;YACF;QACF;uDAAG,EAAE;IACL,MAAM;QACJ,IAAI,MAAM,SAAS,EAAE;YACnB,IAAI,oBAAoB,OAAO,KAAK,MAAM;gBACxC,oBAAoB,OAAO,GAAG,MAAM,SAAS;YAC/C,OAAO,IAAI,CAAC,MAAM,cAAc,EAAE;gBAChC,SAAS;oBACP,gBAAgB;gBAClB;YACF;QACF,OAAO;YACL,IAAI,MAAM,cAAc,EAAE;gBACxB,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,oBAAoB,OAAO,GAAG;QAChC;IACF,GAAG;QAAC,MAAM,SAAS;QAAE,MAAM,cAAc;KAAC;IAC1C,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,0BAA0B,QAAQ,EAAE;QAC1E,OAAO,8JAAM,OAAO;0CAAC,IAAM,CAAC;oBAC1B,GAAG,KAAK;oBACR;oBACA;gBACF,CAAC;yCAAG;YAAC;YAAO;SAAa;IAC3B,GAAG;AACL;AACA;;;;CAIC,GACD,SAAS,cAAc,OAAO,EAAE,OAAO;IACrC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,UAAU,EACX,GAAG;IACJ,MAAM,EACJ,IAAI,QAAQ,EACZ,UAAU,IAAI,EACf,GAAG;IACJ,MAAM,KAAK,YAAY,OAAO,WAAW;IACzC,MAAM,eAAe;IACrB,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,SAAS,EACV,GAAG;IACJ,MAAM;QACJ,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,WAAW;QAChB,SAAS;YACP,OAAO;gBACL,MAAM;gBACN,OAAO,SAAS,cAAc;YAChC;QACF;QACA,IAAI,cAAc,IAAI;YACpB,aAAa;QACf;IACF,GAAG;QAAC;QAAS;QAAI;QAAc;QAAU;QAAW;KAAa;IACjE,MAAM;QACJ,SAAS;YACP,aAAa;YACb,SAAS;gBACP,OAAO;gBACP,WAAW;YACb;QACF;QACA,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,QAAQ,cAAc,IAAI;YAC7B,IAAI,WAAW;gBACb,MAAM,UAAU,OAAO,UAAU,CAAC,OAAO;gBACzC,OAAO;oBACL,aAAa;gBACf;YACF;YACA;QACF;IACF,GAAG;QAAC;QAAS;QAAM;QAAU;QAAW;QAAI;QAAc;QAAc;KAAU;IAClF,MAAM;QACJ,IAAI,CAAC,SAAS;QACd,IAAI,iBAAiB,QAAQ,CAAC,MAAM;QACpC,aAAa;IACf,GAAG;QAAC;QAAS;QAAM;QAAc;KAAG;IACpC,OAAO;AACT;AAEA,IAAI,QAAQ;AACZ,SAAS,aAAa,EAAE,EAAE,OAAO;IAC/B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,gBAAgB,KAAK,EACrB,iBAAiB,IAAI,EACrB,OAAO,KAAK,EACb,GAAG;IACJ,kBAAkB,qBAAqB;IACvC,MAAM,OAAO,IAAM,MAAM,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC;YAChD;QACF;IACA,IAAI,MAAM;QACR;IACF,OAAO;QACL,QAAQ,sBAAsB;IAChC;AACF;AAEA,SAAS,aAAa,KAAK,EAAE,EAAE;IAC7B,IAAI;IACJ,IAAI,eAAe,EAAE;IACrB,IAAI,kBAAkB,CAAC,cAAc,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,GAAG,KAAK,OAAO,KAAK,IAAI,YAAY,QAAQ;IAChH,MAAO,gBAAiB;QACtB,MAAM,cAAc,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACnD,kBAAkB,eAAe,OAAO,KAAK,IAAI,YAAY,QAAQ;QACrE,IAAI,aAAa;YACf,eAAe,aAAa,MAAM,CAAC;QACrC;IACF;IACA,OAAO;AACT;AAEA,SAAS,YAAY,KAAK,EAAE,EAAE;IAC5B,IAAI,cAAc,MAAM,MAAM,CAAC,CAAA;QAC7B,IAAI;QACJ,OAAO,KAAK,QAAQ,KAAK,MAAM,CAAC,CAAC,gBAAgB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,cAAc,IAAI;IACtG;IACA,IAAI,kBAAkB;IACtB,MAAO,gBAAgB,MAAM,CAAE;QAC7B,kBAAkB,MAAM,MAAM,CAAC,CAAA;YAC7B,IAAI;YACJ,OAAO,CAAC,mBAAmB,eAAe,KAAK,OAAO,KAAK,IAAI,iBAAiB,IAAI,CAAC,CAAA;gBACnF,IAAI;gBACJ,OAAO,KAAK,QAAQ,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,IAAI;YAC1G;QACF;QACA,cAAc,YAAY,MAAM,CAAC;IACnC;IACA,OAAO;AACT;AACA,SAAS,eAAe,KAAK,EAAE,EAAE;IAC/B,IAAI;IACJ,IAAI,WAAW,CAAC;IAChB,SAAS,YAAY,MAAM,EAAE,KAAK;QAChC,IAAI,QAAQ,UAAU;YACpB,gBAAgB;YAChB,WAAW;QACb;QACA,MAAM,WAAW,YAAY,OAAO;QACpC,SAAS,OAAO,CAAC,CAAA;YACf,YAAY,MAAM,EAAE,EAAE,QAAQ;QAChC;IACF;IACA,YAAY,IAAI;IAChB,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AACxC;AAEA,qDAAqD;AACrD,sGAAsG;AACtG,IAAI,aAAa,WAAW,GAAE,IAAI;AAClC,IAAI,0BAA0B,WAAW,GAAE,IAAI;AAC/C,IAAI,YAAY,CAAC;AACjB,IAAI,cAAc;AAClB,MAAM,gBAAgB,IAAM,OAAO,gBAAgB,eAAe,WAAW,YAAY,SAAS;AAClG,MAAM,aAAa,CAAA,OAAQ,QAAQ,CAAC,KAAK,IAAI,IAAI,WAAW,KAAK,UAAU,CAAC;AAC5E,MAAM,kBAAkB,CAAC,QAAQ,UAAY,QAAQ,GAAG,CAAC,CAAA;QACvD,IAAI,OAAO,QAAQ,CAAC,SAAS;YAC3B,OAAO;QACT;QACA,MAAM,kBAAkB,WAAW;QACnC,IAAI,OAAO,QAAQ,CAAC,kBAAkB;YACpC,OAAO;QACT;QACA,OAAO;IACT,GAAG,MAAM,CAAC,CAAA,IAAK,KAAK;AACpB,SAAS,uBAAuB,wBAAwB,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK;IAC/E,MAAM,aAAa;IACnB,MAAM,mBAAmB,QAAQ,UAAU,aAAa,gBAAgB;IACxE,MAAM,gBAAgB,gBAAgB,MAAM;IAC5C,MAAM,iBAAiB,IAAI;IAC3B,MAAM,iBAAiB,IAAI,IAAI;IAC/B,MAAM,iBAAiB,EAAE;IACzB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;QAC1B,SAAS,CAAC,WAAW,GAAG,IAAI;IAC9B;IACA,MAAM,gBAAgB,SAAS,CAAC,WAAW;IAC3C,cAAc,OAAO,CAAC;IACtB,KAAK;IACL,eAAe,KAAK;IACpB,SAAS,KAAK,EAAE;QACd,IAAI,CAAC,MAAM,eAAe,GAAG,CAAC,KAAK;YACjC;QACF;QACA,eAAe,GAAG,CAAC;QACnB,GAAG,UAAU,IAAI,KAAK,GAAG,UAAU;IACrC;IACA,SAAS,KAAK,MAAM;QAClB,IAAI,CAAC,UAAU,eAAe,GAAG,CAAC,SAAS;YACzC;QACF;QACA,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,QAAQ,EAAE,CAAA;YAC/B,IAAI,CAAA,GAAA,uLAAA,CAAA,cAAW,AAAD,EAAE,UAAU,UAAU;YACpC,IAAI,eAAe,GAAG,CAAC,OAAO;gBAC5B,KAAK;YACP,OAAO;gBACL,MAAM,OAAO,mBAAmB,KAAK,YAAY,CAAC,oBAAoB;gBACtE,MAAM,gBAAgB,SAAS,QAAQ,SAAS;gBAChD,MAAM,eAAe,CAAC,WAAW,GAAG,CAAC,SAAS,CAAC,IAAI;gBACnD,MAAM,cAAc,CAAC,cAAc,GAAG,CAAC,SAAS,CAAC,IAAI;gBACrD,WAAW,GAAG,CAAC,MAAM;gBACrB,cAAc,GAAG,CAAC,MAAM;gBACxB,eAAe,IAAI,CAAC;gBACpB,IAAI,iBAAiB,KAAK,eAAe;oBACvC,wBAAwB,GAAG,CAAC;gBAC9B;gBACA,IAAI,gBAAgB,GAAG;oBACrB,KAAK,YAAY,CAAC,YAAY;gBAChC;gBACA,IAAI,CAAC,iBAAiB,kBAAkB;oBACtC,KAAK,YAAY,CAAC,kBAAkB;gBACtC;YACF;QACF;IACF;IACA;IACA,OAAO;QACL,eAAe,OAAO,CAAC,CAAA;YACrB,MAAM,eAAe,CAAC,WAAW,GAAG,CAAC,YAAY,CAAC,IAAI;YACtD,MAAM,cAAc,CAAC,cAAc,GAAG,CAAC,YAAY,CAAC,IAAI;YACxD,WAAW,GAAG,CAAC,SAAS;YACxB,cAAc,GAAG,CAAC,SAAS;YAC3B,IAAI,CAAC,cAAc;gBACjB,IAAI,CAAC,wBAAwB,GAAG,CAAC,YAAY,kBAAkB;oBAC7D,QAAQ,eAAe,CAAC;gBAC1B;gBACA,wBAAwB,MAAM,CAAC;YACjC;YACA,IAAI,CAAC,aAAa;gBAChB,QAAQ,eAAe,CAAC;YAC1B;QACF;QACA;QACA,IAAI,CAAC,aAAa;YAChB,aAAa,IAAI;YACjB,aAAa,IAAI;YACjB,0BAA0B,IAAI;YAC9B,YAAY,CAAC;QACf;IACF;AACF;AACA,SAAS,WAAW,aAAa,EAAE,UAAU,EAAE,KAAK;IAClD,IAAI,eAAe,KAAK,GAAG;QACzB,aAAa;IACf;IACA,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ;IACV;IACA,MAAM,OAAO,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,aAAa,CAAC,EAAE,EAAE,IAAI;IAC/C,OAAO,uBAAuB,cAAc,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,gBAAgB,CAAC,kBAAkB,MAAM,YAAY;AAC1H;AAEA,MAAM,qBAAqB,IAAM,CAAC;QAChC,eAAe;QACf,cACA,sEAAsE;QACtE,uEAAuE;QACvE,uDAAuD;QACvD,OAAO,mBAAmB,cAAc,eAAe,QAAQ,GAAG,QAAQ,CAAC,mBAAmB,SAAS;IACzG,CAAC;AACD,SAAS,cAAc,SAAS,EAAE,SAAS;IACzC,MAAM,cAAc,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;IACxC,IAAI,cAAc,QAAQ;QACxB,YAAY,OAAO;IACrB;IACA,MAAM,cAAc,YAAY,OAAO,CAAC,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE;IAClE,MAAM,uBAAuB,YAAY,KAAK,CAAC,cAAc;IAC7D,OAAO,oBAAoB,CAAC,EAAE;AAChC;AACA,SAAS;IACP,OAAO,cAAc,SAAS,IAAI,EAAE;AACtC;AACA,SAAS;IACP,OAAO,cAAc,SAAS,IAAI,EAAE;AACtC;AACA,SAAS,eAAe,KAAK,EAAE,SAAS;IACtC,MAAM,mBAAmB,aAAa,MAAM,aAAa;IACzD,MAAM,gBAAgB,MAAM,aAAa;IACzC,OAAO,CAAC,iBAAiB,CAAC,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB;AACvD;AACA,SAAS,mBAAmB,SAAS;IACnC,MAAM,mBAAmB,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;IAC7C,iBAAiB,OAAO,CAAC,CAAA;QACvB,QAAQ,OAAO,CAAC,QAAQ,GAAG,QAAQ,YAAY,CAAC,eAAe;QAC/D,QAAQ,YAAY,CAAC,YAAY;IACnC;AACF;AACA,SAAS,kBAAkB,SAAS;IAClC,MAAM,WAAW,UAAU,gBAAgB,CAAC;IAC5C,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;QACzC,OAAO,QAAQ,OAAO,CAAC,QAAQ;QAC/B,IAAI,UAAU;YACZ,QAAQ,YAAY,CAAC,YAAY;QACnC,OAAO;YACL,QAAQ,eAAe,CAAC;QAC1B;IACF;AACF;AAEA,yEAAyE;AACzE,yEAAyE;AAEzE,MAAM,gBAAgB;IACpB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,SAAS;IACT,UAAU;IACV,YAAY;IACZ,OAAO;IACP,KAAK;IACL,MAAM;AACR;AACA,IAAI;AACJ,SAAS,sBAAsB,KAAK;IAClC,IAAI,MAAM,GAAG,KAAK,OAAO;QACvB,MAAM,MAAM;QACZ,aAAa;IACf;AACF;AACA,MAAM,aAAa,WAAW,GAAE,8JAAM,UAAU,CAAC,SAAS,WAAW,KAAK,EAAE,GAAG;IAC7E,MAAM,CAAC,MAAM,QAAQ,GAAG,8JAAM,QAAQ;IACtC,MAAM;QACJ,IAAI,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,KAAK;YACd,wEAAwE;YACxE,uEAAuE;YACvE,mEAAmE;YACnE,uEAAuE;YACvE,eAAe;YACf,QAAQ;QACV;QACA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG,EAAE;IACL,MAAM,YAAY;QAChB;QACA,UAAU;QACV,6BAA6B;QAC7B;QACA,eAAe,OAAO,YAAY;QAClC,CAAC,gBAAgB,eAAe,EAAE;QAClC,OAAO;IACT;IACA,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,QAAQ,SAAS,CAAC,GAAG,OAAO;AACtE;AAEA,MAAM,gBAAgB,WAAW,GAAE,8JAAM,aAAa,CAAC;AACvD,MAAM,OAAO,WAAW,GAAE,gBAAgB;AAC1C;;CAEC,GACD,SAAS,sBAAsB,KAAK;IAClC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,EAAE,EACF,IAAI,EACL,GAAG;IACJ,MAAM,WAAW;IACjB,MAAM,gBAAgB;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,8JAAM,QAAQ,CAAC;IACnD,MAAM,gBAAgB,8JAAM,MAAM,CAAC;IACnC,MAAM;QACJ,OAAO;YACL,cAAc,QAAQ,WAAW,MAAM;YACvC,uEAAuE;YACvE,uDAAuD;YACvD,yDAAyD;YACzD,eAAe;gBACb,cAAc,OAAO,GAAG;YAC1B;QACF;IACF,GAAG;QAAC;KAAW;IACf,MAAM;QACJ,2EAA2E;QAC3E,mEAAmE;QACnE,yDAAyD;QACzD,IAAI,CAAC,UAAU;QACf,IAAI,cAAc,OAAO,EAAE;QAC3B,MAAM,iBAAiB,KAAK,SAAS,cAAc,CAAC,MAAM;QAC1D,IAAI,CAAC,gBAAgB;QACrB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,EAAE,GAAG;QACb,QAAQ,YAAY,CAAC,MAAM;QAC3B,eAAe,WAAW,CAAC;QAC3B,cAAc,OAAO,GAAG;QACxB,cAAc;IAChB,GAAG;QAAC;QAAI;KAAS;IACjB,MAAM;QACJ,4EAA4E;QAC5E,8DAA8D;QAC9D,IAAI,SAAS,MAAM;QACnB,IAAI,CAAC,UAAU;QACf,IAAI,cAAc,OAAO,EAAE;QAC3B,IAAI,YAAY,QAAQ,CAAC,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU;QAClF,IAAI,aAAa,CAAC,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,YAAY,YAAY,UAAU,OAAO;QACrE,YAAY,aAAa,SAAS,IAAI;QACtC,IAAI,YAAY;QAChB,IAAI,IAAI;YACN,YAAY,SAAS,aAAa,CAAC;YACnC,UAAU,EAAE,GAAG;YACf,UAAU,WAAW,CAAC;QACxB;QACA,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,QAAQ,EAAE,GAAG;QACb,QAAQ,YAAY,CAAC,MAAM;QAC3B,YAAY,aAAa;QACzB,UAAU,WAAW,CAAC;QACtB,cAAc,OAAO,GAAG;QACxB,cAAc;IAChB,GAAG;QAAC;QAAI;QAAM;QAAU;KAAc;IACtC,OAAO;AACT;AACA;;;;;;;CAOC,GACD,SAAS,eAAe,KAAK;IAC3B,MAAM,EACJ,QAAQ,EACR,EAAE,EACF,IAAI,EACJ,mBAAmB,IAAI,EACxB,GAAG;IACJ,MAAM,aAAa,sBAAsB;QACvC;QACA;IACF;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,8JAAM,QAAQ,CAAC;IACjE,MAAM,mBAAmB,8JAAM,MAAM,CAAC;IACtC,MAAM,kBAAkB,8JAAM,MAAM,CAAC;IACrC,MAAM,kBAAkB,8JAAM,MAAM,CAAC;IACrC,MAAM,iBAAiB,8JAAM,MAAM,CAAC;IACpC,MAAM,QAAQ,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,KAAK;IAC1E,MAAM,OAAO,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,IAAI;IACxE,MAAM,qBACN,sEAAsE;IACtE,YAAY;IACZ,CAAC,CAAC,qBACF,kDAAkD;IAClD,CAAC,kBAAkB,KAAK,IACxB,4CAA4C;IAC5C,kBAAkB,IAAI,IAAI,oBAAoB,CAAC,CAAC,CAAC,QAAQ,UAAU;IAEnE,8EAA8E;IAC9E,8JAAM,SAAS;oCAAC;YACd,IAAI,CAAC,cAAc,CAAC,oBAAoB,OAAO;gBAC7C;YACF;YAEA,0EAA0E;YAC1E,uEAAuE;YACvE,sCAAsC;YACtC,SAAS,QAAQ,KAAK;gBACpB,IAAI,cAAc,eAAe,QAAQ;oBACvC,MAAM,WAAW,MAAM,IAAI,KAAK;oBAChC,MAAM,cAAc,WAAW,oBAAoB;oBACnD,YAAY;gBACd;YACF;YACA,wEAAwE;YACxE,wCAAwC;YACxC,WAAW,gBAAgB,CAAC,WAAW,SAAS;YAChD,WAAW,gBAAgB,CAAC,YAAY,SAAS;YACjD;4CAAO;oBACL,WAAW,mBAAmB,CAAC,WAAW,SAAS;oBACnD,WAAW,mBAAmB,CAAC,YAAY,SAAS;gBACtD;;QACF;mCAAG;QAAC;QAAY;QAAkB;KAAM;IACxC,8JAAM,SAAS;oCAAC;YACd,IAAI,CAAC,YAAY;YACjB,IAAI,MAAM;YACV,kBAAkB;QACpB;mCAAG;QAAC;QAAM;KAAW;IACrB,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,cAAc,QAAQ,EAAE;QAC9D,OAAO,8JAAM,OAAO;sCAAC,IAAM,CAAC;oBAC1B;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;gBACF,CAAC;qCAAG;YAAC;YAAkB;SAAW;IACpC,GAAG,sBAAsB,cAAc,WAAW,GAAE,8JAAM,aAAa,CAAC,YAAY;QAClF,aAAa;QACb,KAAK;QACL,SAAS,CAAA;YACP,IAAI,eAAe,OAAO,aAAa;gBACrC,IAAI;gBACJ,CAAC,wBAAwB,gBAAgB,OAAO,KAAK,QAAQ,sBAAsB,KAAK;YAC1F,OAAO;gBACL,MAAM,eAAe,yBAAyB,CAAC,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,IAAI,CAAC,YAAY,CAAC,OAAO;gBAC/H,gBAAgB,QAAQ,aAAa,KAAK;YAC5C;QACF;IACF,IAAI,sBAAsB,cAAc,WAAW,GAAE,8JAAM,aAAa,CAAC,QAAQ;QAC/E,aAAa,WAAW,EAAE;QAC1B,OAAO;IACT,IAAI,cAAc,WAAW,GAAE,CAAA,GAAA,oKAAA,CAAA,eAAqB,AAAD,EAAE,UAAU,aAAa,sBAAsB,cAAc,WAAW,GAAE,8JAAM,aAAa,CAAC,YAAY;QAC3J,aAAa;QACb,KAAK;QACL,SAAS,CAAA;YACP,IAAI,eAAe,OAAO,aAAa;gBACrC,IAAI;gBACJ,CAAC,wBAAwB,eAAe,OAAO,KAAK,QAAQ,sBAAsB,KAAK;YACzF,OAAO;gBACL,MAAM,eAAe,qBAAqB,CAAC,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,IAAI,CAAC,YAAY,CAAC,OAAO;gBAC3H,gBAAgB,QAAQ,aAAa,KAAK;gBAC1C,CAAC,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,eAAe,KAAK,CAAC,qBAAqB,OAAO,KAAK,IAAI,kBAAkB,YAAY,CAAC,OAAO,MAAM,WAAW,EAAE,YAAY;YACzL;QACF;IACF;AACF;AACA,MAAM,mBAAmB,IAAM,8JAAM,UAAU,CAAC;AAEhD,MAAM,sBAAsB;AAC5B,SAAS,wBAAwB,eAAe;IAC9C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IACA,2EAA2E;IAC3E,8EAA8E;IAC9E,8EAA8E;IAC9E,cAAc;IACd,OAAO,gBAAgB,YAAY,CAAC,uBAAuB,kBAAkB,gBAAgB,aAAa,CAAC,MAAM,sBAAsB,QAAQ;AACjJ;AAEA,MAAM,aAAa;AACnB,IAAI,4BAA4B,EAAE;AAClC,SAAS,4BAA4B,OAAO;IAC1C,4BAA4B,0BAA0B,MAAM,CAAC,CAAA,KAAM,GAAG,WAAW;IACjF,IAAI,aAAa;IACjB,IAAI,CAAC,cAAc,CAAA,GAAA,uLAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,QAAQ;IACvD,IAAI,CAAC,CAAA,GAAA,mJAAA,CAAA,aAAU,AAAD,EAAE,YAAY,uBAAuB;QACjD,MAAM,gBAAgB,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,qBAAqB,CAAC,EAAE;QACnE,IAAI,eAAe;YACjB,aAAa;QACf;IACF;IACA,0BAA0B,IAAI,CAAC;IAC/B,IAAI,0BAA0B,MAAM,GAAG,YAAY;QACjD,4BAA4B,0BAA0B,KAAK,CAAC,CAAC;IAC/D;AACF;AACA,SAAS;IACP,OAAO,0BAA0B,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,CAAA,KAAM,GAAG,WAAW;AAC9E;AACA,MAAM,wBAAwB,WAAW,GAAE,8JAAM,UAAU,CAAC,SAAS,sBAAsB,KAAK,EAAE,GAAG;IACnG,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,UAAU,SAAS,CAAC,GAAG,OAAO;QACpE,MAAM;QACN,KAAK;QACL,UAAU,CAAC;QACX,OAAO;IACT;AACF;AACA;;;CAGC,GACD,SAAS,qBAAqB,KAAK;IACjC,MAAM,EACJ,OAAO,EACP,QAAQ,EACR,WAAW,KAAK,EAChB,QAAQ;QAAC;KAAU,EACnB,QAAQ,UAAU,IAAI,EACtB,eAAe,CAAC,EAChB,cAAc,IAAI,EAClB,eAAe,KAAK,EACpB,QAAQ,IAAI,EACZ,wBAAwB,KAAK,EAC7B,kBAAkB,IAAI,EACvB,GAAG;IACJ,MAAM,EACJ,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,MAAM,EACN,OAAO,EACP,UAAU,EACV,UAAU,EACR,YAAY,EACZ,QAAQ,EACT,EACF,GAAG;IACJ,MAAM,qBAAqB,OAAO,iBAAiB,YAAY,eAAe;IAC9E,wEAAwE;IACxE,8EAA8E;IAC9E,0EAA0E;IAC1E,2EAA2E;IAC3E,SAAS;IACT,MAAM,8BAA8B,CAAA,GAAA,yLAAA,CAAA,qBAAkB,AAAD,EAAE,iBAAiB;IAExE,6EAA6E;IAC7E,MAAM,SAAS,kBAAkB,UAAU;IAC3C,MAAM,WAAW,aAAa;IAC9B,MAAM,kBAAkB,aAAa;IACrC,MAAM,iBAAiB,aAAa;IACpC,MAAM,OAAO;IACb,MAAM,gBAAgB;IACtB,MAAM,wBAAwB,8JAAM,MAAM,CAAC;IAC3C,MAAM,sBAAsB,8JAAM,MAAM,CAAC;IACzC,MAAM,wBAAwB,8JAAM,MAAM,CAAC;IAC3C,MAAM,mBAAmB,8JAAM,MAAM,CAAC;IACtC,MAAM,mBAAmB,8JAAM,MAAM,CAAC,CAAC;IACvC,MAAM,iBAAiB,iBAAiB;IACxC,MAAM,uBAAuB,wBAAwB;IACrD,MAAM,qBAAqB;mEAAe,SAAU,SAAS;YAC3D,IAAI,cAAc,KAAK,GAAG;gBACxB,YAAY;YACd;YACA,OAAO,YAAY,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,wBAAwB,EAAE;QACnE;;IACA,MAAM,sBAAsB;oEAAe,CAAA;YACzC,MAAM,UAAU,mBAAmB;YACnC,OAAO,SAAS,OAAO,CAAC,GAAG;4EAAC,CAAA;oBAC1B,IAAI,gBAAgB,SAAS,aAAa;wBACxC,OAAO;oBACT;oBACA,IAAI,wBAAwB,SAAS,YAAY;wBAC/C,OAAO;oBACT;oBACA,OAAO;gBACT;2EAAG,MAAM,CAAC,SAAS,IAAI;QACzB;;IACA,8JAAM,SAAS;0CAAC;YACd,IAAI,UAAU;YACd,IAAI,CAAC,OAAO;YACZ,SAAS,UAAU,KAAK;gBACtB,IAAI,MAAM,GAAG,KAAK,OAAO;oBACvB,wEAAwE;oBACxE,IAAI,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,sBAAsB,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,2BAA2B,qBAAqB,MAAM,KAAK,KAAK,CAAC,6BAA6B;wBACzJ,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;oBACZ;oBACA,MAAM,MAAM;oBACZ,MAAM,SAAS,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;oBACzB,IAAI,SAAS,OAAO,CAAC,EAAE,KAAK,eAAe,WAAW,cAAc;wBAClE,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;wBACV,IAAI,MAAM,QAAQ,EAAE;4BAClB,aAAa,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;wBAClC,OAAO;4BACL,aAAa,GAAG,CAAC,EAAE;wBACrB;oBACF;oBACA,IAAI,SAAS,OAAO,CAAC,EAAE,KAAK,cAAc,WAAW,wBAAwB,MAAM,QAAQ,EAAE;wBAC3F,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;wBACV,aAAa,GAAG,CAAC,EAAE;oBACrB;gBACF;YACF;YACA,MAAM,MAAM,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE;YACxB,IAAI,gBAAgB,CAAC,WAAW;YAChC;kDAAO;oBACL,IAAI,mBAAmB,CAAC,WAAW;gBACrC;;QACF;yCAAG;QAAC;QAAU;QAAc;QAAsB;QAAO;QAAU;QAA6B;QAAoB;KAAoB;IACxI,8JAAM,SAAS;0CAAC;YACd,IAAI,UAAU;YACd,IAAI,CAAC,UAAU;YACf,SAAS,cAAc,KAAK;gBAC1B,MAAM,SAAS,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;gBACzB,MAAM,kBAAkB;gBACxB,MAAM,gBAAgB,gBAAgB,OAAO,CAAC;gBAC9C,IAAI,kBAAkB,CAAC,GAAG;oBACxB,iBAAiB,OAAO,GAAG;gBAC7B;YACF;YACA,SAAS,gBAAgB,CAAC,WAAW;YACrC;kDAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;yCAAG;QAAC;QAAU;QAAU;KAAmB;IAC3C,8JAAM,SAAS;0CAAC;YACd,IAAI,UAAU;YACd,IAAI,CAAC,iBAAiB;YAEtB,oDAAoD;YACpD,SAAS;gBACP,iBAAiB,OAAO,GAAG;gBAC3B;wEAAW;wBACT,iBAAiB,OAAO,GAAG;oBAC7B;;YACF;YACA,SAAS,mBAAmB,KAAK;gBAC/B,MAAM,gBAAgB,MAAM,aAAa;gBACzC;yEAAe;wBACb,MAAM,uBAAuB,CAAC,CAAC,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,kBAAkB,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,kBAAkB,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,aAAa,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,OAAO,KAAK,IAAI,cAAc,UAAU,EAAE,kBAAkB,iBAAiB,QAAQ,cAAc,YAAY,CAAC,gBAAgB,mBAAmB,QAAQ,CAAC,YAAY,KAAK,QAAQ,CAAC,OAAO,EAAE,QAAQ,IAAI;iFAAC,CAAA;gCACtX,IAAI,eAAe;gCACnB,OAAO,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,gBAAgB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,cAAc,QAAQ,CAAC,QAAQ,EAAE,kBAAkB,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,YAAY,EAAE;4BACzN;mFAAM,aAAa,KAAK,QAAQ,CAAC,OAAO,EAAE,QAAQ,IAAI;iFAAC,CAAA;gCACrD,IAAI,gBAAgB;gCACpB,OAAO,CAAC,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,QAAQ,MAAM,iBAAiB,CAAC,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,YAAY,MAAM;4BAClN;+EAAE,CAAC;wBAEH,kEAAkE;wBAClE,mDAAmD;wBACnD,IAAI,gBAAgB,wBAAwB,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,2BAA2B,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,sBAAsB,IAAI,EAAE;4BACvI,mEAAmE;4BACnE,iBAAiB;4BACjB,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,uBAAuB;gCACvC,qBAAqB,KAAK;4BAC5B;4BACA,MAAM,oBAAoB,iBAAiB,OAAO;4BAClD,MAAM,kBAAkB;4BACxB,MAAM,cAAc,eAAe,CAAC,kBAAkB,IAAI,eAAe,CAAC,gBAAgB,MAAM,GAAG,EAAE,IAAI;4BACzG,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,cAAc;gCAC9B,YAAY,KAAK;4BACnB;wBACF;wBAEA,yEAAyE;wBACzE,mCAAmC;wBACnC,IAAI,CAAC,8BAA8B,OAAO,CAAC,KAAK,KAAK,iBAAiB,wBAAwB,CAAC,iBAAiB,OAAO,IACvH,gEAAgE;wBAChE,kBAAkB,+BAA+B;4BAC/C,sBAAsB,OAAO,GAAG;4BAChC,aAAa,OAAO,OAAO;wBAC7B;oBACF;;YACF;YACA,IAAI,YAAY,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;gBAC3C,aAAa,gBAAgB,CAAC,YAAY;gBAC1C,aAAa,gBAAgB,CAAC,eAAe;gBAC7C,SAAS,gBAAgB,CAAC,YAAY;gBACtC;sDAAO;wBACL,aAAa,mBAAmB,CAAC,YAAY;wBAC7C,aAAa,mBAAmB,CAAC,eAAe;wBAChD,SAAS,mBAAmB,CAAC,YAAY;oBAC3C;;YACF;QACF;yCAAG;QAAC;QAAU;QAAc;QAAU;QAAsB;QAAO;QAAQ;QAAM;QAAe;QAAc;QAAiB;QAAc;QAAoB;KAA4B;IAC7L,8JAAM,SAAS;0CAAC;YACd,IAAI;YACJ,IAAI,UAAU;YAEd,sDAAsD;YACtD,MAAM,cAAc,MAAM,IAAI,CAAC,CAAC,iBAAiB,QAAQ,CAAC,wBAAwB,cAAc,UAAU,KAAK,OAAO,KAAK,IAAI,sBAAsB,gBAAgB,CAAC,MAAM,gBAAgB,YAAY,IAAI,KAAK,EAAE;YACnN,IAAI,UAAU;gBACZ,MAAM,iBAAiB;oBAAC;uBAAa;oBAAa,sBAAsB,OAAO;oBAAE,oBAAoB,OAAO;oBAAE,SAAS,OAAO,CAAC,QAAQ,CAAC,gBAAgB,8BAA8B,eAAe;iBAAK,CAAC,MAAM;qEAAC,CAAA,IAAK,KAAK;;gBAC5N,MAAM,UAAU,SAAS,8BAA8B,WAAW,gBAAgB,QAAQ,CAAC,UAAU,WAAW;gBAChH;sDAAO;wBACL;oBACF;;YACF;QACF;yCAAG;QAAC;QAAU;QAAc;QAAU;QAAO;QAAU;QAAe;QAA6B;KAAO;IAC1G,MAAM;QACJ,IAAI,YAAY,CAAC,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,uBAAuB;QACtD,MAAM,MAAM,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE;QACxB,MAAM,2BAA2B,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAE/C,yEAAyE;QACzE,eAAe;YACb,MAAM,oBAAoB,oBAAoB;YAC9C,MAAM,oBAAoB,gBAAgB,OAAO;YACjD,MAAM,YAAY,CAAC,OAAO,sBAAsB,WAAW,iBAAiB,CAAC,kBAAkB,GAAG,kBAAkB,OAAO,KAAK;YAChI,MAAM,+BAA+B,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,sBAAsB;YACpE,IAAI,CAAC,sBAAsB,CAAC,gCAAgC,MAAM;gBAChE,aAAa,WAAW;oBACtB,eAAe,cAAc;gBAC/B;YACF;QACF;IACF,GAAG;QAAC;QAAU;QAAM;QAAsB;QAAoB;QAAqB;KAAgB;IACnG,MAAM;QACJ,IAAI,YAAY,CAAC,sBAAsB;QACvC,IAAI,2BAA2B;QAC/B,MAAM,MAAM,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE;QACxB,MAAM,2BAA2B,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C,MAAM,cAAc,QAAQ,OAAO;QACnC,IAAI,YAAY,YAAY,SAAS;QACrC,4BAA4B;QAE5B,qEAAqE;QACrE,8BAA8B;QAC9B,SAAS,aAAa,IAAI;YACxB,IAAI,EACF,IAAI,EACJ,MAAM,EACN,KAAK,EACL,MAAM,EACP,GAAG;YACJ,IAAI,MAAM;gBACR,YAAY;YACd;YACA,IAAI,WAAW,gBAAgB,KAAK,YAAY,CAAC,OAAO,EAAE;gBACxD,4BAA4B,KAAK,YAAY,CAAC,OAAO;YACvD;YACA,IAAI,WAAW,WAAW,MAAM,IAAI,KAAK,cAAc;gBACrD,sBAAsB,OAAO,GAAG;YAClC;YACA,IAAI,WAAW,iBAAiB;YAChC,IAAI,QAAQ;gBACV,sBAAsB,OAAO,GAAG;gBAChC,2BAA2B;YAC7B,OAAO;gBACL,sBAAsB,OAAO,GAAG,CAAC,CAAC,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;YACzF;QACF;QACA,OAAO,EAAE,CAAC,cAAc;QACxB,MAAM,aAAa,IAAI,aAAa,CAAC;QACrC,WAAW,YAAY,CAAC,YAAY;QACpC,WAAW,YAAY,CAAC,eAAe;QACvC,OAAO,MAAM,CAAC,WAAW,KAAK,EAAE;QAChC,IAAI,kBAAkB,cAAc;YAClC,aAAa,qBAAqB,CAAC,YAAY;QACjD;QACA,SAAS;YACP,IAAI,OAAO,eAAe,OAAO,KAAK,WAAW;gBAC/C,OAAO,iCAAiC;YAC1C;YACA,OAAO,eAAe,OAAO,CAAC,OAAO,IAAI;QAC3C;QACA,OAAO;YACL,OAAO,GAAG,CAAC,cAAc;YACzB,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;YAC/B,MAAM,4BAA4B,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,aAAa,QAAQ,YAAY,KAAK,QAAQ,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAA;gBACxH,IAAI;gBACJ,OAAO,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,CAAC,QAAQ,EAAE;YACvG;YACA,MAAM,uBAAuB,6BAA6B,aAAa;gBAAC;gBAAS;aAAY,CAAC,QAAQ,CAAC,UAAU,IAAI;YACrH,IAAI,wBAAwB,KAAK,YAAY,CAAC,OAAO,EAAE;gBACrD,4BAA4B,KAAK,YAAY,CAAC,OAAO;YACvD;YACA,MAAM,gBAAgB;YACtB,eAAe;gBACb,IACA,uDAAuD;gBACvD,eAAe,OAAO,IAAI,CAAC,sBAAsB,OAAO,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAC5F,uEAAuE;gBACvE,8DAA8D;gBAC9D,oEAAoE;gBACpE,kBAAkB,YAAY,aAAa,IAAI,IAAI,GAAG,4BAA4B,IAAI,GAAG;oBACvF,cAAc,KAAK,CAAC;wBAClB,eAAe;oBACjB;gBACF;gBACA,WAAW,MAAM;YACnB;QACF;IACF,GAAG;QAAC;QAAU;QAAU;QAAsB;QAAgB;QAAS;QAAM;QAAQ;QAAM;QAAQ;QAAgB;KAAa;IAChI,8JAAM,SAAS;0CAAC;YACd,sEAAsE;YACtE,qDAAqD;YACrD;kDAAe;oBACb,sBAAsB,OAAO,GAAG;gBAClC;;QACF;yCAAG;QAAC;KAAS;IAEb,2EAA2E;IAC3E,mEAAmE;IACnE,MAAM;QACJ,IAAI,UAAU;QACd,IAAI,CAAC,eAAe;QACpB,cAAc,oBAAoB,CAAC;YACjC;YACA;YACA;YACA;YACA;QACF;QACA,OAAO;YACL,cAAc,oBAAoB,CAAC;QACrC;IACF,GAAG;QAAC;QAAU;QAAe;QAAO;QAAM;QAAc;QAAM;KAAgB;IAC9E,MAAM;QACJ,IAAI,UAAU;QACd,IAAI,CAAC,sBAAsB;QAC3B,IAAI,OAAO,qBAAqB,YAAY;QAC5C,IAAI,oBAAoB;QACxB,MAAM,iBAAiB;YACrB,MAAM,WAAW,qBAAqB,YAAY,CAAC;YACnD,MAAM,kBAAkB;YACxB,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE;YAC3C,MAAM,gBAAgB,gBAAgB,OAAO,CAAC;YAC9C,IAAI,kBAAkB,CAAC,GAAG;gBACxB,iBAAiB,OAAO,GAAG;YAC7B;YACA,IAAI,SAAS,OAAO,CAAC,QAAQ,CAAC,eAAe,aAAa,KAAK,YAAY,CAAC,OAAO,IAAI,gBAAgB,MAAM,KAAK,GAAG;gBACnH,IAAI,aAAa,KAAK;oBACpB,qBAAqB,YAAY,CAAC,YAAY;gBAChD;YACF,OAAO,IAAI,aAAa,MAAM;gBAC5B,qBAAqB,YAAY,CAAC,YAAY;YAChD;QACF;QACA;QACA,MAAM,WAAW,IAAI,iBAAiB;QACtC,SAAS,OAAO,CAAC,sBAAsB;YACrC,WAAW;YACX,SAAS;YACT,YAAY;QACd;QACA,OAAO;YACL,SAAS,UAAU;QACrB;IACF,GAAG;QAAC;QAAU;QAAU;QAAsB;QAAM;QAAU;QAAoB;KAAmB;IACrG,SAAS,oBAAoB,QAAQ;QACnC,IAAI,YAAY,CAAC,yBAAyB,CAAC,OAAO;YAChD,OAAO;QACT;QACA,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,uBAAuB;YAC7D,KAAK,aAAa,UAAU,wBAAwB;YACpD,SAAS,CAAA,QAAS,aAAa,OAAO,MAAM,WAAW;QACzD,GAAG,OAAO,0BAA0B,WAAW,wBAAwB;IACzE;IACA,MAAM,qBAAqB,CAAC,YAAY,UAAU,CAAC,QAAQ,CAAC,8BAA8B,IAAI,KAAK,CAAC,kBAAkB,KAAK;IAC3H,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,8JAAM,QAAQ,EAAE,MAAM,sBAAsB,WAAW,GAAE,8JAAM,aAAa,CAAC,YAAY;QAC/H,aAAa;QACb,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,eAAe;QACnE,SAAS,CAAA;YACP,IAAI,OAAO;gBACT,MAAM,MAAM;gBACZ,aAAa,KAAK,CAAC,EAAE,KAAK,cAAc,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;YACtE,OAAO,IAAI,iBAAiB,QAAQ,cAAc,gBAAgB,IAAI,cAAc,UAAU,EAAE;gBAC9F,sBAAsB,OAAO,GAAG;gBAChC,IAAI,eAAe,OAAO,cAAc,UAAU,GAAG;oBACnD,MAAM,eAAe,qBAAqB;oBAC1C,gBAAgB,QAAQ,aAAa,KAAK;gBAC5C,OAAO;oBACL,IAAI;oBACJ,CAAC,wBAAwB,cAAc,gBAAgB,CAAC,OAAO,KAAK,QAAQ,sBAAsB,KAAK;gBACzG;YACF;QACF;IACF,IAAI,CAAC,+BAA+B,oBAAoB,UAAU,UAAU,oBAAoB,QAAQ,sBAAsB,WAAW,GAAE,8JAAM,aAAa,CAAC,YAAY;QACzK,aAAa;QACb,KAAK,iBAAiB,OAAO,KAAK,IAAI,cAAc,cAAc;QAClE,SAAS,CAAA;YACP,IAAI,OAAO;gBACT,aAAa,qBAAqB,CAAC,EAAE;YACvC,OAAO,IAAI,iBAAiB,QAAQ,cAAc,gBAAgB,IAAI,cAAc,UAAU,EAAE;gBAC9F,IAAI,iBAAiB;oBACnB,sBAAsB,OAAO,GAAG;gBAClC;gBACA,IAAI,eAAe,OAAO,cAAc,UAAU,GAAG;oBACnD,MAAM,eAAe,yBAAyB;oBAC9C,gBAAgB,QAAQ,aAAa,KAAK;gBAC5C,OAAO;oBACL,IAAI;oBACJ,CAAC,wBAAwB,cAAc,eAAe,CAAC,OAAO,KAAK,QAAQ,sBAAsB,KAAK;gBACxG;YACF;QACF;IACF;AACF;AAEA,IAAI,YAAY;AAChB,SAAS;IACP,MAAM,QAAQ,qBAAqB,IAAI,CAAC,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD;IAClD,MAAM,YAAY,SAAS,IAAI,CAAC,KAAK;IACrC,uBAAuB;IACvB,MAAM,aAAa,KAAK,KAAK,CAAC,SAAS,eAAe,CAAC,qBAAqB,GAAG,IAAI,IAAI,SAAS,eAAe,CAAC,UAAU;IAC1H,MAAM,cAAc,aAAa,gBAAgB;IACjD,MAAM,iBAAiB,OAAO,UAAU,GAAG,SAAS,eAAe,CAAC,WAAW;IAC/E,MAAM,UAAU,UAAU,IAAI,GAAG,WAAW,UAAU,IAAI,IAAI,OAAO,OAAO;IAC5E,MAAM,UAAU,UAAU,GAAG,GAAG,WAAW,UAAU,GAAG,IAAI,OAAO,OAAO;IAC1E,UAAU,QAAQ,GAAG;IACrB,IAAI,gBAAgB;QAClB,SAAS,CAAC,YAAY,GAAG,iBAAiB;IAC5C;IAEA,yEAAyE;IACzE,oCAAoC;IACpC,IAAI,OAAO;QACT,IAAI,uBAAuB;QAC3B,4CAA4C;QAC5C,MAAM,aAAa,CAAC,CAAC,wBAAwB,OAAO,cAAc,KAAK,OAAO,KAAK,IAAI,sBAAsB,UAAU,KAAK;QAC5H,MAAM,YAAY,CAAC,CAAC,yBAAyB,OAAO,cAAc,KAAK,OAAO,KAAK,IAAI,uBAAuB,SAAS,KAAK;QAC5H,OAAO,MAAM,CAAC,WAAW;YACvB,UAAU;YACV,KAAK,CAAC,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,IAAI;YAC1C,MAAM,CAAC,CAAC,UAAU,KAAK,KAAK,CAAC,WAAW,IAAI;YAC5C,OAAO;QACT;IACF;IACA,OAAO;QACL,OAAO,MAAM,CAAC,WAAW;YACvB,UAAU;YACV,CAAC,YAAY,EAAE;QACjB;QACA,IAAI,OAAO;YACT,OAAO,MAAM,CAAC,WAAW;gBACvB,UAAU;gBACV,KAAK;gBACL,MAAM;gBACN,OAAO;YACT;YACA,OAAO,QAAQ,CAAC,SAAS;QAC3B;IACF;AACF;AACA,IAAI,UAAU,KAAO;AAErB;;;;;CAKC,GACD,MAAM,kBAAkB,WAAW,GAAE,8JAAM,UAAU,CAAC,SAAS,gBAAgB,KAAK,EAAE,GAAG;IACvF,MAAM,EACJ,aAAa,KAAK,EAClB,GAAG,MACJ,GAAG;IACJ,MAAM;QACJ,IAAI,CAAC,YAAY;QACjB;QACA,IAAI,cAAc,GAAG;YACnB,UAAU;QACZ;QACA,OAAO;YACL;YACA,IAAI,cAAc,GAAG;gBACnB;YACF;QACF;IACF,GAAG;QAAC;KAAW;IACf,OAAO,WAAW,GAAE,8JAAM,aAAa,CAAC,OAAO,SAAS;QACtD,KAAK;IACP,GAAG,MAAM;QACP,OAAO;YACL,UAAU;YACV,UAAU;YACV,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;YACN,GAAG,KAAK,KAAK;QACf;IACF;AACF;AAEA,SAAS,eAAe,KAAK;IAC3B,OAAO,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,MAAM,KAAK,MAAM,MAAM,CAAC,OAAO,KAAK;AACjE;AACA,SAAS,eAAe,OAAO;IAC7B,OAAO,CAAA,GAAA,yLAAA,CAAA,oBAAiB,AAAD,EAAE;AAC3B;AACA;;;CAGC,GACD,SAAS,SAAS,OAAO,EAAE,KAAK;IAC9B,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,OAAO,EACP,UAAU,EACR,YAAY,EACb,EACF,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,OAAO,cAAc,OAAO,EAC5B,SAAS,IAAI,EACb,cAAc,KAAK,EACnB,mBAAmB,IAAI,EACvB,cAAc,IAAI,EACnB,GAAG;IACJ,MAAM,iBAAiB,8JAAM,MAAM;IACnC,MAAM,gBAAgB,8JAAM,MAAM,CAAC;IACnC,MAAM,YAAY,8JAAM,OAAO;uCAAC,IAAM,CAAC;gBACrC,eAAc,KAAK;oBACjB,eAAe,OAAO,GAAG,MAAM,WAAW;gBAC5C;gBACA,aAAY,KAAK;oBACf,MAAM,cAAc,eAAe,OAAO;oBAE1C,mDAAmD;oBACnD,qEAAqE;oBACrE,IAAI,MAAM,MAAM,KAAK,GAAG;oBACxB,IAAI,gBAAgB,SAAS;oBAC7B,IAAI,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS,aAAa;oBAC9D,IAAI,QAAQ,UAAU,CAAC,QAAQ,OAAO,CAAC,SAAS,IAAI,cAAc,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,cAAc,IAAI,GAAG;wBACxH,aAAa,OAAO,MAAM,WAAW,EAAE;oBACzC,OAAO;wBACL,mDAAmD;wBACnD,MAAM,cAAc;wBACpB,aAAa,MAAM,MAAM,WAAW,EAAE;oBACxC;gBACF;gBACA,SAAQ,KAAK;oBACX,MAAM,cAAc,eAAe,OAAO;oBAC1C,IAAI,gBAAgB,eAAe,eAAe,OAAO,EAAE;wBACzD,eAAe,OAAO,GAAG;wBACzB;oBACF;oBACA,IAAI,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS,aAAa;oBAC9D,IAAI,QAAQ,UAAU,CAAC,QAAQ,OAAO,CAAC,SAAS,IAAI,cAAc,QAAQ,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,IAAI,GAAG;wBACpH,aAAa,OAAO,MAAM,WAAW,EAAE;oBACzC,OAAO;wBACL,aAAa,MAAM,MAAM,WAAW,EAAE;oBACxC;gBACF;gBACA,WAAU,KAAK;oBACb,eAAe,OAAO,GAAG;oBACzB,IAAI,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,eAAe,QAAQ;wBACxE;oBACF;oBACA,IAAI,MAAM,GAAG,KAAK,OAAO,CAAC,eAAe,eAAe;wBACtD,oBAAoB;wBACpB,MAAM,cAAc;wBACpB,cAAc,OAAO,GAAG;oBAC1B;oBACA,IAAI,MAAM,GAAG,KAAK,SAAS;wBACzB,IAAI,QAAQ,QAAQ;4BAClB,aAAa,OAAO,MAAM,WAAW,EAAE;wBACzC,OAAO;4BACL,aAAa,MAAM,MAAM,WAAW,EAAE;wBACxC;oBACF;gBACF;gBACA,SAAQ,KAAK;oBACX,IAAI,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,eAAe,UAAU,eAAe,eAAe;wBACxG;oBACF;oBACA,IAAI,MAAM,GAAG,KAAK,OAAO,cAAc,OAAO,EAAE;wBAC9C,cAAc,OAAO,GAAG;wBACxB,IAAI,QAAQ,QAAQ;4BAClB,aAAa,OAAO,MAAM,WAAW,EAAE;wBACzC,OAAO;4BACL,aAAa,MAAM,MAAM,WAAW,EAAE;wBACxC;oBACF;gBACF;YACF,CAAC;sCAAG;QAAC;QAAS;QAAc;QAAa;QAAa;QAAkB;QAAc;QAAM;QAAa;KAAO;IAChH,OAAO,8JAAM,OAAO;4BAAC,IAAM,UAAU;gBACnC;YACF,IAAI,CAAC;2BAAG;QAAC;QAAS;KAAU;AAC9B;AAEA,SAAS,qBAAqB,UAAU,EAAE,IAAI;IAC5C,IAAI,UAAU;IACd,IAAI,UAAU;IACd,IAAI,oBAAoB;IACxB,OAAO;QACL,gBAAgB,cAAc;QAC9B;YACE,IAAI;YACJ,MAAM,UAAU,CAAC,cAAc,OAAO,KAAK,IAAI,WAAW,qBAAqB,EAAE,KAAK;gBACpF,OAAO;gBACP,QAAQ;gBACR,GAAG;gBACH,GAAG;YACL;YACA,MAAM,UAAU,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK;YACnD,MAAM,UAAU,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK;YACnD,MAAM,6BAA6B;gBAAC;gBAAc;aAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAwB,KAAK,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI,KAAK,OAAO,KAAK,WAAW,KAAK;YAChN,IAAI,QAAQ,QAAQ,KAAK;YACzB,IAAI,SAAS,QAAQ,MAAM;YAC3B,IAAI,IAAI,QAAQ,CAAC;YACjB,IAAI,IAAI,QAAQ,CAAC;YACjB,IAAI,WAAW,QAAQ,KAAK,CAAC,IAAI,SAAS;gBACxC,UAAU,QAAQ,CAAC,GAAG,KAAK,CAAC;YAC9B;YACA,IAAI,WAAW,QAAQ,KAAK,CAAC,IAAI,SAAS;gBACxC,UAAU,QAAQ,CAAC,GAAG,KAAK,CAAC;YAC9B;YACA,KAAK,WAAW;YAChB,KAAK,WAAW;YAChB,QAAQ;YACR,SAAS;YACT,IAAI,CAAC,qBAAqB,4BAA4B;gBACpD,QAAQ,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,GAAG;gBAC5C,SAAS,KAAK,IAAI,KAAK,MAAM,QAAQ,MAAM,GAAG;gBAC9C,IAAI,WAAW,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,GAAG;gBACzC,IAAI,WAAW,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,GAAG;YAC3C,OAAO,IAAI,qBAAqB,CAAC,4BAA4B;gBAC3D,SAAS,KAAK,IAAI,KAAK,MAAM,QAAQ,MAAM,GAAG;gBAC9C,QAAQ,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,GAAG;YAC9C;YACA,oBAAoB;YACpB,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA,KAAK;gBACL,OAAO,IAAI;gBACX,QAAQ,IAAI;gBACZ,MAAM;YACR;QACF;IACF;AACF;AACA,SAAS,kBAAkB,KAAK;IAC9B,OAAO,SAAS,QAAQ,MAAM,OAAO,IAAI;AAC3C;AACA;;;;CAIC,GACD,SAAS,eAAe,OAAO,EAAE,KAAK;IACpC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,UAAU,EACR,QAAQ,EACR,YAAY,EACb,EACD,IAAI,EACL,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,OAAO,MAAM,EACb,IAAI,IAAI,EACR,IAAI,IAAI,EACT,GAAG;IACJ,MAAM,aAAa,8JAAM,MAAM,CAAC;IAChC,MAAM,qBAAqB,8JAAM,MAAM,CAAC;IACxC,MAAM,CAAC,aAAa,eAAe,GAAG,8JAAM,QAAQ;IACpD,MAAM,CAAC,UAAU,YAAY,GAAG,8JAAM,QAAQ,CAAC,EAAE;IACjD,MAAM,eAAe;uDAAe,CAAC,GAAG;YACtC,IAAI,WAAW,OAAO,EAAE;YAExB,6DAA6D;YAC7D,+DAA+D;YAC/D,kCAAkC;YAClC,IAAI,QAAQ,OAAO,CAAC,SAAS,IAAI,CAAC,kBAAkB,QAAQ,OAAO,CAAC,SAAS,GAAG;gBAC9E;YACF;YACA,KAAK,oBAAoB,CAAC,qBAAqB,cAAc;gBAC3D;gBACA;gBACA;gBACA;gBACA;YACF;QACF;;IACA,MAAM,6BAA6B;qEAAe,CAAA;YAChD,IAAI,KAAK,QAAQ,KAAK,MAAM;YAC5B,IAAI,CAAC,MAAM;gBACT,aAAa,MAAM,OAAO,EAAE,MAAM,OAAO;YAC3C,OAAO,IAAI,CAAC,mBAAmB,OAAO,EAAE;gBACtC,oEAAoE;gBACpE,uEAAuE;gBACvE,sDAAsD;gBACtD,YAAY,EAAE;YAChB;QACF;;IAEA,4EAA4E;IAC5E,oEAAoE;IACpE,yEAAyE;IACzE,6BAA6B;IAC7B,MAAM,YAAY,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,WAAW;IACnE,MAAM,cAAc,8JAAM,WAAW;mDAAC;YACpC,qEAAqE;YACrE,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,QAAQ,KAAK,MAAM;YACtD,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE;YACtB,SAAS,gBAAgB,KAAK;gBAC5B,MAAM,SAAS,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;gBACzB,IAAI,CAAC,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,SAAS;oBAC/B,aAAa,MAAM,OAAO,EAAE,MAAM,OAAO;gBAC3C,OAAO;oBACL,IAAI,mBAAmB,CAAC,aAAa;oBACrC,mBAAmB,OAAO,GAAG;gBAC/B;YACF;YACA,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAS,IAAI,kBAAkB,QAAQ,OAAO,CAAC,SAAS,GAAG;gBAC9E,IAAI,gBAAgB,CAAC,aAAa;gBAClC,MAAM;uEAAU;wBACd,IAAI,mBAAmB,CAAC,aAAa;wBACrC,mBAAmB,OAAO,GAAG;oBAC/B;;gBACA,mBAAmB,OAAO,GAAG;gBAC7B,OAAO;YACT;YACA,KAAK,oBAAoB,CAAC;QAC5B;kDAAG;QAAC;QAAW;QAAS;QAAG;QAAG;QAAU;QAAS;QAAM;QAAc;KAAa;IAClF,8JAAM,SAAS;oCAAC;YACd,OAAO;QACT;mCAAG;QAAC;QAAa;KAAS;IAC1B,8JAAM,SAAS;oCAAC;YACd,IAAI,WAAW,CAAC,UAAU;gBACxB,WAAW,OAAO,GAAG;YACvB;QACF;mCAAG;QAAC;QAAS;KAAS;IACtB,8JAAM,SAAS;oCAAC;YACd,IAAI,CAAC,WAAW,MAAM;gBACpB,WAAW,OAAO,GAAG;YACvB;QACF;mCAAG;QAAC;QAAS;KAAK;IAClB,MAAM;QACJ,IAAI,WAAW,CAAC,KAAK,QAAQ,KAAK,IAAI,GAAG;YACvC,WAAW,OAAO,GAAG;YACrB,aAAa,GAAG;QAClB;IACF,GAAG;QAAC;QAAS;QAAG;QAAG;KAAa;IAChC,MAAM,YAAY,8JAAM,OAAO;6CAAC;YAC9B,SAAS,kBAAkB,IAAI;gBAC7B,IAAI,EACF,WAAW,EACZ,GAAG;gBACJ,eAAe;YACjB;YACA,OAAO;gBACL,eAAe;gBACf,gBAAgB;gBAChB,aAAa;gBACb,cAAc;YAChB;QACF;4CAAG;QAAC;KAA2B;IAC/B,OAAO,8JAAM,OAAO;kCAAC,IAAM,UAAU;gBACnC;YACF,IAAI,CAAC;iCAAG;QAAC;QAAS;KAAU;AAC9B;AAEA,MAAM,oBAAoB;IACxB,aAAa;IACb,WAAW;IACX,OAAO;AACT;AACA,MAAM,qBAAqB;IACzB,aAAa;IACb,WAAW;IACX,OAAO;AACT;AACA,MAAM,gBAAgB,CAAA;IACpB,IAAI,uBAAuB;IAC3B,OAAO;QACL,WAAW,OAAO,iBAAiB,YAAY,eAAe,CAAC,wBAAwB,gBAAgB,OAAO,KAAK,IAAI,aAAa,SAAS,KAAK,OAAO,wBAAwB;QACjL,cAAc,OAAO,iBAAiB,YAAY,eAAe,CAAC,wBAAwB,gBAAgB,OAAO,KAAK,IAAI,aAAa,YAAY,KAAK,OAAO,wBAAwB;IACzL;AACF;AACA;;;;CAIC,GACD,SAAS,WAAW,OAAO,EAAE,KAAK;IAChC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,OAAO,EACR,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,YAAY,IAAI,EAChB,cAAc,wBAAwB,IAAI,EAC1C,oBAAoB,aAAa,EACjC,iBAAiB,KAAK,EACtB,sBAAsB,aAAa,EACnC,iBAAiB,KAAK,EACtB,OAAO,EACP,OAAO,EACR,GAAG;IACJ,MAAM,OAAO;IACb,MAAM,iBAAiB,eAAe,OAAO,0BAA0B,aAAa;qDAAwB,IAAM;;IAClH,MAAM,eAAe,OAAO,0BAA0B,aAAa,iBAAiB;IACpF,MAAM,qBAAqB,8JAAM,MAAM,CAAC;IACxC,MAAM,0BAA0B,8JAAM,MAAM,CAAC;IAC7C,MAAM,EACJ,WAAW,gBAAgB,EAC3B,cAAc,mBAAmB,EAClC,GAAG,cAAc;IAClB,MAAM,EACJ,WAAW,gBAAgB,EAC3B,cAAc,mBAAmB,EAClC,GAAG,cAAc;IAClB,MAAM,iBAAiB,8JAAM,MAAM,CAAC;IACpC,MAAM,uBAAuB;2DAAe,CAAA;YAC1C,IAAI;YACJ,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,aAAa,MAAM,GAAG,KAAK,UAAU;gBAC7D;YACF;YAEA,sEAAsE;YACtE,wDAAwD;YACxD,IAAI,eAAe,OAAO,EAAE;gBAC1B;YACF;YACA,MAAM,SAAS,CAAC,wBAAwB,QAAQ,OAAO,CAAC,eAAe,KAAK,OAAO,KAAK,IAAI,sBAAsB,MAAM;YACxH,MAAM,WAAW,OAAO,YAAY,KAAK,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE;YACvE,IAAI,CAAC,kBAAkB;gBACrB,MAAM,eAAe;gBACrB,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,IAAI,gBAAgB;oBACpB,SAAS,OAAO;2EAAC,CAAA;4BACf,IAAI;4BACJ,IAAI,CAAC,iBAAiB,MAAM,OAAO,KAAK,QAAQ,eAAe,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE;gCACxH,gBAAgB;gCAChB;4BACF;wBACF;;oBACA,IAAI,CAAC,eAAe;wBAClB;oBACF;gBACF;YACF;YACA,aAAa,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,SAAS,MAAM,WAAW,GAAG,OAAO;QACvE;;IACA,MAAM,8BAA8B;kEAAe,CAAA;YACjD,IAAI;YACJ,MAAM;mFAAW;oBACf,IAAI;oBACJ,qBAAqB;oBACrB,CAAC,aAAa,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE,MAAM,KAAK,QAAQ,WAAW,mBAAmB,CAAC,WAAW;gBACvF;;YACA,CAAC,cAAc,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE,MAAM,KAAK,QAAQ,YAAY,gBAAgB,CAAC,WAAW;QACtF;;IACA,MAAM,sBAAsB;0DAAe,CAAA;YACzC,IAAI;YACJ,oEAAoE;YACpE,kDAAkD;YAClD,MAAM,kBAAkB,mBAAmB,OAAO;YAClD,mBAAmB,OAAO,GAAG;YAE7B,+DAA+D;YAC/D,kBAAkB;YAClB,mDAAmD;YACnD,iDAAiD;YACjD,MAAM,uBAAuB,wBAAwB,OAAO;YAC5D,wBAAwB,OAAO,GAAG;YAClC,IAAI,sBAAsB,WAAW,sBAAsB;gBACzD;YACF;YACA,IAAI,iBAAiB;gBACnB;YACF;YACA,IAAI,OAAO,iBAAiB,cAAc,CAAC,aAAa,QAAQ;gBAC9D;YACF;YACA,MAAM,SAAS,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;YACzB,MAAM,gBAAgB,MAAM,gBAAgB,WAAW;YACvD,MAAM,UAAU,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,SAAS,QAAQ,EAAE,gBAAgB,CAAC;YAChE,IAAI,qBAAqB,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,UAAU,SAAS;YACtD,MAAO,sBAAsB,CAAC,CAAA,GAAA,uLAAA,CAAA,wBAAqB,AAAD,EAAE,oBAAqB;gBACvE,MAAM,aAAa,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;gBACjC,IAAI,CAAA,GAAA,uLAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,CAAC,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,aAAa;oBAC/D;gBACF;gBACA,qBAAqB;YACvB;YAEA,0EAA0E;YAC1E,6BAA6B;YAC7B,IAAI,QAAQ,MAAM,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,WAAW,CAAC,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,WAC1D,uDAAuD;YACvD,CAAC,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,SAAS,QAAQ,KACnC,oEAAoE;YACpE,4DAA4D;YAC5D,MAAM,IAAI,CAAC,SAAS,KAAK;kEAAC,CAAA,SAAU,CAAC,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,oBAAoB;kEAAU;gBAC1E;YACF;YAEA,+CAA+C;YAC/C,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,UAAU;gBACrC,mEAAmE;gBACnE,YAAY;gBACZ,MAAM,aAAa,OAAO,WAAW,GAAG,KAAK,OAAO,WAAW,GAAG,OAAO,WAAW;gBACpF,MAAM,aAAa,OAAO,YAAY,GAAG,KAAK,OAAO,YAAY,GAAG,OAAO,YAAY;gBACvF,IAAI,QAAQ,cAAc,MAAM,OAAO,GAAG,OAAO,WAAW;gBAE5D,mEAAmE;gBACnE,mEAAmE;gBACnE,gEAAgE;gBAChE,qEAAqE;gBACrE,IAAI,YAAY;oBACd,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,SAAS,KAAK;oBACrD,IAAI,OAAO;wBACT,QAAQ,MAAM,OAAO,IAAI,OAAO,WAAW,GAAG,OAAO,WAAW;oBAClE;gBACF;gBACA,IAAI,SAAS,cAAc,MAAM,OAAO,GAAG,OAAO,YAAY,EAAE;oBAC9D;gBACF;YACF;YACA,MAAM,SAAS,CAAC,yBAAyB,QAAQ,OAAO,CAAC,eAAe,KAAK,OAAO,KAAK,IAAI,uBAAuB,MAAM;YAC1H,MAAM,yBAAyB,QAAQ,YAAY,KAAK,QAAQ,CAAC,OAAO,EAAE,QAAQ,IAAI;kEAAC,CAAA;oBACrF,IAAI;oBACJ,OAAO,CAAA,GAAA,yLAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,CAAC,gBAAgB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,cAAc,QAAQ,CAAC,QAAQ;gBACrH;;YACA,IAAI,CAAA,GAAA,yLAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,QAAQ,KAAK,CAAA,GAAA,yLAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,SAAS,YAAY,KAAK,wBAAwB;gBAChI;YACF;YACA,MAAM,WAAW,OAAO,YAAY,KAAK,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE;YACvE,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,IAAI,gBAAgB;gBACpB,SAAS,OAAO;sEAAC,CAAA;wBACf,IAAI;wBACJ,IAAI,CAAC,kBAAkB,MAAM,OAAO,KAAK,QAAQ,gBAAgB,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE;4BAC7H,gBAAgB;4BAChB;wBACF;oBACF;;gBACA,IAAI,CAAC,eAAe;oBAClB;gBACF;YACF;YACA,aAAa,OAAO,OAAO;QAC7B;;IACA,MAAM,6BAA6B;iEAAe,CAAA;YAChD,IAAI;YACJ,MAAM;kFAAW;oBACf,IAAI;oBACJ,oBAAoB;oBACpB,CAAC,cAAc,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE,MAAM,KAAK,QAAQ,YAAY,mBAAmB,CAAC,mBAAmB;gBACjG;;YACA,CAAC,cAAc,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE,MAAM,KAAK,QAAQ,YAAY,gBAAgB,CAAC,mBAAmB;QAC9F;;IACA,8JAAM,SAAS;gCAAC;YACd,IAAI,CAAC,QAAQ,CAAC,SAAS;gBACrB;YACF;YACA,QAAQ,OAAO,CAAC,kBAAkB,GAAG;YACrC,QAAQ,OAAO,CAAC,qBAAqB,GAAG;YACxC,IAAI,qBAAqB,CAAC;YAC1B,SAAS,SAAS,KAAK;gBACrB,aAAa,OAAO,OAAO;YAC7B;YACA,SAAS;gBACP,OAAO,YAAY,CAAC;gBACpB,eAAe,OAAO,GAAG;YAC3B;YACA,SAAS;gBACP,qEAAqE;gBACrE,uDAAuD;gBACvD,iDAAiD;gBACjD,qBAAqB,OAAO,UAAU;iEAAC;wBACrC,eAAe,OAAO,GAAG;oBAC3B;gEACA,qEAAqE;gBACrE,mDAAmD;gBACnD,CAAA,GAAA,uLAAA,CAAA,WAAQ,AAAD,MAAM,IAAI;YACnB;YACA,MAAM,MAAM,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,SAAS,QAAQ;YACzC,IAAI,WAAW;gBACb,IAAI,gBAAgB,CAAC,WAAW,mBAAmB,8BAA8B,sBAAsB;gBACvG,IAAI,gBAAgB,CAAC,oBAAoB;gBACzC,IAAI,gBAAgB,CAAC,kBAAkB;YACzC;YACA,gBAAgB,IAAI,gBAAgB,CAAC,mBAAmB,sBAAsB,6BAA6B,qBAAqB;YAChI,IAAI,YAAY,EAAE;YAClB,IAAI,gBAAgB;gBAClB,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY,GAAG;oBACpC,YAAY,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,YAAY;gBACxD;gBACA,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,QAAQ,GAAG;oBAChC,YAAY,UAAU,MAAM,CAAC,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,QAAQ;gBACrE;gBACA,IAAI,CAAC,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,SAAS,KAAK,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,cAAc,EAAE;oBAC7F,YAAY,UAAU,MAAM,CAAC,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,SAAS,CAAC,cAAc;gBACrF;YACF;YAEA,wEAAwE;YACxE,YAAY,UAAU,MAAM;wCAAC,CAAA;oBAC3B,IAAI;oBACJ,OAAO,aAAa,CAAC,CAAC,mBAAmB,IAAI,WAAW,KAAK,OAAO,KAAK,IAAI,iBAAiB,cAAc;gBAC9G;;YACA,UAAU,OAAO;wCAAC,CAAA;oBAChB,SAAS,gBAAgB,CAAC,UAAU,UAAU;wBAC5C,SAAS;oBACX;gBACF;;YACA;wCAAO;oBACL,IAAI,WAAW;wBACb,IAAI,mBAAmB,CAAC,WAAW,mBAAmB,8BAA8B,sBAAsB;wBAC1G,IAAI,mBAAmB,CAAC,oBAAoB;wBAC5C,IAAI,mBAAmB,CAAC,kBAAkB;oBAC5C;oBACA,gBAAgB,IAAI,mBAAmB,CAAC,mBAAmB,sBAAsB,6BAA6B,qBAAqB;oBACnI,UAAU,OAAO;gDAAC,CAAA;4BAChB,SAAS,mBAAmB,CAAC,UAAU;wBACzC;;oBACA,OAAO,YAAY,CAAC;gBACtB;;QACF;+BAAG;QAAC;QAAS;QAAU;QAAW;QAAc;QAAmB;QAAM;QAAc;QAAgB;QAAS;QAAkB;QAAqB;QAAsB;QAAkB;QAA6B;QAAqB;QAAqB;KAA2B;IACjS,8JAAM,SAAS;gCAAC;YACd,mBAAmB,OAAO,GAAG;QAC/B;+BAAG;QAAC;QAAc;KAAkB;IACpC,MAAM,YAAY,8JAAM,OAAO;yCAAC,IAAM,CAAC;gBACrC,WAAW;gBACX,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;qDAAE,CAAA;wBACxC,IAAI,gBAAgB;4BAClB,aAAa,OAAO,MAAM,WAAW,EAAE;wBACzC;oBACF;;YACF,CAAC;wCAAG;QAAC;QAAsB;QAAc;QAAgB;KAAoB;IAC7E,MAAM,WAAW,8JAAM,OAAO;wCAAC,IAAM,CAAC;gBACpC,WAAW;gBACX;oBACE,wBAAwB,OAAO,GAAG;gBACpC;gBACA;oBACE,wBAAwB,OAAO,GAAG;gBACpC;gBACA,CAAC,kBAAkB,CAAC,kBAAkB,CAAC;oDAAE;wBACvC,mBAAmB,OAAO,GAAG;oBAC/B;;YACF,CAAC;uCAAG;QAAC;QAAsB;KAAkB;IAC7C,OAAO,8JAAM,OAAO;8BAAC,IAAM,UAAU;gBACnC;gBACA;YACF,IAAI,CAAC;6BAAG;QAAC;QAAS;QAAW;KAAS;AACxC;AAEA,SAAS,uBAAuB,OAAO;IACrC,MAAM,EACJ,OAAO,KAAK,EACZ,cAAc,gBAAgB,EAC9B,UAAU,YAAY,EACvB,GAAG;IACJ,MAAM,aAAa;IACnB,MAAM,UAAU,8JAAM,MAAM,CAAC,CAAC;IAC9B,MAAM,CAAC,OAAO,GAAG,8JAAM,QAAQ;2CAAC,IAAM;;IACtC,MAAM,SAAS,6BAA6B;IAC5C,wCAA2C;QACzC,MAAM,qBAAqB,aAAa,SAAS;QACjD,IAAI,sBAAsB,CAAC,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,qBAAqB;YACxD,MAAM,qEAAqE,uEAAuE;QACpJ;IACF;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,8JAAM,QAAQ,CAAC,aAAa,SAAS;IACvF,MAAM,eAAe;+DAAe,CAAC,MAAM,OAAO;YAChD,QAAQ,OAAO,CAAC,SAAS,GAAG,OAAO,QAAQ;YAC3C,OAAO,IAAI,CAAC,cAAc;gBACxB;gBACA;gBACA;gBACA;YACF;YACA,oBAAoB,QAAQ,iBAAiB,MAAM,OAAO;QAC5D;;IACA,MAAM,OAAO,8JAAM,OAAO;gDAAC,IAAM,CAAC;gBAChC;YACF,CAAC;+CAAG,EAAE;IACN,MAAM,WAAW,8JAAM,OAAO;oDAAC,IAAM,CAAC;gBACpC,WAAW,qBAAqB,aAAa,SAAS,IAAI;gBAC1D,UAAU,aAAa,QAAQ,IAAI;gBACnC,cAAc,aAAa,SAAS;YACtC,CAAC;mDAAG;QAAC;QAAmB,aAAa,SAAS;QAAE,aAAa,QAAQ;KAAC;IACtE,OAAO,8JAAM,OAAO;0CAAC,IAAM,CAAC;gBAC1B;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;yCAAG;QAAC;QAAM;QAAc;QAAU;QAAQ;QAAY;KAAK;AAC9D;AAEA;;;CAGC,GACD,SAAS,YAAY,OAAO;IAC1B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,MAAM,sBAAsB,uBAAuB;QACjD,GAAG,OAAO;QACV,UAAU;YACR,WAAW;YACX,UAAU;YACV,GAAG,QAAQ,QAAQ;QACrB;IACF;IACA,MAAM,cAAc,QAAQ,WAAW,IAAI;IAC3C,MAAM,mBAAmB,YAAY,QAAQ;IAC7C,MAAM,CAAC,eAAe,gBAAgB,GAAG,8JAAM,QAAQ,CAAC;IACxD,MAAM,CAAC,mBAAmB,sBAAsB,GAAG,8JAAM,QAAQ,CAAC;IAClE,MAAM,qBAAqB,oBAAoB,OAAO,KAAK,IAAI,iBAAiB,YAAY;IAC5F,MAAM,eAAe,sBAAsB;IAC3C,MAAM,kBAAkB,8JAAM,MAAM,CAAC;IACrC,MAAM,OAAO;IACb,MAAM;QACJ,IAAI,cAAc;YAChB,gBAAgB,OAAO,GAAG;QAC5B;IACF,GAAG;QAAC;KAAa;IACjB,MAAM,WAAW,CAAA,GAAA,8MAAA,CAAA,cAAa,AAAD,EAAE;QAC7B,GAAG,OAAO;QACV,UAAU;YACR,GAAG,gBAAgB;YACnB,GAAI,qBAAqB;gBACvB,WAAW;YACb,CAAC;QACH;IACF;IACA,MAAM,uBAAuB,8JAAM,WAAW;yDAAC,CAAA;YAC7C,MAAM,4BAA4B,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;gBAClD,qBAAqB;qEAAE,IAAM,KAAK,qBAAqB;;gBACvD,gBAAgB;YAClB,IAAI;YACJ,4FAA4F;YAC5F,2FAA2F;YAC3F,sBAAsB;YACtB,SAAS,IAAI,CAAC,YAAY,CAAC;QAC7B;wDAAG;QAAC,SAAS,IAAI;KAAC;IAClB,MAAM,eAAe,8JAAM,WAAW;iDAAC,CAAA;YACrC,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,SAAS,MAAM;gBACpC,gBAAgB,OAAO,GAAG;gBAC1B,gBAAgB;YAClB;YAEA,uEAAuE;YACvE,sCAAsC;YACtC,IAAI,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,QACtF,uEAAuE;YACvE,kEAAkE;YAClE,gBAAgB;YAChB,SAAS,QAAQ,CAAC,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,OAAO;gBACjC,SAAS,IAAI,CAAC,YAAY,CAAC;YAC7B;QACF;gDAAG;QAAC,SAAS,IAAI;KAAC;IAClB,MAAM,OAAO,8JAAM,OAAO;qCAAC,IAAM,CAAC;gBAChC,GAAG,SAAS,IAAI;gBAChB;gBACA;gBACA,cAAc;YAChB,CAAC;oCAAG;QAAC,SAAS,IAAI;QAAE;QAAc;KAAqB;IACvD,MAAM,WAAW,8JAAM,OAAO;yCAAC,IAAM,CAAC;gBACpC,GAAG,SAAS,QAAQ;gBACpB,cAAc;YAChB,CAAC;wCAAG;QAAC,SAAS,QAAQ;QAAE;KAAa;IACrC,MAAM,UAAU,8JAAM,OAAO;wCAAC,IAAM,CAAC;gBACnC,GAAG,QAAQ;gBACX,GAAG,WAAW;gBACd;gBACA;gBACA;YACF,CAAC;uCAAG;QAAC;QAAU;QAAM;QAAU;QAAQ;KAAY;IACnD,MAAM;QACJ,YAAY,OAAO,CAAC,OAAO,CAAC,eAAe,GAAG;QAC9C,MAAM,OAAO,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACpF,IAAI,MAAM;YACR,KAAK,OAAO,GAAG;QACjB;IACF;IACA,OAAO,8JAAM,OAAO;+BAAC,IAAM,CAAC;gBAC1B,GAAG,QAAQ;gBACX;gBACA;gBACA;YACF,CAAC;8BAAG;QAAC;QAAU;QAAM;QAAU;KAAQ;AACzC;AAEA;;;;CAIC,GACD,SAAS,SAAS,OAAO,EAAE,KAAK;IAC9B,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,MAAM,EACN,OAAO,EACP,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,cAAc,IAAI,EACnB,GAAG;IACJ,MAAM,gBAAgB,8JAAM,MAAM,CAAC;IACnC,MAAM,aAAa,8JAAM,MAAM;IAC/B,MAAM,sBAAsB,8JAAM,MAAM,CAAC;IACzC,8JAAM,SAAS;8BAAC;YACd,IAAI,CAAC,SAAS;YACd,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY;YAE3C,yEAAyE;YACzE,uEAAuE;YACvE,4BAA4B;YAC5B,SAAS;gBACP,IAAI,CAAC,QAAQ,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,YAAY,KAAK,SAAS,YAAY,KAAK,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,SAAS,YAAY,IAAI;oBAChI,cAAc,OAAO,GAAG;gBAC1B;YACF;YACA,SAAS;gBACP,oBAAoB,OAAO,GAAG;YAChC;YACA,IAAI,gBAAgB,CAAC,QAAQ;YAC7B,IAAI,gBAAgB,CAAC,WAAW,WAAW;YAC3C;sCAAO;oBACL,IAAI,mBAAmB,CAAC,QAAQ;oBAChC,IAAI,mBAAmB,CAAC,WAAW,WAAW;gBAChD;;QACF;6BAAG;QAAC,SAAS,YAAY;QAAE;QAAM;KAAQ;IACzC,8JAAM,SAAS;8BAAC;YACd,IAAI,CAAC,SAAS;YACd,SAAS,aAAa,IAAI;gBACxB,IAAI,EACF,MAAM,EACP,GAAG;gBACJ,IAAI,WAAW,qBAAqB,WAAW,cAAc;oBAC3D,cAAc,OAAO,GAAG;gBAC1B;YACF;YACA,OAAO,EAAE,CAAC,cAAc;YACxB;sCAAO;oBACL,OAAO,GAAG,CAAC,cAAc;gBAC3B;;QACF;6BAAG;QAAC;QAAQ;KAAQ;IACpB,8JAAM,SAAS;8BAAC;YACd;sCAAO;oBACL,aAAa,WAAW,OAAO;gBACjC;;QACF;6BAAG,EAAE;IACL,MAAM,YAAY,8JAAM,OAAO;uCAAC,IAAM,CAAC;gBACrC,eAAc,KAAK;oBACjB,IAAI,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,WAAW,GAAG;oBAC9C,oBAAoB,OAAO,GAAG;gBAChC;gBACA;oBACE,cAAc,OAAO,GAAG;gBAC1B;gBACA,SAAQ,KAAK;oBACX,IAAI,cAAc,OAAO,EAAE;oBAC3B,MAAM,SAAS,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE,MAAM,WAAW;oBAC1C,IAAI,eAAe,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,SAAS;wBACpC,IAAI;4BACF,kEAAkE;4BAClE,6DAA6D;4BAC7D,WAAW;4BACX,IAAI,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,OAAO,CAAA,GAAA,yLAAA,CAAA,QAAK,AAAD,KAAK,MAAM;4BACjC,IAAI,CAAC,OAAO,OAAO,CAAC,mBAAmB;wBACzC,EAAE,OAAO,GAAG;4BACV,gEAAgE;4BAChE,IAAI,CAAC,oBAAoB,OAAO,IAAI,CAAC,CAAA,GAAA,yLAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;gCAC9D;4BACF;wBACF;oBACF;oBACA,aAAa,MAAM,MAAM,WAAW,EAAE;gBACxC;gBACA,QAAO,KAAK;oBACV,cAAc,OAAO,GAAG;oBACxB,MAAM,gBAAgB,MAAM,aAAa;oBACzC,MAAM,cAAc,MAAM,WAAW;oBAErC,iEAAiE;oBACjE,qDAAqD;oBACrD,MAAM,oBAAoB,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,cAAc,YAAY,CAAC,gBAAgB,mBAAmB,cAAc,YAAY,CAAC,iBAAiB;oBAEhK,6CAA6C;oBAC7C,WAAW,OAAO,GAAG,OAAO,UAAU;uDAAC;4BACrC,IAAI;4BACJ,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,YAAY,GAAG,SAAS,YAAY,CAAC,aAAa,GAAG;4BAE7F,qCAAqC;4BACrC,IAAI,CAAC,iBAAiB,aAAa,SAAS,YAAY,EAAE;4BAE1D,iEAAiE;4BACjE,8DAA8D;4BAC9D,oDAAoD;4BACpD,mEAAmE;4BACnE,wEAAwE;4BACxE,wEAAwE;4BACxE,wBAAwB;4BACxB,IAAI,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,wBAAwB,QAAQ,OAAO,CAAC,eAAe,KAAK,OAAO,KAAK,IAAI,sBAAsB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAa,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,YAAY,EAAE,aAAa,mBAAmB;gCAClN;4BACF;4BACA,aAAa,OAAO,aAAa;wBACnC;;gBACF;YACF,CAAC;sCAAG;QAAC;QAAS,SAAS,YAAY;QAAE;QAAc;KAAY;IAC/D,OAAO,8JAAM,OAAO;4BAAC,IAAM,UAAU;gBACnC;YACF,IAAI,CAAC;2BAAG;QAAC;QAAS;KAAU;AAC9B;AAEA,MAAM,aAAa;AACnB,MAAM,eAAe;AACrB,SAAS,WAAW,SAAS,EAAE,SAAS,EAAE,UAAU;IAClD,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,eAAe;IAC9B,IAAI,eAAe;IACnB,IAAI,UAAU,WAAW;QACvB,MAAM,EACJ,CAAC,WAAW,EAAE,CAAC,EACf,CAAC,aAAa,EAAE,EAAE,EAClB,GAAG,YACJ,GAAG;QACJ,eAAe;IACjB;IACA,OAAO;QACL,GAAI,eAAe,cAAc;YAC/B,UAAU,CAAC;YACX,CAAC,oBAAoB,EAAE;QACzB,CAAC;QACD,GAAG,YAAY;QACf,GAAG,UAAU,GAAG,CAAC,CAAA;YACf,MAAM,kBAAkB,QAAQ,KAAK,CAAC,WAAW,GAAG;YACpD,IAAI,OAAO,oBAAoB,YAAY;gBACzC,OAAO,YAAY,gBAAgB,aAAa;YAClD;YACA,OAAO;QACT,GAAG,MAAM,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK;YAChC,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YACA,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,IAAI,UAAU;oBAAC;oBAAY;iBAAa,CAAC,QAAQ,CAAC,MAAM;oBACtD;gBACF;gBACA,IAAI,IAAI,OAAO,CAAC,UAAU,GAAG;oBAC3B,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM;wBACjB,IAAI,GAAG,CAAC,KAAK,EAAE;oBACjB;oBACA,IAAI,OAAO,UAAU,YAAY;wBAC/B,IAAI;wBACJ,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,SAAS,IAAI,CAAC;wBACnD,GAAG,CAAC,IAAI,GAAG;4BACT,IAAI;4BACJ,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;gCACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;4BAC9B;4BACA,OAAO,CAAC,YAAY,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO,KAAK,IAAI,UAAU,GAAG,CAAC,CAAA,KAAM,MAAM,OAAO,IAAI,CAAC,CAAA,MAAO,QAAQ;wBAC5G;oBACF;gBACF,OAAO;oBACL,GAAG,CAAC,IAAI,GAAG;gBACb;YACF;YACA,OAAO;QACT,GAAG,CAAC,EAAE;IACR;AACF;AACA;;;;;CAKC,GACD,SAAS,gBAAgB,SAAS;IAChC,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY,EAAE;IAChB;IACA,MAAM,gBAAgB,UAAU,GAAG,CAAC,CAAA,MAAO,OAAO,OAAO,KAAK,IAAI,IAAI,SAAS;IAC/E,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,MAAO,OAAO,OAAO,KAAK,IAAI,IAAI,QAAQ;IAC7E,MAAM,WAAW,UAAU,GAAG,CAAC,CAAA,MAAO,OAAO,OAAO,KAAK,IAAI,IAAI,IAAI;IACrE,MAAM,oBAAoB,8JAAM,WAAW;0DAAC,CAAA,YAAa,WAAW,WAAW,WAAW;yDAC1F,uDAAuD;IACvD;IACA,MAAM,mBAAmB,8JAAM,WAAW;yDAAC,CAAA,YAAa,WAAW,WAAW,WAAW;wDACzF,uDAAuD;IACvD;IACA,MAAM,eAAe,8JAAM,WAAW;qDAAC,CAAA,YAAa,WAAW,WAAW,WAAW;oDACrF,uDAAuD;IACvD;IACA,OAAO,8JAAM,OAAO;mCAAC,IAAM,CAAC;gBAC1B;gBACA;gBACA;YACF,CAAC;kCAAG;QAAC;QAAmB;QAAkB;KAAa;AACzD;AAEA,IAAI,2BAA2B;AAC/B,SAAS,SAAS,WAAW,EAAE,QAAQ,EAAE,UAAU;IACjD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,YAAY;IACvB;AACF;AACA,SAAS,qBAAqB,GAAG,EAAE,WAAW;IAC5C,MAAM,WAAW,QAAQ,YAAY,QAAQ;IAC7C,MAAM,aAAa,QAAQ,cAAc,QAAQ;IACjD,OAAO,SAAS,aAAa,UAAU;AACzC;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW,EAAE,GAAG;IACtD,MAAM,WAAW,QAAQ;IACzB,MAAM,aAAa,MAAM,QAAQ,aAAa,QAAQ;IACtD,OAAO,SAAS,aAAa,UAAU,eAAe,QAAQ,WAAW,QAAQ,OAAO,QAAQ;AAClG;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW,EAAE,GAAG;IACtD,MAAM,WAAW,MAAM,QAAQ,aAAa,QAAQ;IACpD,MAAM,aAAa,QAAQ;IAC3B,OAAO,SAAS,aAAa,UAAU;AACzC;AACA,SAAS,2BAA2B,GAAG,EAAE,WAAW,EAAE,GAAG;IACvD,MAAM,WAAW,MAAM,QAAQ,cAAc,QAAQ;IACrD,MAAM,aAAa,QAAQ;IAC3B,OAAO,SAAS,aAAa,UAAU;AACzC;AACA;;;;CAIC,GACD,SAAS,kBAAkB,OAAO,EAAE,KAAK;IACvC,MAAM,EACJ,IAAI,EACJ,YAAY,EACZ,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,OAAO,EACP,WAAW,EACX,YAAY,sBAAsB,KAAO,CAAC,EAC1C,UAAU,IAAI,EACd,gBAAgB,IAAI,EACpB,cAAc,KAAK,EACnB,OAAO,KAAK,EACZ,SAAS,KAAK,EACd,MAAM,KAAK,EACX,UAAU,KAAK,EACf,kBAAkB,MAAM,EACxB,mBAAmB,IAAI,EACvB,qBAAqB,IAAI,EACzB,kBAAkB,SAAS,EAC3B,cAAc,UAAU,EACxB,OAAO,CAAC,EACR,qBAAqB,IAAI,EACzB,cAAc,EACd,SAAS,EACT,QAAQ,KAAK,EACd,GAAG;IACJ,wCAA2C;QACzC,IAAI,aAAa;YACf,IAAI,CAAC,MAAM;gBACT,KAAK;YACP;YACA,IAAI,CAAC,SAAS;gBACZ,KAAK;YACP;QACF;QACA,IAAI,gBAAgB,cAAc,OAAO,GAAG;YAC1C,KAAK,uEAAuE;QAC9E;IACF;IACA,MAAM,uBAAuB,wBAAwB,SAAS,QAAQ;IACtE,MAAM,0BAA0B,aAAa;IAC7C,MAAM,WAAW;IACjB,MAAM,OAAO;IACb,MAAM,aAAa,eAAe;IAClC,MAAM,4BAA4B,CAAA,GAAA,yLAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,YAAY;IAC1E,MAAM,qBAAqB,8JAAM,MAAM,CAAC;IACxC,MAAM,WAAW,8JAAM,MAAM,CAAC,iBAAiB,OAAO,gBAAgB,CAAC;IACvE,MAAM,SAAS,8JAAM,MAAM,CAAC;IAC5B,MAAM,uBAAuB,8JAAM,MAAM,CAAC;IAC1C,MAAM,wBAAwB,8JAAM,MAAM,CAAC;IAC3C,MAAM,qBAAqB,8JAAM,MAAM,CAAC,CAAC,CAAC,SAAS,QAAQ;IAC3D,MAAM,kBAAkB,8JAAM,MAAM,CAAC;IACrC,MAAM,iBAAiB,8JAAM,MAAM,CAAC;IACpC,MAAM,yBAAyB,8JAAM,MAAM,CAAC;IAC5C,MAAM,qBAAqB,aAAa;IACxC,MAAM,gBAAgB,aAAa;IACnC,MAAM,wBAAwB,aAAa;IAC3C,MAAM,mBAAmB,aAAa;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,8JAAM,QAAQ;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,8JAAM,QAAQ;IAChD,MAAM,YAAY;uDAAe,SAAU,OAAO,EAAE,QAAQ,EAAE,mBAAmB;YAC/E,IAAI,wBAAwB,KAAK,GAAG;gBAClC,sBAAsB;YACxB;YACA,SAAS,SAAS,IAAI;gBACpB,IAAI,SAAS;oBACX,YAAY,KAAK,EAAE;oBACnB,QAAQ,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC,gBAAgB;oBACjD,IAAI,gBAAgB;wBAClB,eAAe,OAAO,GAAG;oBAC3B;gBACF,OAAO;oBACL,aAAa,MAAM;wBACjB,eAAe;wBACf,oEAAoE;wBACpE,mEAAmE;wBACnE,4DAA4D;wBAC5D,sEAAsE;wBACtE,4DAA4D;wBAC5D,mEAAmE;wBACnE,8BAA8B;wBAC9B,kEAAkE;wBAClE,MAAM,CAAA,GAAA,yLAAA,CAAA,QAAK,AAAD,OAAO,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,MAAM,4BAA4B,eAAe,OAAO,GAAG;oBACrF;gBACF;YACF;YACA,MAAM,cAAc,QAAQ,OAAO,CAAC,SAAS,OAAO,CAAC;YACrD,IAAI,aAAa;gBACf,SAAS;YACX;YACA;+DAAsB;oBACpB,MAAM,aAAa,QAAQ,OAAO,CAAC,SAAS,OAAO,CAAC,IAAI;oBACxD,IAAI,CAAC,YAAY;oBACjB,IAAI,CAAC,aAAa;wBAChB,SAAS;oBACX;oBACA,MAAM,wBAAwB,sBAAsB,OAAO;oBAC3D,MAAM,uBAAuB,yBAAyB,QAAQ,CAAC,uBAAuB,CAAC,qBAAqB,OAAO;oBACnH,IAAI,sBAAsB;wBACxB,sEAAsE;wBACtE,mBAAmB;wBACnB,WAAW,cAAc,IAAI,QAAQ,WAAW,cAAc,CAAC,OAAO,0BAA0B,YAAY;4BAC1G,OAAO;4BACP,QAAQ;wBACV,IAAI;oBACN;gBACF;;QACF;;IACA,MAAM;QACJ,SAAS,aAAa,CAAC,OAAO,KAAK,CAAC;YAClC,IAAI,iBAAgB;gBAClB,2BAA2B;gBAC3B,OAAO;YACT;QACF;IACF,GAAG,EAAE;IAEL,yEAAyE;IACzE,wEAAwE;IACxE,MAAM;QACJ,IAAI,CAAC,SAAS;QACd,IAAI,QAAQ,SAAS,QAAQ,EAAE;YAC7B,IAAI,mBAAmB,OAAO,IAAI,iBAAiB,MAAM;gBACvD,qEAAqE;gBACrE,4DAA4D;gBAC5D,uBAAuB,OAAO,GAAG;gBACjC,SAAS,OAAO,GAAG;gBACnB,WAAW;YACb;QACF,OAAO,IAAI,mBAAmB,OAAO,EAAE;YACrC,wDAAwD;YACxD,0DAA0D;YAC1D,2CAA2C;YAC3C,SAAS,OAAO,GAAG,CAAC;YACpB,sBAAsB,OAAO,CAAC;QAChC;IACF,GAAG;QAAC;QAAS;QAAM,SAAS,QAAQ;QAAE;QAAe;KAAW;IAEhE,0EAA0E;IAC1E,QAAQ;IACR,MAAM;QACJ,IAAI,CAAC,SAAS;QACd,IAAI,QAAQ,SAAS,QAAQ,EAAE;YAC7B,IAAI,eAAe,MAAM;gBACvB,eAAe,OAAO,GAAG;gBACzB,IAAI,iBAAiB,OAAO,IAAI,MAAM;oBACpC;gBACF;gBAEA,qEAAqE;gBACrE,IAAI,mBAAmB,OAAO,EAAE;oBAC9B,SAAS,OAAO,GAAG,CAAC;oBACpB,UAAU,SAAS;gBACrB;gBAEA,gBAAgB;gBAChB,IAAI,CAAC,CAAC,gBAAgB,OAAO,IAAI,CAAC,mBAAmB,OAAO,KAAK,mBAAmB,OAAO,IAAI,CAAC,OAAO,OAAO,IAAI,QAAQ,mBAAmB,OAAO,KAAK,QAAQ,OAAO,OAAO,IAAI,IAAI,GAAG;oBACxL,IAAI,OAAO;oBACX,MAAM,uBAAuB;wBAC3B,IAAI,QAAQ,OAAO,CAAC,EAAE,IAAI,MAAM;4BAC9B,gEAAgE;4BAChE,gEAAgE;4BAChE,sBAAsB;4BACtB,IAAI,OAAO,GAAG;gCACZ,MAAM,YAAY,OAAO,wBAAwB;gCACjD,UAAU;4BACZ;4BACA;wBACF,OAAO;4BACL,SAAS,OAAO,GAAG,OAAO,OAAO,IAAI,QAAQ,0BAA0B,OAAO,OAAO,EAAE,aAAa,QAAQ,SAAS,YAAY,SAAS,mBAAmB,OAAO,IAAI,YAAY,SAAS,mBAAmB,OAAO;4BACvN,OAAO,OAAO,GAAG;4BACjB,WAAW,SAAS,OAAO;wBAC7B;oBACF;oBACA;gBACF;YACF,OAAO,IAAI,CAAC,mBAAmB,SAAS,cAAc;gBACpD,SAAS,OAAO,GAAG;gBACnB,UAAU,SAAS,UAAU,uBAAuB,OAAO;gBAC3D,uBAAuB,OAAO,GAAG;YACnC;QACF;IACF,GAAG;QAAC;QAAS;QAAM,SAAS,QAAQ;QAAE;QAAa;QAAkB;QAAQ;QAAS;QAAa;QAAK;QAAY;QAAW;KAAmB;IAElJ,0EAA0E;IAC1E,4EAA4E;IAC5E,MAAM;QACJ,IAAI;QACJ,IAAI,CAAC,WAAW,SAAS,QAAQ,IAAI,CAAC,QAAQ,WAAW,CAAC,mBAAmB,OAAO,EAAE;YACpF;QACF;QACA,MAAM,QAAQ,KAAK,QAAQ,CAAC,OAAO;QACnC,MAAM,SAAS,CAAC,cAAc,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,KAAK,QAAQ,CAAC,cAAc,YAAY,OAAO,KAAK,OAAO,KAAK,IAAI,YAAY,QAAQ,CAAC,QAAQ;QACvK,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,SAAS,QAAQ;QAC5D,MAAM,uBAAuB,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,IAAI,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE;QACzG,IAAI,UAAU,CAAC,wBAAwB,qBAAqB,OAAO,EAAE;YACnE,OAAO,KAAK,CAAC;gBACX,eAAe;YACjB;QACF;IACF,GAAG;QAAC;QAAS,SAAS,QAAQ;QAAE;QAAM;QAAU;KAAQ;IACxD,MAAM;QACJ,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,SAAS;QACd,IAAI,UAAU;QACd,SAAS,mBAAmB,IAAI;YAC9B,aAAa,KAAK,EAAE;YACpB,IAAI,gBAAgB;gBAClB,eAAe,OAAO,GAAG;YAC3B;QACF;QACA,KAAK,MAAM,CAAC,EAAE,CAAC,gBAAgB;QAC/B,OAAO;YACL,KAAK,MAAM,CAAC,GAAG,CAAC,gBAAgB;QAClC;IACF,GAAG;QAAC;QAAS;QAAM;QAAS;QAAU;KAAe;IACrD,MAAM;QACJ,sBAAsB,OAAO,GAAG;QAChC,mBAAmB,OAAO,GAAG,CAAC,CAAC,SAAS,QAAQ;IAClD;IACA,MAAM;QACJ,IAAI,CAAC,MAAM;YACT,OAAO,OAAO,GAAG;QACnB;IACF,GAAG;QAAC;KAAK;IACT,MAAM;QACJ,gBAAgB,OAAO,GAAG;IAC5B,GAAG;QAAC;KAAK;IACT,MAAM,iBAAiB,eAAe;IACtC,MAAM,OAAO,8JAAM,OAAO;2CAAC;YACzB,SAAS,kBAAkB,aAAa;gBACtC,IAAI,CAAC,MAAM;gBACX,MAAM,QAAQ,QAAQ,OAAO,CAAC,OAAO,CAAC;gBACtC,IAAI,UAAU,CAAC,GAAG;oBAChB,WAAW;gBACb;YACF;YACA,MAAM,QAAQ;gBACZ,SAAQ,IAAI;oBACV,IAAI,EACF,aAAa,EACd,GAAG;oBACJ,kBAAkB;gBACpB;gBACA,OAAO;uDAAE,CAAA;wBACP,IAAI,EACF,aAAa,EACd,GAAG;wBACJ,OAAO,cAAc,KAAK,CAAC;4BACzB,eAAe;wBACjB;oBACF;;gBACA,SAAS;gBACT,GAAI,oBAAoB;oBACtB,aAAY,KAAK;wBACf,IAAI,EACF,aAAa,EACd,GAAG;wBACJ,kBAAkB;oBACpB;oBACA,gBAAe,KAAK;wBAClB,IAAI,EACF,WAAW,EACZ,GAAG;wBACJ,IAAI,CAAC,qBAAqB,OAAO,IAAI,gBAAgB,SAAS;4BAC5D;wBACF;wBACA,SAAS,OAAO,GAAG,CAAC;wBACpB,UAAU,SAAS;wBACnB,WAAW;wBACX,IAAI,CAAC,SAAS;4BACZ,aAAa,wBAAwB,OAAO,EAAE;gCAC5C,eAAe;4BACjB;wBACF;oBACF;gBACF,CAAC;YACH;YACA,OAAO;QACT;0CAAG;QAAC;QAAM;QAAyB;QAAW;QAAkB;QAAS;QAAY;KAAQ;IAC7F,MAAM,kBAAkB;6DAAe,CAAA;YACrC,qBAAqB,OAAO,GAAG;YAC/B,eAAe,OAAO,GAAG;YAEzB,2EAA2E;YAC3E,0EAA0E;YAC1E,2EAA2E;YAC3E,wDAAwD;YACxD,IAAI,MAAM,KAAK,KAAK,KAAK;gBACvB;YACF;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,wDAAwD;YACxD,IAAI,CAAC,cAAc,OAAO,IAAI,MAAM,aAAa,KAAK,wBAAwB,OAAO,EAAE;gBACrF;YACF;YACA,IAAI,UAAU,2BAA2B,MAAM,GAAG,EAAE,aAAa,MAAM;gBACrE,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;gBACV,aAAa,OAAO,MAAM,WAAW,EAAE;gBACvC,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,YAAY,GAAG;oBACxC,IAAI,SAAS;wBACX,QAAQ,QAAQ,KAAK,MAAM,CAAC,IAAI,CAAC,gBAAgB,SAAS,YAAY;oBACxE,OAAO;wBACL,SAAS,YAAY,CAAC,KAAK;oBAC7B;gBACF;gBACA;YACF;YACA,MAAM,eAAe,SAAS,OAAO;YACrC,MAAM,WAAW,YAAY,SAAS;YACtC,MAAM,WAAW,YAAY,SAAS;YACtC,IAAI,CAAC,2BAA2B;gBAC9B,IAAI,MAAM,GAAG,KAAK,QAAQ;oBACxB,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;oBACV,SAAS,OAAO,GAAG;oBACnB,WAAW,SAAS,OAAO;gBAC7B;gBACA,IAAI,MAAM,GAAG,KAAK,OAAO;oBACvB,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;oBACV,SAAS,OAAO,GAAG;oBACnB,WAAW,SAAS,OAAO;gBAC7B;YACF;YAEA,mBAAmB;YACnB,IAAI,OAAO,GAAG;gBACZ,MAAM,QAAQ,aAAa,MAAM,IAAI,CAAC;oBACpC,QAAQ,QAAQ,OAAO,CAAC,MAAM;gBAChC;yEAAG,IAAM,CAAC;4BACR,OAAO;4BACP,QAAQ;wBACV,CAAC;;gBACD,uEAAuE;gBACvE,+DAA+D;gBAC/D,MAAM,UAAU,aAAa,OAAO,MAAM;gBAC1C,MAAM,eAAe,QAAQ,SAAS;sFAAC,CAAA,QAAS,SAAS,QAAQ,CAAC,WAAW,QAAQ,OAAO,EAAE,OAAO;;gBACrG,qBAAqB;gBACrB,MAAM,eAAe,QAAQ,MAAM;sFAAC,CAAC,YAAY,OAAO,YAAc,SAAS,QAAQ,CAAC,WAAW,QAAQ,OAAO,EAAE,OAAO,mBAAmB,YAAY;qFAAY,CAAC;gBACvK,MAAM,QAAQ,OAAO,CAAC,sBAAsB;oBAC1C,SAAS,QAAQ,GAAG;6EAAC,CAAA,YAAa,aAAa,OAAO,QAAQ,OAAO,CAAC,UAAU,GAAG;;gBACrF,GAAG;oBACD;oBACA;oBACA;oBACA;oBACA;oBACA,gEAAgE;oBAChE,uBAAuB;oBACvB,iBAAiB,eAAe;2BAAK,mBAAmB,QAAQ,OAAO,CAAC,GAAG;iFAAC,CAAC,GAAG,QAAU,WAAW,QAAQ,OAAO,EAAE,SAAS,QAAQ;;wBAAa;qBAAU,EAAE;oBAChK,UAAU;oBACV,UAAU;oBACV,WAAW,qBAAqB,SAAS,OAAO,GAAG,WAAW,WAAW,SAAS,OAAO,EAAE,OAAO,SAAS,MAC3G,0DAA0D;oBAC1D,8DAA8D;oBAC9D,8BAA8B;oBAC9B,MAAM,GAAG,KAAK,aAAa,OAAO,MAAM,GAAG,KAAK,CAAC,MAAM,aAAa,WAAW,IAAI,OAAO;oBAC1F,WAAW;gBACb,GAAG;gBACH,IAAI,SAAS,MAAM;oBACjB,SAAS,OAAO,GAAG;oBACnB,WAAW,SAAS,OAAO;gBAC7B;gBACA,IAAI,gBAAgB,QAAQ;oBAC1B;gBACF;YACF;YACA,IAAI,qBAAqB,MAAM,GAAG,EAAE,cAAc;gBAChD,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;gBAEV,yCAAyC;gBACzC,IAAI,QAAQ,CAAC,WAAW,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,aAAa,CAAC,aAAa,MAAM,MAAM,aAAa,EAAE;oBAChG,SAAS,OAAO,GAAG,0BAA0B,MAAM,GAAG,EAAE,aAAa,OAAO,WAAW;oBACvF,WAAW,SAAS,OAAO;oBAC3B;gBACF;gBACA,IAAI,0BAA0B,MAAM,GAAG,EAAE,aAAa,MAAM;oBAC1D,IAAI,MAAM;wBACR,SAAS,OAAO,GAAG,gBAAgB,WAAW,eAAe,iBAAiB,QAAQ,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,qBAAqB,SAAS;4BACnJ,eAAe;4BACf;wBACF;oBACF,OAAO;wBACL,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,qBAAqB,SAAS;4BAClE,eAAe;4BACf;wBACF;oBACF;gBACF,OAAO;oBACL,IAAI,MAAM;wBACR,SAAS,OAAO,GAAG,gBAAgB,WAAW,eAAe,iBAAiB,CAAC,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,WAAW,qBAAqB,SAAS;4BACnJ,eAAe;4BACf,WAAW;4BACX;wBACF;oBACF,OAAO;wBACL,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,qBAAqB,SAAS;4BAClE,eAAe;4BACf,WAAW;4BACX;wBACF;oBACF;gBACF;gBACA,IAAI,mBAAmB,SAAS,SAAS,OAAO,GAAG;oBACjD,WAAW;gBACb,OAAO;oBACL,WAAW,SAAS,OAAO;gBAC7B;YACF;QACF;;IACA,MAAM,2BAA2B,8JAAM,OAAO;+DAAC;YAC7C,OAAO,WAAW,QAAQ,kBAAkB;gBAC1C,yBAAyB,aAAa;YACxC;QACF;8DAAG;QAAC;QAAS;QAAM;QAAgB;QAAW;KAAS;IACvD,MAAM,WAAW,8JAAM,OAAO;+CAAC;YAC7B,OAAO;gBACL,oBAAoB,gBAAgB,SAAS,YAAY;gBACzD,GAAI,CAAC,CAAA,GAAA,yLAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,YAAY,KAAK,wBAAwB;gBAC1E,WAAW;gBACX;oBACE,qBAAqB,OAAO,GAAG;gBACjC;YACF;QACF;8CAAG;QAAC;QAA0B;QAAiB,SAAS,YAAY;QAAE;KAAY;IAClF,MAAM,YAAY,8JAAM,OAAO;gDAAC;YAC9B,SAAS,kBAAkB,KAAK;gBAC9B,IAAI,oBAAoB,UAAU,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW,GAAG;oBACnE,mBAAmB,OAAO,GAAG;gBAC/B;YACF;YACA,SAAS,oBAAoB,KAAK;gBAChC,sEAAsE;gBACtE,mBAAmB,OAAO,GAAG;gBAC7B,IAAI,oBAAoB,UAAU,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,WAAW,GAAG;oBAC1E,mBAAmB,OAAO,GAAG;gBAC/B;YACF;YACA,OAAO;gBACL,GAAG,wBAAwB;gBAC3B,WAAU,KAAK;oBACb,qBAAqB,OAAO,GAAG;oBAC/B,MAAM,aAAa,MAAM,GAAG,CAAC,UAAU,CAAC;oBACxC,MAAM,iBAAiB;wBAAC;wBAAQ;qBAAM,CAAC,QAAQ,CAAC,MAAM,GAAG;oBACzD,MAAM,YAAY,cAAc;oBAChC,MAAM,iBAAiB,0BAA0B,MAAM,GAAG,EAAE,aAAa;oBACzE,MAAM,kBAAkB,2BAA2B,MAAM,GAAG,EAAE,aAAa;oBAC3E,MAAM,YAAY,qBAAqB,MAAM,GAAG,EAAE;oBAClD,MAAM,kBAAkB,CAAC,SAAS,iBAAiB,SAAS,KAAK,MAAM,GAAG,KAAK,WAAW,MAAM,GAAG,CAAC,IAAI,OAAO;oBAC/G,IAAI,WAAW,MAAM;wBACnB,MAAM,WAAW,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,OAAO,CAAC,IAAI;oEAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI;;wBAC7F,MAAM,cAAc,QAAQ,WAAW,eAAe,KAAK,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI;wBAC5F,IAAI,aAAa,eAAe,gBAAgB;4BAC9C,MAAM,cAAc,IAAI,cAAc,WAAW;gCAC/C,KAAK,MAAM,GAAG;gCACd,SAAS;4BACX;4BACA,IAAI,kBAAkB,iBAAiB;gCACrC,IAAI,sBAAsB;gCAC1B,MAAM,kBAAkB,CAAC,CAAC,uBAAuB,YAAY,OAAO,KAAK,OAAO,KAAK,IAAI,qBAAqB,QAAQ,CAAC,YAAY,MAAM,MAAM,aAAa;gCAC5J,MAAM,eAAe,mBAAmB,CAAC,kBAAkB,CAAC,wBAAwB,YAAY,OAAO,KAAK,OAAO,KAAK,IAAI,sBAAsB,QAAQ,CAAC,YAAY,GAAG,iBAAiB,QAAQ,OAAO,CAAC,IAAI;4EAAC,CAAA,OAAQ,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,MAAM;6EAAY;gCAC1Q,IAAI,cAAc;oCAChB,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;oCACV,aAAa,aAAa,CAAC;oCAC3B,aAAa;gCACf;4BACF;4BACA,IAAI,CAAC,aAAa,cAAc,KAAK,YAAY,OAAO,EAAE;gCACxD,IAAI,YAAY,OAAO,CAAC,IAAI,IAAI,YAAY,QAAQ,IAAI,MAAM,aAAa,KAAK,YAAY,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE;oCACzH,IAAI;oCACJ,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;oCACV,CAAC,wBAAwB,YAAY,OAAO,CAAC,QAAQ,CAAC,YAAY,KAAK,QAAQ,sBAAsB,aAAa,CAAC;oCACnH;gCACF;4BACF;wBACF;wBACA,OAAO,gBAAgB;oBACzB;oBAEA,iEAAiE;oBACjE,2CAA2C;oBAC3C,IAAI,CAAC,QAAQ,CAAC,sBAAsB,YAAY;wBAC9C;oBACF;oBACA,IAAI,iBAAiB;wBACnB,OAAO,OAAO,GAAG,UAAU,YAAY,OAAO,MAAM,GAAG;oBACzD;oBACA,IAAI,QAAQ;wBACV,IAAI,gBAAgB;4BAClB,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;4BACV,IAAI,MAAM;gCACR,SAAS,OAAO,GAAG,YAAY,SAAS,mBAAmB,OAAO;gCAClE,WAAW,SAAS,OAAO;4BAC7B,OAAO;gCACL,aAAa,MAAM,MAAM,WAAW,EAAE;4BACxC;wBACF;wBACA;oBACF;oBACA,IAAI,WAAW;wBACb,IAAI,iBAAiB,MAAM;4BACzB,SAAS,OAAO,GAAG;wBACrB;wBACA,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;wBACV,IAAI,CAAC,QAAQ,oBAAoB;4BAC/B,aAAa,MAAM,MAAM,WAAW,EAAE;wBACxC,OAAO;4BACL,gBAAgB;wBAClB;wBACA,IAAI,MAAM;4BACR,WAAW,SAAS,OAAO;wBAC7B;oBACF;gBACF;gBACA;oBACE,IAAI,QAAQ,CAAC,SAAS;wBACpB,WAAW;oBACb;gBACF;gBACA,eAAe;gBACf,aAAa;gBACb,SAAS;YACX;QACF;+CAAG;QAAC;QAAU;QAA0B;QAAiB;QAAoB;QAAiB;QAAS;QAAQ;QAAY;QAAc;QAAM;QAAoB;QAAa;QAAK;QAAe;QAAM;QAAS;KAAe;IAClO,OAAO,8JAAM,OAAO;qCAAC,IAAM,UAAU;gBACnC;gBACA;gBACA;YACF,IAAI,CAAC;oCAAG;QAAC;QAAS;QAAW;QAAU;KAAK;AAC9C;AAEA,MAAM,6BAA6B,WAAW,GAAE,IAAI,IAAI;IAAC;QAAC;QAAU;KAAU;IAAE;QAAC;QAAY;KAAU;IAAE;QAAC;QAAS;KAAM;CAAC;AAE1H;;;;CAIC,GACD,SAAS,QAAQ,OAAO,EAAE,KAAK;IAC7B,IAAI;IACJ,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,UAAU,EACX,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,OAAO,QAAQ,EAChB,GAAG;IACJ,MAAM,WAAW,CAAC,wBAAwB,2BAA2B,GAAG,CAAC,KAAK,KAAK,OAAO,wBAAwB;IAClH,MAAM,cAAc;IACpB,MAAM,WAAW;IACjB,MAAM,WAAW,YAAY;IAC7B,MAAM,YAAY,8JAAM,OAAO;sCAAC;YAC9B,IAAI,aAAa,aAAa,SAAS,SAAS;gBAC9C,OAAO;oBACL,CAAC,UAAU,CAAC,SAAS,UAAU,eAAe,aAAa,EAAE,EAAE,OAAO,aAAa;gBACrF;YACF;YACA,OAAO;gBACL,iBAAiB,OAAO,SAAS;gBACjC,iBAAiB,aAAa,gBAAgB,WAAW;gBACzD,iBAAiB,OAAO,aAAa;gBACrC,GAAI,aAAa,aAAa;oBAC5B,MAAM;gBACR,CAAC;gBACD,GAAI,aAAa,UAAU;oBACzB,IAAI;gBACN,CAAC;gBACD,GAAI,aAAa,UAAU,YAAY;oBACrC,MAAM;gBACR,CAAC;gBACD,GAAI,SAAS,YAAY;oBACvB,qBAAqB;gBACvB,CAAC;gBACD,GAAI,SAAS,cAAc;oBACzB,qBAAqB;gBACvB,CAAC;YACH;QACF;qCAAG;QAAC;QAAU;QAAY;QAAU;QAAM;QAAa;KAAK;IAC5D,MAAM,WAAW,8JAAM,OAAO;qCAAC;YAC7B,MAAM,gBAAgB;gBACpB,IAAI;gBACJ,GAAI,YAAY;oBACd,MAAM;gBACR,CAAC;YACH;YACA,IAAI,aAAa,aAAa,SAAS,SAAS;gBAC9C,OAAO;YACT;YACA,OAAO;gBACL,GAAG,aAAa;gBAChB,GAAI,aAAa,UAAU;oBACzB,mBAAmB;gBACrB,CAAC;YACH;QACF;oCAAG;QAAC;QAAU;QAAY;QAAa;KAAK;IAC5C,MAAM,OAAO,8JAAM,WAAW;qCAAC,CAAA;YAC7B,IAAI,EACF,MAAM,EACN,QAAQ,EACT,GAAG;YACJ,MAAM,cAAc;gBAClB,MAAM;gBACN,GAAI,UAAU;oBACZ,IAAI,aAAa;gBACnB,CAAC;YACH;YAEA,qEAAqE;YACrE,mEAAmE;YACnE,wEAAwE;YACxE,OAAQ;gBACN,KAAK;oBACH,OAAO;wBACL,GAAG,WAAW;wBACd,iBAAiB,UAAU;oBAC7B;gBACF,KAAK;oBACH;wBACE,OAAO;4BACL,GAAG,WAAW;4BACd,GAAI,UAAU;gCACZ,iBAAiB;4BACnB,CAAC;wBACH;oBACF;YACJ;YACA,OAAO,CAAC;QACV;oCAAG;QAAC;QAAY;KAAK;IACrB,OAAO,8JAAM,OAAO;2BAAC,IAAM,UAAU;gBACnC;gBACA;gBACA;YACF,IAAI,CAAC;0BAAG;QAAC;QAAS;QAAW;QAAU;KAAK;AAC9C;AAEA,8EAA8E;AAC9E,2BAA2B;AAC3B,MAAM,uBAAuB,CAAA,MAAO,IAAI,OAAO,CAAC,0BAA0B,CAAC,GAAG,MAAQ,CAAC,MAAM,MAAM,EAAE,IAAI,EAAE,WAAW;AACtH,SAAS,qBAAqB,SAAS,EAAE,IAAI;IAC3C,OAAO,OAAO,cAAc,aAAa,UAAU,QAAQ;AAC7D;AACA,SAAS,gBAAgB,IAAI,EAAE,UAAU;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,8JAAM,QAAQ,CAAC;IACjD,IAAI,QAAQ,CAAC,WAAW;QACtB,aAAa;IACf;IACA,8JAAM,SAAS;qCAAC;YACd,IAAI,CAAC,QAAQ,WAAW;gBACtB,MAAM,UAAU;yDAAW,IAAM,aAAa;wDAAQ;gBACtD;iDAAO,IAAM,aAAa;;YAC5B;QACF;oCAAG;QAAC;QAAM;QAAW;KAAW;IAChC,OAAO;AACT;AACA;;;;CAIC,GACD,SAAS,oBAAoB,OAAO,EAAE,KAAK;IACzC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,IAAI,EACJ,UAAU,EACR,QAAQ,EACT,EACF,GAAG;IACJ,MAAM,EACJ,WAAW,GAAG,EACf,GAAG;IACJ,MAAM,mBAAmB,OAAO,aAAa;IAC7C,MAAM,gBAAgB,CAAC,mBAAmB,WAAW,SAAS,KAAK,KAAK;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,8JAAM,QAAQ,CAAC;IAC3C,MAAM,YAAY,gBAAgB,MAAM;IACxC,IAAI,CAAC,aAAa,WAAW,SAAS;QACpC,UAAU;IACZ;IACA,MAAM;QACJ,IAAI,CAAC,UAAU;QACf,IAAI,MAAM;YACR,UAAU;YACV,MAAM,QAAQ,sBAAsB;gBAClC,UAAU;YACZ;YACA,OAAO;gBACL,qBAAqB;YACvB;QACF;QACA,UAAU;IACZ,GAAG;QAAC;QAAM;KAAS;IACnB,OAAO;QACL;QACA;IACF;AACF;AACA;;;;CAIC,GACD,SAAS,oBAAoB,OAAO,EAAE,KAAK;IACzC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,MAAM,EACJ,SAAS,mBAAmB;QAC1B,SAAS;IACX,CAAC,EACD,MAAM,aAAa,EACnB,OAAO,cAAc,EACrB,QAAQ,eAAe,EACvB,WAAW,GAAG,EACf,GAAG;IACJ,MAAM,YAAY,QAAQ,SAAS;IACnC,MAAM,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;IACpC,MAAM,SAAS,8JAAM,OAAO;+CAAC,IAAM,CAAC;gBAClC;gBACA;YACF,CAAC;8CAAG;QAAC;QAAM;KAAU;IACrB,MAAM,mBAAmB,OAAO,aAAa;IAC7C,MAAM,eAAe,CAAC,mBAAmB,WAAW,SAAS,IAAI,KAAK;IACtE,MAAM,gBAAgB,CAAC,mBAAmB,WAAW,SAAS,KAAK,KAAK;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,8JAAM,QAAQ;wCAAC,IAAM,CAAC;gBAChD,GAAG,qBAAqB,iBAAiB,OAAO;gBAChD,GAAG,qBAAqB,kBAAkB,OAAO;YACnD,CAAC;;IACD,MAAM,EACJ,SAAS,EACT,MAAM,EACP,GAAG,oBAAoB,SAAS;QAC/B;IACF;IACA,MAAM,aAAa,aAAa;IAChC,MAAM,UAAU,aAAa;IAC7B,MAAM,WAAW,aAAa;IAC9B,MAAM,YAAY,aAAa;IAC/B,MAAM;QACJ,MAAM,gBAAgB,qBAAqB,WAAW,OAAO,EAAE;QAC/D,MAAM,cAAc,qBAAqB,SAAS,OAAO,EAAE;QAC3D,MAAM,eAAe,qBAAqB,UAAU,OAAO,EAAE;QAC7D,MAAM,aAAa,qBAAqB,QAAQ,OAAO,EAAE,WAAW,OAAO,IAAI,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK;YAC1G,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACT,GAAG,CAAC;QACJ,IAAI,WAAW,WAAW;YACxB,UAAU,CAAA,SAAU,CAAC;oBACnB,oBAAoB,OAAO,kBAAkB;oBAC7C,GAAG,YAAY;oBACf,GAAG,aAAa;gBAClB,CAAC;QACH;QACA,IAAI,WAAW,QAAQ;YACrB,UAAU;gBACR,oBAAoB,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,sBAAsB,IAAI,CAAC;gBAC3E,oBAAoB,eAAe;gBACnC,GAAG,YAAY;gBACf,GAAG,UAAU;YACf;QACF;QACA,IAAI,WAAW,SAAS;YACtB,MAAM,SAAS,eAAe;YAC9B,UAAU;gBACR,oBAAoB,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,sBAAsB,IAAI,CAAC;gBACvE,oBAAoB,gBAAgB;gBACpC,GAAG,YAAY;gBACf,GAAG,MAAM;YACX;QACF;IACF,GAAG;QAAC;QAAe;QAAU;QAAY;QAAS;QAAW;QAAc;QAAQ;KAAO;IAC1F,OAAO;QACL;QACA;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,aAAa,OAAO,EAAE,KAAK;IAClC,IAAI;IACJ,MAAM,EACJ,IAAI,EACJ,OAAO,EACR,GAAG;IACJ,MAAM,EACJ,OAAO,EACP,WAAW,EACX,SAAS,gBAAgB,EACzB,gBAAgB,uBAAuB,EACvC,UAAU,IAAI,EACd,YAAY,IAAI,EAChB,UAAU,GAAG,EACb,aAAa,EAAE,EACf,gBAAgB,IAAI,EACrB,GAAG;IACJ,MAAM,eAAe,8JAAM,MAAM;IACjC,MAAM,YAAY,8JAAM,MAAM,CAAC;IAC/B,MAAM,eAAe,8JAAM,MAAM,CAAC,CAAC,OAAO,iBAAiB,OAAO,gBAAgB,WAAW,KAAK,OAAO,OAAO,CAAC;IACjH,MAAM,gBAAgB,8JAAM,MAAM,CAAC;IACnC,MAAM,UAAU,eAAe;IAC/B,MAAM,iBAAiB,eAAe;IACtC,MAAM,eAAe,aAAa;IAClC,MAAM,gBAAgB,aAAa;IACnC,MAAM;QACJ,IAAI,MAAM;YACR,aAAa,aAAa,OAAO;YACjC,cAAc,OAAO,GAAG;YACxB,UAAU,OAAO,GAAG;QACtB;IACF,GAAG;QAAC;KAAK;IACT,MAAM;QACJ,0DAA0D;QAC1D,IAAI,QAAQ,UAAU,OAAO,KAAK,IAAI;YACpC,IAAI;YACJ,aAAa,OAAO,GAAG,CAAC,QAAQ,iBAAiB,OAAO,gBAAgB,WAAW,KAAK,OAAO,QAAQ,CAAC;QAC1G;IACF,GAAG;QAAC;QAAM;QAAe;KAAY;IACrC,MAAM,kBAAkB;wDAAe,CAAA;YACrC,IAAI,OAAO;gBACT,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,EAAE;oBAC3B,QAAQ,OAAO,CAAC,MAAM,GAAG;oBACzB,eAAe;gBACjB;YACF,OAAO;gBACL,IAAI,QAAQ,OAAO,CAAC,MAAM,EAAE;oBAC1B,QAAQ,OAAO,CAAC,MAAM,GAAG;oBACzB,eAAe;gBACjB;YACF;QACF;;IACA,MAAM,YAAY;kDAAe,CAAA;YAC/B,SAAS,iBAAiB,IAAI,EAAE,WAAW,EAAE,MAAM;gBACjD,MAAM,MAAM,aAAa,OAAO,GAAG,aAAa,OAAO,CAAC,aAAa,UAAU,YAAY,IAAI;+EAAC,CAAA,OAAQ,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,iBAAiB,GAAG,OAAO,CAAC,OAAO,iBAAiB,GAAG,MAAM;;gBACnM,OAAO,MAAM,KAAK,OAAO,CAAC,OAAO,CAAC;YACpC;YACA,MAAM,cAAc,QAAQ,OAAO;YACnC,IAAI,UAAU,OAAO,CAAC,MAAM,GAAG,KAAK,UAAU,OAAO,CAAC,EAAE,KAAK,KAAK;gBAChE,IAAI,iBAAiB,aAAa,aAAa,UAAU,OAAO,MAAM,CAAC,GAAG;oBACxE,gBAAgB;gBAClB,OAAO,IAAI,MAAM,GAAG,KAAK,KAAK;oBAC5B,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;gBACZ;YACF;YACA,IAAI,eAAe,QAAQ,cAAc,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,KACnE,iBAAiB;YACjB,MAAM,GAAG,CAAC,MAAM,KAAK,KACrB,gBAAgB;YAChB,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE;gBAC9C;YACF;YACA,IAAI,QAAQ,MAAM,GAAG,KAAK,KAAK;gBAC7B,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;gBACV,gBAAgB;YAClB;YAEA,sEAAsE;YACtE,8BAA8B;YAC9B,MAAM,oCAAoC,YAAY,KAAK;4FAAC,CAAA;oBAC1D,IAAI,QAAQ;oBACZ,OAAO,OAAO,CAAC,CAAC,SAAS,IAAI,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,OAAO,iBAAiB,EAAE,MAAM,CAAC,CAAC,UAAU,IAAI,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,QAAQ,iBAAiB,EAAE,IAAI;gBAC9J;;YAEA,yEAAyE;YACzE,uBAAuB;YACvB,IAAI,qCAAqC,UAAU,OAAO,KAAK,MAAM,GAAG,EAAE;gBACxE,UAAU,OAAO,GAAG;gBACpB,aAAa,OAAO,GAAG,cAAc,OAAO;YAC9C;YACA,UAAU,OAAO,IAAI,MAAM,GAAG;YAC9B,aAAa,aAAa,OAAO;YACjC,aAAa,OAAO,GAAG;0DAAW;oBAChC,UAAU,OAAO,GAAG;oBACpB,aAAa,OAAO,GAAG,cAAc,OAAO;oBAC5C,gBAAgB;gBAClB;yDAAG;YACH,MAAM,YAAY,aAAa,OAAO;YACtC,MAAM,QAAQ,iBAAiB,aAAa;mBAAI,YAAY,KAAK,CAAC,CAAC,aAAa,CAAC,IAAI;mBAAO,YAAY,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI;aAAG,EAAE,UAAU,OAAO;YAC1J,IAAI,UAAU,CAAC,GAAG;gBAChB,QAAQ;gBACR,cAAc,OAAO,GAAG;YAC1B,OAAO,IAAI,MAAM,GAAG,KAAK,KAAK;gBAC5B,UAAU,OAAO,GAAG;gBACpB,gBAAgB;YAClB;QACF;;IACA,MAAM,YAAY,8JAAM,OAAO;2CAAC,IAAM,CAAC;gBACrC;YACF,CAAC;0CAAG;QAAC;KAAU;IACf,MAAM,WAAW,8JAAM,OAAO;0CAAC;YAC7B,OAAO;gBACL;gBACA,SAAQ,KAAK;oBACX,IAAI,MAAM,GAAG,KAAK,KAAK;wBACrB,gBAAgB;oBAClB;gBACF;YACF;QACF;yCAAG;QAAC;QAAW;KAAgB;IAC/B,OAAO,8JAAM,OAAO;gCAAC,IAAM,UAAU;gBACnC;gBACA;YACF,IAAI,CAAC;+BAAG;QAAC;QAAS;QAAW;KAAS;AACxC;AAEA,SAAS,gCAAgC,KAAK,EAAE,MAAM;IACpD,OAAO;QACL,GAAG,KAAK;QACR,OAAO;YACL,GAAG,MAAM,KAAK;YACd,UAAU;gBACR,GAAG,MAAM,KAAK,CAAC,QAAQ;gBACvB;YACF;QACF;IACF;AACF;AACA;;;;CAIC,GACD,MAAM,QAAQ,CAAA,QAAS,CAAC;QACtB,MAAM;QACN,SAAS;QACT,MAAM,IAAG,KAAK;YACZ,MAAM,EACJ,OAAO,EACP,WAAW,EACX,gBAAgB,EAChB,QAAQ,cAAc,CAAC,EACvB,QAAQ,CAAC,EACT,kBAAkB,CAAC,EACnB,6BAA6B,CAAC,EAC9B,SAAS,EACT,GAAG,uBACJ,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;YACpB,MAAM,EACJ,KAAK,EACL,UAAU,EACR,QAAQ,EACT,EACF,GAAG;YACJ,MAAM,OAAO,QAAQ,OAAO,CAAC,MAAM;YACnC,MAAM,WAAW,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO,KAAK;YAErE,sBAAsB;YACtB,kEAAkE;YAClE,0EAA0E;YAC1E,mEAAmE;YACnE,wEAAwE;YACxE,qCAAqC;YACrC,MAAM,YAAY,SAAS,SAAS,IAAI,SAAS,SAAS;YAC1D,MAAM,qBAAqB,SAAS,SAAS,KAAK;YAClD,MAAM,qBAAqB,SAAS,SAAS,KAAK;YAClD,MAAM,qBAAqB,aAAa;YACxC,wCAA2C;gBACzC,IAAI,CAAC,MAAM,SAAS,CAAC,UAAU,CAAC,WAAW;oBACzC,KAAK,4DAA4D;gBACnE;YACF;YACA,IAAI,CAAC,MAAM;gBACT,OAAO,CAAC;YACV;YACA,MAAM,WAAW;gBACf,GAAG,KAAK;gBACR,GAAI,MAAM,CAAA,GAAA,8MAAA,CAAA,SAAM,AAAD,EAAE,CAAC,KAAK,SAAS,GAAG,SAAS,SAAS,GAAG,MAAM,SAAS,CAAC,MAAM,GAAG,IAAI,KAAK,YAAY,GAAG,IAAI,aAAa,EAAE,CAAC,MAAM;YACrI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,gCAAgC,UAAU,SAAS,YAAY,GAAG,YAAY,SAAS,SAAS,GAAG;YACzI,MAAM,cAAc,MAAM,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBACjD,GAAG,qBAAqB;gBACxB,gBAAgB;YAClB;YACA,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,GAAG,SAAS,GAAG;YACjC,MAAM,QAAQ,SAAS,CAAC,GAAG;YAC3B,MAAM,eAAe,SAAS,YAAY,GAAG,SAAS,YAAY;YAClE,MAAM,UAAU,eAAe,CAAA,IAAK,IAAI,gLAAA,CAAA,QAAK;YAC7C,MAAM,YAAY,QAAQ,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,GAAG,SAAS,YAAY,GAAG,CAAC,sBAAsB,sBAAsB,qBAAqB,YAAY,IAAI,CAAC,IAAI,QAAQ,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,GAAG,SAAS,MAAM;YAC9K,SAAS,KAAK,CAAC,SAAS,GAAG,YAAY;YACvC,SAAS,SAAS,GAAG;YAErB,uEAAuE;YACvE,IAAI,kBAAkB;gBACpB,MAAM,iBAAiB,SAAS,YAAY,GAAG,KAAK,YAAY,GAAG,CAAA,GAAA,gLAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB,QAAQ,OAAO,CAAC,MAAM,IAAI,KAAK,YAAY,GAAG,IAAI,CAAC,8BAA8B,YAAY,MAAM,IAAI,CAAC;gBAChM,CAAA,GAAA,oKAAA,CAAA,YAAkB,AAAD,EAAE,IAAM,iBAAiB;YAC5C;YACA,IAAI,aAAa;gBACf,YAAY,OAAO,GAAG,MAAM,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,gCAAgC;oBACzE,GAAG,QAAQ;oBACX,GAAG;gBACL,GAAG,SAAS,YAAY,GAAG,YAAY,SAAS,SAAS,GAAG;YAC9D;YACA,OAAO;gBACL,GAAG;YACL;QACF;IACF,CAAC;AACD;;;;CAIC,GACD,SAAS,eAAe,OAAO,EAAE,KAAK;IACpC,MAAM,EACJ,IAAI,EACJ,QAAQ,EACT,GAAG;IACJ,MAAM,EACJ,UAAU,IAAI,EACd,WAAW,EACX,SAAS,EACT,UAAU,iBAAiB,EAC5B,GAAG;IACJ,MAAM,WAAW,eAAe;IAChC,MAAM,yBAAyB,8JAAM,MAAM,CAAC;IAC5C,MAAM,mBAAmB,8JAAM,MAAM,CAAC;IACtC,MAAM,qBAAqB,8JAAM,MAAM,CAAC;IACxC,8JAAM,SAAS;oCAAC;YACd,IAAI,CAAC,SAAS;YACd,SAAS,QAAQ,CAAC;gBAChB,IAAI,EAAE,OAAO,IAAI,CAAC,MAAM,YAAY,OAAO,IAAI,MAAM;oBACnD;gBACF;gBACA,MAAM,KAAK,EAAE,MAAM;gBACnB,MAAM,UAAU,YAAY,OAAO,CAAC,GAAG,IAAI,CAAC;gBAC5C,MAAM,aAAa,YAAY,OAAO,CAAC,MAAM,IAAI,CAAC;gBAClD,MAAM,kBAAkB,GAAG,YAAY,GAAG,GAAG,YAAY;gBACzD,MAAM,OAAO,KAAK,IAAI,CAAC,IAAI;gBAC3B,MAAM,SAAS,KAAK,IAAI,QAAQ;gBAChC,IAAI,GAAG,YAAY,IAAI,GAAG,YAAY,EAAE;oBACtC;gBACF;gBACA,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,cAAc,KAAK,GAAG;oBAC/C,EAAE,cAAc;oBAChB,CAAA,GAAA,oKAAA,CAAA,YAAkB,AAAD;4DAAE;4BACjB;oEAAS,CAAA,IAAK,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,kBAAkB;;wBACvD;;gBACF,OAAO,IAAI,WAAW,IAAI,CAAC,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,MAAM;oBAC1C,qEAAqE;oBACrE,iEAAiE;oBACjE,GAAG,SAAS,IAAI;gBAClB;YACF;YACA,MAAM,KAAK,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO,KAAK,SAAS,QAAQ;YAChF,IAAI,QAAQ,IAAI;gBACd,GAAG,gBAAgB,CAAC,SAAS;gBAE7B,qCAAqC;gBACrC;gDAAsB;wBACpB,iBAAiB,OAAO,GAAG,GAAG,SAAS;wBACvC,IAAI,YAAY,OAAO,IAAI,MAAM;4BAC/B,mBAAmB,OAAO,GAAG;gCAC3B,GAAG,YAAY,OAAO;4BACxB;wBACF;oBACF;;gBACA;gDAAO;wBACL,iBAAiB,OAAO,GAAG;wBAC3B,mBAAmB,OAAO,GAAG;wBAC7B,GAAG,mBAAmB,CAAC,SAAS;oBAClC;;YACF;QACF;mCAAG;QAAC;QAAS;QAAM,SAAS,QAAQ;QAAE;QAAa;QAAW;KAAS;IACvE,MAAM,WAAW,8JAAM,OAAO;4CAAC,IAAM,CAAC;gBACpC;oBACE,uBAAuB,OAAO,GAAG;gBACnC;gBACA;oBACE,uBAAuB,OAAO,GAAG;gBACnC;gBACA;oBACE,uBAAuB,OAAO,GAAG;gBACnC;gBACA;oBACE,MAAM,KAAK,CAAC,aAAa,OAAO,KAAK,IAAI,UAAU,OAAO,KAAK,SAAS,QAAQ;oBAChF,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,OAAO,EAAE;wBAClE;oBACF;oBACA,IAAI,iBAAiB,OAAO,KAAK,MAAM;wBACrC,MAAM,aAAa,GAAG,SAAS,GAAG,iBAAiB,OAAO;wBAC1D,IAAI,YAAY,OAAO,CAAC,MAAM,GAAG,CAAC,OAAO,aAAa,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,aAAa,GAAG;4BAC5G,CAAA,GAAA,oKAAA,CAAA,YAAkB,AAAD;oEAAE,IAAM;4EAAS,CAAA,IAAK,IAAI;;;wBAC7C;oBACF;oBAEA,6DAA6D;oBAC7D;4DAAsB;4BACpB,iBAAiB,OAAO,GAAG,GAAG,SAAS;wBACzC;;gBACF;YACF,CAAC;2CAAG;QAAC,SAAS,QAAQ;QAAE;QAAU;QAAa;KAAU;IACzD,OAAO,8JAAM,OAAO;kCAAC,IAAM,UAAU;gBACnC;YACF,IAAI,CAAC;iCAAG;QAAC;QAAS;KAAS;AAC7B;AAEA,SAAS,iBAAiB,KAAK,EAAE,OAAO;IACtC,MAAM,CAAC,GAAG,EAAE,GAAG;IACf,IAAI,WAAW;IACf,MAAM,SAAS,QAAQ,MAAM;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAAI,QAAQ,IAAI,IAAK;QACnD,MAAM,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,EAAE,IAAI;YAAC;YAAG;SAAE;QACrC,MAAM,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,EAAE,IAAI;YAAC;YAAG;SAAE;QACrC,MAAM,YAAY,MAAM,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI;QACjF,IAAI,WAAW;YACb,WAAW,CAAC;QACd;IACF;IACA,OAAO;AACT;AACA,SAAS,SAAS,KAAK,EAAE,IAAI;IAC3B,OAAO,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,GAAG,KAAK,MAAM;AACxH;AACA;;;;CAIC,GACD,SAAS,YAAY,OAAO;IAC1B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,MAAM,EACJ,SAAS,GAAG,EACZ,qBAAqB,KAAK,EAC1B,gBAAgB,IAAI,EACrB,GAAG;IACJ,IAAI;IACJ,IAAI,YAAY;IAChB,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,iBAAiB,YAAY,GAAG;IACpC,SAAS,eAAe,CAAC,EAAE,CAAC;QAC1B,MAAM,cAAc,YAAY,GAAG;QACnC,MAAM,cAAc,cAAc;QAClC,IAAI,UAAU,QAAQ,UAAU,QAAQ,gBAAgB,GAAG;YACzD,QAAQ;YACR,QAAQ;YACR,iBAAiB;YACjB,OAAO;QACT;QACA,MAAM,SAAS,IAAI;QACnB,MAAM,SAAS,IAAI;QACnB,MAAM,WAAW,KAAK,IAAI,CAAC,SAAS,SAAS,SAAS;QACtD,MAAM,QAAQ,WAAW,aAAa,UAAU;QAEhD,QAAQ;QACR,QAAQ;QACR,iBAAiB;QACjB,OAAO;IACT;IACA,MAAM,KAAK,CAAA;QACT,IAAI,EACF,CAAC,EACD,CAAC,EACD,SAAS,EACT,QAAQ,EACR,OAAO,EACP,MAAM,EACN,IAAI,EACL,GAAG;QACJ,OAAO,SAAS,YAAY,KAAK;YAC/B,SAAS;gBACP,aAAa;gBACb;YACF;YACA,aAAa;YACb,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,QAAQ,IAAI,aAAa,QAAQ,KAAK,QAAQ,KAAK,MAAM;gBAC/F;YACF;YACA,MAAM,EACJ,OAAO,EACP,OAAO,EACR,GAAG;YACJ,MAAM,cAAc;gBAAC;gBAAS;aAAQ;YACtC,MAAM,SAAS,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;YACzB,MAAM,UAAU,MAAM,IAAI,KAAK;YAC/B,MAAM,mBAAmB,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ,EAAE;YACrD,MAAM,oBAAoB,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,YAAY,EAAE;YAC1D,MAAM,UAAU,SAAS,YAAY,CAAC,qBAAqB;YAC3D,MAAM,OAAO,SAAS,QAAQ,CAAC,qBAAqB;YACpD,MAAM,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;YACpC,MAAM,uBAAuB,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG;YAC3D,MAAM,wBAAwB,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG;YAC9D,MAAM,sBAAsB,SAAS,aAAa;YAClD,MAAM,kBAAkB,KAAK,KAAK,GAAG,QAAQ,KAAK;YAClD,MAAM,mBAAmB,KAAK,MAAM,GAAG,QAAQ,MAAM;YACrD,MAAM,OAAO,CAAC,kBAAkB,UAAU,IAAI,EAAE,IAAI;YACpD,MAAM,QAAQ,CAAC,kBAAkB,UAAU,IAAI,EAAE,KAAK;YACtD,MAAM,MAAM,CAAC,mBAAmB,UAAU,IAAI,EAAE,GAAG;YACnD,MAAM,SAAS,CAAC,mBAAmB,UAAU,IAAI,EAAE,MAAM;YACzD,IAAI,kBAAkB;gBACpB,YAAY;gBACZ,IAAI,CAAC,SAAS;oBACZ;gBACF;YACF;YACA,IAAI,mBAAmB;gBACrB,YAAY;YACd;YACA,IAAI,qBAAqB,CAAC,SAAS;gBACjC,YAAY;gBACZ;YACF;YAEA,yEAAyE;YACzE,+DAA+D;YAC/D,IAAI,WAAW,CAAA,GAAA,uLAAA,CAAA,YAAS,AAAD,EAAE,MAAM,aAAa,KAAK,CAAA,GAAA,yLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ,EAAE,MAAM,aAAa,GAAG;gBACjG;YACF;YAEA,sCAAsC;YACtC,IAAI,QAAQ,YAAY,KAAK,QAAQ,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAA;gBAC1D,IAAI,EACF,OAAO,EACR,GAAG;gBACJ,OAAO,WAAW,OAAO,KAAK,IAAI,QAAQ,IAAI;YAChD,IAAI;gBACF;YACF;YAEA,uEAAuE;YACvE,yEAAyE;YACzE,WAAW;YACX,0DAA0D;YAC1D,IAAI,SAAS,SAAS,KAAK,QAAQ,MAAM,GAAG,KAAK,SAAS,YAAY,KAAK,QAAQ,GAAG,GAAG,KAAK,SAAS,UAAU,KAAK,QAAQ,KAAK,GAAG,KAAK,SAAS,WAAW,KAAK,QAAQ,IAAI,GAAG,GAAG;gBACpL,OAAO;YACT;YAEA,sEAAsE;YACtE,qEAAqE;YACrE,qEAAqE;YACrE,yEAAyE;YACzE,+CAA+C;YAC/C,IAAI,WAAW,EAAE;YACjB,OAAQ;gBACN,KAAK;oBACH,WAAW;wBAAC;4BAAC;4BAAM,QAAQ,GAAG,GAAG;yBAAE;wBAAE;4BAAC;4BAAM,KAAK,MAAM,GAAG;yBAAE;wBAAE;4BAAC;4BAAO,KAAK,MAAM,GAAG;yBAAE;wBAAE;4BAAC;4BAAO,QAAQ,GAAG,GAAG;yBAAE;qBAAC;oBACjH;gBACF,KAAK;oBACH,WAAW;wBAAC;4BAAC;4BAAM,KAAK,GAAG,GAAG;yBAAE;wBAAE;4BAAC;4BAAM,QAAQ,MAAM,GAAG;yBAAE;wBAAE;4BAAC;4BAAO,QAAQ,MAAM,GAAG;yBAAE;wBAAE;4BAAC;4BAAO,KAAK,GAAG,GAAG;yBAAE;qBAAC;oBACjH;gBACF,KAAK;oBACH,WAAW;wBAAC;4BAAC,KAAK,KAAK,GAAG;4BAAG;yBAAO;wBAAE;4BAAC,KAAK,KAAK,GAAG;4BAAG;yBAAI;wBAAE;4BAAC,QAAQ,IAAI,GAAG;4BAAG;yBAAI;wBAAE;4BAAC,QAAQ,IAAI,GAAG;4BAAG;yBAAO;qBAAC;oBACjH;gBACF,KAAK;oBACH,WAAW;wBAAC;4BAAC,QAAQ,KAAK,GAAG;4BAAG;yBAAO;wBAAE;4BAAC,QAAQ,KAAK,GAAG;4BAAG;yBAAI;wBAAE;4BAAC,KAAK,IAAI,GAAG;4BAAG;yBAAI;wBAAE;4BAAC,KAAK,IAAI,GAAG;4BAAG;yBAAO;qBAAC;oBACjH;YACJ;YACA,SAAS,WAAW,KAAK;gBACvB,IAAI,CAAC,GAAG,EAAE,GAAG;gBACb,OAAQ;oBACN,KAAK;wBACH;4BACE,MAAM,iBAAiB;gCAAC,kBAAkB,IAAI,SAAS,IAAI,uBAAuB,IAAI,SAAS,IAAI,IAAI,SAAS;gCAAG,IAAI,SAAS;6BAAE;4BAClI,MAAM,iBAAiB;gCAAC,kBAAkB,IAAI,SAAS,IAAI,uBAAuB,IAAI,SAAS,IAAI,IAAI,SAAS;gCAAG,IAAI,SAAS;6BAAE;4BAClI,MAAM,eAAe;gCAAC;oCAAC,KAAK,IAAI;oCAAE,uBAAuB,KAAK,MAAM,GAAG,SAAS,kBAAkB,KAAK,MAAM,GAAG,SAAS,KAAK,GAAG;iCAAC;gCAAE;oCAAC,KAAK,KAAK;oCAAE,uBAAuB,kBAAkB,KAAK,MAAM,GAAG,SAAS,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;iCAAO;6BAAC;4BAClP,OAAO;gCAAC;gCAAgB;mCAAmB;6BAAa;wBAC1D;oBACF,KAAK;wBACH;4BACE,MAAM,iBAAiB;gCAAC,kBAAkB,IAAI,SAAS,IAAI,uBAAuB,IAAI,SAAS,IAAI,IAAI,SAAS;gCAAG,IAAI;6BAAO;4BAC9H,MAAM,iBAAiB;gCAAC,kBAAkB,IAAI,SAAS,IAAI,uBAAuB,IAAI,SAAS,IAAI,IAAI,SAAS;gCAAG,IAAI;6BAAO;4BAC9H,MAAM,eAAe;gCAAC;oCAAC,KAAK,IAAI;oCAAE,uBAAuB,KAAK,GAAG,GAAG,SAAS,kBAAkB,KAAK,GAAG,GAAG,SAAS,KAAK,MAAM;iCAAC;gCAAE;oCAAC,KAAK,KAAK;oCAAE,uBAAuB,kBAAkB,KAAK,GAAG,GAAG,SAAS,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG;iCAAO;6BAAC;4BAC5O,OAAO;gCAAC;gCAAgB;mCAAmB;6BAAa;wBAC1D;oBACF,KAAK;wBACH;4BACE,MAAM,iBAAiB;gCAAC,IAAI,SAAS;gCAAG,mBAAmB,IAAI,SAAS,IAAI,wBAAwB,IAAI,SAAS,IAAI,IAAI,SAAS;6BAAE;4BACpI,MAAM,iBAAiB;gCAAC,IAAI,SAAS;gCAAG,mBAAmB,IAAI,SAAS,IAAI,wBAAwB,IAAI,SAAS,IAAI,IAAI,SAAS;6BAAE;4BACpI,MAAM,eAAe;gCAAC;oCAAC,wBAAwB,KAAK,KAAK,GAAG,SAAS,mBAAmB,KAAK,KAAK,GAAG,SAAS,KAAK,IAAI;oCAAE,KAAK,GAAG;iCAAC;gCAAE;oCAAC,wBAAwB,mBAAmB,KAAK,KAAK,GAAG,SAAS,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;oCAAQ,KAAK,MAAM;iCAAC;6BAAC;4BACpP,OAAO;mCAAI;gCAAc;gCAAgB;6BAAe;wBAC1D;oBACF,KAAK;wBACH;4BACE,MAAM,iBAAiB;gCAAC,IAAI;gCAAQ,mBAAmB,IAAI,SAAS,IAAI,wBAAwB,IAAI,SAAS,IAAI,IAAI,SAAS;6BAAE;4BAChI,MAAM,iBAAiB;gCAAC,IAAI;gCAAQ,mBAAmB,IAAI,SAAS,IAAI,wBAAwB,IAAI,SAAS,IAAI,IAAI,SAAS;6BAAE;4BAChI,MAAM,eAAe;gCAAC;oCAAC,wBAAwB,KAAK,IAAI,GAAG,SAAS,mBAAmB,KAAK,IAAI,GAAG,SAAS,KAAK,KAAK;oCAAE,KAAK,GAAG;iCAAC;gCAAE;oCAAC,wBAAwB,mBAAmB,KAAK,IAAI,GAAG,SAAS,KAAK,KAAK,GAAG,KAAK,IAAI,GAAG;oCAAQ,KAAK,MAAM;iCAAC;6BAAC;4BAClP,OAAO;gCAAC;gCAAgB;mCAAmB;6BAAa;wBAC1D;gBACJ;YACF;YACA,IAAI,iBAAiB;gBAAC;gBAAS;aAAQ,EAAE,WAAW;gBAClD;YACF;YACA,IAAI,aAAa,CAAC,qBAAqB;gBACrC,OAAO;YACT;YACA,IAAI,CAAC,WAAW,eAAe;gBAC7B,MAAM,cAAc,eAAe,MAAM,OAAO,EAAE,MAAM,OAAO;gBAC/D,MAAM,uBAAuB;gBAC7B,IAAI,gBAAgB,QAAQ,cAAc,sBAAsB;oBAC9D,OAAO;gBACT;YACF;YACA,IAAI,CAAC,iBAAiB;gBAAC;gBAAS;aAAQ,EAAE,WAAW;gBAAC;gBAAG;aAAE,IAAI;gBAC7D;YACF,OAAO,IAAI,CAAC,aAAa,eAAe;gBACtC,YAAY,OAAO,UAAU,CAAC,OAAO;YACvC;QACF;IACF;IACA,GAAG,SAAS,GAAG;QACb;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}]}