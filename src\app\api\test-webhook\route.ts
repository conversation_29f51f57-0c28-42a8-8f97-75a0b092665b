import { NextRequest, NextResponse } from 'next/server';

// Simple test endpoint to verify webhook functionality
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const verifyToken = process.env.FACEBOOK_VERIFY_TOKEN;
    
    // Test webhook verification
    const mode = searchParams.get('hub.mode');
    const token = searchParams.get('hub.verify_token');
    const challenge = searchParams.get('hub.challenge');

    if (mode === 'subscribe' && token === verifyToken) {
      return new NextResponse(challenge);
    }

    // Return test information
    return NextResponse.json({
      webhook_url: `${request.nextUrl.origin}/api/webhook/facebook`,
      verify_token_configured: !!verifyToken,
      page_token_configured: !!process.env.FACEBOOK_PAGE_ACCESS_TOKEN,
      test_verification_url: `${request.nextUrl.origin}/api/webhook/facebook?hub.mode=subscribe&hub.verify_token=${verifyToken}&hub.challenge=test_challenge`,
      instructions: {
        step1: 'Configure FACEBOOK_VERIFY_TOKEN and FACEBOOK_PAGE_ACCESS_TOKEN in .env.local',
        step2: 'Use ngrok to expose localhost: ngrok http 3000',
        step3: 'Set webhook URL in Facebook App to: https://your-ngrok-url.ngrok.io/api/webhook/facebook',
        step4: 'Test verification by visiting the test_verification_url above',
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Test webhook failed', details: error },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    return NextResponse.json({
      message: 'Test webhook received POST request',
      received_data: body,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Test webhook POST failed', details: error },
      { status: 500 }
    );
  }
}
