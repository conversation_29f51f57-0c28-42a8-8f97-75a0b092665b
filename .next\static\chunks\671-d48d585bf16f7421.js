"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[671],{6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(12115);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=a(e,n)),t&&(o.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},useLinkStatus:function(){return v}});let n=r(6966),o=r(95155),a=n._(r(12115)),l=r(82757),i=r(95227),u=r(69818),s=r(6654),c=r(69991),f=r(85929);r(43230);let p=r(24930),d=r(92664),h=r(6634);function g(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function y(e){let t,r,n,[l,y]=(0,a.useOptimistic)(p.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:b,as:P,children:k,prefetch:w=null,passHref:x,replace:S,shallow:_,scroll:j,onClick:E,onMouseEnter:C,onTouchStart:M,legacyBehavior:O=!1,onNavigate:A,ref:N,unstable_dynamicOnHover:T,...R}=e;t=k,O&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let L=a.default.useContext(i.AppRouterContext),I=!1!==w,U=null===w?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:z,as:B}=a.default.useMemo(()=>{let e=g(b);return{href:e,as:P?g(P):e}},[b,P]);O&&(r=a.default.Children.only(t));let D=O?r&&"object"==typeof r&&r.ref:N,F=a.default.useCallback(e=>(null!==L&&(v.current=(0,p.mountLinkInstance)(e,z,L,U,I,y)),()=>{v.current&&((0,p.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,p.unmountPrefetchableInstance)(e)}),[I,z,L,U,y]),K={ref:(0,s.useMergedRef)(F,D),onClick(e){O||"function"!=typeof E||E(e),O&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,o,l,i){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(r||t,o?"replace":"push",null==l||l,n.current)})}}(e,z,B,v,S,j,A))},onMouseEnter(e){O||"function"!=typeof C||C(e),O&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),L&&I&&(0,p.onNavigationIntent)(e.currentTarget,!0===T)},onTouchStart:function(e){O||"function"!=typeof M||M(e),O&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),L&&I&&(0,p.onNavigationIntent)(e.currentTarget,!0===T)}};return(0,c.isAbsoluteUrl)(B)?K.href=B:O&&!x&&("a"!==r.type||"href"in r.props)||(K.href=(0,f.addBasePath)(B)),n=O?a.default.cloneElement(r,K):(0,o.jsx)("a",{...R,...K,children:t}),(0,o.jsx)(m.Provider,{value:l,children:n})}r(73180);let m=(0,a.createContext)(p.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(m);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13420:(e,t,r)=>{r.d(t,{A:()=>n});var n=(0,r(86467).A)("outline","login","IconLogin",[["path",{d:"M15 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M21 12h-13l3 -3",key:"svg-1"}],["path",{d:"M11 15l-3 -3",key:"svg-2"}]])},15011:(e,t,r)=>{r.d(t,{A:()=>n});var n=(0,r(86467).A)("outline","package","IconPackage",[["path",{d:"M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5",key:"svg-0"}],["path",{d:"M12 12l8 -4.5",key:"svg-1"}],["path",{d:"M12 12l0 9",key:"svg-2"}],["path",{d:"M12 12l-8 -4.5",key:"svg-3"}],["path",{d:"M16 5.25l-8 4.5",key:"svg-4"}]])},21220:(e,t,r)=>{r.d(t,{Stack:()=>d});var n=r(95155);r(12115);var o=r(56204),a=r(68918),l=r(43664),i=r(53791),u=r(69604),s=r(36960),c={root:"m_6d731127"};let f={gap:"md",align:"stretch",justify:"flex-start"},p=(0,a.createVarsResolver)((e,t)=>{let{gap:r,align:n,justify:a}=t;return{root:{"--stack-gap":(0,o.getSpacing)(r),"--stack-align":n,"--stack-justify":a}}}),d=(0,s.factory)((e,t)=>{let r=(0,l.useProps)("Stack",f,e),{classNames:o,className:a,style:s,styles:d,unstyled:h,vars:g,align:y,justify:m,gap:v,variant:b,...P}=r,k=(0,i.useStyles)({name:"Stack",props:r,classes:c,className:a,style:s,classNames:o,styles:d,unstyled:h,vars:g,varsResolver:p});return(0,n.jsx)(u.Box,{ref:t,...k("root"),variant:b,...P})});d.classes=c,d.displayName="@mantine/core/Stack"},26743:(e,t,r)=>{r.d(t,{A:()=>n});var n=(0,r(86467).A)("outline","messages","IconMessages",[["path",{d:"M21 14l-3 -3h-7a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1h9a1 1 0 0 1 1 1v10",key:"svg-0"}],["path",{d:"M14 15v2a1 1 0 0 1 -1 1h-7l-3 3v-10a1 1 0 0 1 1 -1h2",key:"svg-1"}]])},28031:(e,t,r)=>{r.d(t,{A:()=>n});var n=(0,r(86467).A)("outline","dashboard","IconDashboard",[["path",{d:"M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M13.45 11.55l2.05 -2.05",key:"svg-1"}],["path",{d:"M6.4 20a9 9 0 1 1 11.2 0z",key:"svg-2"}]])},56970:(e,t,r)=>{r.d(t,{createSafeContext:()=>a});var n=r(95155),o=r(12115);function a(e){let t=(0,o.createContext)(null);return[e=>{let{children:r,value:o}=e;return(0,n.jsx)(t.Provider,{value:o,children:r})},()=>{let r=(0,o.useContext)(t);if(null===r)throw Error(e);return r}]}},69991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return m},NormalizeError:function(){return g},PageNotFoundError:function(){return y},SP:function(){return p},ST:function(){return d},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return l},getURL:function(){return i},isAbsoluteUrl:function(){return a},isResSent:function(){return s},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=l();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&s(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,d=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class m extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},70112:(e,t,r)=>{r.d(t,{Group:()=>h});var n=r(95155),o=r(12115),a=r(56204),l=r(68918),i=r(43664),u=r(53791),s=r(69604),c=r(36960),f={root:"m_4081bf90"};let p={preventGrowOverflow:!0,gap:"md",align:"center",justify:"flex-start",wrap:"wrap"},d=(0,l.createVarsResolver)((e,t,r)=>{let{grow:n,preventGrowOverflow:o,gap:l,align:i,justify:u,wrap:s}=t,{childWidth:c}=r;return{root:{"--group-child-width":n&&o?c:void 0,"--group-gap":(0,a.getSpacing)(l),"--group-align":i,"--group-justify":u,"--group-wrap":s}}}),h=(0,c.factory)((e,t)=>{let r=(0,i.useProps)("Group",p,e),{classNames:l,className:c,style:h,styles:g,unstyled:y,children:m,gap:v,align:b,justify:P,wrap:k,grow:w,preventGrowOverflow:x,vars:S,variant:_,__size:j,mod:E,...C}=r,M=o.Children.toArray(m).filter(Boolean),O=M.length,A=(0,a.getSpacing)(null!=v?v:"md"),N="calc(".concat(100/O,"% - (").concat(A," - ").concat(A," / ").concat(O,"))"),T=(0,u.useStyles)({name:"Group",props:r,stylesCtx:{childWidth:N},className:c,style:h,classes:f,classNames:l,styles:g,unstyled:y,vars:S,varsResolver:d});return(0,n.jsx)(s.Box,{...T("root"),ref:t,variant:_,mod:[{grow:w},E],size:j,...C,children:M})});h.classes=f,h.displayName="@mantine/core/Group"},73180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},78859:(e,t)=>{function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},82757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return i},urlObjectKeys:function(){return l}});let n=r(6966)._(r(78859)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",l=e.pathname||"",i=e.hash||"",u=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==s?(s="//"+(s||""),l&&"/"!==l[0]&&(l="/"+l)):s||(s=""),i&&"#"!==i[0]&&(i="#"+i),c&&"?"!==c[0]&&(c="?"+c),""+a+s+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+i}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return a(e)}},86467:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(12115),o={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let a=(e,t,r,a)=>{let l=(0,n.forwardRef)((r,l)=>{let{color:i="currentColor",size:u=24,stroke:s=2,title:c,className:f,children:p,...d}=r;return(0,n.createElement)("svg",{ref:l,...o[e],width:u,height:u,className:["tabler-icon","tabler-icon-".concat(t),f].join(" "),..."filled"===e?{fill:i}:{strokeWidth:s,stroke:i},...d},[c&&(0,n.createElement)("title",{key:"svg-title"},c),...a.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(p)?p:[p]])});return l.displayName="".concat(r),l}},91590:(e,t,r)=>{r.d(t,{Container:()=>d});var n=r(95155);r(12115);var o=r(56204),a=r(68918),l=r(43664),i=r(53791),u=r(69604),s=r(36960),c={root:"m_7485cace"};let f={},p=(0,a.createVarsResolver)((e,t)=>{let{size:r,fluid:n}=t;return{root:{"--container-size":n?void 0:(0,o.getSize)(r,"container-size")}}}),d=(0,s.factory)((e,t)=>{let r=(0,l.useProps)("Container",f,e),{classNames:o,className:a,style:s,styles:d,unstyled:h,vars:g,fluid:y,mod:m,...v}=r,b=(0,i.useStyles)({name:"Container",classes:c,props:r,className:a,style:s,classNames:o,styles:d,unstyled:h,vars:g,varsResolver:p});return(0,n.jsx)(u.Box,{ref:t,mod:[{fluid:y},m],...b("root"),...v})});d.classes=c,d.displayName="@mantine/core/Container"},92664:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(69991),o=r(87102);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},93751:(e,t,r)=>{r.d(t,{Title:()=>g});var n=r(95155);r(12115);var o=r(68918),a=r(43664),l=r(53791),i=r(69604),u=r(36960),s=r(5903);let c=["h1","h2","h3","h4","h5","h6"],f=["xs","sm","md","lg","xl"];var p={root:"m_8a5d1357"};let d={order:1},h=(0,o.createVarsResolver)((e,t)=>{let{order:r,size:n,lineClamp:o,textWrap:a}=t,l=function(e,t){let r=void 0!==t?t:"h".concat(e);return c.includes(r)?{fontSize:"var(--mantine-".concat(r,"-font-size)"),fontWeight:"var(--mantine-".concat(r,"-font-weight)"),lineHeight:"var(--mantine-".concat(r,"-line-height)")}:f.includes(r)?{fontSize:"var(--mantine-font-size-".concat(r,")"),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}:{fontSize:(0,s.D)(r),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}}(r||1,n);return{root:{"--title-fw":l.fontWeight,"--title-lh":l.lineHeight,"--title-fz":l.fontSize,"--title-line-clamp":"number"==typeof o?o.toString():void 0,"--title-text-wrap":a}}}),g=(0,u.factory)((e,t)=>{let r=(0,a.useProps)("Title",d,e),{classNames:o,className:u,style:s,styles:c,unstyled:f,order:g,vars:y,size:m,variant:v,lineClamp:b,textWrap:P,mod:k,...w}=r,x=(0,l.useStyles)({name:"Title",props:r,classes:p,className:u,style:s,classNames:o,styles:c,unstyled:f,vars:y,varsResolver:h});return[1,2,3,4,5,6].includes(g)?(0,n.jsx)(i.Box,{...x("root"),component:"h".concat(g),variant:v,ref:t,mod:[{order:g,"data-line-clamp":"number"==typeof b},k],size:m,...w}):null});g.classes=p,g.displayName="@mantine/core/Title"},97287:(e,t,r)=>{r.d(t,{Paper:()=>d});var n=r(95155);r(12115);var o=r(56204),a=r(68918),l=r(43664),i=r(53791),u=r(69604),s=r(64511),c={root:"m_1b7284a3"};let f={},p=(0,a.createVarsResolver)((e,t)=>{let{radius:r,shadow:n}=t;return{root:{"--paper-radius":void 0===r?void 0:(0,o.getRadius)(r),"--paper-shadow":(0,o.getShadow)(n)}}}),d=(0,s.polymorphicFactory)((e,t)=>{let r=(0,l.useProps)("Paper",f,e),{classNames:o,className:a,style:s,styles:d,unstyled:h,withBorder:g,vars:y,radius:m,shadow:v,variant:b,mod:P,...k}=r,w=(0,i.useStyles)({name:"Paper",props:r,classes:c,className:a,style:s,classNames:o,styles:d,unstyled:h,vars:y,varsResolver:p});return(0,n.jsx)(u.Box,{ref:t,mod:[{"data-with-border":g},P],...w("root"),variant:b,...k})});d.classes=c,d.displayName="@mantine/core/Paper"}}]);