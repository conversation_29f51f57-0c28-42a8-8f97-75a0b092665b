(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{22080:(e,i,s)=>{Promise.resolve().then(s.bind(s,33792))},33792:(e,i,s)=>{"use strict";s.r(i),s.d(i,{default:()=>b});var r=s(95155),n=s(91590),d=s(21220),t=s(93751),a=s(58887),c=s(89047),l=s(18403),o=s(70112),h=s(26903),x=s(15011),m=s(26743),j=s(28031),u=s(13420),g=s(6874),p=s.n(g);function b(){return(0,r.jsx)(n.Container,{size:"lg",py:"xl",children:(0,r.jsxs)(d<PERSON>,{align:"center",gap:"xl",children:[(0,r.jsxs)("div",{style:{textAlign:"center"},children:[(0,r.jsx)(t.Title,{order:1,size:"3rem",mb:"md",children:"Product Management System"}),(0,r.jsx)(a.Text,{size:"xl",c:"dimmed",mb:"xl",children:"Full-stack Next.js application with Mantine UI, Supabase, and Facebook Messenger integration"})]}),(0,r.jsxs)(c.Grid,{children:[(0,r.jsx)(c.Grid.Col,{span:{base:12,md:6},children:(0,r.jsx)(l.Card,{withBorder:!0,h:"100%",children:(0,r.jsxs)(d.Stack,{children:[(0,r.jsx)(x.A,{size:"2rem",color:"blue"}),(0,r.jsx)(t.Title,{order:3,children:"Product Management"}),(0,r.jsx)(a.Text,{c:"dimmed",children:"Manage your product catalog with full CRUD operations, search functionality, and detailed specifications."})]})})}),(0,r.jsx)(c.Grid.Col,{span:{base:12,md:6},children:(0,r.jsx)(l.Card,{withBorder:!0,h:"100%",children:(0,r.jsxs)(d.Stack,{children:[(0,r.jsx)(m.A,{size:"2rem",color:"green"}),(0,r.jsx)(t.Title,{order:3,children:"Message Handling"}),(0,r.jsx)(a.Text,{c:"dimmed",children:"Handle customer messages from Facebook Messenger with automated responses and manual override capabilities."})]})})}),(0,r.jsx)(c.Grid.Col,{span:{base:12,md:6},children:(0,r.jsx)(l.Card,{withBorder:!0,h:"100%",children:(0,r.jsxs)(d.Stack,{children:[(0,r.jsx)(j.A,{size:"2rem",color:"orange"}),(0,r.jsx)(t.Title,{order:3,children:"Admin Dashboard"}),(0,r.jsx)(a.Text,{c:"dimmed",children:"Comprehensive admin interface for managing products, viewing messages, and configuring system settings."})]})})}),(0,r.jsx)(c.Grid.Col,{span:{base:12,md:6},children:(0,r.jsx)(l.Card,{withBorder:!0,h:"100%",children:(0,r.jsxs)(d.Stack,{children:[(0,r.jsx)(u.A,{size:"2rem",color:"purple"}),(0,r.jsx)(t.Title,{order:3,children:"Secure Authentication"}),(0,r.jsx)(a.Text,{c:"dimmed",children:"Session-based authentication with JWT tokens and protected admin routes for secure access."})]})})})]}),(0,r.jsxs)(o.Group,{children:[(0,r.jsx)(h.Button,{component:p(),href:"/admin",size:"lg",leftSection:(0,r.jsx)(j.A,{size:"1.2rem"}),children:"Go to Admin Dashboard"}),(0,r.jsx)(h.Button,{component:p(),href:"/login",variant:"outline",size:"lg",leftSection:(0,r.jsx)(u.A,{size:"1.2rem"}),children:"Admin Login"})]}),(0,r.jsx)(l.Card,{withBorder:!0,bg:"gray.0",mt:"xl",children:(0,r.jsxs)(d.Stack,{children:[(0,r.jsx)(t.Title,{order:4,children:"Getting Started"}),(0,r.jsx)(a.Text,{size:"sm",children:"1. Set up your Supabase database using the provided schema file"}),(0,r.jsx)(a.Text,{size:"sm",children:"2. Configure your environment variables in .env.local"}),(0,r.jsx)(a.Text,{size:"sm",children:"3. Login with default credentials: admin / admin123"}),(0,r.jsx)(a.Text,{size:"sm",children:"4. Start managing your products and customer messages!"})]})})]})})}}},e=>{var i=i=>e(e.s=i);e.O(0,[865,903,849,671,441,684,358],()=>i(22080)),_N_E=e.O()}]);