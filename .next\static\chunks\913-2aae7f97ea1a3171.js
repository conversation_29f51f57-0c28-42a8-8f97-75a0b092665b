"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[913],{6942:(e,t,n)=>{n.d(t,{cleanNotifications:()=>f,cleanNotificationsQueue:()=>p,createNotificationsStore:()=>a,hideNotification:()=>s,notifications:()=>h,notificationsStore:()=>i,showNotification:()=>l,updateNotification:()=>d,updateNotificationsState:()=>u,useNotifications:()=>c});var r=n(74275),o=n(12115);let a=()=>(function(e){let t=e,n=!1,r=new Set;return{getState:()=>t,updateState(e){t="function"==typeof e?e(t):e},setState(e){this.updateState(e),r.forEach(e=>e(t))},initialize(e){n||(t=e,n=!0)},subscribe:e=>(r.add(e),()=>r.delete(e))}})({notifications:[],queue:[],defaultPosition:"bottom-right",limit:5}),i=a(),c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i;return(0,o.useSyncExternalStore)(e.subscribe,()=>e.getState(),()=>e.getState())};function u(e,t){let n=e.getState(),r=function(e,t,n){let r=[],o=[],a={};for(let i of e){let e=i.position||t;a[e]=a[e]||0,a[e]+=1,a[e]<=n?o.push(i):r.push(i)}return{notifications:o,queue:r}}(t([...n.notifications,...n.queue]),n.defaultPosition,n.limit);e.setState({notifications:r.notifications,queue:r.queue,limit:n.limit,defaultPosition:n.defaultPosition})}function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i,n=e.id||(0,r.randomId)();return u(t,t=>e.id&&t.some(t=>t.id===e.id)?t:[...t,{...e,id:n}]),n}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return u(t,t=>t.filter(t=>{if(t.id===e){var n;return null==(n=t.onClose)||n.call(t,t),!1}return!0})),e}function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;return u(t,t=>t.map(t=>t.id===e.id?{...t,...e}:t)),e.id}function f(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i;u(e,()=>[])}function p(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i;u(e,t=>t.slice(0,e.getState().limit))}let h={show:l,hide:s,update:d,clean:f,cleanQueue:p,updateState:u}},8141:(e,t,n)=>{n.d(t,{Center:()=>s});var r=n(95155);n(12115);var o=n(43664),a=n(53791),i=n(69604),c=n(64511),u={root:"m_4451eb3a"};let l={},s=(0,c.polymorphicFactory)((e,t)=>{let n=(0,o.useProps)("Center",l,e),{classNames:c,className:s,style:d,styles:f,unstyled:p,vars:h,inline:v,mod:m,...g}=n,y=(0,a.useStyles)({name:"Center",props:n,classes:u,className:s,style:d,classNames:c,styles:f,unstyled:p,vars:h});return(0,r.jsx)(i.Box,{ref:t,mod:[{inline:v},m],...y("root"),...g})});s.classes=u,s.displayName="@mantine/core/Center"},28261:(e,t,n)=>{n.d(t,{useWindowEvent:()=>o});var r=n(12115);function o(e,t,n){(0,r.useEffect)(()=>(window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)),[e,t])}},56970:(e,t,n)=>{n.d(t,{createSafeContext:()=>a});var r=n(95155),o=n(12115);function a(e){let t=(0,o.createContext)(null);return[e=>{let{children:n,value:o}=e;return(0,r.jsx)(t.Provider,{value:o,children:n})},()=>{let n=(0,o.useContext)(t);if(null===n)throw Error(e);return n}]}},58750:(e,t,n)=>{n.d(t,{getDefaultZIndex:()=>o});let r={app:100,modal:200,popover:300,overlay:400,max:9999};function o(e){return r[e]}},70112:(e,t,n)=>{n.d(t,{Group:()=>h});var r=n(95155),o=n(12115),a=n(56204),i=n(68918),c=n(43664),u=n(53791),l=n(69604),s=n(36960),d={root:"m_4081bf90"};let f={preventGrowOverflow:!0,gap:"md",align:"center",justify:"flex-start",wrap:"wrap"},p=(0,i.createVarsResolver)((e,t,n)=>{let{grow:r,preventGrowOverflow:o,gap:i,align:c,justify:u,wrap:l}=t,{childWidth:s}=n;return{root:{"--group-child-width":r&&o?s:void 0,"--group-gap":(0,a.getSpacing)(i),"--group-align":c,"--group-justify":u,"--group-wrap":l}}}),h=(0,s.factory)((e,t)=>{let n=(0,c.useProps)("Group",f,e),{classNames:i,className:s,style:h,styles:v,unstyled:m,children:g,gap:y,align:w,justify:b,wrap:E,grow:S,preventGrowOverflow:C,vars:N,variant:R,__size:x,mod:k,...M}=n,P=o.Children.toArray(g).filter(Boolean),L=P.length,O=(0,a.getSpacing)(null!=y?y:"md"),T="calc(".concat(100/L,"% - (").concat(O," - ").concat(O," / ").concat(L,"))"),j=(0,u.useStyles)({name:"Group",props:n,stylesCtx:{childWidth:T},className:s,style:h,classes:d,classNames:i,styles:v,unstyled:m,vars:N,varsResolver:p});return(0,r.jsx)(l.Box,{...j("root"),ref:t,variant:R,mod:[{grow:S},k],size:x,...M,children:P})});h.classes=d,h.displayName="@mantine/core/Group"},73141:(e,t,n)=>{n.d(t,{useIsomorphicEffect:()=>o});var r=n(12115);let o="undefined"!=typeof document?r.useLayoutEffect:r.useEffect},74275:(e,t,n)=>{n.d(t,{randomId:()=>r});function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mantine-";return"".concat(e).concat(Math.random().toString(36).slice(2,11))}},88551:(e,t,n)=>{n.d(t,{assignRef:()=>o,mergeRefs:()=>a,useMergedRef:()=>i});var r=n(12115);function o(e,t){if("function"==typeof e)return e(t);"object"==typeof e&&null!==e&&"current"in e&&(e.current=t)}function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=new Map;return e=>{if(t.forEach(t=>{let n=o(t,e);n&&r.set(t,n)}),r.size>0)return()=>{t.forEach(e=>{let t=r.get(e);t?t():o(e,null)}),r.clear()}}}function i(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useCallback)(a(...t),t)}},93795:(e,t,n)=>{n.d(t,{A:()=>q});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var c=("function"==typeof SuppressedError&&SuppressedError,n(12115)),u="right-scroll-bar-position",l="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?c.useLayoutEffect:c.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=a({async:!0,ssr:!1},e),i}(),v=function(){},m=c.forwardRef(function(e,t){var n,r,o,u,l=c.useRef(null),p=c.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),m=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,E=e.removeScrollBar,S=e.enabled,C=e.shards,N=e.sideCar,R=e.noRelative,x=e.noIsolation,k=e.inert,M=e.allowPinchZoom,P=e.as,L=e.gapMode,O=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(n=[l,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,c.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),j=a(a({},O),m);return c.createElement(c.Fragment,null,S&&c.createElement(N,{sideCar:h,removeScrollBar:E,shards:C,noRelative:R,noIsolation:x,inert:k,setCallbacks:g,allowPinchZoom:!!M,lockRef:l,gapMode:L}),y?c.cloneElement(c.Children.only(w),a(a({},j),{ref:T})):c.createElement(void 0===P?"div":P,a({},j,{className:b,ref:T}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:l,zeroRight:u};var g=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return c.createElement(r,a({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){c.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},E={left:0,top:0,right:0,gap:0},S=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[S(n),S(r),S(o)]},N=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return E;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=b(),x="data-scroll-locked",k=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(x,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(x,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(x)||"0",10);return isFinite(e)?e:0},P=function(){c.useEffect(function(){return document.body.setAttribute(x,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(x):document.body.setAttribute(x,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;P();var a=c.useMemo(function(){return N(o)},[o]);return c.createElement(R,{styles:k(a,!t,o,n?"":"!important")})},O=!1;if("undefined"!=typeof window)try{var T=Object.defineProperty({},"passive",{get:function(){return O=!0,!0}});window.addEventListener("test",T,T),window.removeEventListener("test",T,T)}catch(e){O=!1}var j=!!O&&{passive:!1},A=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},B=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=W(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?A(t,"overflowY"):A(t,"overflowX")},W=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),c=i*r,u=n.target,l=t.contains(u),s=!1,d=c>0,f=0,p=0;do{if(!u)break;var h=W(e,u),v=h[0],m=h[1]-h[2]-i*v;(v||m)&&I(e,u)&&(f+=m,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&c>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-c>p)&&(s=!0),s},X=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Y=function(e){return[e.deltaX,e.deltaY]},D=function(e){return e&&"current"in e?e.current:e},G=0,z=[];let F=(r=function(e){var t=c.useRef([]),n=c.useRef([0,0]),r=c.useRef(),o=c.useState(G++)[0],a=c.useState(b)[0],i=c.useRef(e);c.useEffect(function(){i.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(D),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=c.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=X(e),c=n.current,u="deltaX"in e?e.deltaX:c[0]-a[0],l="deltaY"in e?e.deltaY:c[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=B(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=B(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?u:l,!0)},[]),l=c.useCallback(function(e){if(z.length&&z[z.length-1]===a){var n="deltaY"in e?Y(e):X(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(D).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=c.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=c.useCallback(function(e){n.current=X(e),r.current=void 0},[]),f=c.useCallback(function(t){s(t.type,Y(t),t.target,u(t,e.lockRef.current))},[]),p=c.useCallback(function(t){s(t.type,X(t),t.target,u(t,e.lockRef.current))},[]);c.useEffect(function(){return z.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,j),document.addEventListener("touchmove",l,j),document.addEventListener("touchstart",d,j),function(){z=z.filter(function(e){return e!==a}),document.removeEventListener("wheel",l,j),document.removeEventListener("touchmove",l,j),document.removeEventListener("touchstart",d,j)}},[]);var h=e.removeScrollBar,v=e.inert;return c.createElement(c.Fragment,null,v?c.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?c.createElement(L,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var Z=c.forwardRef(function(e,t){return c.createElement(m,a({},e,{ref:t,sideCar:F}))});Z.classNames=m.classNames;let q=Z}}]);