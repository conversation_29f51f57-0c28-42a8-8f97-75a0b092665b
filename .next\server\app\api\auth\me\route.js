(()=>{var e={};e.id=673,e.ids=[673],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,r,t)=>{"use strict";t.d(r,{$u:()=>c,l4:()=>p});var s=t(85663),n=t(43205),i=t.n(n),u=t(56621);let a=process.env.JWT_SECRET||"your-secret-key-change-in-production";async function o(e,r){return s.Ay.compare(e,r)}async function c(e){try{let{data:r,error:t}=await u.N.from("admin_users").select("*").eq("username",e.username).eq("is_active",!0).single();if(t||!r||!await o(e.password,r.password_hash))return{success:!1,error:"Invalid username or password"};await u.N.from("admin_users").update({last_login:new Date().toISOString()}).eq("id",r.id);let s={id:r.id,username:r.username,email:r.email},n=i().sign({id:s.id,username:s.username,email:s.email},a,{expiresIn:"7d"});return{success:!0,user:s,token:n}}catch(e){return console.error("Authentication error:",e),{success:!1,error:"Authentication failed"}}}async function p(e){let r=function(e){try{return i().verify(e,a)}catch(e){return null}}(e);if(!r)return null;try{let{data:e,error:t}=await u.N.from("admin_users").select("id, username, email").eq("id",r.id).eq("is_active",!0).single();if(t||!e)return null;return{id:e.id,username:e.username,email:e.email}}catch(e){return console.error("Get user from token error:",e),null}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,r,t)=>{"use strict";t.d(r,{N:()=>u});var s=t(39398);let n=process.env.NEXT_PUBLIC_SUPABASE_URL,i=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,u=(0,s.createClient)(n,i);(0,s.createClient)(n,process.env.SUPABASE_SERVICE_ROLE_KEY)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75059:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),i=t(48088),u=t(37719),a=t(32190),o=t(12909),c=t(44999);async function p(e){try{let e=await (0,c.UL)(),r=e.get("auth-token")?.value;if(!r)return a.NextResponse.json({error:"Not authenticated"},{status:401});let t=await (0,o.l4)(r);if(!t)return a.NextResponse.json({error:"Invalid token"},{status:401});return a.NextResponse.json({user:t})}catch(e){return console.error("Get user API error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/me/route",pathname:"/api/auth/me",filename:"route",bundlePath:"app/api/auth/me/route"},resolvedPagePath:"E:\\cozy\\nextjs\\src\\app\\api\\auth\\me\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:x}=l;function v(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,398,999,315],()=>t(75059));module.exports=s})();