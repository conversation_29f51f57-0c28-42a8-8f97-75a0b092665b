(()=>{var e={};e.id=856,e.ids=[856],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,r,t)=>{"use strict";t.d(r,{$u:()=>c,l4:()=>d});var s=t(85663),o=t(43205),n=t.n(o),i=t(56621);let a=process.env.JWT_SECRET||"your-secret-key-change-in-production";async function u(e,r){return s.Ay.compare(e,r)}async function c(e){try{let{data:r,error:t}=await i.N.from("admin_users").select("*").eq("username",e.username).eq("is_active",!0).single();if(t||!r||!await u(e.password,r.password_hash))return{success:!1,error:"Invalid username or password"};await i.N.from("admin_users").update({last_login:new Date().toISOString()}).eq("id",r.id);let s={id:r.id,username:r.username,email:r.email},o=n().sign({id:s.id,username:s.username,email:s.email},a,{expiresIn:"7d"});return{success:!0,user:s,token:o}}catch(e){return console.error("Authentication error:",e),{success:!1,error:"Authentication failed"}}}async function d(e){let r=function(e){try{return n().verify(e,a)}catch(e){return null}}(e);if(!r)return null;try{let{data:e,error:t}=await i.N.from("admin_users").select("id, username, email").eq("id",r.id).eq("is_active",!0).single();if(t||!e)return null;return{id:e.id,username:e.username,email:e.email}}catch(e){return console.error("Get user from token error:",e),null}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},40361:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>q,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{DELETE:()=>x,GET:()=>p,PUT:()=>l});var o=t(96559),n=t(48088),i=t(37719),a=t(32190),u=t(56621),c=t(12909),d=t(44999);async function p(e,{params:r}){try{let{id:e}=await r,{data:t,error:s}=await u.N.from("products").select("*").eq("id",e).single();if(s||!t)return a.NextResponse.json({error:"Product not found"},{status:404});return a.NextResponse.json({product:t})}catch(e){return console.error("Get product API error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e,{params:r}){try{let t=await (0,d.UL)(),s=t.get("auth-token")?.value;if(!s)return a.NextResponse.json({error:"Authentication required"},{status:401});if(!await (0,c.l4)(s))return a.NextResponse.json({error:"Invalid authentication"},{status:401});let{id:o}=await r,{name:n,description:i,price:p,image_url:l,specs:x}=await e.json(),m={};void 0!==n&&(m.name=n),void 0!==i&&(m.description=i),void 0!==p&&(m.price=parseFloat(p)),void 0!==l&&(m.image_url=l),void 0!==x&&(m.specs=x);let{data:v,error:f}=await u.N.from("products").update(m).eq("id",o).select().single();if(f)return console.error("Product update error:",f),a.NextResponse.json({error:"Failed to update product"},{status:500});return a.NextResponse.json({product:v})}catch(e){return console.error("Update product API error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let e=await (0,d.UL)(),t=e.get("auth-token")?.value;if(!t)return a.NextResponse.json({error:"Authentication required"},{status:401});if(!await (0,c.l4)(t))return a.NextResponse.json({error:"Invalid authentication"},{status:401});let{id:s}=await r,{error:o}=await u.N.from("products").delete().eq("id",s);if(o)return console.error("Product deletion error:",o),a.NextResponse.json({error:"Failed to delete product"},{status:500});return a.NextResponse.json({success:!0})}catch(e){return console.error("Delete product API error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/products/[id]/route",pathname:"/api/products/[id]",filename:"route",bundlePath:"app/api/products/[id]/route"},resolvedPagePath:"E:\\cozy\\nextjs\\src\\app\\api\\products\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:f,serverHooks:q}=m;function h(){return(0,i.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:f})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,r,t)=>{"use strict";t.d(r,{N:()=>i});var s=t(39398);let o=process.env.NEXT_PUBLIC_SUPABASE_URL,n=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,i=(0,s.createClient)(o,n);(0,s.createClient)(o,process.env.SUPABASE_SERVICE_ROLE_KEY)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,398,999,315],()=>t(40361));module.exports=s})();