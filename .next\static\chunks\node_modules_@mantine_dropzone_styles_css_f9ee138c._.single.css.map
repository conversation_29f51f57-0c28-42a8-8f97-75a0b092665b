{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@mantine/dropzone/styles.css"], "sourcesContent": [".m_d46a4834 {\n  position: relative;\n  border: calc(0.0625rem * var(--mantine-scale)) dashed;\n  color: var(--mantine-color-text);\n  padding: var(--mantine-spacing-md);\n  border-radius: var(--dropzone-radius);\n  cursor: pointer;\n  user-select: none;\n  transition:\n    background-color 100ms ease,\n    border-color 100ms ease;\n}\n\n  .m_d46a4834:where([data-loading]),\n  .m_d46a4834:where(:not([data-activate-on-click])) {\n    cursor: default;\n  }\n\n  :where([data-mantine-color-scheme='light']) .m_d46a4834 {\n    background-color: var(--mantine-color-white);\n    border-color: var(--mantine-color-gray-4);\n}\n\n  :where([data-mantine-color-scheme='dark']) .m_d46a4834 {\n    background-color: var(--mantine-color-dark-6);\n    border-color: var(--mantine-color-dark-4);\n}\n\n  @media (hover: hover) {\n      :where([data-mantine-color-scheme='light']) .m_d46a4834:hover:where([data-activate-on-click]:not([data-loading])) {\n        background-color: var(--mantine-color-gray-0);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_d46a4834:hover:where([data-activate-on-click]:not([data-loading])) {\n        background-color: var(--mantine-color-dark-5);\n  }\n}\n\n  @media (hover: none) {\n      :where([data-mantine-color-scheme='light']) .m_d46a4834:active:where([data-activate-on-click]:not([data-loading])) {\n        background-color: var(--mantine-color-gray-0);\n  }\n\n      :where([data-mantine-color-scheme='dark']) .m_d46a4834:active:where([data-activate-on-click]:not([data-loading])) {\n        background-color: var(--mantine-color-dark-5);\n  }\n}\n\n  .m_d46a4834:where([data-accept]) {\n    background-color: var(--dropzone-accept-bg);\n    border-color: var(--dropzone-accept-bg);\n    color: var(--dropzone-accept-color);\n  }\n\n  .m_d46a4834:where([data-reject]) {\n    background-color: var(--dropzone-reject-bg);\n    border-color: var(--dropzone-reject-bg);\n    color: var(--dropzone-reject-color);\n  }\n\n.m_b85f7144 {\n  pointer-events: none;\n  user-select: none;\n}\n\n.m_b85f7144:where([data-enable-pointer-events]) {\n    pointer-events: all;\n  }\n\n.m_96f6e9ad {\n  position: fixed;\n  inset: 0;\n  background-color: var(--mantine-color-body);\n  display: flex;\n  flex-direction: column;\n  padding: var(--mantine-spacing-xs);\n  transition: opacity 100ms ease;\n}\n\n.m_96f6e9ad .m_7946116d {\n    flex: 1;\n  }\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAaE;;;;AAKA;;;;;AAKA;;;;;AAKA;EACI;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;AAKJ;;;;;;AAMA;;;;;;AAMF;;;;;AAKA;;;;AAIA;;;;;;;;;;AAUA", "ignoreList": [0]}}]}