"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[621],{2278:(e,t,n)=>{n.d(t,{o:()=>o,x:()=>l}),n(12115);var r=n(56970);n(95155);let[o,l]=(0,r.createSafeContext)("ModalBase component was not found in tree")},10866:(e,t,n)=>{n.d(t,{isElement:()=>o});var r=n(12115);function o(e){return!Array.isArray(e)&&null!==e&&"object"==typeof e&&e.type!==r.Fragment}},11681:(e,t,n)=>{n.d(t,{useDisclosure:()=>o});var r=n(12115);function o(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},[n,o]=(0,r.useState)(e),l=(0,r.useCallback)(()=>{o(e=>{if(!e){var n;return null==(n=t.onOpen)||n.call(t),!0}return e})},[t.onOpen]),i=(0,r.useCallback)(()=>{o(e=>{if(e){var n;return null==(n=t.onClose)||n.call(t),!1}return e})},[t.onClose]),a=(0,r.useCallback)(()=>{n?i():l()},[i,l,n]);return[n,{open:l,close:i,toggle:a}]}},12151:(e,t,n)=>{n.d(t,{ModalBaseOverlay:()=>u});var r=n(95155),o=n(12115),l=n(56231),i=n(60384),a=n(2278);let s={duration:200,timingFunction:"ease",transition:"fade"},u=(0,o.forwardRef)((e,t)=>{let{onClick:n,transitionProps:o,style:u,visible:c,...d}=e,f=(0,a.x)(),p=function(e){let t=(0,a.x)();return{...s,...t.transitionProps,...e}}(o);return(0,r.jsx)(i.Transition,{mounted:void 0!==c?c:f.opened,...p,transition:"fade",children:e=>(0,r.jsx)(l.Overlay,{ref:t,fixed:!0,style:[u,e],zIndex:f.zIndex,unstyled:f.unstyled,onClick:e=>{null==n||n(e),f.closeOnClickOutside&&f.onClose()},...d})})});u.displayName="@mantine/core/ModalBaseOverlay"},20678:(e,t,n)=>{n.d(t,{R:()=>r});function r(e){return e?parseInt(e,10):0}},23869:(e,t,n)=>{n.d(t,{ModalCloseButton:()=>c});var r=n(95155);n(12115);var o=n(43664),l=n(36960),i=n(91033),a=n(57026),s=n(91688);let u={},c=(0,l.factory)((e,t)=>{let{classNames:n,className:l,style:s,styles:c,vars:d,...f}=(0,o.useProps)("ModalCloseButton",u,e),p=(0,a.k)();return(0,r.jsx)(i.ModalBaseCloseButton,{ref:t,...p.getStyles("close",{classNames:n,style:s,styles:c,className:l}),...f})});c.classes=s.A,c.displayName="@mantine/core/ModalCloseButton"},25067:(e,t,n)=>{n.d(t,{useCallbackRef:()=>o});var r=n(12115);function o(e){let t=(0,r.useRef)(e);return(0,r.useEffect)(()=>{t.current=e}),(0,r.useMemo)(()=>function(){for(var e,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return null==(e=t.current)?void 0:e.call(t,...r)},[])}},25126:(e,t,n)=>{n.d(t,{$u:()=>y,EW:()=>g,F2:()=>v,Go:()=>f,O_:()=>p,Pg:()=>s,RS:()=>o,YE:()=>h,cX:()=>d,gR:()=>l,nr:()=>u,tZ:()=>m});var r=n(86301);function o(e){let t=e.activeElement;for(;(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement)!=null;){var n;t=t.shadowRoot.activeElement}return t}function l(e,t){if(!e||!t)return!1;let n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&(0,r.Ng)(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function i(){let e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function a(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:n}=e;return t+"/"+n}).join(" "):navigator.userAgent}function s(e){return!a().includes("jsdom/")&&(!c()&&0===e.width&&0===e.height||c()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}function u(){return/apple/i.test(navigator.vendor)}function c(){let e=/android/i;return e.test(i())||e.test(a())}function d(){return i().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints}function f(e,t){let n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function p(e){return"nativeEvent"in e}function m(e){return e.matches("html,body")}function h(e){return(null==e?void 0:e.ownerDocument)||document}function v(e,t){return null!=t&&("composedPath"in e?e.composedPath().includes(t):null!=e.target&&t.contains(e.target))}function g(e){return"composedPath"in e?e.composedPath()[0]:e.target}function y(e){return(0,r.sb)(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}},26029:(e,t,n)=>{n.d(t,{Badge:()=>m});var r=n(95155);n(12115);var o=n(56204),l=n(68918),i=n(71180),a=n(43664),s=n(53791),u=n(69604),c=n(64511),d={root:"m_347db0ec","root--dot":"m_fbd81e3d",label:"m_5add502a",section:"m_91fdda9b"};let f={},p=(0,l.createVarsResolver)((e,t)=>{let{radius:n,color:r,gradient:l,variant:a,size:s,autoContrast:u}=t,c=e.variantColorResolver({color:r||e.primaryColor,theme:e,gradient:l,variant:a||"filled",autoContrast:u});return{root:{"--badge-height":(0,o.getSize)(s,"badge-height"),"--badge-padding-x":(0,o.getSize)(s,"badge-padding-x"),"--badge-fz":(0,o.getSize)(s,"badge-fz"),"--badge-radius":void 0===n?void 0:(0,o.getRadius)(n),"--badge-bg":r||a?c.background:void 0,"--badge-color":r||a?c.color:void 0,"--badge-bd":r||a?c.border:void 0,"--badge-dot-color":"dot"===a?(0,i.getThemeColor)(r,e):void 0}}}),m=(0,c.polymorphicFactory)((e,t)=>{let n=(0,a.useProps)("Badge",f,e),{classNames:o,className:l,style:i,styles:c,unstyled:m,vars:h,radius:v,color:g,gradient:y,leftSection:b,rightSection:w,children:x,variant:S,fullWidth:R,autoContrast:C,circle:T,mod:E,...A}=n,M=(0,s.useStyles)({name:"Badge",props:n,classes:d,className:l,style:i,classNames:o,styles:c,unstyled:m,vars:h,varsResolver:p});return(0,r.jsxs)(u.Box,{variant:S,mod:[{block:R,circle:T,"with-right-section":!!w,"with-left-section":!!b},E],...M("root",{variant:S}),ref:t,...A,children:[b&&(0,r.jsx)("span",{...M("section"),"data-position":"left",children:b}),(0,r.jsx)("span",{...M("label"),children:x}),w&&(0,r.jsx)("span",{...M("section"),"data-position":"right",children:w})]})});m.classes=d,m.displayName="@mantine/core/Badge"},26051:(e,t,n)=>{n.d(t,{ModalTitle:()=>c});var r=n(95155);n(12115);var o=n(43664),l=n(36960),i=n(54578),a=n(57026),s=n(91688);let u={},c=(0,l.factory)((e,t)=>{let{classNames:n,className:l,style:s,styles:c,vars:d,...f}=(0,o.useProps)("ModalTitle",u,e),p=(0,a.k)();return(0,r.jsx)(i.ModalBaseTitle,{ref:t,...p.getStyles("title",{classNames:n,style:s,styles:c,className:l}),...f})});c.classes=s.A,c.displayName="@mantine/core/ModalTitle"},29586:(e,t,n)=>{n.d(t,{ModalBaseContent:()=>d});var r=n(95155),o=n(12115),l=n(52596),i=n(49781),a=n(97287),s=n(60384),u=n(2278),c=n(33396);let d=(0,o.forwardRef)((e,t)=>{let{transitionProps:n,className:o,innerProps:d,onKeyDown:f,style:p,...m}=e,h=(0,u.x)();return(0,r.jsx)(s.Transition,{mounted:h.opened,transition:"pop",...h.transitionProps,onExited:()=>{var e,t,n;null==(e=h.onExitTransitionEnd)||e.call(h),null==(n=h.transitionProps)||null==(t=n.onExited)||t.call(n)},onEntered:()=>{var e,t,n;null==(e=h.onEnterTransitionEnd)||e.call(h),null==(n=h.transitionProps)||null==(t=n.onEntered)||t.call(n)},...n,children:e=>(0,r.jsx)("div",{...d,className:(0,l.A)({[c.A.inner]:!h.unstyled},d.className),children:(0,r.jsx)(i.FocusTrap,{active:h.opened&&h.trapFocus,innerRef:t,children:(0,r.jsx)(a.Paper,{...m,component:"section",role:"dialog",tabIndex:-1,"aria-modal":!0,"aria-describedby":h.bodyMounted?h.getBodyId():void 0,"aria-labelledby":h.titleMounted?h.getTitleId():void 0,style:[p,e],className:(0,l.A)({[c.A.content]:!h.unstyled},o),unstyled:h.unstyled,children:m.children})})})})});d.displayName="@mantine/core/ModalBaseContent"},30097:(e,t,n)=>{n.d(t,{ModalStack:()=>u,useModalStackContext:()=>s});var r=n(95155),o=n(12115),l=n(49830),i=n(58750);let[a,s]=(0,l.createOptionalContext)();function u(e){let{children:t}=e,[n,l]=(0,o.useState)([]),[s,u]=(0,o.useState)((0,i.getDefaultZIndex)("modal"));return(0,r.jsx)(a,{value:{stack:n,addModal:(e,t)=>{l(t=>[...new Set([...t,e])]),u(e=>"number"==typeof t&&"number"==typeof e?Math.max(e,t):e)},removeModal:e=>l(t=>t.filter(t=>t!==e)),getZIndex:e=>"calc(".concat(s," + ").concat(n.indexOf(e)," + 1)"),currentId:n[n.length-1],maxZIndex:s},children:t})}u.displayName="@mantine/core/ModalStack"},33396:(e,t,n)=>{n.d(t,{A:()=>r});var r={title:"m_615af6c9",header:"m_b5489c3c",inner:"m_60c222c7",content:"m_fd1ab0aa",close:"m_606cb269",body:"m_5df29311"}},40613:(e,t,n)=>{n.d(t,{useDebouncedCallback:()=>l});var r=n(12115),o=n(25067);function l(e,t){let{delay:n,flushOnUnmount:l,leading:i}="number"==typeof t?{delay:t,flushOnUnmount:!1,leading:!1}:t,a=(0,o.useCallbackRef)(e),s=(0,r.useRef)(0),u=(0,r.useMemo)(()=>{let e=Object.assign(function(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];window.clearTimeout(s.current);let l=e._isFirstCall;if(e._isFirstCall=!1,i&&l)return void a(...r);function u(){window.clearTimeout(s.current),s.current=0,e._isFirstCall=!0}let c=()=>{0!==s.current&&(u(),a(...r))};e.flush=c,e.cancel=()=>{u()},s.current=window.setTimeout(c,n)},{flush:()=>{},cancel:()=>{},_isFirstCall:!0});return e},[a,n,i]);return(0,r.useEffect)(()=>()=>{l?u.flush():u.cancel()},[u,l]),u}},40978:(e,t,n)=>{n.d(t,{ModalBaseHeader:()=>u});var r=n(95155),o=n(12115),l=n(52596),i=n(69604),a=n(2278),s=n(33396);let u=(0,o.forwardRef)((e,t)=>{let{className:n,...o}=e,u=(0,a.x)();return(0,r.jsx)(i.Box,{component:"header",ref:t,className:(0,l.A)({[s.A.header]:!u.unstyled},n),...o})});u.displayName="@mantine/core/ModalBaseHeader"},41407:(e,t,n)=>{n.d(t,{ModalBase:()=>h});var r=n(95155),o=n(12115),l=n(93795),i=n(58750),a=n(56204),s=n(69604),u=n(62143),c=n(2278),d=n(64173),f=n(28261),p=n(60497),m=n(43589);let h=(0,o.forwardRef)((e,t)=>{let{keepMounted:n,opened:h,onClose:v,id:g,transitionProps:y,onExitTransitionEnd:b,onEnterTransitionEnd:w,trapFocus:x,closeOnEscape:S,returnFocus:R,closeOnClickOutside:C,withinPortal:T,portalProps:E,lockScroll:A,children:M,zIndex:P,shadow:j,padding:k,__vars:L,unstyled:B,removeScrollProps:D,...N}=e,{_id:z,titleMounted:O,bodyMounted:F,shouldLockScroll:H,setTitleMounted:_,setBodyMounted:I}=function(e){let{id:t,transitionProps:n,opened:r,trapFocus:l,closeOnEscape:i,onClose:a,returnFocus:s}=e,u=(0,d.useId)(t),[c,h]=(0,o.useState)(!1),[v,g]=(0,o.useState)(!1),y=function(e){let{opened:t,transitionDuration:n}=e,[r,l]=(0,o.useState)(t),i=(0,o.useRef)(-1),a=(0,m.useReducedMotion)()?0:n;return(0,o.useEffect)(()=>(t?(l(!0),window.clearTimeout(i.current)):0===a?l(!1):i.current=window.setTimeout(()=>l(!1),a),()=>window.clearTimeout(i.current)),[t,a]),r}({opened:r,transitionDuration:"number"==typeof(null==n?void 0:n.duration)?null==n?void 0:n.duration:200});return(0,f.useWindowEvent)("keydown",e=>{if("Escape"===e.key&&i&&!e.isComposing&&r){var t;(null==(t=e.target)?void 0:t.getAttribute("data-mantine-stop-propagation"))!=="true"&&a()}},{capture:!0}),(0,p.useFocusReturn)({opened:r,shouldReturnFocus:l&&s}),{_id:u,titleMounted:c,bodyMounted:v,shouldLockScroll:y,setTitleMounted:h,setBodyMounted:g}}({id:g,transitionProps:y,opened:h,trapFocus:x,closeOnEscape:S,onClose:v,returnFocus:R}),{key:W,...U}=D||{};return(0,r.jsx)(u.OptionalPortal,{...E,withinPortal:T,children:(0,r.jsx)(c.o,{value:{opened:h,onClose:v,closeOnClickOutside:C,onExitTransitionEnd:b,onEnterTransitionEnd:w,transitionProps:{...y,keepMounted:n},getTitleId:()=>"".concat(z,"-title"),getBodyId:()=>"".concat(z,"-body"),titleMounted:O,bodyMounted:F,setTitleMounted:_,setBodyMounted:I,trapFocus:x,closeOnEscape:S,zIndex:P,unstyled:B},children:(0,r.jsx)(l.A,{enabled:H&&A,...U,children:(0,r.jsx)(s.Box,{ref:t,...N,__vars:{...L,"--mb-z-index":(P||(0,i.getDefaultZIndex)("modal")).toString(),"--mb-shadow":(0,a.getShadow)(j),"--mb-padding":(0,a.getSpacing)(k)},children:M})},W)})})});h.displayName="@mantine/core/ModalBase"},41954:(e,t,n)=>{n.d(t,{Textarea:()=>j});var r=n(95155),o=n(79630),l=n(93495),i=n(12115),a=i.useLayoutEffect,s=function(e){var t=i.useRef(e);return a(function(){t.current=e}),t},u=function(e,t){if("function"==typeof e)return void e(t);e.current=t},c=function(e,t){var n=i.useRef();return i.useCallback(function(r){e.current=r,n.current&&u(n.current,null),n.current=t,t&&u(t,r)},[t])},d={"min-height":"0","max-height":"none",height:"0",visibility:"hidden",overflow:"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0",display:"block"},f=function(e){Object.keys(d).forEach(function(t){e.style.setProperty(t,d[t],"important")})},p=null,m=function(e,t){var n=e.scrollHeight;return"border-box"===t.sizingStyle.boxSizing?n+t.borderSize:n-t.paddingSize},h=function(){},v=["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth","boxSizing","fontFamily","fontSize","fontStyle","fontWeight","letterSpacing","lineHeight","paddingBottom","paddingLeft","paddingRight","paddingTop","tabSize","textIndent","textRendering","textTransform","width","wordBreak","wordSpacing","scrollbarGutter"],g=!!document.documentElement.currentStyle,y=function(e){var t=window.getComputedStyle(e);if(null===t)return null;var n=v.reduce(function(e,n){return e[n]=t[n],e},{}),r=n.boxSizing;if(""===r)return null;g&&"border-box"===r&&(n.width=parseFloat(n.width)+parseFloat(n.borderRightWidth)+parseFloat(n.borderLeftWidth)+parseFloat(n.paddingRight)+parseFloat(n.paddingLeft)+"px");var o=parseFloat(n.paddingBottom)+parseFloat(n.paddingTop),l=parseFloat(n.borderBottomWidth)+parseFloat(n.borderTopWidth);return{sizingStyle:n,paddingSize:o,borderSize:l}};function b(e,t,n){var r=s(n);i.useLayoutEffect(function(){var n=function(e){return r.current(e)};if(e)return e.addEventListener(t,n),function(){return e.removeEventListener(t,n)}},[])}var w=function(e,t){b(document.body,"reset",function(n){e.current.form===n.target&&t(n)})},x=function(e){b(window,"resize",e)},S=function(e){b(document.fonts,"loadingdone",e)},R=["cacheMeasurements","maxRows","minRows","onChange","onHeightChange"],C=i.forwardRef(function(e,t){var n=e.cacheMeasurements,r=e.maxRows,a=e.minRows,s=e.onChange,u=void 0===s?h:s,d=e.onHeightChange,v=void 0===d?h:d,g=(0,l.A)(e,R),b=void 0!==g.value,C=i.useRef(null),T=c(C,t),E=i.useRef(0),A=i.useRef(),M=function(){var e=C.current,t=n&&A.current?A.current:y(e);if(t){A.current=t;var o,l,i,s,u,c,d,h,g,b,w,x=(o=e.value||e.placeholder||"x",void 0===(l=a)&&(l=1),void 0===(i=r)&&(i=1/0),p||((p=document.createElement("textarea")).setAttribute("tabindex","-1"),p.setAttribute("aria-hidden","true"),f(p)),null===p.parentNode&&document.body.appendChild(p),s=t.paddingSize,u=t.borderSize,d=(c=t.sizingStyle).boxSizing,Object.keys(c).forEach(function(e){p.style[e]=c[e]}),f(p),p.value=o,h=m(p,t),p.value=o,h=m(p,t),p.value="x",b=(g=p.scrollHeight-s)*l,"border-box"===d&&(b=b+s+u),h=Math.max(b,h),w=g*i,"border-box"===d&&(w=w+s+u),[h=Math.min(w,h),g]),S=x[0],R=x[1];E.current!==S&&(E.current=S,e.style.setProperty("height",S+"px","important"),v(S,{rowHeight:R}))}};return i.useLayoutEffect(M),w(C,function(){if(!b){var e=C.current.value;requestAnimationFrame(function(){var t=C.current;t&&e!==t.value&&M()})}}),x(M),S(M),i.createElement("textarea",(0,o.A)({},g,{onChange:function(e){b||M(),u(e)},ref:T}))}),T=n(67414),E=n(43664),A=n(36960),M=n(24225);let P={},j=(0,A.factory)((e,t)=>{let{autosize:n,maxRows:o,minRows:l,__staticSelector:i,resize:a,...s}=(0,E.useProps)("Textarea",P,e),u=n&&"test"!==(0,T.getEnv)();return(0,r.jsx)(M.InputBase,{component:u?C:"textarea",ref:t,...s,__staticSelector:i||"Textarea",multiline:!0,"data-no-overflow":n&&void 0===o||void 0,__vars:{"--input-resize":a},...u?{maxRows:o,minRows:l}:{}})});j.classes=M.InputBase.classes,j.displayName="@mantine/core/Textarea"},48254:(e,t,n)=>{n.d(t,{ModalBaseBody:()=>u});var r=n(95155),o=n(12115),l=n(52596),i=n(69604),a=n(2278),s=n(33396);let u=(0,o.forwardRef)((e,t)=>{let{className:n,...u}=e,c=function(){let e=(0,a.x)();return(0,o.useEffect)(()=>(e.setBodyMounted(!0),()=>e.setBodyMounted(!1)),[]),e.getBodyId()}(),d=(0,a.x)();return(0,r.jsx)(i.Box,{ref:t,...u,id:c,className:(0,l.A)({[s.A.body]:!d.unstyled},n)})});u.displayName="@mantine/core/ModalBaseBody"},49781:(e,t,n)=>{n.d(t,{FocusTrap:()=>u,FocusTrapInitialFocus:()=>c});var r=n(95155),o=n(12115),l=n(54822),i=n(88551),a=n(10866),s=n(99026);function u(e){let{children:t,active:n=!0,refProp:r="ref",innerRef:s}=e,u=(0,l.useFocusTrap)(n),c=(0,i.useMergedRef)(u,s);return(0,a.isElement)(t)?(0,o.cloneElement)(t,{[r]:c}):t}function c(e){return(0,r.jsx)(s.VisuallyHidden,{tabIndex:-1,"data-autofocus":!0,...e})}u.displayName="@mantine/core/FocusTrap",c.displayName="@mantine/core/FocusTrapInitialFocus",u.InitialFocus=c},53304:(e,t,n)=>{n.d(t,{DirectionContext:()=>i,DirectionProvider:()=>s,useDirection:()=>a});var r=n(95155),o=n(12115),l=n(73141);let i=(0,o.createContext)({dir:"ltr",toggleDirection:()=>{},setDirection:()=>{}});function a(){return(0,o.useContext)(i)}function s(e){let{children:t,initialDirection:n="ltr",detectDirection:a=!0}=e,[s,u]=(0,o.useState)(n),c=e=>{u(e),document.documentElement.setAttribute("dir",e)};return(0,l.useIsomorphicEffect)(()=>{if(a){let e=document.documentElement.getAttribute("dir");("rtl"===e||"ltr"===e)&&c(e)}},[]),(0,r.jsx)(i.Provider,{value:{dir:s,toggleDirection:()=>c("ltr"===s?"rtl":"ltr"),setDirection:c},children:t})}},54578:(e,t,n)=>{n.d(t,{ModalBaseTitle:()=>u});var r=n(95155),o=n(12115),l=n(52596),i=n(69604),a=n(2278),s=n(33396);let u=(0,o.forwardRef)((e,t)=>{let{className:n,...u}=e,c=function(){let e=(0,a.x)();return(0,o.useEffect)(()=>(e.setTitleMounted(!0),()=>e.setTitleMounted(!1)),[]),e.getTitleId()}(),d=(0,a.x)();return(0,r.jsx)(i.Box,{component:"h2",ref:t,className:(0,l.A)({[s.A.title]:!d.unstyled},n),...u,id:c})});u.displayName="@mantine/core/ModalBaseTitle"},54822:(e,t,n)=>{n.d(t,{useFocusTrap:()=>u});var r=n(12115);let o=/input|select|textarea|button|object/,l="a, input, select, textarea, button, object, [tabindex]";function i(e){let t=e.getAttribute("tabindex");return null===t&&(t=void 0),parseInt(t,10)}function a(e){let t=e.nodeName.toLowerCase(),n=!Number.isNaN(i(e));return(o.test(t)&&!e.disabled||e instanceof HTMLAnchorElement&&e.href||n)&&function(e){if(e.getAttribute("aria-hidden")||e.getAttribute("hidden")||"hidden"===e.getAttribute("type"))return!1;let t=e;for(;t&&t!==document.body&&11!==t.nodeType;){if("none"===t.style.display)return!1;t=t.parentNode}return!0}(e)}function s(e){let t=i(e);return(Number.isNaN(t)||t>=0)&&a(e)}function u(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,r.useRef)(null),n=e=>{let t=e.querySelector("[data-autofocus]");if(!t){let n=Array.from(e.querySelectorAll(l));!(t=n.find(s)||n.find(a)||null)&&a(e)&&(t=e)}t&&t.focus({preventScroll:!0})},o=(0,r.useCallback)(r=>{e&&null!==r&&t.current!==r&&(r?(setTimeout(()=>{r.getRootNode()&&n(r)}),t.current=r):t.current=null)},[e]);return(0,r.useEffect)(()=>{if(!e)return;t.current&&setTimeout(()=>n(t.current));let r=e=>{"Tab"===e.key&&t.current&&function(e,t){let n=Array.from(e.querySelectorAll(l)).filter(s);if(!n.length)return t.preventDefault();let r=n[t.shiftKey?0:n.length-1],o=e.getRootNode(),i=r===o.activeElement||e===o.activeElement,a=o.activeElement;if("INPUT"===a.tagName&&"radio"===a.getAttribute("type")&&(i=n.filter(e=>"radio"===e.getAttribute("type")&&e.getAttribute("name")===a.getAttribute("name")).includes(r)),!i)return;t.preventDefault();let u=n[t.shiftKey?n.length-1:0];u&&u.focus()}(t.current,e)};return document.addEventListener("keydown",r),()=>document.removeEventListener("keydown",r)},[e]),o}},56231:(e,t,n)=>{n.d(t,{Overlay:()=>v});var r=n(95155),o=n(5903);n(12115);var l=n(58750),i=n(56204),a=n(68918),s=n(70714),u=n(43664),c=n(53791),d=n(69604),f=n(64511),p={root:"m_9814e45f"};let m={zIndex:(0,l.getDefaultZIndex)("modal")},h=(0,a.createVarsResolver)((e,t)=>{let{gradient:n,color:r,backgroundOpacity:l,blur:a,radius:u,zIndex:c}=t;return{root:{"--overlay-bg":n||(void 0!==r||void 0!==l)&&(0,s.B)(r||"#000",null!=l?l:.6)||void 0,"--overlay-filter":a?"blur(".concat((0,o.D)(a),")"):void 0,"--overlay-radius":void 0===u?void 0:(0,i.getRadius)(u),"--overlay-z-index":null==c?void 0:c.toString()}}}),v=(0,f.polymorphicFactory)((e,t)=>{let n=(0,u.useProps)("Overlay",m,e),{classNames:o,className:l,style:i,styles:a,unstyled:s,vars:f,fixed:v,center:g,children:y,radius:b,zIndex:w,gradient:x,blur:S,color:R,backgroundOpacity:C,mod:T,...E}=n,A=(0,c.useStyles)({name:"Overlay",props:n,classes:p,className:l,style:i,classNames:o,styles:a,unstyled:s,vars:f,varsResolver:h});return(0,r.jsx)(d.Box,{ref:t,...A("root"),mod:[{center:g,fixed:v},T],...E,children:y})});v.classes=p,v.displayName="@mantine/core/Overlay"},57026:(e,t,n)=>{n.d(t,{Z:()=>o,k:()=>l}),n(12115);var r=n(56970);n(95155);let[o,l]=(0,r.createSafeContext)("Modal component was not found in tree")},60497:(e,t,n)=>{n.d(t,{useFocusReturn:()=>l});var r=n(12115),o=n(84237);function l(e){let{opened:t,shouldReturnFocus:n=!0}=e,l=(0,r.useRef)(null),i=()=>{if(l.current&&"focus"in l.current&&"function"==typeof l.current.focus){var e;null==(e=l.current)||e.focus({preventScroll:!0})}};return(0,o.useDidUpdate)(()=>{let e=-1,r=t=>{"Tab"===t.key&&window.clearTimeout(e)};return document.addEventListener("keydown",r),t?l.current=document.activeElement:n&&(e=window.setTimeout(i,10)),()=>{window.clearTimeout(e),document.removeEventListener("keydown",r)}},[t,n]),i}},61318:(e,t,n)=>{n.d(t,{ModalContent:()=>f});var r=n(95155),o=n(5903);n(12115);var l=n(43664),i=n(36960),a=n(29586),s=n(69112),u=n(57026),c=n(91688);let d={},f=(0,i.factory)((e,t)=>{let{classNames:n,className:i,style:c,styles:f,vars:p,children:m,__hidden:h,...v}=(0,l.useProps)("ModalContent",d,e),g=(0,u.k)(),y=g.scrollAreaComponent||s.NativeScrollArea;return(0,r.jsx)(a.ModalBaseContent,{...g.getStyles("content",{className:i,style:c,styles:f,classNames:n}),innerProps:g.getStyles("inner",{className:i,style:c,styles:f,classNames:n}),"data-full-screen":g.fullScreen||void 0,"data-modal-content":!0,"data-hidden":h||void 0,ref:t,...v,children:(0,r.jsx)(y,{style:{maxHeight:g.fullScreen?"100dvh":"calc(100dvh - (".concat((0,o.D)(g.yOffset)," * 2))")},children:m})})});f.classes=c.A,f.displayName="@mantine/core/ModalContent"},62143:(e,t,n)=>{n.d(t,{OptionalPortal:()=>a});var r=n(95155);n(12115);var o=n(13656),l=n(36960),i=n(79407);let a=(0,l.factory)((e,t)=>{let{withinPortal:n=!0,children:l,...a}=e;return"test"!==(0,o.useMantineEnv)()&&n?(0,r.jsx)(i.Portal,{ref:t,...a,children:l}):(0,r.jsx)(r.Fragment,{children:l})});a.displayName="@mantine/core/OptionalPortal"},63617:(e,t,n)=>{n.d(t,{Modal:()=>y});var r=n(95155),o=n(12115),l=n(58750),i=n(43664),a=n(36960),s=n(86765),u=n(23869),c=n(61318),d=n(91558),f=n(71561),p=n(70789),m=n(30097),h=n(26051),v=n(91688);let g={closeOnClickOutside:!0,withinPortal:!0,lockScroll:!0,trapFocus:!0,returnFocus:!0,closeOnEscape:!0,keepMounted:!1,zIndex:(0,l.getDefaultZIndex)("modal"),transitionProps:{duration:200,transition:"fade-down"},withOverlay:!0,withCloseButton:!0},y=(0,a.factory)((e,t)=>{let{title:n,withOverlay:a,overlayProps:v,withCloseButton:y,closeButtonProps:b,children:w,radius:x,opened:S,stackId:R,zIndex:C,...T}=(0,i.useProps)("Modal",g,e),E=(0,m.useModalStackContext)(),A=!!n||y,M=E&&R?{closeOnEscape:E.currentId===R,trapFocus:E.currentId===R,zIndex:E.getZIndex(R)}:{},P=!1!==a&&(R&&E?E.currentId===R:S);return(0,o.useEffect)(()=>{E&&R&&(S?E.addModal(R,C||(0,l.getDefaultZIndex)("modal")):E.removeModal(R))},[S,R,C]),(0,r.jsxs)(p.ModalRoot,{ref:t,radius:x,opened:S,zIndex:E&&R?E.getZIndex(R):C,...T,...M,children:[a&&(0,r.jsx)(f.ModalOverlay,{visible:P,transitionProps:E&&R?{duration:0}:void 0,...v}),(0,r.jsxs)(c.ModalContent,{radius:x,__hidden:!!E&&!!R&&!!S&&R!==E.currentId,children:[A&&(0,r.jsxs)(d.ModalHeader,{children:[n&&(0,r.jsx)(h.ModalTitle,{children:n}),y&&(0,r.jsx)(u.ModalCloseButton,{...b})]}),(0,r.jsx)(s.ModalBody,{children:w})]})]})});y.classes=v.A,y.displayName="@mantine/core/Modal",y.Root=p.ModalRoot,y.Overlay=f.ModalOverlay,y.Content=c.ModalContent,y.Body=s.ModalBody,y.Header=d.ModalHeader,y.Title=h.ModalTitle,y.CloseButton=u.ModalCloseButton,y.Stack=m.ModalStack},67414:(e,t,n)=>{n.d(t,{getEnv:()=>o});var r=n(49509);function o(){return void 0!==r&&r.env&&1?"production":"development"}},69112:(e,t,n)=>{n.d(t,{NativeScrollArea:()=>o});var r=n(95155);function o(e){let{children:t}=e;return(0,r.jsx)(r.Fragment,{children:t})}},70789:(e,t,n)=>{n.d(t,{ModalRoot:()=>g});var r=n(95155),o=n(5903);n(12115);var l=n(58750),i=n(56204),a=n(68918),s=n(43664),u=n(53791),c=n(36960),d=n(41407),f=n(74271),p=n(57026),m=n(91688);let h={__staticSelector:"Modal",closeOnClickOutside:!0,withinPortal:!0,lockScroll:!0,trapFocus:!0,returnFocus:!0,closeOnEscape:!0,keepMounted:!1,zIndex:(0,l.getDefaultZIndex)("modal"),transitionProps:{duration:200,transition:"fade-down"},yOffset:"5dvh"},v=(0,a.createVarsResolver)((e,t)=>{let{radius:n,size:r,yOffset:l,xOffset:a}=t;return{root:{"--modal-radius":void 0===n?void 0:(0,i.getRadius)(n),"--modal-size":(0,i.getSize)(r,"modal-size"),"--modal-y-offset":(0,o.D)(l),"--modal-x-offset":(0,o.D)(a)}}}),g=(0,c.factory)((e,t)=>{let n=(0,s.useProps)("ModalRoot",h,e),{classNames:o,className:l,style:i,styles:a,unstyled:c,vars:g,yOffset:y,scrollAreaComponent:b,radius:w,fullScreen:x,centered:S,xOffset:R,__staticSelector:C,...T}=n,E=(0,u.useStyles)({name:C,classes:m.A,props:n,className:l,style:i,classNames:o,styles:a,unstyled:c,vars:g,varsResolver:v});return(0,r.jsx)(p.Z,{value:{yOffset:y,scrollAreaComponent:b,getStyles:E,fullScreen:x},children:(0,r.jsx)(d.ModalBase,{ref:t,...E("root"),"data-full-screen":x||void 0,"data-centered":S||void 0,"data-offset-scrollbars":b===f.ScrollArea.Autosize||void 0,unstyled:c,...T})})});g.classes=m.A,g.displayName="@mantine/core/ModalRoot"},71561:(e,t,n)=>{n.d(t,{ModalOverlay:()=>c});var r=n(95155);n(12115);var o=n(43664),l=n(36960),i=n(12151),a=n(57026),s=n(91688);let u={},c=(0,l.factory)((e,t)=>{let{classNames:n,className:l,style:s,styles:c,vars:d,...f}=(0,o.useProps)("ModalOverlay",u,e),p=(0,a.k)();return(0,r.jsx)(i.ModalBaseOverlay,{ref:t,...p.getStyles("overlay",{classNames:n,style:s,styles:c,className:l}),...f})});c.classes=s.A,c.displayName="@mantine/core/ModalOverlay"},74271:(e,t,n)=>{n.d(t,{ScrollArea:()=>Z,ScrollAreaAutosize:()=>X});var r=n(95155),o=n(12115),l=n(45299),i=n(5903),a=n(68918),s=n(43664),u=n(53791),c=n(69604),d=n(36960),f=n(56970);let[p,m]=(0,f.createSafeContext)("ScrollArea.Root component was not found in tree");var h=n(25067),v=n(73141);function g(e,t){let n=(0,h.useCallbackRef)(t);(0,v.useIsomorphicEffect)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}let y=(0,o.forwardRef)((e,t)=>{let{style:n,...l}=e,i=m(),[a,s]=(0,o.useState)(0),[u,c]=(0,o.useState)(0),d=!!(a&&u);return g(i.scrollbarX,()=>{var e;let t=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),c(t)}),g(i.scrollbarY,()=>{var e;let t=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),d?(0,r.jsx)("div",{...l,ref:t,style:{...n,width:a,height:u}}):null}),b=(0,o.forwardRef)((e,t)=>{let n=m(),o=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&o?(0,r.jsx)(y,{...e,ref:t}):null});var w=n(88551);let x={scrollHideDelay:1e3,type:"hover"},S=(0,o.forwardRef)((e,t)=>{let{type:n,scrollHideDelay:l,scrollbars:i,getStyles:a,...u}=(0,s.useProps)("ScrollAreaRoot",x,e),[d,f]=(0,o.useState)(null),[m,h]=(0,o.useState)(null),[v,g]=(0,o.useState)(null),[y,b]=(0,o.useState)(null),[S,R]=(0,o.useState)(null),[C,T]=(0,o.useState)(0),[E,A]=(0,o.useState)(0),[M,P]=(0,o.useState)(!1),[j,k]=(0,o.useState)(!1),L=(0,w.useMergedRef)(t,e=>f(e));return(0,r.jsx)(p,{value:{type:n,scrollHideDelay:l,scrollArea:d,viewport:m,onViewportChange:h,content:v,onContentChange:g,scrollbarX:y,onScrollbarXChange:b,scrollbarXEnabled:M,onScrollbarXEnabledChange:P,scrollbarY:S,onScrollbarYChange:R,scrollbarYEnabled:j,onScrollbarYEnabledChange:k,onCornerWidthChange:T,onCornerHeightChange:A,getStyles:a},children:(0,r.jsx)(c.Box,{...u,ref:L,__vars:{"--sa-corner-width":"xy"!==i?"0px":"".concat(C,"px"),"--sa-corner-height":"xy"!==i?"0px":"".concat(E,"px")}})})});S.displayName="@mantine/core/ScrollAreaRoot";var R=n(40613),C=n(53304);function T(e,t){let n=e/t;return Number.isNaN(n)?0:n}function E(e){let t=T(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function A(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function M(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",r=E(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=function(e,t){let[n,r]=t;return Math.min(r,Math.max(n,e))}(e,"ltr"===n?[0,i]:[-1*i,0]);return A([0,i],[0,l-r])(a)}var P=n(20678);function j(e,t){let{checkForDefaultPrevented:n=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=>{null==e||e(r),!1!==n&&r.defaultPrevented||null==t||t(r)}}let[k,L]=(0,f.createSafeContext)("ScrollAreaScrollbar was not found in tree"),B=(0,o.forwardRef)((e,t)=>{let{sizes:n,hasThumb:l,onThumbChange:i,onThumbPointerUp:a,onThumbPointerDown:s,onThumbPositionChange:u,onDragScroll:c,onWheelScroll:d,onResize:f,...p}=e,v=m(),[y,b]=(0,o.useState)(null),x=(0,w.useMergedRef)(t,e=>b(e)),S=(0,o.useRef)(null),C=(0,o.useRef)(""),{viewport:T}=v,E=n.content-n.viewport,A=(0,h.useCallbackRef)(d),M=(0,h.useCallbackRef)(u),P=(0,R.useDebouncedCallback)(f,10),L=e=>{S.current&&c({x:e.clientX-S.current.left,y:e.clientY-S.current.top})};return(0,o.useEffect)(()=>{let e=e=>{let t=e.target;(null==y?void 0:y.contains(t))&&A(e,E)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[T,y,E,A]),(0,o.useEffect)(M,[n,M]),g(y,P),g(v.content,P),(0,r.jsx)(k,{value:{scrollbar:y,hasThumb:l,onThumbChange:(0,h.useCallbackRef)(i),onThumbPointerUp:(0,h.useCallbackRef)(a),onThumbPositionChange:M,onThumbPointerDown:(0,h.useCallbackRef)(s)},children:(0,r.jsx)("div",{...p,ref:x,"data-mantine-scrollbar":!0,style:{position:"absolute",...p.style},onPointerDown:j(e.onPointerDown,e=>{e.preventDefault(),0===e.button&&(e.target.setPointerCapture(e.pointerId),S.current=y.getBoundingClientRect(),C.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",L(e))}),onPointerMove:j(e.onPointerMove,L),onPointerUp:j(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(e.preventDefault(),t.releasePointerCapture(e.pointerId))}),onLostPointerCapture:()=>{document.body.style.webkitUserSelect=C.current,S.current=null}})})}),D=(0,o.forwardRef)((e,t)=>{let{sizes:n,onSizesChange:l,style:i,...a}=e,s=m(),[u,c]=(0,o.useState)(),d=(0,o.useRef)(null),f=(0,w.useMergedRef)(t,d,s.onScrollbarXChange);return(0,o.useEffect)(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,r.jsx)(B,{"data-orientation":"horizontal",...a,ref:f,sizes:n,style:{...i,"--sa-thumb-width":"".concat(E(n),"px")},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(s.viewport){let r=s.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&l({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:(0,P.R)(u.paddingLeft),paddingEnd:(0,P.R)(u.paddingRight)}})}})});D.displayName="@mantine/core/ScrollAreaScrollbarX";let N=(0,o.forwardRef)((e,t)=>{let{sizes:n,onSizesChange:l,style:i,...a}=e,s=m(),[u,c]=(0,o.useState)(),d=(0,o.useRef)(null),f=(0,w.useMergedRef)(t,d,s.onScrollbarYChange);return(0,o.useEffect)(()=>{d.current&&c(window.getComputedStyle(d.current))},[]),(0,r.jsx)(B,{...a,"data-orientation":"vertical",ref:f,sizes:n,style:{"--sa-thumb-height":"".concat(E(n),"px"),...i},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(s.viewport){let r=s.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&l({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:(0,P.R)(u.paddingTop),paddingEnd:(0,P.R)(u.paddingBottom)}})}})});N.displayName="@mantine/core/ScrollAreaScrollbarY";let z=(0,o.forwardRef)((e,t)=>{let{orientation:n="vertical",...l}=e,{dir:i}=(0,C.useDirection)(),a=m(),s=(0,o.useRef)(null),u=(0,o.useRef)(0),[c,d]=(0,o.useState)({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),f=T(c.viewport,c.content),p={...l,sizes:c,onSizesChange:d,hasThumb:!!(f>0&&f<1),onThumbChange:e=>{s.current=e},onThumbPointerUp:()=>{u.current=0},onThumbPointerDown:e=>{u.current=e}},h=(e,t)=>(function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=E(n),l=t||o/2,i=n.scrollbar.paddingStart+l,a=n.scrollbar.size-n.scrollbar.paddingEnd-(o-l),s=n.content-n.viewport;return A([i,a],"ltr"===r?[0,s]:[-1*s,0])(e)})(e,u.current,c,t);return"horizontal"===n?(0,r.jsx)(D,{...p,ref:t,onThumbPositionChange:()=>{if(a.viewport&&s.current){let e=M(a.viewport.scrollLeft,c,i);s.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollLeft=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollLeft=h(e,i))}}):"vertical"===n?(0,r.jsx)(N,{...p,ref:t,onThumbPositionChange:()=>{if(a.viewport&&s.current){let e=M(a.viewport.scrollTop,c);0===c.scrollbar.size?s.current.style.setProperty("--thumb-opacity","0"):s.current.style.setProperty("--thumb-opacity","1"),s.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollTop=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollTop=h(e))}}):null});z.displayName="@mantine/core/ScrollAreaScrollbarVisible";let O=(0,o.forwardRef)((e,t)=>{let n=m(),{forceMount:l,...i}=e,[a,s]=(0,o.useState)(!1),u="horizontal"===e.orientation,c=(0,R.useDebouncedCallback)(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;s(u?e:t)}},10);return(g(n.viewport,c),g(n.content,c),l||a)?(0,r.jsx)(z,{"data-state":a?"visible":"hidden",...i,ref:t}):null});O.displayName="@mantine/core/ScrollAreaScrollbarAuto";let F=(0,o.forwardRef)((e,t)=>{let{forceMount:n,...l}=e,i=m(),[a,s]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{let{scrollArea:e}=i,t=0;if(e){let n=()=>{window.clearTimeout(t),s(!0)},r=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[i.scrollArea,i.scrollHideDelay]),n||a)?(0,r.jsx)(O,{"data-state":a?"visible":"hidden",...l,ref:t}):null});F.displayName="@mantine/core/ScrollAreaScrollbarHover";let H=(0,o.forwardRef)((e,t)=>{let{forceMount:n,...l}=e,i=m(),a="horizontal"===e.orientation,[s,u]=(0,o.useState)("hidden"),c=(0,R.useDebouncedCallback)(()=>u("idle"),100);return((0,o.useEffect)(()=>{if("idle"===s){let e=window.setTimeout(()=>u("hidden"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[s,i.scrollHideDelay]),(0,o.useEffect)(()=>{let{viewport:e}=i,t=a?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(u("scrolling"),c()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[i.viewport,a,c]),n||"hidden"!==s)?(0,r.jsx)(z,{"data-state":"hidden"===s?"hidden":"visible",...l,ref:t,onPointerEnter:j(e.onPointerEnter,()=>u("interacting")),onPointerLeave:j(e.onPointerLeave,()=>u("idle"))}):null}),_=(0,o.forwardRef)((e,t)=>{let{forceMount:n,...l}=e,i=m(),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:s}=i,u="horizontal"===e.orientation;return(0,o.useEffect)(()=>(u?a(!0):s(!0),()=>{u?a(!1):s(!1)}),[u,a,s]),"hover"===i.type?(0,r.jsx)(F,{...l,ref:t,forceMount:n}):"scroll"===i.type?(0,r.jsx)(H,{...l,ref:t,forceMount:n}):"auto"===i.type?(0,r.jsx)(O,{...l,ref:t,forceMount:n}):"always"===i.type?(0,r.jsx)(z,{...l,ref:t}):null});_.displayName="@mantine/core/ScrollAreaScrollbar";let I=(0,o.forwardRef)((e,t)=>{let{style:n,...l}=e,i=m(),a=L(),{onThumbPositionChange:s}=a,u=(0,w.useMergedRef)(t,e=>a.onThumbChange(e)),c=(0,o.useRef)(void 0),d=(0,R.useDebouncedCallback)(()=>{c.current&&(c.current(),c.current=void 0)},100);return(0,o.useEffect)(()=>{let{viewport:e}=i;if(e){let t=()=>{d(),c.current||(c.current=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=n.left!==l.left,a=n.top!==l.top;(i||a)&&t(),n=l,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)}(e,s),s())};return s(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[i.viewport,d,s]),(0,r.jsx)("div",{"data-state":a.hasThumb?"visible":"hidden",...l,ref:u,style:{width:"var(--sa-thumb-width)",height:"var(--sa-thumb-height)",...n},onPointerDownCapture:j(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;a.onThumbPointerDown({x:n,y:r})}),onPointerUp:j(e.onPointerUp,a.onThumbPointerUp)})});I.displayName="@mantine/core/ScrollAreaThumb";let W=(0,o.forwardRef)((e,t)=>{let{forceMount:n,...o}=e,l=L();return n||l.hasThumb?(0,r.jsx)(I,{ref:t,...o}):null});W.displayName="@mantine/core/ScrollAreaThumb";let U=(0,o.forwardRef)((e,t)=>{let{children:n,style:o,...l}=e,i=m(),a=(0,w.useMergedRef)(t,i.onViewportChange);return(0,r.jsx)(c.Box,{...l,ref:a,style:{overflowX:i.scrollbarXEnabled?"scroll":"hidden",overflowY:i.scrollbarYEnabled?"scroll":"hidden",...o},children:(0,r.jsx)("div",{...i.getStyles("content"),ref:i.onContentChange,children:n})})});U.displayName="@mantine/core/ScrollAreaViewport";var q={root:"m_d57069b5",viewport:"m_c0783ff9",viewportInner:"m_f8f631dd",scrollbar:"m_c44ba933",thumb:"m_d8b5e363",corner:"m_21657268",content:"m_b1336c6"};let V={scrollHideDelay:1e3,type:"hover",scrollbars:"xy"},Y=(0,a.createVarsResolver)((e,t)=>{let{scrollbarSize:n,overscrollBehavior:r}=t;return{root:{"--scrollarea-scrollbar-size":(0,i.D)(n),"--scrollarea-over-scroll-behavior":r}}}),Z=(0,d.factory)((e,t)=>{let n=(0,s.useProps)("ScrollArea",V,e),{classNames:i,className:a,style:c,styles:d,unstyled:f,scrollbarSize:p,vars:m,type:h,scrollHideDelay:v,viewportProps:g,viewportRef:y,onScrollPositionChange:w,children:x,offsetScrollbars:R,scrollbars:C,onBottomReached:T,onTopReached:E,overscrollBehavior:A,...M}=n,[P,j]=(0,o.useState)(!1),[k,L]=(0,o.useState)(!1),[B,D]=(0,o.useState)(!1),N=(0,u.useStyles)({name:"ScrollArea",props:n,classes:q,className:a,style:c,classNames:i,styles:d,unstyled:f,vars:m,varsResolver:Y}),z=(0,o.useRef)(null),O=(0,l.SV)([y,z]);return(0,o.useEffect)(()=>{if(!z.current||"present"!==R)return;let e=z.current,t=new ResizeObserver(()=>{let{scrollHeight:t,clientHeight:n,scrollWidth:r,clientWidth:o}=e;L(t>n),D(r>o)});return t.observe(e),()=>t.disconnect()},[z,R]),(0,r.jsxs)(S,{getStyles:N,type:"never"===h?"always":h,scrollHideDelay:v,ref:t,scrollbars:C,...N("root"),...M,children:[(0,r.jsx)(U,{...g,...N("viewport",{style:null==g?void 0:g.style}),ref:O,"data-offset-scrollbars":!0===R?"xy":R||void 0,"data-scrollbars":C||void 0,"data-horizontal-hidden":"present"!==R||B?void 0:"true","data-vertical-hidden":"present"!==R||k?void 0:"true",onScroll:e=>{var t;null==g||null==(t=g.onScroll)||t.call(g,e),null==w||w({x:e.currentTarget.scrollLeft,y:e.currentTarget.scrollTop});let{scrollTop:n,scrollHeight:r,clientHeight:o}=e.currentTarget;n-(r-o)>=-.6&&(null==T||T()),0===n&&(null==E||E())},children:x}),("xy"===C||"x"===C)&&(0,r.jsx)(_,{...N("scrollbar"),orientation:"horizontal","data-hidden":"never"===h||"present"===R&&!B||void 0,forceMount:!0,onMouseEnter:()=>j(!0),onMouseLeave:()=>j(!1),children:(0,r.jsx)(W,{...N("thumb")})}),("xy"===C||"y"===C)&&(0,r.jsx)(_,{...N("scrollbar"),orientation:"vertical","data-hidden":"never"===h||"present"===R&&!k||void 0,forceMount:!0,onMouseEnter:()=>j(!0),onMouseLeave:()=>j(!1),children:(0,r.jsx)(W,{...N("thumb")})}),(0,r.jsx)(b,{...N("corner"),"data-hovered":P||void 0,"data-hidden":"never"===h||void 0})]})});Z.displayName="@mantine/core/ScrollArea";let X=(0,d.factory)((e,t)=>{let{children:n,classNames:o,styles:l,scrollbarSize:i,scrollHideDelay:a,type:u,dir:d,offsetScrollbars:f,viewportRef:p,onScrollPositionChange:m,unstyled:h,variant:v,viewportProps:g,scrollbars:y,style:b,vars:w,onBottomReached:x,onTopReached:S,...R}=(0,s.useProps)("ScrollAreaAutosize",V,e);return(0,r.jsx)(c.Box,{...R,ref:t,style:[{display:"flex",overflow:"auto"},b],children:(0,r.jsx)(c.Box,{style:{display:"flex",flexDirection:"column",flex:1},children:(0,r.jsx)(Z,{classNames:o,styles:l,scrollHideDelay:a,scrollbarSize:i,type:u,dir:d,offsetScrollbars:f,viewportRef:p,onScrollPositionChange:m,unstyled:h,variant:v,viewportProps:g,vars:w,scrollbars:y,onBottomReached:x,onTopReached:S,children:n})})})});Z.classes=q,X.displayName="@mantine/core/ScrollAreaAutosize",X.classes=q,Z.Autosize=X},76492:(e,t,n)=>{n.d(t,{UE:()=>G,ll:()=>q,rD:()=>K,UU:()=>Z,jD:()=>$,mG:()=>Q,ER:()=>J,cY:()=>V,BN:()=>Y,Ej:()=>X});let r=["top","right","bottom","left"],o=Math.min,l=Math.max,i=Math.round,a=Math.floor,s=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function h(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(f(e))?"y":"x"}function g(e){return e.replace(/start|end/g,e=>c[e])}function y(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function w(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function x(e,t,n){let r,{reference:o,floating:l}=e,i=v(t),a=m(v(t)),s=h(a),u=f(t),c="y"===i,d=o.x+o.width/2-l.width/2,g=o.y+o.height/2-l.height/2,y=o[s]/2-l[s]/2;switch(u){case"top":r={x:d,y:o.y-l.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:g};break;case"left":r={x:o.x-l.width,y:g};break;default:r={x:o.x,y:o.y}}switch(p(t)){case"start":r[a]-=y*(n&&c?-1:1);break;case"end":r[a]+=y*(n&&c?-1:1)}return r}let S=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=x(u,r,s),f=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:l,fn:h}=a[n],{x:v,y:g,data:y,reset:b}=await h({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[l]:{...p[l],...y}},b&&m<=50&&(m++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=x(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=d(t,e),h=b(m),v=a[p?"floating"===f?"reference":"floating":f],g=w(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(v)))||n?v:v.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===f?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),S=await (null==l.isElement?void 0:l.isElement(x))&&await (null==l.getScale?void 0:l.getScale(x))||{x:1,y:1},R=w(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:x,strategy:s}):y);return{top:(g.top-R.top+h.top)/S.y,bottom:(R.bottom-g.bottom+h.bottom)/S.y,left:(g.left-R.left+h.left)/S.x,right:(R.right-g.right+h.right)/S.x}}function C(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return r.some(t=>e[t]>=0)}function E(e){let t=o(...e.map(e=>e.left)),n=o(...e.map(e=>e.top));return{x:t,y:n,width:l(...e.map(e=>e.right))-t,height:l(...e.map(e=>e.bottom))-n}}async function A(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=f(n),a=p(n),s="y"===v(n),u=["left","top"].includes(i)?-1:1,c=l&&s?-1:1,m=d(t,e),{mainAxis:h,crossAxis:g,alignmentAxis:y}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return a&&"number"==typeof y&&(g="end"===a?-1*y:y),s?{x:g*c,y:h*u}:{x:h*u,y:g*c}}var M=n(86301);function P(e){let t=(0,M.L9)(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=(0,M.sb)(e),l=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=i(n)!==l||i(r)!==a;return s&&(n=l,r=a),{width:n,height:r,$:s}}function j(e){return(0,M.vq)(e)?e:e.contextElement}function k(e){let t=j(e);if(!(0,M.sb)(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=P(t),a=(l?i(n.width):n.width)/r,u=(l?i(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let L=s(0);function B(e){let t=(0,M.zk)(e);return(0,M.Tc)()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:L}function D(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=j(e),a=s(1);t&&(r?(0,M.vq)(r)&&(a=k(r)):a=k(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===(0,M.zk)(i))&&o)?B(i):s(0),c=(l.left+u.x)/a.x,d=(l.top+u.y)/a.y,f=l.width/a.x,p=l.height/a.y;if(i){let e=(0,M.zk)(i),t=r&&(0,M.vq)(r)?(0,M.zk)(r):r,n=e,o=(0,M._m)(n);for(;o&&r&&t!==n;){let e=k(o),t=o.getBoundingClientRect(),r=(0,M.L9)(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=l,d+=i,n=(0,M.zk)(o),o=(0,M._m)(n)}}return w({width:f,height:p,x:c,y:d})}function N(e,t){let n=(0,M.CP)(e).scrollLeft;return t?t.left+n:D((0,M.ep)(e)).left+n}function z(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:N(e,r)),y:r.top+t.scrollTop}}function O(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=(0,M.zk)(e),r=(0,M.ep)(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=(0,M.Tc)();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=(0,M.ep)(e),n=(0,M.CP)(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+N(e),s=-n.scrollTop;return"rtl"===(0,M.L9)(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}((0,M.ep)(e));else if((0,M.vq)(t))r=function(e,t){let n=D(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=(0,M.sb)(e)?k(e):s(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=B(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return w(r)}function F(e){return"static"===(0,M.L9)(e).position}function H(e,t){if(!(0,M.sb)(e)||"fixed"===(0,M.L9)(e).position)return null;if(t)return t(e);let n=e.offsetParent;return(0,M.ep)(e)===n&&(n=n.ownerDocument.body),n}function _(e,t){let n=(0,M.zk)(e);if((0,M.Tf)(e))return n;if(!(0,M.sb)(e)){let t=(0,M.$4)(e);for(;t&&!(0,M.eu)(t);){if((0,M.vq)(t)&&!F(t))return t;t=(0,M.$4)(t)}return n}let r=H(e,t);for(;r&&(0,M.Lv)(r)&&F(r);)r=H(r,t);return r&&(0,M.eu)(r)&&F(r)&&!(0,M.sQ)(r)?n:r||(0,M.gJ)(e)||n}let I=async function(e){let t=this.getOffsetParent||_,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=(0,M.sb)(t),o=(0,M.ep)(t),l="fixed"===n,i=D(e,!0,l,t),a={scrollLeft:0,scrollTop:0},u=s(0);if(r||!r&&!l)if(("body"!==(0,M.mq)(t)||(0,M.ZU)(o))&&(a=(0,M.CP)(t)),r){let e=D(t,!0,l,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=N(o));l&&!r&&o&&(u.x=N(o));let c=!o||r||l?s(0):z(o,a);return{x:i.left+a.scrollLeft-u.x-c.x,y:i.top+a.scrollTop-u.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},W={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=(0,M.ep)(r),a=!!t&&(0,M.Tf)(t.floating);if(r===i||a&&l)return n;let u={scrollLeft:0,scrollTop:0},c=s(1),d=s(0),f=(0,M.sb)(r);if((f||!f&&!l)&&(("body"!==(0,M.mq)(r)||(0,M.ZU)(i))&&(u=(0,M.CP)(r)),(0,M.sb)(r))){let e=D(r);c=k(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!i||f||l?s(0):z(i,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-u.scrollTop*c.y+d.y+p.y}},getDocumentElement:M.ep,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?(0,M.Tf)(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=(0,M.v9)(e,[],!1).filter(e=>(0,M.vq)(e)&&"body"!==(0,M.mq)(e)),o=null,l="fixed"===(0,M.L9)(e).position,i=l?(0,M.$4)(e):e;for(;(0,M.vq)(i)&&!(0,M.eu)(i);){let t=(0,M.L9)(i),n=(0,M.sQ)(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||(0,M.ZU)(i)&&!n&&function e(t,n){let r=(0,M.$4)(t);return!(r===n||!(0,M.vq)(r)||(0,M.eu)(r))&&("fixed"===(0,M.L9)(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=(0,M.$4)(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{let r=O(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},O(t,s,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:_,getElementRects:I,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=P(e);return{width:t,height:n}},getScale:k,isElement:M.vq,isRTL:function(e){return"rtl"===(0,M.L9)(e).direction}};function U(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function q(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:s=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=j(e),m=s||u?[...p?(0,M.v9)(p):[],...(0,M.v9)(t)]:[];m.forEach(e=>{s&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let h=p&&d?function(e,t){let n,r=null,i=(0,M.ep)(e);function s(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),s();let f=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=f;if(c||t(),!h||!v)return;let g=a(m),y=a(i.clientWidth-(p+h)),b={rootMargin:-g+"px "+-y+"px "+-a(i.clientHeight-(m+v))+"px "+-a(p)+"px",threshold:l(0,o(1,d))||1},w=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!w)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||U(f,e.getBoundingClientRect())||u(),w=!1}try{r=new IntersectionObserver(x,{...b,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(x,b)}r.observe(e)}(!0),s}(p,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?D(e):null;return f&&function t(){let r=D(e);y&&!U(y,r)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{s&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(i)}}let V=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await A(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}},Y=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),p={x:n,y:r},h=await R(t,c),g=v(f(i)),y=m(g),b=p[y],w=p[g];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=b+h[e],r=b-h[t];b=l(n,o(b,r))}if(s){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,o(w,r))}let x=u.fn({...t,[y]:b,[g]:w});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:a,[g]:s}}}}}},Z=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:b,elements:w}=t,{mainAxis:x=!0,crossAxis:S=!0,fallbackPlacements:C,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:A=!0,...M}=d(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let P=f(a),j=v(c),k=f(c)===c,L=await (null==b.isRTL?void 0:b.isRTL(w.floating)),B=C||(k||!A?[y(c)]:function(e){let t=y(e);return[g(e),t,g(t)]}(c)),D="none"!==E;!C&&D&&B.push(...function(e,t,n,r){let o=p(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(f(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(g)))),l}(c,A,E,L));let N=[c,...B],z=await R(t,M),O=[],F=(null==(r=s.flip)?void 0:r.overflows)||[];if(x&&O.push(z[P]),S){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),o=m(v(e)),l=h(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=y(i)),[i,y(i)]}(a,u,L);O.push(z[e[0]],z[e[1]])}if(F=[...F,{placement:a,overflows:O}],!O.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=N[e];if(t&&("alignment"!==S||j===v(t)||F.every(e=>e.overflows[0]>0&&v(e.placement)===j)))return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(l=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(T){case"bestFit":{let e=null==(i=F.filter(e=>{if(D){let t=v(e.placement);return t===j||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}},X=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:s,rects:u,platform:c,elements:m}=t,{apply:h=()=>{},...g}=d(e,t),y=await R(t,g),b=f(s),w=p(s),x="y"===v(s),{width:S,height:C}=u.floating;"top"===b||"bottom"===b?(i=b,a=w===(await (null==c.isRTL?void 0:c.isRTL(m.floating))?"start":"end")?"left":"right"):(a=b,i="end"===w?"top":"bottom");let T=C-y.top-y.bottom,E=S-y.left-y.right,A=o(C-y[i],T),M=o(S-y[a],E),P=!t.middlewareData.shift,j=A,k=M;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=E),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(j=T),P&&!w){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);x?k=S-2*(0!==e||0!==t?e+t:l(y.left,y.right)):j=C-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await h({...t,availableWidth:k,availableHeight:j});let L=await c.getDimensions(m.floating);return S!==L.width||C!==L.height?{reset:{rects:!0}}:{}}}},$=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=C(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=C(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}},G=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:f,padding:g=0}=d(e,t)||{};if(null==f)return{};let y=b(g),w={x:n,y:r},x=m(v(i)),S=h(x),R=await s.getDimensions(f),C="y"===x,T=C?"clientHeight":"clientWidth",E=a.reference[S]+a.reference[x]-w[x]-a.floating[S],A=w[x]-a.reference[x],M=await (null==s.getOffsetParent?void 0:s.getOffsetParent(f)),P=M?M[T]:0;P&&await (null==s.isElement?void 0:s.isElement(M))||(P=u.floating[T]||a.floating[S]);let j=P/2-R[S]/2-1,k=o(y[C?"top":"left"],j),L=o(y[C?"bottom":"right"],j),B=P-R[S]-L,D=P/2-R[S]/2+(E/2-A/2),N=l(k,o(D,B)),z=!c.arrow&&null!=p(i)&&D!==N&&a.reference[S]/2-(D<k?k:L)-R[S]/2<0,O=z?D<k?D-k:D-B:0;return{[x]:w[x]+O,data:{[x]:N,centerOffset:D-N-O,...z&&{alignmentOffset:O}},reset:z}}}),Q=function(e){return void 0===e&&(e={}),{name:"inline",options:e,async fn(t){let{placement:n,elements:r,rects:i,platform:a,strategy:s}=t,{padding:u=2,x:c,y:p}=d(e,t),m=Array.from(await (null==a.getClientRects?void 0:a.getClientRects(r.reference))||[]),h=function(e){let t=e.slice().sort((e,t)=>e.y-t.y),n=[],r=null;for(let e=0;e<t.length;e++){let o=t[e];!r||o.y-r.y>r.height/2?n.push([o]):n[n.length-1].push(o),r=o}return n.map(e=>w(E(e)))}(m),g=w(E(m)),y=b(u),x=await a.getElementRects({reference:{getBoundingClientRect:function(){if(2===h.length&&h[0].left>h[1].right&&null!=c&&null!=p)return h.find(e=>c>e.left-y.left&&c<e.right+y.right&&p>e.top-y.top&&p<e.bottom+y.bottom)||g;if(h.length>=2){if("y"===v(n)){let e=h[0],t=h[h.length-1],r="top"===f(n),o=e.top,l=t.bottom,i=r?e.left:t.left,a=r?e.right:t.right;return{top:o,bottom:l,left:i,right:a,width:a-i,height:l-o,x:i,y:o}}let e="left"===f(n),t=l(...h.map(e=>e.right)),r=o(...h.map(e=>e.left)),i=h.filter(n=>e?n.left===r:n.right===t),a=i[0].top,s=i[i.length-1].bottom;return{top:a,bottom:s,left:r,right:t,width:t-r,height:s-a,x:r,y:a}}return g}},floating:r.floating,strategy:s});return i.reference.x!==x.reference.x||i.reference.y!==x.reference.y||i.reference.width!==x.reference.width||i.reference.height!==x.reference.height?{reset:{rects:x}}:{}}}},J=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=d(e,t),c={x:n,y:r},p=v(o),h=m(p),g=c[h],y=c[p],b=d(a,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(s){let e="y"===h?"height":"width",t=l.reference[h]-l.floating[e]+w.mainAxis,n=l.reference[h]+l.reference[e]-w.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var x,S;let e="y"===h?"width":"height",t=["top","left"].includes(f(o)),n=l.reference[p]-l.floating[e]+(t&&(null==(x=i.offset)?void 0:x[p])||0)+(t?0:w.crossAxis),r=l.reference[p]+l.reference[e]+(t?0:(null==(S=i.offset)?void 0:S[p])||0)-(t?w.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:g,[p]:y}}}},K=(e,t,n)=>{let r=new Map,o={platform:W,...n},l={...o.platform,_c:r};return S(e,t,{...o,platform:l})}},79407:(e,t,n)=>{n.d(t,{Portal:()=>f});var r=n(95155),o=n(12115),l=n(47650),i=n(73141),a=n(88551),s=n(43664),u=n(36960);function c(e){let t=document.createElement("div");return t.setAttribute("data-portal","true"),"string"==typeof e.className&&t.classList.add(...e.className.split(" ").filter(Boolean)),"object"==typeof e.style&&Object.assign(t.style,e.style),"string"==typeof e.id&&t.setAttribute("id",e.id),t}let d={reuseTargetNode:!0},f=(0,u.factory)((e,t)=>{let{children:n,target:u,reuseTargetNode:f,...p}=(0,s.useProps)("Portal",d,e),[m,h]=(0,o.useState)(!1),v=(0,o.useRef)(null);return((0,i.useIsomorphicEffect)(()=>(h(!0),v.current=function(e){let{target:t,reuseTargetNode:n,...r}=e;if(t)return"string"==typeof t?document.querySelector(t)||c(r):t;if(n){let e=document.querySelector("[data-mantine-shared-portal-node]");if(e)return e;let t=c(r);return t.setAttribute("data-mantine-shared-portal-node","true"),document.body.appendChild(t),t}return c(r)}({target:u,reuseTargetNode:f,...p}),(0,a.assignRef)(t,v.current),u||f||!v.current||document.body.appendChild(v.current),()=>{u||f||!v.current||document.body.removeChild(v.current)}),[u]),m&&v.current)?(0,l.createPortal)((0,r.jsx)(r.Fragment,{children:n}),v.current):null});f.displayName="@mantine/core/Portal"},79630:(e,t,n)=>{n.d(t,{A:()=>r});function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}},84945:(e,t,n)=>{n.d(t,{BN:()=>m,ER:()=>h,Ej:()=>g,UE:()=>w,UU:()=>v,cY:()=>p,jD:()=>y,mG:()=>b,we:()=>d});var r=n(76492),o=n(12115),l=n(47650),i="undefined"!=typeof document?o.useLayoutEffect:function(){};function a(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!a(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!a(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function s(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function u(e,t){let n=s(e);return Math.round(t*n)/n}function c(e){let t=o.useRef(e);return i(()=>{t.current=e}),t}function d(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:d=[],platform:f,elements:{reference:p,floating:m}={},transform:h=!0,whileElementsMounted:v,open:g}=e,[y,b]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[w,x]=o.useState(d);a(w,d)||x(d);let[S,R]=o.useState(null),[C,T]=o.useState(null),E=o.useCallback(e=>{e!==j.current&&(j.current=e,R(e))},[]),A=o.useCallback(e=>{e!==k.current&&(k.current=e,T(e))},[]),M=p||S,P=m||C,j=o.useRef(null),k=o.useRef(null),L=o.useRef(y),B=null!=v,D=c(v),N=c(f),z=c(g),O=o.useCallback(()=>{if(!j.current||!k.current)return;let e={placement:t,strategy:n,middleware:w};N.current&&(e.platform=N.current),(0,r.rD)(j.current,k.current,e).then(e=>{let t={...e,isPositioned:!1!==z.current};F.current&&!a(L.current,t)&&(L.current=t,l.flushSync(()=>{b(t)}))})},[w,t,n,N,z]);i(()=>{!1===g&&L.current.isPositioned&&(L.current.isPositioned=!1,b(e=>({...e,isPositioned:!1})))},[g]);let F=o.useRef(!1);i(()=>(F.current=!0,()=>{F.current=!1}),[]),i(()=>{if(M&&(j.current=M),P&&(k.current=P),M&&P){if(D.current)return D.current(M,P,O);O()}},[M,P,O,D,B]);let H=o.useMemo(()=>({reference:j,floating:k,setReference:E,setFloating:A}),[E,A]),_=o.useMemo(()=>({reference:M,floating:P}),[M,P]),I=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!_.floating)return e;let t=u(_.floating,y.x),r=u(_.floating,y.y);return h?{...e,transform:"translate("+t+"px, "+r+"px)",...s(_.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,h,_.floating,y.x,y.y]);return o.useMemo(()=>({...y,update:O,refs:H,elements:_,floatingStyles:I}),[y,O,H,_,I])}let f=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:o}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:o}).fn(t):{}:n?(0,r.UE)({element:n,padding:o}).fn(t):{}}}),p=(e,t)=>({...(0,r.cY)(e),options:[e,t]}),m=(e,t)=>({...(0,r.BN)(e),options:[e,t]}),h=(e,t)=>({...(0,r.ER)(e),options:[e,t]}),v=(e,t)=>({...(0,r.UU)(e),options:[e,t]}),g=(e,t)=>({...(0,r.Ej)(e),options:[e,t]}),y=(e,t)=>({...(0,r.jD)(e),options:[e,t]}),b=(e,t)=>({...(0,r.mG)(e),options:[e,t]}),w=(e,t)=>({...f(e),options:[e,t]})},86301:(e,t,n)=>{function r(){return"undefined"!=typeof window}function o(e){return a(e)?(e.nodeName||"").toLowerCase():"#document"}function l(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function i(e){var t;return null==(t=(a(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function a(e){return!!r()&&(e instanceof Node||e instanceof l(e).Node)}function s(e){return!!r()&&(e instanceof Element||e instanceof l(e).Element)}function u(e){return!!r()&&(e instanceof HTMLElement||e instanceof l(e).HTMLElement)}function c(e){return!!r()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof l(e).ShadowRoot)}function d(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=y(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function f(e){return["table","td","th"].includes(o(e))}function p(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function m(e){let t=v(),n=s(e)?y(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function h(e){let t=w(e);for(;u(t)&&!g(t);){if(m(t))return t;if(p(t))break;t=w(t)}return null}function v(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function g(e){return["html","body","#document"].includes(o(e))}function y(e){return l(e).getComputedStyle(e)}function b(e){return s(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function w(e){if("html"===o(e))return e;let t=e.assignedSlot||e.parentNode||c(e)&&e.host||i(e);return c(t)?t.host:t}function x(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}n.d(t,{$4:()=>w,CP:()=>b,L9:()=>y,Lv:()=>f,Ng:()=>c,Tc:()=>v,Tf:()=>p,ZU:()=>d,_m:()=>x,ep:()=>i,eu:()=>g,gJ:()=>h,mq:()=>o,sQ:()=>m,sb:()=>u,v9:()=>function e(t,n,r){var o;void 0===n&&(n=[]),void 0===r&&(r=!0);let i=function e(t){let n=w(t);return g(n)?t.ownerDocument?t.ownerDocument.body:t.body:u(n)&&d(n)?n:e(n)}(t),a=i===(null==(o=t.ownerDocument)?void 0:o.body),s=l(i);if(a){let t=x(s);return n.concat(s,s.visualViewport||[],d(i)?i:[],t&&r?e(t):[])}return n.concat(i,e(i,[],r))},vq:()=>s,zk:()=>l})},86765:(e,t,n)=>{n.d(t,{ModalBody:()=>c});var r=n(95155);n(12115);var o=n(43664),l=n(36960),i=n(48254),a=n(57026),s=n(91688);let u={},c=(0,l.factory)((e,t)=>{let{classNames:n,className:l,style:s,styles:c,vars:d,...f}=(0,o.useProps)("ModalBody",u,e),p=(0,a.k)();return(0,r.jsx)(i.ModalBaseBody,{ref:t,...p.getStyles("body",{classNames:n,style:s,styles:c,className:l}),...f})});c.classes=s.A,c.displayName="@mantine/core/ModalBody"},91033:(e,t,n)=>{n.d(t,{ModalBaseCloseButton:()=>u});var r=n(95155),o=n(12115),l=n(52596),i=n(95642),a=n(2278),s=n(33396);let u=(0,o.forwardRef)((e,t)=>{let{className:n,onClick:o,...u}=e,c=(0,a.x)();return(0,r.jsx)(i.CloseButton,{ref:t,...u,onClick:e=>{c.onClose(),null==o||o(e)},className:(0,l.A)({[s.A.close]:!c.unstyled},n),unstyled:c.unstyled})});u.displayName="@mantine/core/ModalBaseCloseButton"},91558:(e,t,n)=>{n.d(t,{ModalHeader:()=>c});var r=n(95155);n(12115);var o=n(43664),l=n(36960),i=n(40978),a=n(57026),s=n(91688);let u={},c=(0,l.factory)((e,t)=>{let{classNames:n,className:l,style:s,styles:c,vars:d,...f}=(0,o.useProps)("ModalHeader",u,e),p=(0,a.k)();return(0,r.jsx)(i.ModalBaseHeader,{ref:t,...p.getStyles("header",{classNames:n,style:s,styles:c,className:l}),...f})});c.classes=s.A,c.displayName="@mantine/core/ModalHeader"},91688:(e,t,n)=>{n.d(t,{A:()=>r});var r={root:"m_9df02822",content:"m_54c44539",inner:"m_1f958f16",header:"m_d0e2b9cd"}},93495:(e,t,n)=>{n.d(t,{A:()=>r});function r(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}},99026:(e,t,n)=>{n.d(t,{VisuallyHidden:()=>c});var r=n(95155);n(12115);var o=n(43664),l=n(53791),i=n(69604),a=n(36960),s={root:"m_515a97f8"};let u={},c=(0,a.factory)((e,t)=>{let n=(0,o.useProps)("VisuallyHidden",u,e),{classNames:a,className:c,style:d,styles:f,unstyled:p,vars:m,...h}=n,v=(0,l.useStyles)({name:"VisuallyHidden",classes:s,props:n,className:c,style:d,classNames:a,styles:f,unstyled:p});return(0,r.jsx)(i.Box,{component:"span",ref:t,...v("root"),...h})});c.classes=s,c.displayName="@mantine/core/VisuallyHidden"}}]);