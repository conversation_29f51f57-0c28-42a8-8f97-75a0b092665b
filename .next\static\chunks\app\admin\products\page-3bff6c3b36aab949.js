(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[575],{19309:(e,r,t)=>{Promise.resolve().then(t.bind(t,75868))},75868:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>M});var o=t(95155),s=t(12115),l=t(58887),i=t(8141),c=t(83347),n=t(21220),a=t(70112),d=t(93751),u=t(26903),p=t(8593),h=t(74634),m=t(30934),x=t(26029),j=t(81001),g=t(63617),T=t(86467),f=(0,T.A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]]),b=(0,T.A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]]),v=t(78977),y=(0,T.A)("outline","edit","IconEdit",[["path",{d:"M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1",key:"svg-0"}],["path",{d:"M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z",key:"svg-1"}],["path",{d:"M16 5l3 3",key:"svg-2"}]]),S=(0,T.A)("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]]),k=t(11681),P=t(6942),E=t(91423),w=t(41954),I=t(44644),C=t(79085),N=t(11659);function A(e){let{product:r,onSave:t,onCancel:l}=e,[i,c]=(0,s.useState)(!1),d=(0,N.m)({initialValues:{name:(null==r?void 0:r.name)||"",description:(null==r?void 0:r.description)||"",price:(null==r?void 0:r.price)||0,image_url:(null==r?void 0:r.image_url)||"",specs:(null==r?void 0:r.specs)?JSON.stringify(r.specs,null,2):"{}"},validate:{name:e=>e?null:"Product name is required",price:e=>e<=0?"Price must be greater than 0":null,specs:e=>{try{return JSON.parse(e),null}catch(e){return"Invalid JSON format"}}}}),h=async e=>{c(!0);try{let o=null;if(e.specs.trim())try{o=JSON.parse(e.specs)}catch(e){d.setFieldError("specs","Invalid JSON format"),c(!1);return}let s={name:e.name,description:e.description||null,price:e.price,image_url:e.image_url||null,specs:o},l=r?"/api/products/".concat(r.id):"/api/products",i=r?"PUT":"POST",n=await fetch(l,{method:i,headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),a=await n.json();n.ok?(P.notifications.show({title:"Success",message:"Product ".concat(r?"updated":"created"," successfully"),color:"green"}),t()):P.notifications.show({title:"Error",message:a.error||"Failed to ".concat(r?"update":"create"," product"),color:"red"})}catch(e){console.error("Error saving product:",e),P.notifications.show({title:"Error",message:"An unexpected error occurred",color:"red"})}finally{c(!1)}};return(0,o.jsx)("form",{onSubmit:d.onSubmit(h),children:(0,o.jsxs)(n.Stack,{children:[(0,o.jsx)(p.TextInput,{label:"Product Name",placeholder:"Enter product name",required:!0,...d.getInputProps("name")}),(0,o.jsx)(w.Textarea,{label:"Description",placeholder:"Enter product description",rows:3,...d.getInputProps("description")}),(0,o.jsx)(I.NumberInput,{label:"Price",placeholder:"0.00",min:0,step:.01,decimalScale:2,fixedDecimalScale:!0,required:!0,...d.getInputProps("price")}),(0,o.jsx)(p.TextInput,{label:"Image URL",placeholder:"https://example.com/image.jpg",...d.getInputProps("image_url")}),(0,o.jsx)(C.JsonInput,{label:"Specifications (JSON)",placeholder:'{"color": "blue", "size": "medium"}',validationError:"Invalid JSON",formatOnBlur:!0,autosize:!0,minRows:4,...d.getInputProps("specs")}),(0,o.jsxs)(a.Group,{justify:"flex-end",mt:"md",children:[(0,o.jsx)(u.Button,{variant:"subtle",onClick:l,children:"Cancel"}),(0,o.jsxs)(u.Button,{type:"submit",loading:i,children:[r?"Update":"Create"," Product"]})]})]})})}function M(){let[e,r]=(0,s.useState)([]),[t,T]=(0,s.useState)(!0),[w,I]=(0,s.useState)(""),[C,N]=(0,s.useState)(null),[M,{open:z,close:O}]=(0,k.useDisclosure)(!1);(0,s.useEffect)(()=>{_()},[]);let _=async e=>{try{T(!0);let t=e?"/api/products?search=".concat(encodeURIComponent(e)):"/api/products",o=await fetch(t),s=await o.json();o.ok?r(s.products||[]):P.notifications.show({title:"Error",message:s.error||"Failed to load products",color:"red"})}catch(e){console.error("Error loading products:",e),P.notifications.show({title:"Error",message:"Failed to load products",color:"red"})}finally{T(!1)}},D=()=>{_(w)},J=e=>{N(e),z()},F=e=>{E.modals.openConfirmModal({title:"Delete Product",children:(0,o.jsxs)(l.Text,{size:"sm",children:['Are you sure you want to delete "',e.name,'"? This action cannot be undone.']}),labels:{confirm:"Delete",cancel:"Cancel"},confirmProps:{color:"red"},onConfirm:()=>B(e.id)})},B=async e=>{try{let r=await fetch("/api/products/".concat(e),{method:"DELETE"});if(r.ok)P.notifications.show({title:"Success",message:"Product deleted successfully",color:"green"}),_();else{let e=await r.json();P.notifications.show({title:"Error",message:e.error||"Failed to delete product",color:"red"})}}catch(e){console.error("Error deleting product:",e),P.notifications.show({title:"Error",message:"Failed to delete product",color:"red"})}};return t&&0===e.length?(0,o.jsx)(i.Center,{h:400,children:(0,o.jsx)(c.Loader,{size:"lg"})}):(0,o.jsxs)(n.Stack,{children:[(0,o.jsxs)(a.Group,{justify:"space-between",children:[(0,o.jsx)(d.Title,{order:1,children:"Products"}),(0,o.jsx)(u.Button,{leftSection:(0,o.jsx)(f,{size:"1rem"}),onClick:()=>{N(null),z()},children:"Add Product"})]}),(0,o.jsxs)(a.Group,{children:[(0,o.jsx)(p.TextInput,{placeholder:"Search products...",leftSection:(0,o.jsx)(b,{size:"1rem"}),value:w,onChange:e=>I(e.currentTarget.value),onKeyPress:e=>"Enter"===e.key&&D(),style:{flex:1}}),(0,o.jsx)(u.Button,{onClick:D,children:"Search"})]}),0===e.length?(0,o.jsx)(h.Alert,{icon:(0,o.jsx)(v.A,{size:"1rem"}),title:"No products found",color:"blue",children:w?"No products match your search criteria.":'No products have been added yet. Click "Add Product" to get started.'}):(0,o.jsxs)(m.Table,{striped:!0,highlightOnHover:!0,children:[(0,o.jsx)(m.Table.Thead,{children:(0,o.jsxs)(m.Table.Tr,{children:[(0,o.jsx)(m.Table.Th,{children:"Name"}),(0,o.jsx)(m.Table.Th,{children:"Description"}),(0,o.jsx)(m.Table.Th,{children:"Price"}),(0,o.jsx)(m.Table.Th,{children:"Created"}),(0,o.jsx)(m.Table.Th,{children:"Actions"})]})}),(0,o.jsx)(m.Table.Tbody,{children:e.map(e=>(0,o.jsxs)(m.Table.Tr,{children:[(0,o.jsx)(m.Table.Td,{children:(0,o.jsx)(l.Text,{fw:500,children:e.name})}),(0,o.jsx)(m.Table.Td,{children:(0,o.jsx)(l.Text,{size:"sm",c:"dimmed",truncate:!0,children:e.description||"No description"})}),(0,o.jsx)(m.Table.Td,{children:(0,o.jsxs)(x.Badge,{variant:"light",color:"green",children:["$",e.price.toFixed(2)]})}),(0,o.jsx)(m.Table.Td,{children:(0,o.jsx)(l.Text,{size:"sm",c:"dimmed",children:new Date(e.created_at).toLocaleDateString()})}),(0,o.jsx)(m.Table.Td,{children:(0,o.jsxs)(a.Group,{gap:"xs",children:[(0,o.jsx)(j.ActionIcon,{variant:"subtle",color:"blue",onClick:()=>J(e),children:(0,o.jsx)(y,{size:"1rem"})}),(0,o.jsx)(j.ActionIcon,{variant:"subtle",color:"red",onClick:()=>F(e),children:(0,o.jsx)(S,{size:"1rem"})})]})})]},e.id))})]}),(0,o.jsx)(g.Modal,{opened:M,onClose:O,title:C?"Edit Product":"Add Product",size:"lg",children:(0,o.jsx)(A,{product:C,onSave:()=>{O(),_()},onCancel:O})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[545,865,903,913,299,621,191,95,441,684,358],()=>r(19309)),_N_E=e.O()}]);