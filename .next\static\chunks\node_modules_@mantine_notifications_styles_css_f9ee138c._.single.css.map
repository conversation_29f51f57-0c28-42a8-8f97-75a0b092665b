{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@mantine/notifications/styles.css"], "sourcesContent": [".m_b37d9ac7 {\n  width: calc(100% - var(--mantine-spacing-md) * 2);\n  position: fixed;\n  z-index: var(--notifications-z-index);\n  max-width: var(--notifications-container-width);\n}\n\n  .m_b37d9ac7:where([data-position='top-center']) {\n    top: var(--mantine-spacing-md);\n    left: 50%;\n    transform: translateX(-50%);\n  }\n\n  .m_b37d9ac7:where([data-position='top-left']) {\n    top: var(--mantine-spacing-md);\n    left: var(--mantine-spacing-md);\n  }\n\n  .m_b37d9ac7:where([data-position='top-right']) {\n    top: var(--mantine-spacing-md);\n    right: var(--mantine-spacing-md);\n  }\n\n  .m_b37d9ac7:where([data-position='bottom-center']) {\n    bottom: var(--mantine-spacing-md);\n    left: 50%;\n    transform: translateX(-50%);\n  }\n\n  .m_b37d9ac7:where([data-position='bottom-left']) {\n    bottom: var(--mantine-spacing-md);\n    left: var(--mantine-spacing-md);\n  }\n\n  .m_b37d9ac7:where([data-position='bottom-right']) {\n    bottom: var(--mantine-spacing-md);\n    right: var(--mantine-spacing-md);\n  }\n\n.m_5ed0edd0 + .m_5ed0edd0 {\n    margin-top: var(--mantine-spacing-md);\n  }\n"], "names": [], "mappings": "AAAA;;;;;;;AAOE;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;AAKF", "ignoreList": [0]}}]}