{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Container, Title, Text, <PERSON>ton, Stack, Group, Card, Grid, Badge, Loader, Alert } from '@mantine/core';\nimport { IconLogin, IconDashboard, IconPackage, IconMessages, IconCheck, IconX, IconAlertCircle } from '@tabler/icons-react';\nimport Link from 'next/link';\n\ninterface SetupStatus {\n  database: { status: string; details: string };\n  tables: { status: string; details: string };\n  admin_user: { status: string; details: string };\n  llm: { status: string; details: string };\n  facebook: { status: string; details: string };\n}\n\nexport default function Home() {\n  const [setupStatus, setSetupStatus] = useState<SetupStatus | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  useEffect(() => {\n    checkSetupStatus();\n    checkAuthStatus();\n  }, []);\n\n  const checkSetupStatus = async () => {\n    try {\n      const response = await fetch('/api/setup');\n      const data = await response.json();\n      if (data.success) {\n        setSetupStatus(data.results);\n      }\n    } catch (error) {\n      console.error('Setup check failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkAuthStatus = async () => {\n    try {\n      const response = await fetch('/api/auth/me');\n      setIsAuthenticated(response.ok);\n    } catch (error) {\n      setIsAuthenticated(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case 'OK':\n      case 'CONFIGURED':\n      case 'EXISTS':\n        return <Badge color=\"green\" leftSection={<IconCheck size=\"0.8rem\" />}>OK</Badge>;\n      case 'CREATED':\n        return <Badge color=\"blue\" leftSection={<IconCheck size=\"0.8rem\" />}>Created</Badge>;\n      case 'PARTIAL':\n        return <Badge color=\"yellow\" leftSection={<IconAlertCircle size=\"0.8rem\" />}>Partial</Badge>;\n      case 'WEBHOOK_ERROR':\n        return <Badge color=\"orange\" leftSection={<IconAlertCircle size=\"0.8rem\" />}>Webhook Issue</Badge>;\n      case 'NOT_CONFIGURED':\n        return <Badge color=\"gray\" leftSection={<IconAlertCircle size=\"0.8rem\" />}>Not Set</Badge>;\n      case 'ERROR':\n        return <Badge color=\"red\" leftSection={<IconX size=\"0.8rem\" />}>Error</Badge>;\n      default:\n        return <Badge color=\"gray\">Unknown</Badge>;\n    }\n  };\n\n  return (\n    <Container size=\"lg\" py=\"xl\">\n      <Stack align=\"center\" gap=\"xl\">\n        <div style={{ textAlign: 'center' }}>\n          <Title order={1} size=\"3rem\" mb=\"md\">\n            Product Management System\n          </Title>\n          <Text size=\"xl\" c=\"dimmed\" mb=\"xl\">\n            Full-stack Next.js application with Mantine UI, Supabase, and Facebook Messenger integration\n          </Text>\n        </div>\n\n        <Grid>\n          <Grid.Col span={{ base: 12, md: 6 }}>\n            <Card withBorder h=\"100%\">\n              <Stack>\n                <IconPackage size=\"2rem\" color=\"blue\" />\n                <Title order={3}>Product Management</Title>\n                <Text c=\"dimmed\">\n                  Manage your product catalog with full CRUD operations, search functionality, and detailed specifications.\n                </Text>\n              </Stack>\n            </Card>\n          </Grid.Col>\n\n          <Grid.Col span={{ base: 12, md: 6 }}>\n            <Card withBorder h=\"100%\">\n              <Stack>\n                <IconMessages size=\"2rem\" color=\"green\" />\n                <Title order={3}>Message Handling</Title>\n                <Text c=\"dimmed\">\n                  Handle customer messages from Facebook Messenger with automated responses and manual override capabilities.\n                </Text>\n              </Stack>\n            </Card>\n          </Grid.Col>\n\n          <Grid.Col span={{ base: 12, md: 6 }}>\n            <Card withBorder h=\"100%\">\n              <Stack>\n                <IconDashboard size=\"2rem\" color=\"orange\" />\n                <Title order={3}>Admin Dashboard</Title>\n                <Text c=\"dimmed\">\n                  Comprehensive admin interface for managing products, viewing messages, and configuring system settings.\n                </Text>\n              </Stack>\n            </Card>\n          </Grid.Col>\n\n          <Grid.Col span={{ base: 12, md: 6 }}>\n            <Card withBorder h=\"100%\">\n              <Stack>\n                <IconLogin size=\"2rem\" color=\"purple\" />\n                <Title order={3}>Secure Authentication</Title>\n                <Text c=\"dimmed\">\n                  Session-based authentication with JWT tokens and protected admin routes for secure access.\n                </Text>\n              </Stack>\n            </Card>\n          </Grid.Col>\n        </Grid>\n\n        <Group justify=\"center\">\n          {isAuthenticated ? (\n            <Button\n              component={Link}\n              href=\"/admin\"\n              size=\"lg\"\n              leftSection={<IconDashboard size=\"1.2rem\" />}\n            >\n              Go to Admin Dashboard\n            </Button>\n          ) : (\n            <Button\n              component={Link}\n              href=\"/login\"\n              size=\"lg\"\n              leftSection={<IconLogin size=\"1.2rem\" />}\n            >\n              Admin Login\n            </Button>\n          )}\n        </Group>\n\n        {/* System Status */}\n        <Card withBorder bg=\"gray.0\" mt=\"xl\" w=\"100%\">\n          <Stack>\n            <Group justify=\"space-between\">\n              <Title order={4}>System Status</Title>\n              <Button size=\"xs\" variant=\"subtle\" onClick={checkSetupStatus} loading={loading}>\n                Refresh\n              </Button>\n            </Group>\n\n            {loading ? (\n              <Group justify=\"center\">\n                <Loader size=\"sm\" />\n                <Text size=\"sm\">Checking system status...</Text>\n              </Group>\n            ) : setupStatus ? (\n              <Grid>\n                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>\n                  <Group justify=\"space-between\">\n                    <Text size=\"sm\" fw={500}>Database:</Text>\n                    {getStatusBadge(setupStatus.database.status)}\n                  </Group>\n                  <Text size=\"xs\" c=\"dimmed\">{setupStatus.database.details}</Text>\n                </Grid.Col>\n\n                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>\n                  <Group justify=\"space-between\">\n                    <Text size=\"sm\" fw={500}>Tables:</Text>\n                    {getStatusBadge(setupStatus.tables.status)}\n                  </Group>\n                  <Text size=\"xs\" c=\"dimmed\">{setupStatus.tables.details}</Text>\n                </Grid.Col>\n\n                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>\n                  <Group justify=\"space-between\">\n                    <Text size=\"sm\" fw={500}>Admin User:</Text>\n                    {getStatusBadge(setupStatus.admin_user.status)}\n                  </Group>\n                  <Text size=\"xs\" c=\"dimmed\">{setupStatus.admin_user.details}</Text>\n                </Grid.Col>\n\n                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>\n                  <Group justify=\"space-between\">\n                    <Text size=\"sm\" fw={500}>LLM Provider:</Text>\n                    {getStatusBadge(setupStatus.llm.status)}\n                  </Group>\n                  <Text size=\"xs\" c=\"dimmed\">{setupStatus.llm.details}</Text>\n                </Grid.Col>\n\n                <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>\n                  <Group justify=\"space-between\">\n                    <Text size=\"sm\" fw={500}>Facebook Webhook:</Text>\n                    {getStatusBadge(setupStatus.facebook.status)}\n                  </Group>\n                  <Text size=\"xs\" c=\"dimmed\">{setupStatus.facebook.details}</Text>\n                </Grid.Col>\n              </Grid>\n            ) : (\n              <Alert color=\"red\" icon={<IconX size=\"1rem\" />}>\n                Failed to check system status\n              </Alert>\n            )}\n          </Stack>\n        </Card>\n\n        <Card withBorder bg=\"blue.0\" mt=\"md\" w=\"100%\">\n          <Stack>\n            <Group justify=\"space-between\">\n              <Title order={4}>Setup Instructions</Title>\n              <Button\n                size=\"xs\"\n                variant=\"subtle\"\n                component={Link}\n                href=\"/api/test-webhook\"\n                target=\"_blank\"\n              >\n                Test Webhook\n              </Button>\n            </Group>\n            <Text size=\"sm\">\n              1. Set up your Supabase database using the provided schema file\n            </Text>\n            <Text size=\"sm\">\n              2. Configure your environment variables in .env.local\n            </Text>\n            <Text size=\"sm\">\n              3. Visit this page to verify all connections are working\n            </Text>\n            <Text size=\"sm\">\n              4. For Facebook Messenger: Use ngrok to expose localhost and configure webhook\n            </Text>\n            <Text size=\"sm\">\n              5. Login to admin dashboard and start managing your products!\n            </Text>\n          </Stack>\n        </Card>\n      </Stack>\n    </Container>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;YACA;QACF;yBAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,eAAe,KAAK,OAAO;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,mBAAmB,SAAS,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,mBAAmB;QACrB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,4KAAA,CAAA,QAAK;oBAAC,OAAM;oBAAQ,2BAAa,6LAAC,2NAAA,CAAA,YAAS;wBAAC,MAAK;;;;;;8BAAa;;;;;;YACxE,KAAK;gBACH,qBAAO,6LAAC,4KAAA,CAAA,QAAK;oBAAC,OAAM;oBAAO,2BAAa,6LAAC,2NAAA,CAAA,YAAS;wBAAC,MAAK;;;;;;8BAAa;;;;;;YACvE,KAAK;gBACH,qBAAO,6LAAC,4KAAA,CAAA,QAAK;oBAAC,OAAM;oBAAS,2BAAa,6LAAC,uOAAA,CAAA,kBAAe;wBAAC,MAAK;;;;;;8BAAa;;;;;;YAC/E,KAAK;gBACH,qBAAO,6LAAC,4KAAA,CAAA,QAAK;oBAAC,OAAM;oBAAS,2BAAa,6LAAC,uOAAA,CAAA,kBAAe;wBAAC,MAAK;;;;;;8BAAa;;;;;;YAC/E,KAAK;gBACH,qBAAO,6LAAC,4KAAA,CAAA,QAAK;oBAAC,OAAM;oBAAO,2BAAa,6LAAC,uOAAA,CAAA,kBAAe;wBAAC,MAAK;;;;;;8BAAa;;;;;;YAC7E,KAAK;gBACH,qBAAO,6LAAC,4KAAA,CAAA,QAAK;oBAAC,OAAM;oBAAM,2BAAa,6LAAC,mNAAA,CAAA,QAAK;wBAAC,MAAK;;;;;;8BAAa;;;;;;YAClE;gBACE,qBAAO,6LAAC,4KAAA,CAAA,QAAK;oBAAC,OAAM;8BAAO;;;;;;QAC/B;IACF;IAEA,qBACE,6LAAC,oLAAA,CAAA,YAAS;QAAC,MAAK;QAAK,IAAG;kBACtB,cAAA,6LAAC,4KAAA,CAAA,QAAK;YAAC,OAAM;YAAS,KAAI;;8BACxB,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAS;;sCAChC,6LAAC,4KAAA,CAAA,QAAK;4BAAC,OAAO;4BAAG,MAAK;4BAAO,IAAG;sCAAK;;;;;;sCAGrC,6LAAC,0KAAA,CAAA,OAAI;4BAAC,MAAK;4BAAK,GAAE;4BAAS,IAAG;sCAAK;;;;;;;;;;;;8BAKrC,6LAAC,0KAAA,CAAA,OAAI;;sCACH,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;4BAAC,MAAM;gCAAE,MAAM;gCAAI,IAAI;4BAAE;sCAChC,cAAA,6LAAC,0KAAA,CAAA,OAAI;gCAAC,UAAU;gCAAC,GAAE;0CACjB,cAAA,6LAAC,4KAAA,CAAA,QAAK;;sDACJ,6LAAC,+NAAA,CAAA,cAAW;4CAAC,MAAK;4CAAO,OAAM;;;;;;sDAC/B,6LAAC,4KAAA,CAAA,QAAK;4CAAC,OAAO;sDAAG;;;;;;sDACjB,6LAAC,0KAAA,CAAA,OAAI;4CAAC,GAAE;sDAAS;;;;;;;;;;;;;;;;;;;;;;sCAOvB,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;4BAAC,MAAM;gCAAE,MAAM;gCAAI,IAAI;4BAAE;sCAChC,cAAA,6LAAC,0KAAA,CAAA,OAAI;gCAAC,UAAU;gCAAC,GAAE;0CACjB,cAAA,6LAAC,4KAAA,CAAA,QAAK;;sDACJ,6LAAC,iOAAA,CAAA,eAAY;4CAAC,MAAK;4CAAO,OAAM;;;;;;sDAChC,6LAAC,4KAAA,CAAA,QAAK;4CAAC,OAAO;sDAAG;;;;;;sDACjB,6LAAC,0KAAA,CAAA,OAAI;4CAAC,GAAE;sDAAS;;;;;;;;;;;;;;;;;;;;;;sCAOvB,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;4BAAC,MAAM;gCAAE,MAAM;gCAAI,IAAI;4BAAE;sCAChC,cAAA,6LAAC,0KAAA,CAAA,OAAI;gCAAC,UAAU;gCAAC,GAAE;0CACjB,cAAA,6LAAC,4KAAA,CAAA,QAAK;;sDACJ,6LAAC,mOAAA,CAAA,gBAAa;4CAAC,MAAK;4CAAO,OAAM;;;;;;sDACjC,6LAAC,4KAAA,CAAA,QAAK;4CAAC,OAAO;sDAAG;;;;;;sDACjB,6LAAC,0KAAA,CAAA,OAAI;4CAAC,GAAE;sDAAS;;;;;;;;;;;;;;;;;;;;;;sCAOvB,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;4BAAC,MAAM;gCAAE,MAAM;gCAAI,IAAI;4BAAE;sCAChC,cAAA,6LAAC,0KAAA,CAAA,OAAI;gCAAC,UAAU;gCAAC,GAAE;0CACjB,cAAA,6LAAC,4KAAA,CAAA,QAAK;;sDACJ,6LAAC,2NAAA,CAAA,YAAS;4CAAC,MAAK;4CAAO,OAAM;;;;;;sDAC7B,6LAAC,4KAAA,CAAA,QAAK;4CAAC,OAAO;sDAAG;;;;;;sDACjB,6LAAC,0KAAA,CAAA,OAAI;4CAAC,GAAE;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQzB,6LAAC,4KAAA,CAAA,QAAK;oBAAC,SAAQ;8BACZ,gCACC,6LAAC,8KAAA,CAAA,SAAM;wBACL,WAAW,+JAAA,CAAA,UAAI;wBACf,MAAK;wBACL,MAAK;wBACL,2BAAa,6LAAC,mOAAA,CAAA,gBAAa;4BAAC,MAAK;;;;;;kCAClC;;;;;6CAID,6LAAC,8KAAA,CAAA,SAAM;wBACL,WAAW,+JAAA,CAAA,UAAI;wBACf,MAAK;wBACL,MAAK;wBACL,2BAAa,6LAAC,2NAAA,CAAA,YAAS;4BAAC,MAAK;;;;;;kCAC9B;;;;;;;;;;;8BAOL,6LAAC,0KAAA,CAAA,OAAI;oBAAC,UAAU;oBAAC,IAAG;oBAAS,IAAG;oBAAK,GAAE;8BACrC,cAAA,6LAAC,4KAAA,CAAA,QAAK;;0CACJ,6LAAC,4KAAA,CAAA,QAAK;gCAAC,SAAQ;;kDACb,6LAAC,4KAAA,CAAA,QAAK;wCAAC,OAAO;kDAAG;;;;;;kDACjB,6LAAC,8KAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAS,SAAS;wCAAkB,SAAS;kDAAS;;;;;;;;;;;;4BAKjF,wBACC,6LAAC,4KAAA,CAAA,QAAK;gCAAC,SAAQ;;kDACb,6LAAC,8KAAA,CAAA,SAAM;wCAAC,MAAK;;;;;;kDACb,6LAAC,0KAAA,CAAA,OAAI;wCAAC,MAAK;kDAAK;;;;;;;;;;;uCAEhB,4BACF,6LAAC,0KAAA,CAAA,OAAI;;kDACH,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;wCAAC,MAAM;4CAAE,MAAM;4CAAI,IAAI;4CAAG,IAAI;wCAAE;;0DACvC,6LAAC,4KAAA,CAAA,QAAK;gDAAC,SAAQ;;kEACb,6LAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAK,IAAI;kEAAK;;;;;;oDACxB,eAAe,YAAY,QAAQ,CAAC,MAAM;;;;;;;0DAE7C,6LAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAK,GAAE;0DAAU,YAAY,QAAQ,CAAC,OAAO;;;;;;;;;;;;kDAG1D,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;wCAAC,MAAM;4CAAE,MAAM;4CAAI,IAAI;4CAAG,IAAI;wCAAE;;0DACvC,6LAAC,4KAAA,CAAA,QAAK;gDAAC,SAAQ;;kEACb,6LAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAK,IAAI;kEAAK;;;;;;oDACxB,eAAe,YAAY,MAAM,CAAC,MAAM;;;;;;;0DAE3C,6LAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAK,GAAE;0DAAU,YAAY,MAAM,CAAC,OAAO;;;;;;;;;;;;kDAGxD,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;wCAAC,MAAM;4CAAE,MAAM;4CAAI,IAAI;4CAAG,IAAI;wCAAE;;0DACvC,6LAAC,4KAAA,CAAA,QAAK;gDAAC,SAAQ;;kEACb,6LAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAK,IAAI;kEAAK;;;;;;oDACxB,eAAe,YAAY,UAAU,CAAC,MAAM;;;;;;;0DAE/C,6LAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAK,GAAE;0DAAU,YAAY,UAAU,CAAC,OAAO;;;;;;;;;;;;kDAG5D,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;wCAAC,MAAM;4CAAE,MAAM;4CAAI,IAAI;4CAAG,IAAI;wCAAE;;0DACvC,6LAAC,4KAAA,CAAA,QAAK;gDAAC,SAAQ;;kEACb,6LAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAK,IAAI;kEAAK;;;;;;oDACxB,eAAe,YAAY,GAAG,CAAC,MAAM;;;;;;;0DAExC,6LAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAK,GAAE;0DAAU,YAAY,GAAG,CAAC,OAAO;;;;;;;;;;;;kDAGrD,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;wCAAC,MAAM;4CAAE,MAAM;4CAAI,IAAI;4CAAG,IAAI;wCAAE;;0DACvC,6LAAC,4KAAA,CAAA,QAAK;gDAAC,SAAQ;;kEACb,6LAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAK,IAAI;kEAAK;;;;;;oDACxB,eAAe,YAAY,QAAQ,CAAC,MAAM;;;;;;;0DAE7C,6LAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAK,GAAE;0DAAU,YAAY,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;qDAI5D,6LAAC,4KAAA,CAAA,QAAK;gCAAC,OAAM;gCAAM,oBAAM,6LAAC,mNAAA,CAAA,QAAK;oCAAC,MAAK;;;;;;0CAAW;;;;;;;;;;;;;;;;;8BAOtD,6LAAC,0KAAA,CAAA,OAAI;oBAAC,UAAU;oBAAC,IAAG;oBAAS,IAAG;oBAAK,GAAE;8BACrC,cAAA,6LAAC,4KAAA,CAAA,QAAK;;0CACJ,6LAAC,4KAAA,CAAA,QAAK;gCAAC,SAAQ;;kDACb,6LAAC,4KAAA,CAAA,QAAK;wCAAC,OAAO;kDAAG;;;;;;kDACjB,6LAAC,8KAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAW,+JAAA,CAAA,UAAI;wCACf,MAAK;wCACL,QAAO;kDACR;;;;;;;;;;;;0CAIH,6LAAC,0KAAA,CAAA,OAAI;gCAAC,MAAK;0CAAK;;;;;;0CAGhB,6LAAC,0KAAA,CAAA,OAAI;gCAAC,MAAK;0CAAK;;;;;;0CAGhB,6LAAC,0KAAA,CAAA,OAAI;gCAAC,MAAK;0CAAK;;;;;;0CAGhB,6LAAC,0KAAA,CAAA,OAAI;gCAAC,MAAK;0CAAK;;;;;;0CAGhB,6LAAC,0KAAA,CAAA,OAAI;gCAAC,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B;GA7OwB;KAAA", "debugId": null}}]}