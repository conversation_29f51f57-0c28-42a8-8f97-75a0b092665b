{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Container, Title, Text, Button, Stack, Group, Card, Grid } from '@mantine/core';\nimport { IconLogin, IconDashboard, IconPackage, IconMessages } from '@tabler/icons-react';\nimport Link from 'next/link';\n\nexport default function Home() {\n  return (\n    <Container size=\"lg\" py=\"xl\">\n      <Stack align=\"center\" gap=\"xl\">\n        <div style={{ textAlign: 'center' }}>\n          <Title order={1} size=\"3rem\" mb=\"md\">\n            Product Management System\n          </Title>\n          <Text size=\"xl\" c=\"dimmed\" mb=\"xl\">\n            Full-stack Next.js application with Mantine UI, Supabase, and Facebook Messenger integration\n          </Text>\n        </div>\n\n        <Grid>\n          <Grid.Col span={{ base: 12, md: 6 }}>\n            <Card withBorder h=\"100%\">\n              <Stack>\n                <IconPackage size=\"2rem\" color=\"blue\" />\n                <Title order={3}>Product Management</Title>\n                <Text c=\"dimmed\">\n                  Manage your product catalog with full CRUD operations, search functionality, and detailed specifications.\n                </Text>\n              </Stack>\n            </Card>\n          </Grid.Col>\n\n          <Grid.Col span={{ base: 12, md: 6 }}>\n            <Card withBorder h=\"100%\">\n              <Stack>\n                <IconMessages size=\"2rem\" color=\"green\" />\n                <Title order={3}>Message Handling</Title>\n                <Text c=\"dimmed\">\n                  Handle customer messages from Facebook Messenger with automated responses and manual override capabilities.\n                </Text>\n              </Stack>\n            </Card>\n          </Grid.Col>\n\n          <Grid.Col span={{ base: 12, md: 6 }}>\n            <Card withBorder h=\"100%\">\n              <Stack>\n                <IconDashboard size=\"2rem\" color=\"orange\" />\n                <Title order={3}>Admin Dashboard</Title>\n                <Text c=\"dimmed\">\n                  Comprehensive admin interface for managing products, viewing messages, and configuring system settings.\n                </Text>\n              </Stack>\n            </Card>\n          </Grid.Col>\n\n          <Grid.Col span={{ base: 12, md: 6 }}>\n            <Card withBorder h=\"100%\">\n              <Stack>\n                <IconLogin size=\"2rem\" color=\"purple\" />\n                <Title order={3}>Secure Authentication</Title>\n                <Text c=\"dimmed\">\n                  Session-based authentication with JWT tokens and protected admin routes for secure access.\n                </Text>\n              </Stack>\n            </Card>\n          </Grid.Col>\n        </Grid>\n\n        <Group>\n          <Button\n            component={Link}\n            href=\"/admin\"\n            size=\"lg\"\n            leftSection={<IconDashboard size=\"1.2rem\" />}\n          >\n            Go to Admin Dashboard\n          </Button>\n          <Button\n            component={Link}\n            href=\"/login\"\n            variant=\"outline\"\n            size=\"lg\"\n            leftSection={<IconLogin size=\"1.2rem\" />}\n          >\n            Admin Login\n          </Button>\n        </Group>\n\n        <Card withBorder bg=\"gray.0\" mt=\"xl\">\n          <Stack>\n            <Title order={4}>Getting Started</Title>\n            <Text size=\"sm\">\n              1. Set up your Supabase database using the provided schema file\n            </Text>\n            <Text size=\"sm\">\n              2. Configure your environment variables in .env.local\n            </Text>\n            <Text size=\"sm\">\n              3. Login with default credentials: admin / admin123\n            </Text>\n            <Text size=\"sm\">\n              4. Start managing your products and customer messages!\n            </Text>\n          </Stack>\n        </Card>\n      </Stack>\n    </Container>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,6LAAC,oLAAA,CAAA,YAAS;QAAC,MAAK;QAAK,IAAG;kBACtB,cAAA,6LAAC,4KAAA,CAAA,QAAK;YAAC,OAAM;YAAS,KAAI;;8BACxB,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAS;;sCAChC,6LAAC,4KAAA,CAAA,QAAK;4BAAC,OAAO;4BAAG,MAAK;4BAAO,IAAG;sCAAK;;;;;;sCAGrC,6LAAC,0KAAA,CAAA,OAAI;4BAAC,MAAK;4BAAK,GAAE;4BAAS,IAAG;sCAAK;;;;;;;;;;;;8BAKrC,6LAAC,0KAAA,CAAA,OAAI;;sCACH,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;4BAAC,MAAM;gCAAE,MAAM;gCAAI,IAAI;4BAAE;sCAChC,cAAA,6LAAC,0KAAA,CAAA,OAAI;gCAAC,UAAU;gCAAC,GAAE;0CACjB,cAAA,6LAAC,4KAAA,CAAA,QAAK;;sDACJ,6LAAC,+NAAA,CAAA,cAAW;4CAAC,MAAK;4CAAO,OAAM;;;;;;sDAC/B,6LAAC,4KAAA,CAAA,QAAK;4CAAC,OAAO;sDAAG;;;;;;sDACjB,6LAAC,0KAAA,CAAA,OAAI;4CAAC,GAAE;sDAAS;;;;;;;;;;;;;;;;;;;;;;sCAOvB,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;4BAAC,MAAM;gCAAE,MAAM;gCAAI,IAAI;4BAAE;sCAChC,cAAA,6LAAC,0KAAA,CAAA,OAAI;gCAAC,UAAU;gCAAC,GAAE;0CACjB,cAAA,6LAAC,4KAAA,CAAA,QAAK;;sDACJ,6LAAC,iOAAA,CAAA,eAAY;4CAAC,MAAK;4CAAO,OAAM;;;;;;sDAChC,6LAAC,4KAAA,CAAA,QAAK;4CAAC,OAAO;sDAAG;;;;;;sDACjB,6LAAC,0KAAA,CAAA,OAAI;4CAAC,GAAE;sDAAS;;;;;;;;;;;;;;;;;;;;;;sCAOvB,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;4BAAC,MAAM;gCAAE,MAAM;gCAAI,IAAI;4BAAE;sCAChC,cAAA,6LAAC,0KAAA,CAAA,OAAI;gCAAC,UAAU;gCAAC,GAAE;0CACjB,cAAA,6LAAC,4KAAA,CAAA,QAAK;;sDACJ,6LAAC,mOAAA,CAAA,gBAAa;4CAAC,MAAK;4CAAO,OAAM;;;;;;sDACjC,6LAAC,4KAAA,CAAA,QAAK;4CAAC,OAAO;sDAAG;;;;;;sDACjB,6LAAC,0KAAA,CAAA,OAAI;4CAAC,GAAE;sDAAS;;;;;;;;;;;;;;;;;;;;;;sCAOvB,6LAAC,0KAAA,CAAA,OAAI,CAAC,GAAG;4BAAC,MAAM;gCAAE,MAAM;gCAAI,IAAI;4BAAE;sCAChC,cAAA,6LAAC,0KAAA,CAAA,OAAI;gCAAC,UAAU;gCAAC,GAAE;0CACjB,cAAA,6LAAC,4KAAA,CAAA,QAAK;;sDACJ,6LAAC,2NAAA,CAAA,YAAS;4CAAC,MAAK;4CAAO,OAAM;;;;;;sDAC7B,6LAAC,4KAAA,CAAA,QAAK;4CAAC,OAAO;sDAAG;;;;;;sDACjB,6LAAC,0KAAA,CAAA,OAAI;4CAAC,GAAE;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQzB,6LAAC,4KAAA,CAAA,QAAK;;sCACJ,6LAAC,8KAAA,CAAA,SAAM;4BACL,WAAW,+JAAA,CAAA,UAAI;4BACf,MAAK;4BACL,MAAK;4BACL,2BAAa,6LAAC,mOAAA,CAAA,gBAAa;gCAAC,MAAK;;;;;;sCAClC;;;;;;sCAGD,6LAAC,8KAAA,CAAA,SAAM;4BACL,WAAW,+JAAA,CAAA,UAAI;4BACf,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,2BAAa,6LAAC,2NAAA,CAAA,YAAS;gCAAC,MAAK;;;;;;sCAC9B;;;;;;;;;;;;8BAKH,6LAAC,0KAAA,CAAA,OAAI;oBAAC,UAAU;oBAAC,IAAG;oBAAS,IAAG;8BAC9B,cAAA,6LAAC,4KAAA,CAAA,QAAK;;0CACJ,6LAAC,4KAAA,CAAA,QAAK;gCAAC,OAAO;0CAAG;;;;;;0CACjB,6LAAC,0KAAA,CAAA,OAAI;gCAAC,MAAK;0CAAK;;;;;;0CAGhB,6LAAC,0KAAA,CAAA,OAAI;gCAAC,MAAK;0CAAK;;;;;;0CAGhB,6LAAC,0KAAA,CAAA,OAAI;gCAAC,MAAK;0CAAK;;;;;;0CAGhB,6LAAC,0KAAA,CAAA,OAAI;gCAAC,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B;KAvGwB", "debugId": null}}]}