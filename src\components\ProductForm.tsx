'use client';

import { useState } from 'react';
import {
  Stack,
  TextInput,
  Textarea,
  NumberInput,
  Button,
  Group,
  JsonInput,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { Product } from '@/types/database';

interface ProductFormProps {
  product?: Product | null;
  onSave: () => void;
  onCancel: () => void;
}

export default function ProductForm({ product, onSave, onCancel }: ProductFormProps) {
  const [loading, setLoading] = useState(false);

  const form = useForm({
    initialValues: {
      name: product?.name || '',
      description: product?.description || '',
      price: product?.price || 0,
      image_url: product?.image_url || '',
      specs: product?.specs ? JSON.stringify(product.specs, null, 2) : '{}',
    },
    validate: {
      name: (value) => (!value ? 'Product name is required' : null),
      price: (value) => (value <= 0 ? 'Price must be greater than 0' : null),
      specs: (value) => {
        try {
          JSON.parse(value);
          return null;
        } catch {
          return 'Invalid JSON format';
        }
      },
    },
  });

  const handleSubmit = async (values: typeof form.values) => {
    setLoading(true);

    try {
      let specs = null;
      if (values.specs.trim()) {
        try {
          specs = JSON.parse(values.specs);
        } catch (error) {
          form.setFieldError('specs', 'Invalid JSON format');
          setLoading(false);
          return;
        }
      }

      const productData = {
        name: values.name,
        description: values.description || null,
        price: values.price,
        image_url: values.image_url || null,
        specs,
      };

      const url = product ? `/api/products/${product.id}` : '/api/products';
      const method = product ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      const data = await response.json();

      if (response.ok) {
        notifications.show({
          title: 'Success',
          message: `Product ${product ? 'updated' : 'created'} successfully`,
          color: 'green',
        });
        onSave();
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || `Failed to ${product ? 'update' : 'create'} product`,
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error saving product:', error);
      notifications.show({
        title: 'Error',
        message: 'An unexpected error occurred',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={form.onSubmit(handleSubmit)}>
      <Stack>
        <TextInput
          label="Product Name"
          placeholder="Enter product name"
          required
          {...form.getInputProps('name')}
        />

        <Textarea
          label="Description"
          placeholder="Enter product description"
          rows={3}
          {...form.getInputProps('description')}
        />

        <NumberInput
          label="Price"
          placeholder="0.00"
          min={0}
          step={0.01}
          decimalScale={2}
          fixedDecimalScale
          required
          {...form.getInputProps('price')}
        />

        <TextInput
          label="Image URL"
          placeholder="https://example.com/image.jpg"
          {...form.getInputProps('image_url')}
        />

        <JsonInput
          label="Specifications (JSON)"
          placeholder='{"color": "blue", "size": "medium"}'
          validationError="Invalid JSON"
          formatOnBlur
          autosize
          minRows={4}
          {...form.getInputProps('specs')}
        />

        <Group justify="flex-end" mt="md">
          <Button variant="subtle" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" loading={loading}>
            {product ? 'Update' : 'Create'} Product
          </Button>
        </Group>
      </Stack>
    </form>
  );
}
