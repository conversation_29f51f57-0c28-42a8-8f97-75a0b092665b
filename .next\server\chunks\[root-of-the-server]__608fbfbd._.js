module.exports = {

"[project]/.next-internal/server/app/api/setup/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://sumqnhdtdmslsxstabsq.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN1bXFuaGR0ZG1zbHN4c3RhYnNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzODgyMTcsImV4cCI6MjA2NTk2NDIxN30.FziDRFp4tJghnCrGUrXMlUlNuZr6suHHheloq5NWGwA");
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY);
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authenticateUser": (()=>authenticateUser),
    "createAdminUser": (()=>createAdminUser),
    "generateToken": (()=>generateToken),
    "getUserFromToken": (()=>getUserFromToken),
    "hashPassword": (()=>hashPassword),
    "verifyPassword": (()=>verifyPassword),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
;
;
;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = '7d';
async function hashPassword(password) {
    const saltRounds = 12;
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, saltRounds);
}
async function verifyPassword(password, hash) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hash);
}
function generateToken(user) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign({
        id: user.id,
        username: user.username,
        email: user.email
    }, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN
    });
}
function verifyToken(token) {
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
        return decoded;
    } catch (error) {
        return null;
    }
}
async function authenticateUser(credentials) {
    try {
        const { data: user, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('admin_users').select('*').eq('username', credentials.username).eq('is_active', true).single();
        if (error || !user) {
            return {
                success: false,
                error: 'Invalid username or password'
            };
        }
        const isValidPassword = await verifyPassword(credentials.password, user.password_hash);
        if (!isValidPassword) {
            return {
                success: false,
                error: 'Invalid username or password'
            };
        }
        // Update last login
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('admin_users').update({
            last_login: new Date().toISOString()
        }).eq('id', user.id);
        const authUser = {
            id: user.id,
            username: user.username,
            email: user.email
        };
        const token = generateToken(authUser);
        return {
            success: true,
            user: authUser,
            token
        };
    } catch (error) {
        console.error('Authentication error:', error);
        return {
            success: false,
            error: 'Authentication failed'
        };
    }
}
async function getUserFromToken(token) {
    const decoded = verifyToken(token);
    if (!decoded) return null;
    try {
        const { data: user, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('admin_users').select('id, username, email').eq('id', decoded.id).eq('is_active', true).single();
        if (error || !user) return null;
        return {
            id: user.id,
            username: user.username,
            email: user.email
        };
    } catch (error) {
        console.error('Get user from token error:', error);
        return null;
    }
}
async function createAdminUser(username, password, email) {
    try {
        const passwordHash = await hashPassword(password);
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('admin_users').insert({
            username,
            password_hash: passwordHash,
            email
        });
        if (error) {
            if (error.code === '23505') {
                return {
                    success: false,
                    error: 'Username already exists'
                };
            }
            return {
                success: false,
                error: 'Failed to create user'
            };
        }
        return {
            success: true
        };
    } catch (error) {
        console.error('Create admin user error:', error);
        return {
            success: false,
            error: 'Failed to create user'
        };
    }
}
}}),
"[project]/src/lib/config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Configuration system for LLM providers and application settings
__turbopack_context__.s({
    "config": (()=>config),
    "getSystemPrompt": (()=>getSystemPrompt),
    "loadConfig": (()=>loadConfig),
    "validateConfig": (()=>validateConfig)
});
// Default configuration
const defaultConfig = {
    llm: {
        provider: 'gemini',
        apiKey: process.env.GEMINI_API_KEY || '',
        model: 'gemini-pro'
    },
    replyTone: 'friendly',
    confidenceThreshold: 0.8,
    maxResponseLength: 500,
    enableAutoReply: true,
    fallbackMessage: 'Thank you for your message. Our team will get back to you soon!'
};
function loadConfig() {
    const provider = process.env.LLM_PROVIDER || defaultConfig.llm.provider;
    let apiKey = '';
    let model = '';
    let baseUrl = '';
    switch(provider){
        case 'gemini':
            apiKey = process.env.GEMINI_API_KEY || '';
            model = process.env.GEMINI_MODEL || 'gemini-pro';
            break;
        case 'openrouter':
            apiKey = process.env.OPENROUTER_API_KEY || '';
            model = process.env.OPENROUTER_MODEL || 'google/gemini-pro';
            baseUrl = 'https://openrouter.ai/api/v1';
            break;
        case 'openai':
            apiKey = process.env.OPENAI_API_KEY || '';
            model = process.env.OPENAI_MODEL || 'gpt-3.5-turbo';
            baseUrl = 'https://api.openai.com/v1';
            break;
        case 'anthropic':
            apiKey = process.env.ANTHROPIC_API_KEY || '';
            model = process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229';
            break;
    }
    return {
        llm: {
            provider,
            apiKey,
            model,
            baseUrl
        },
        replyTone: process.env.REPLY_TONE || defaultConfig.replyTone,
        confidenceThreshold: parseFloat(process.env.CONFIDENCE_THRESHOLD || '0.8'),
        maxResponseLength: parseInt(process.env.MAX_RESPONSE_LENGTH || '500'),
        enableAutoReply: process.env.ENABLE_AUTO_REPLY !== 'false',
        fallbackMessage: process.env.FALLBACK_MESSAGE || defaultConfig.fallbackMessage
    };
}
function getSystemPrompt(tone) {
    const basePrompt = `You are a helpful customer service assistant for an e-commerce business. 
You have access to product information and should help customers with their inquiries.
Always be accurate and helpful. If you don't know something, say so politely.`;
    const tonePrompts = {
        professional: `${basePrompt} Maintain a professional and business-like tone in all responses.`,
        friendly: `${basePrompt} Use a warm, friendly, and approachable tone. Be conversational but helpful.`,
        casual: `${basePrompt} Keep responses casual and relaxed, like talking to a friend. Use simple language.`,
        formal: `${basePrompt} Use formal language and proper business etiquette in all communications.`
    };
    return tonePrompts[tone];
}
function validateConfig(config) {
    const errors = [];
    if (!config.llm.apiKey) {
        errors.push(`API key is required for ${config.llm.provider}`);
    }
    if (config.confidenceThreshold < 0 || config.confidenceThreshold > 1) {
        errors.push('Confidence threshold must be between 0 and 1');
    }
    if (config.maxResponseLength < 50 || config.maxResponseLength > 2000) {
        errors.push('Max response length must be between 50 and 2000 characters');
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
const config = loadConfig();
}}),
"[project]/src/app/api/setup/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config.ts [app-route] (ecmascript)");
;
;
;
;
async function GET() {
    try {
        const results = {
            database: {
                status: 'checking...',
                details: ''
            },
            tables: {
                status: 'checking...',
                details: ''
            },
            admin_user: {
                status: 'checking...',
                details: ''
            },
            llm: {
                status: 'checking...',
                details: ''
            },
            facebook: {
                status: 'checking...',
                details: ''
            }
        };
        // Test database connection
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('products').select('count').limit(1);
            if (error) throw error;
            results.database = {
                status: 'OK',
                details: 'Database connection successful'
            };
        } catch (error) {
            results.database = {
                status: 'ERROR',
                details: `Database connection failed: ${error}`
            };
        }
        // Test table structure
        try {
            const tables = [
                'products',
                'messages',
                'responses',
                'memory',
                'admin_users'
            ];
            const tableResults = [];
            for (const table of tables){
                try {
                    const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from(table).select('*').limit(1);
                    if (error) throw error;
                    tableResults.push(`${table}: OK`);
                } catch (error) {
                    tableResults.push(`${table}: ERROR - ${error}`);
                }
            }
            results.tables = {
                status: tableResults.every((r)=>r.includes('OK')) ? 'OK' : 'PARTIAL',
                details: tableResults.join(', ')
            };
        } catch (error) {
            results.tables = {
                status: 'ERROR',
                details: `Table check failed: ${error}`
            };
        }
        // Check/Create admin user
        try {
            const { data: existingUser } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from('admin_users').select('username').eq('username', 'admin').single();
            if (!existingUser) {
                // Create admin user with correct password hash
                const passwordHash = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hashPassword"])('admin123');
                const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from('admin_users').insert({
                    username: 'admin',
                    password_hash: passwordHash,
                    email: '<EMAIL>'
                });
                if (error) throw error;
                results.admin_user = {
                    status: 'CREATED',
                    details: 'Admin user created with username: admin, password: admin123'
                };
            } else {
                results.admin_user = {
                    status: 'EXISTS',
                    details: 'Admin user already exists'
                };
            }
        } catch (error) {
            results.admin_user = {
                status: 'ERROR',
                details: `Admin user setup failed: ${error}`
            };
        }
        // Test LLM configuration
        try {
            const llmConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["config"].llm;
            if (!llmConfig.apiKey) {
                results.llm = {
                    status: 'NOT_CONFIGURED',
                    details: `${llmConfig.provider} API key not set`
                };
            } else {
                results.llm = {
                    status: 'CONFIGURED',
                    details: `${llmConfig.provider} configured with model: ${llmConfig.model}`
                };
            }
        } catch (error) {
            results.llm = {
                status: 'ERROR',
                details: `LLM config error: ${error}`
            };
        }
        // Test Facebook configuration and webhook
        try {
            const fbVerifyToken = process.env.FACEBOOK_VERIFY_TOKEN;
            const fbPageToken = process.env.FACEBOOK_PAGE_ACCESS_TOKEN;
            if (!fbVerifyToken && !fbPageToken) {
                results.facebook = {
                    status: 'NOT_CONFIGURED',
                    details: 'Facebook tokens not set'
                };
            } else if (!fbVerifyToken) {
                results.facebook = {
                    status: 'PARTIAL',
                    details: 'Verify token missing'
                };
            } else if (!fbPageToken) {
                results.facebook = {
                    status: 'PARTIAL',
                    details: 'Page access token missing'
                };
            } else {
                // Test webhook endpoint
                try {
                    const webhookUrl = `${("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'http'}://${process.env.VERCEL_URL || 'localhost:3000'}/api/webhook/facebook`;
                    const testResponse = await fetch(`${webhookUrl}?hub.mode=subscribe&hub.verify_token=${fbVerifyToken}&hub.challenge=test_challenge`);
                    if (testResponse.ok) {
                        const challengeResponse = await testResponse.text();
                        if (challengeResponse === 'test_challenge') {
                            results.facebook = {
                                status: 'OK',
                                details: `Webhook verified at ${webhookUrl}`
                            };
                        } else {
                            results.facebook = {
                                status: 'WEBHOOK_ERROR',
                                details: 'Webhook verification failed - incorrect response'
                            };
                        }
                    } else {
                        results.facebook = {
                            status: 'WEBHOOK_ERROR',
                            details: `Webhook not accessible (${testResponse.status})`
                        };
                    }
                } catch (webhookError) {
                    results.facebook = {
                        status: 'CONFIGURED',
                        details: 'Tokens configured, webhook test failed (normal for localhost)'
                    };
                }
            }
        } catch (error) {
            results.facebook = {
                status: 'ERROR',
                details: `Facebook config error: ${error}`
            };
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            timestamp: new Date().toISOString(),
            results,
            environment: ("TURBOPACK compile-time value", "development"),
            jwt_secret_configured: !!process.env.JWT_SECRET
        });
    } catch (error) {
        console.error('Setup check error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Setup check failed',
            details: error
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__608fbfbd._.js.map