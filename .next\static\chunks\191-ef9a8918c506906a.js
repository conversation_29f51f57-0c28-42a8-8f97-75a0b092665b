"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[191],{11659:(e,t,r)=>{r.d(t,{m:()=>C});var l=r(12115);let n="undefined"!=typeof window?l.useLayoutEffect:l.useEffect;function a(e,t){n(()=>{if(e)return window.addEventListener(e,t),()=>window.removeEventListener(e,t)},[e])}function c(e){return null===e||"object"!=typeof e?{}:Object.keys(e).reduce((t,r)=>{let l=e[r];return null!=l&&!1!==l&&(t[r]=l),t},{})}function u(e,t){if(null===t||"object"!=typeof t)return{};let r={...t};return Object.keys(t).forEach(t=>{t.includes("".concat(String(e),"."))&&delete r[t]}),r}function o(e,t){return parseInt(e.substring(t.length+1).split(".")[0],10)}function s(e,t,r,l){if(void 0===t)return r;let n="".concat(String(e)),a=r;-1===l&&(a=u("".concat(n,".").concat(t),a));let c={...a},s=new Set;return Object.entries(a).filter(e=>{let[r]=e;if(!r.startsWith("".concat(n,".")))return!1;let l=o(r,n);return!Number.isNaN(l)&&l>=t}).forEach(e=>{let[t,r]=e,a=o(t,n),u=t.replace("".concat(n,".").concat(a),"".concat(n,".").concat(a+l));c[u]=r,s.add(u),s.has(t)||delete c[t]}),c}function i(e){return"string"!=typeof e?[]:e.split(".")}function f(e,t){let r=i(e);if(0===r.length||"object"!=typeof t||null===t)return;let l=t[r[0]];for(let e=1;e<r.length&&null!=l;e+=1)l=l[r[e]];return l}function d(e,t,r){"object"==typeof r.value&&(r.value=p(r.value)),r.enumerable&&!r.get&&!r.set&&r.configurable&&r.writable&&"__proto__"!==t?e[t]=r.value:Object.defineProperty(e,t,r)}function p(e){if("object"!=typeof e)return e;var t,r,l,n=0,a=Object.prototype.toString.call(e);if("[object Object]"===a?l=Object.create(e.__proto__||null):"[object Array]"===a?l=Array(e.length):"[object Set]"===a?(l=new Set,e.forEach(function(e){l.add(p(e))})):"[object Map]"===a?(l=new Map,e.forEach(function(e,t){l.set(p(t),p(e))})):"[object Date]"===a?l=new Date(+e):"[object RegExp]"===a?l=new RegExp(e.source,e.flags):"[object DataView]"===a?l=new e.constructor(p(e.buffer)):"[object ArrayBuffer]"===a?l=e.slice(0):"Array]"===a.slice(-6)&&(l=new e.constructor(e)),l){for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)d(l,r[n],Object.getOwnPropertyDescriptor(e,r[n]));for(n=0,r=Object.getOwnPropertyNames(e);n<r.length;n++)Object.hasOwnProperty.call(l,t=r[n])&&l[t]===e[t]||d(l,t,Object.getOwnPropertyDescriptor(e,t))}return l||e}function h(e,t,r){let l=i(e);if(0===l.length)return r;let n=p(r);if(1===l.length)return n[l[0]]=t,n;let a=n[l[0]];for(let e=1;e<l.length-1;e+=1){if(void 0===a)return n;a=a[l[e]]}return a[l[l.length-1]]=t,n}var b=r(51616);function y(e,t){let r=Object.keys(e);if("string"==typeof t){let l=r.filter(e=>e.startsWith("".concat(t,".")));return e[t]||l.some(t=>e[t])||!1}return r.some(t=>e[t])}function m(e,t){return e?"".concat(e,"-").concat(t.toString()):t.toString()}let v=Symbol("root-rule");function g(e){let t=c(e);return{hasErrors:Object.keys(t).length>0,errors:t}}function k(e,t){return"function"==typeof e?g(e(t)):g(function e(t,r){let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return"object"!=typeof t||null===t?n:Object.keys(t).reduce((n,a)=>{let c=t[a],u="".concat(""===l?"":"".concat(l,".")).concat(a),o=f(u,r),s=!1;return"function"==typeof c&&(n[u]=c(o,r,u)),"object"==typeof c&&Array.isArray(o)&&(s=!0,o.forEach((t,l)=>e(c,r,"".concat(u,".").concat(l),n)),v in c&&(n[u]=c[v](o,r,u))),"object"==typeof c&&"object"==typeof o&&null!==o&&(s||e(c,r,u,n),v in c&&(n[u]=c[v](o,r,u))),n},n)}(e,t))}function E(e,t,r){if("string"!=typeof e)return{hasError:!1,error:null};let l=k(t,r),n=Object.keys(l.errors).find(t=>e.split(".").every((e,r)=>e===t.split(".")[r]));return{hasError:!!n,error:n?l.errors[n]:null}}function V(e,t){return!!t&&("boolean"==typeof t?t:!!Array.isArray(t)&&t.includes(e.replace(/[.][0-9]+/g,".".concat("__MANTINE_FORM_INDEX__"))))}function C(){let{name:e,mode:t="controlled",initialValues:r,initialErrors:n={},initialDirty:o={},initialTouched:i={},clearInputErrorOnChange:d=!0,validateInputOnChange:p=!1,validateInputOnBlur:v=!1,onValuesChange:g,transformValues:C=e=>e,enhanceGetInputProps:j,validate:S,onSubmitPreventDefault:w="always",touchTrigger:O="change"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},F=function(e){let[t,r]=(0,l.useState)(c(e)),n=(0,l.useRef)(t),a=(0,l.useCallback)(e=>{r(t=>{let r=c("function"==typeof e?e(t):e);return n.current=r,r})},[]),u=(0,l.useCallback)(()=>a({}),[]),o=(0,l.useCallback)(e=>{void 0!==n.current[e]&&a(t=>{let r={...t};return delete r[e],r})},[t]),s=(0,l.useCallback)((e,t)=>{null==t||!1===t?o(e):n.current[e]!==t&&a(r=>({...r,[e]:t}))},[t]);return{errorsState:t,setErrors:a,clearErrors:u,setFieldError:s,clearFieldError:o}}(n),A=function(e){let{initialValues:t,onValuesChange:r,mode:n}=e,a=(0,l.useRef)(!1),[c,u]=(0,l.useState)(t||{}),o=(0,l.useRef)(c),s=(0,l.useRef)(c),i=(0,l.useCallback)(e=>{let{values:t,subscribers:l,updateState:a=!0,mergeWithPreviousValues:c=!0}=e,s=o.current,i=t instanceof Function?t(o.current):t,f=c?{...s,...i}:i;o.current=f,a&&(u(f),"uncontrolled"===n&&(o.current=f)),null==r||r(f,s),null==l||l.filter(Boolean).forEach(e=>e({updatedValues:f,previousValues:s}))},[r]),d=(0,l.useCallback)(e=>{let t=f(e.path,o.current),r=e.value instanceof Function?e.value(t):e.value;if(t!==r){var l;let t=o.current,n=h(e.path,r,o.current);i({values:n,updateState:e.updateState}),null==(l=e.subscribers)||l.filter(Boolean).forEach(r=>r({path:e.path,updatedValues:n,previousValues:t}))}},[i]),p=(0,l.useCallback)(e=>{s.current=e},[]),b=(0,l.useCallback)((e,t)=>{a.current||(a.current=!0,i({values:e,updateState:"controlled"===n}),p(e),t())},[i]),y=(0,l.useCallback)(()=>{i({values:s.current,updateState:!0,mergeWithPreviousValues:!1})},[i]),m=(0,l.useCallback)(()=>o.current,[]),v=(0,l.useCallback)(()=>s.current,[]),g=(0,l.useCallback)(e=>{let t=f(e,s.current);void 0!==t&&d({path:e,value:t,updateState:"uncontrolled"===n||void 0})},[d,n]);return{initialized:a,stateValues:c,refValues:o,valuesSnapshot:s,setValues:i,setFieldValue:d,resetValues:y,setValuesSnapshot:p,initialize:b,getValues:m,getValuesSnapshot:v,resetField:g}}({initialValues:r,onValuesChange:g,mode:t}),D=function(e){let{initialDirty:t,initialTouched:r,mode:n,$values:a}=e,[c,o]=(0,l.useState)(r),[s,i]=(0,l.useState)(t),d=(0,l.useRef)(r),p=(0,l.useRef)(t),h=(0,l.useCallback)(e=>{let t="function"==typeof e?e(d.current):e;d.current=t,"controlled"===n&&o(t)},[]),m=(0,l.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r="function"==typeof e?e(p.current):e;p.current=r,("controlled"===n||t)&&i(r)},[]),v=(0,l.useCallback)(()=>h({}),[]),g=(0,l.useCallback)(e=>{let t=e?{...a.refValues.current,...e}:a.refValues.current;a.setValuesSnapshot(t),m({})},[]),k=(0,l.useCallback)((e,t)=>{h(r=>y(r,e)===t?r:{...r,[e]:t})},[]),E=(0,l.useCallback)((e,t,r)=>{m(r=>y(r,e)===t?r:{...r,[e]:t},r)},[]),V=(0,l.useCallback)((e,t)=>{let r=y(p.current,e),l=!b(f(e,a.getValuesSnapshot()),t),n=u(e,p.current);n[e]=l,m(n,r!==l)},[]),C=(0,l.useCallback)(e=>y(d.current,e),[]),j=(0,l.useCallback)(e=>m(t=>{if("string"!=typeof e)return t;let r=u(e,t);return(delete r[e],b(r,t))?t:r}),[]),S=(0,l.useCallback)(e=>{if(e){let t=f(e,p.current);return"boolean"==typeof t?t:!b(f(e,a.refValues.current),f(e,a.valuesSnapshot.current))}return Object.keys(p.current).length>0?y(p.current):!b(a.refValues.current,a.valuesSnapshot.current)},[]),w=(0,l.useCallback)(()=>p.current,[]),O=(0,l.useCallback)(()=>d.current,[]);return{touchedState:c,dirtyState:s,touchedRef:d,dirtyRef:p,setTouched:h,setDirty:m,resetDirty:g,resetTouched:v,isTouched:C,setFieldTouched:k,setFieldDirty:E,setTouchedState:o,setDirtyState:i,clearFieldDirty:j,isDirty:S,getDirty:w,getTouched:O,setCalculatedFieldDirty:V}}({initialDirty:o,initialTouched:i,$values:A,mode:t}),I=function(e){let{$values:t,$errors:r,$status:n}=e,a=(0,l.useCallback)((e,l)=>{n.clearFieldDirty(e),r.setErrors(t=>(function(e,t,r){let{from:l,to:n}=t,a="".concat(e,".").concat(l),c="".concat(e,".").concat(n),u={...r},o=new Set;return Object.keys(r).forEach(e=>{let t,r;if(!o.has(e)&&(e.startsWith(a)?(t=e,r=e.replace(a,c)):e.startsWith(c)&&(t=e.replace(c,a),r=e),t&&r)){let e=u[t],l=u[r];void 0===l?delete u[t]:u[t]=l,void 0===e?delete u[r]:u[r]=e,o.add(t),o.add(r)}}),u})(e,l,t)),t.setValues({values:function(e,t,r){let{from:l,to:n}=t,a=f(e,r);if(!Array.isArray(a))return r;let c=[...a],u=a[l];return c.splice(l,1),c.splice(n,0,u),h(e,c,r)}(e,l,t.refValues.current),updateState:!0})},[]),c=(0,l.useCallback)((e,l)=>{n.clearFieldDirty(e),r.setErrors(t=>s(e,l,t,-1)),t.setValues({values:function(e,t,r){let l=f(e,r);return Array.isArray(l)?h(e,l.filter((e,r)=>r!==t),r):r}(e,l,t.refValues.current),updateState:!0})},[]);return{reorderListItem:a,removeListItem:c,insertListItem:(0,l.useCallback)((e,l,a)=>{n.clearFieldDirty(e),r.setErrors(t=>s(e,a,t,1)),t.setValues({values:function(e,t,r,l){let n=f(e,l);if(!Array.isArray(n))return l;let a=[...n];return a.splice("number"==typeof r?r:a.length,0,t),h(e,a,l)}(e,l,a,t.refValues.current),updateState:!0})},[]),replaceListItem:(0,l.useCallback)((e,r,l)=>{n.clearFieldDirty(e),t.setValues({values:function(e,t,r,l){let n=f(e,l);if(!Array.isArray(n)||n.length<=r)return l;let a=[...n];return a[r]=t,h(e,a,l)}(e,l,r,t.refValues.current),updateState:!0})},[])}}({$values:A,$errors:F,$status:D}),L=function(e){let{$status:t}=e,r=(0,l.useRef)({}),n=(0,l.useCallback)((e,t)=>{(0,l.useEffect)(()=>(r.current[e]=r.current[e]||[],r.current[e].push(t),()=>{r.current[e]=r.current[e].filter(e=>e!==t)}),[t])},[]),a=(0,l.useCallback)(e=>r.current[e]?r.current[e].map(r=>l=>r({previousValue:f(e,l.previousValues),value:f(e,l.updatedValues),touched:t.isTouched(e),dirty:t.isDirty(e)})):[],[]);return{subscribers:r,watch:n,getFieldSubscribers:a}}({$status:D}),[T,_]=(0,l.useState)(0),[x,R]=(0,l.useState)({}),[N,P]=(0,l.useState)(!1),M=(0,l.useCallback)(()=>{A.resetValues(),F.clearErrors(),D.resetDirty(),D.resetTouched(),"uncontrolled"===t&&_(e=>e+1)},[]),W=(0,l.useCallback)(e=>{d&&F.clearErrors(),"uncontrolled"===t&&_(e=>e+1),Object.keys(L.subscribers.current).forEach(t=>{f(t,A.refValues.current)!==f(t,e)&&L.getFieldSubscribers(t).forEach(t=>t({previousValues:e,updatedValues:A.refValues.current}))})},[d]),B=(0,l.useCallback)(e=>{let r=A.refValues.current;A.initialize(e,()=>"uncontrolled"===t&&_(e=>e+1)),W(r)},[W]),z=(0,l.useCallback)((e,r,l)=>{let n=V(e,p),a=r instanceof Function?r(f(e,A.refValues.current)):r;D.setCalculatedFieldDirty(e,a),"change"===O&&D.setFieldTouched(e,!0),!n&&d&&F.clearFieldError(e),A.setFieldValue({path:e,value:r,updateState:"controlled"===t,subscribers:[...L.getFieldSubscribers(e),n?t=>{let r=E(e,S,t.updatedValues);r.hasError?F.setFieldError(e,r.error):F.clearFieldError(e)}:null,(null==l?void 0:l.forceUpdate)!==!1&&"controlled"!==t?()=>R(t=>({...t,[e]:(t[e]||0)+1})):null]})},[g,S]),H=(0,l.useCallback)(e=>{let r=A.refValues.current;A.setValues({values:e,updateState:"controlled"===t}),W(r)},[g,W]),U=(0,l.useCallback)(()=>{let e=k(S,A.refValues.current);return F.setErrors(e.errors),e},[S]),q=(0,l.useCallback)(e=>{let t=E(e,S,A.refValues.current);return t.hasError?F.setFieldError(e,t.error):F.clearFieldError(e),t},[S]),X=(0,l.useCallback)(e=>{e.preventDefault(),M()},[]),Z=(0,l.useCallback)(e=>e?!E(e,S,A.refValues.current).hasError:!k(S,A.refValues.current).hasErrors,[S]),$=(0,l.useCallback)(t=>document.querySelector('[data-path="'.concat(m(e,t),'"]')),[]),G={watch:L.watch,initialized:A.initialized.current,values:"uncontrolled"===t?A.refValues.current:A.stateValues,getValues:A.getValues,getInitialValues:A.getValuesSnapshot,setInitialValues:A.setValuesSnapshot,resetField:A.resetField,initialize:B,setValues:H,setFieldValue:z,submitting:N,setSubmitting:P,errors:F.errorsState,setErrors:F.setErrors,setFieldError:F.setFieldError,clearFieldError:F.clearFieldError,clearErrors:F.clearErrors,resetDirty:D.resetDirty,setTouched:D.setTouched,setDirty:D.setDirty,isTouched:D.isTouched,resetTouched:D.resetTouched,isDirty:D.isDirty,getTouched:D.getTouched,getDirty:D.getDirty,reorderListItem:I.reorderListItem,insertListItem:I.insertListItem,removeListItem:I.removeListItem,replaceListItem:I.replaceListItem,reset:M,validate:U,validateField:q,getInputProps:function(r){var l;let{type:n="input",withError:a=!0,withFocus:c=!0,...u}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o={onChange:(l=e=>z(r,e,{forceUpdate:!1}),e=>{if(e)if("function"==typeof e)l(e);else if("object"==typeof e&&"nativeEvent"in e){let{currentTarget:t}=e;t instanceof HTMLInputElement?"checkbox"===t.type?l(t.checked):l(t.value):(t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement)&&l(t.value)}else l(e);else l(e)}),"data-path":m(e,r)};return a&&(o.error=F.errorsState[r]),"checkbox"===n?o["controlled"===t?"checked":"defaultChecked"]=f(r,A.refValues.current):o["controlled"===t?"value":"defaultValue"]=f(r,A.refValues.current),c&&(o.onFocus=()=>D.setFieldTouched(r,!0),o.onBlur=()=>{if(V(r,v)){let e=E(r,S,A.refValues.current);e.hasError?F.setFieldError(r,e.error):F.clearFieldError(r)}}),Object.assign(o,null==j?void 0:j({inputProps:o,field:r,options:{type:n,withError:a,withFocus:c,...u},form:G}))},onSubmit:(e,t)=>r=>{"always"===w&&(null==r||r.preventDefault());let l=U();if(l.hasErrors)"validation-failed"===w&&(null==r||r.preventDefault()),null==t||t(l.errors,A.refValues.current,r);else{let t=null==e?void 0:e(C(A.refValues.current),r);t instanceof Promise&&(P(!0),t.finally(()=>P(!1)))}},onReset:X,isValid:Z,getTransformedValues:e=>C(e||A.refValues.current),key:e=>"".concat(T,"-").concat(e,"-").concat(x[e]||0),getInputNode:$};return e&&function(e){if(!/^[0-9a-zA-Z-]+$/.test(e))throw Error('[@mantine/use-form] Form name "'.concat(e,'" is invalid, it should contain only letters, numbers and dashes'))}(e),a("mantine-form:".concat(e,":set-field-value"),e=>G.setFieldValue(e.detail.path,e.detail.value)),a("mantine-form:".concat(e,":set-values"),e=>G.setValues(e.detail)),a("mantine-form:".concat(e,":set-initial-values"),e=>G.setInitialValues(e.detail)),a("mantine-form:".concat(e,":set-errors"),e=>G.setErrors(e.detail)),a("mantine-form:".concat(e,":set-field-error"),e=>G.setFieldError(e.detail.path,e.detail.error)),a("mantine-form:".concat(e,":clear-field-error"),e=>G.clearFieldError(e.detail)),a("mantine-form:".concat(e,":clear-errors"),G.clearErrors),a("mantine-form:".concat(e,":reset"),G.reset),a("mantine-form:".concat(e,":validate"),G.validate),a("mantine-form:".concat(e,":validate-field"),e=>G.validateField(e.detail)),a("mantine-form:".concat(e,":reorder-list-item"),e=>G.reorderListItem(e.detail.path,e.detail.payload)),a("mantine-form:".concat(e,":remove-list-item"),e=>G.removeListItem(e.detail.path,e.detail.index)),a("mantine-form:".concat(e,":insert-list-item"),e=>G.insertListItem(e.detail.path,e.detail.item,e.detail.index)),a("mantine-form:".concat(e,":set-dirty"),e=>G.setDirty(e.detail)),a("mantine-form:".concat(e,":set-touched"),e=>G.setTouched(e.detail)),a("mantine-form:".concat(e,":reset-dirty"),e=>G.resetDirty(e.detail)),a("mantine-form:".concat(e,":reset-touched"),G.resetTouched),G}},51616:e=>{e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;if(Array.isArray(t)){if((l=t.length)!=r.length)return!1;for(n=l;0!=n--;)if(!e(t[n],r[n]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((l=(a=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(n=l;0!=n--;)if(!Object.prototype.hasOwnProperty.call(r,a[n]))return!1;for(n=l;0!=n--;){var l,n,a,c=a[n];if(!e(t[c],r[c]))return!1}return!0}return t!=t&&r!=r}},78977:(e,t,r)=>{r.d(t,{A:()=>l});var l=(0,r(86467).A)("outline","alert-circle","IconAlertCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])},86467:(e,t,r)=>{r.d(t,{A:()=>a});var l=r(12115),n={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let a=(e,t,r,a)=>{let c=(0,l.forwardRef)((r,c)=>{let{color:u="currentColor",size:o=24,stroke:s=2,title:i,className:f,children:d,...p}=r;return(0,l.createElement)("svg",{ref:c,...n[e],width:o,height:o,className:["tabler-icon","tabler-icon-".concat(t),f].join(" "),..."filled"===e?{fill:u}:{strokeWidth:s,stroke:u},...p},[i&&(0,l.createElement)("title",{key:"svg-title"},i),...a.map(e=>{let[t,r]=e;return(0,l.createElement)(t,r)}),...Array.isArray(d)?d:[d]])});return c.displayName="".concat(r),c}}}]);