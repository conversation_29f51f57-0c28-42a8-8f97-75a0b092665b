{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/types/database';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);\n\n// Server-side client with service role key for admin operations\nexport const supabaseAdmin = createClient<Database>(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n);\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;AAGrD,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EACtC,aACA,QAAQ,GAAG,CAAC,yBAAyB", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/app/api/webhook/facebook/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { supabase } from '@/lib/supabase';\nimport { MessageInsert, ResponseInsert } from '@/types/database';\n\nconst VERIFY_TOKEN = process.env.FACEBOOK_VERIFY_TOKEN || 'your-verify-token';\n\n// GET /api/webhook/facebook - Webhook verification\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const mode = searchParams.get('hub.mode');\n    const token = searchParams.get('hub.verify_token');\n    const challenge = searchParams.get('hub.challenge');\n\n    if (mode === 'subscribe' && token === VERIFY_TOKEN) {\n      console.log('Webhook verified');\n      return new NextResponse(challenge);\n    } else {\n      return NextResponse.json(\n        { error: 'Forbidden' },\n        { status: 403 }\n      );\n    }\n  } catch (error) {\n    console.error('Webhook verification error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/webhook/facebook - Handle incoming messages\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n\n    // Process each entry in the webhook payload\n    for (const entry of body.entry || []) {\n      for (const messaging of entry.messaging || []) {\n        if (messaging.message) {\n          await handleIncomingMessage(messaging);\n        }\n      }\n    }\n\n    return NextResponse.json({ status: 'ok' });\n  } catch (error) {\n    console.error('Webhook processing error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nasync function handleIncomingMessage(messaging: any) {\n  try {\n    const senderId = messaging.sender.id;\n    const messageText = messaging.message.text;\n    const timestamp = messaging.timestamp;\n\n    if (!messageText) return; // Skip non-text messages for now\n\n    // Save incoming message to database\n    const messageData: MessageInsert = {\n      sender_id: senderId,\n      message_text: messageText,\n      message_type: 'text',\n      platform: 'facebook',\n      metadata: {\n        timestamp,\n        raw_messaging: messaging,\n      },\n    };\n\n    const { data: savedMessage, error: messageError } = await supabase\n      .from('messages')\n      .insert(messageData)\n      .select()\n      .single();\n\n    if (messageError) {\n      console.error('Error saving message:', messageError);\n      return;\n    }\n\n    // Process the message and generate response\n    await processMessageAndRespond(savedMessage);\n  } catch (error) {\n    console.error('Error handling incoming message:', error);\n  }\n}\n\nasync function processMessageAndRespond(message: any) {\n  try {\n    // For now, we'll implement a simple keyword-based response system\n    // In a full implementation, this would integrate with your chosen LLM provider\n    \n    const messageText = message.message_text.toLowerCase();\n    let responseText = '';\n    let confidenceScore = 0.5;\n\n    // Check memory for matching responses\n    const { data: memoryEntries } = await supabase\n      .from('memory')\n      .select('*')\n      .eq('is_active', true);\n\n    let bestMatch = null;\n    let bestScore = 0;\n\n    for (const entry of memoryEntries || []) {\n      const keywords = entry.keywords || [];\n      let score = 0;\n      \n      for (const keyword of keywords) {\n        if (messageText.includes(keyword.toLowerCase())) {\n          score += 1;\n        }\n      }\n      \n      if (score > bestScore && score > 0) {\n        bestScore = score;\n        bestMatch = entry;\n      }\n    }\n\n    if (bestMatch && bestScore >= 1) {\n      responseText = bestMatch.answer;\n      confidenceScore = Math.min(bestScore / bestMatch.keywords.length, 1.0);\n    } else {\n      // Fallback response\n      responseText = 'Thank you for your message. Our team will get back to you soon!';\n      confidenceScore = 0.3;\n    }\n\n    // Save response to database\n    const responseData: ResponseInsert = {\n      message_id: message.id,\n      response_text: responseText,\n      response_type: 'auto',\n      confidence_score: confidenceScore,\n      llm_provider: 'keyword_matching',\n    };\n\n    const { error: responseError } = await supabase\n      .from('responses')\n      .insert(responseData);\n\n    if (responseError) {\n      console.error('Error saving response:', responseError);\n      return;\n    }\n\n    // In a real implementation, you would send the response back to Facebook\n    // using the Facebook Graph API\n    console.log(`Would send response to ${message.sender_id}: ${responseText}`);\n    \n  } catch (error) {\n    console.error('Error processing message:', error);\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGA,MAAM,eAAe,QAAQ,GAAG,CAAC,qBAAqB,IAAI;AAGnD,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,YAAY,aAAa,GAAG,CAAC;QAEnC,IAAI,SAAS,eAAe,UAAU,cAAc;YAClD,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC;QAC1B,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,4CAA4C;QAC5C,KAAK,MAAM,SAAS,KAAK,KAAK,IAAI,EAAE,CAAE;YACpC,KAAK,MAAM,aAAa,MAAM,SAAS,IAAI,EAAE,CAAE;gBAC7C,IAAI,UAAU,OAAO,EAAE;oBACrB,MAAM,sBAAsB;gBAC9B;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,QAAQ;QAAK;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,sBAAsB,SAAc;IACjD,IAAI;QACF,MAAM,WAAW,UAAU,MAAM,CAAC,EAAE;QACpC,MAAM,cAAc,UAAU,OAAO,CAAC,IAAI;QAC1C,MAAM,YAAY,UAAU,SAAS;QAErC,IAAI,CAAC,aAAa,QAAQ,iCAAiC;QAE3D,oCAAoC;QACpC,MAAM,cAA6B;YACjC,WAAW;YACX,cAAc;YACd,cAAc;YACd,UAAU;YACV,UAAU;gBACR;gBACA,eAAe;YACjB;QACF;QAEA,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC/D,IAAI,CAAC,YACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,yBAAyB;YACvC;QACF;QAEA,4CAA4C;QAC5C,MAAM,yBAAyB;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IACpD;AACF;AAEA,eAAe,yBAAyB,OAAY;IAClD,IAAI;QACF,kEAAkE;QAClE,+EAA+E;QAE/E,MAAM,cAAc,QAAQ,YAAY,CAAC,WAAW;QACpD,IAAI,eAAe;QACnB,IAAI,kBAAkB;QAEtB,sCAAsC;QACtC,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa;QAEnB,IAAI,YAAY;QAChB,IAAI,YAAY;QAEhB,KAAK,MAAM,SAAS,iBAAiB,EAAE,CAAE;YACvC,MAAM,WAAW,MAAM,QAAQ,IAAI,EAAE;YACrC,IAAI,QAAQ;YAEZ,KAAK,MAAM,WAAW,SAAU;gBAC9B,IAAI,YAAY,QAAQ,CAAC,QAAQ,WAAW,KAAK;oBAC/C,SAAS;gBACX;YACF;YAEA,IAAI,QAAQ,aAAa,QAAQ,GAAG;gBAClC,YAAY;gBACZ,YAAY;YACd;QACF;QAEA,IAAI,aAAa,aAAa,GAAG;YAC/B,eAAe,UAAU,MAAM;YAC/B,kBAAkB,KAAK,GAAG,CAAC,YAAY,UAAU,QAAQ,CAAC,MAAM,EAAE;QACpE,OAAO;YACL,oBAAoB;YACpB,eAAe;YACf,kBAAkB;QACpB;QAEA,4BAA4B;QAC5B,MAAM,eAA+B;YACnC,YAAY,QAAQ,EAAE;YACtB,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,cAAc;QAChB;QAEA,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,aACL,MAAM,CAAC;QAEV,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,0BAA0B;YACxC;QACF;QAEA,yEAAyE;QACzE,+BAA+B;QAC/B,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,SAAS,CAAC,EAAE,EAAE,cAAc;IAE5E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;AACF", "debugId": null}}]}