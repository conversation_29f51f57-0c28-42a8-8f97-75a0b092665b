"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[299],{4146:(e,t,r)=>{r.d(t,{CloseIcon:()=>n});var o=r(95155);let n=(0,r(12115).forwardRef)((e,t)=>{let{size:r="var(--cb-icon-size, 70%)",style:n,...i}=e;return(0,o.jsx)("svg",{viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{...n,width:r,height:r},ref:t,...i,children:(0,o.jsx)("path",{d:"M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})});n.displayName="@mantine/core/CloseIcon"},5296:(e,t,r)=>{r.d(t,{InputPlaceholder:()=>d});var o=r(95155);r(12115);var n=r(43664),i=r(53791),s=r(69604),a=r(36960),l=r(39972);let c={},d=(0,a.factory)((e,t)=>{let r=(0,n.useProps)("InputPlaceholder",c,e),{classNames:a,className:d,style:p,styles:u,unstyled:v,vars:m,__staticSelector:h,variant:f,error:y,mod:g,...I}=(0,n.useProps)("InputPlaceholder",c,r),x=(0,i.useStyles)({name:["InputPlaceholder",h],props:r,classes:l.A,className:d,style:p,classNames:a,styles:u,unstyled:v,rootSelector:"placeholder"});return(0,o.jsx)(s.Box,{...x("placeholder"),mod:[{error:!!y},g],component:"span",variant:f,ref:t,...I})});d.classes=l.A,d.displayName="@mantine/core/InputPlaceholder"},19394:(e,t,r)=>{r.d(t,{InputDescription:()=>h});var o=r(95155),n=r(5903);r(12115);var i=r(56204),s=r(68918),a=r(43664),l=r(53791),c=r(69604),d=r(36960),p=r(21355),u=r(39972);let v={},m=(0,s.createVarsResolver)((e,t)=>{let{size:r}=t;return{description:{"--input-description-size":void 0===r?void 0:"calc(".concat((0,i.getFontSize)(r)," - ").concat((0,n.D)(2),")")}}}),h=(0,d.factory)((e,t)=>{let r=(0,a.useProps)("InputDescription",v,e),{classNames:n,className:i,style:s,styles:d,unstyled:h,vars:f,size:y,__staticSelector:g,__inheritStyles:I=!0,variant:x,...b}=(0,a.useProps)("InputDescription",v,r),S=(0,p.useInputWrapperContext)(),z=(0,l.useStyles)({name:["InputWrapper",g],props:r,classes:u.A,className:i,style:s,classNames:n,styles:d,unstyled:h,rootSelector:"description",vars:f,varsResolver:m}),C=I&&(null==S?void 0:S.getStyles)||z;return(0,o.jsx)(c.Box,{component:"p",ref:t,variant:x,size:y,...C("description",(null==S?void 0:S.getStyles)?{className:i,style:s}:void 0),...b})});h.classes=u.A,h.displayName="@mantine/core/InputDescription"},21220:(e,t,r)=>{r.d(t,{Stack:()=>v});var o=r(95155);r(12115);var n=r(56204),i=r(68918),s=r(43664),a=r(53791),l=r(69604),c=r(36960),d={root:"m_6d731127"};let p={gap:"md",align:"stretch",justify:"flex-start"},u=(0,i.createVarsResolver)((e,t)=>{let{gap:r,align:o,justify:i}=t;return{root:{"--stack-gap":(0,n.getSpacing)(r),"--stack-align":o,"--stack-justify":i}}}),v=(0,c.factory)((e,t)=>{let r=(0,s.useProps)("Stack",p,e),{classNames:n,className:i,style:c,styles:v,unstyled:m,vars:h,align:f,justify:y,gap:g,variant:I,...x}=r,b=(0,a.useStyles)({name:"Stack",props:r,classes:d,className:i,style:c,classNames:n,styles:v,unstyled:m,vars:h,varsResolver:u});return(0,o.jsx)(l.Box,{ref:t,...b("root"),variant:I,...x})});v.classes=d,v.displayName="@mantine/core/Stack"},21355:(e,t,r)=>{r.d(t,{D:()=>o,useInputWrapperContext:()=>n}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)({offsetBottom:!1,offsetTop:!1,describedBy:void 0,getStyles:null,inputId:void 0,labelId:void 0})},21770:(e,t,r)=>{r.d(t,{InputLabel:()=>m});var o=r(95155);r(12115);var n=r(56204),i=r(68918),s=r(43664),a=r(53791),l=r(69604),c=r(36960),d=r(21355),p=r(39972);let u={labelElement:"label"},v=(0,i.createVarsResolver)((e,t)=>{let{size:r}=t;return{label:{"--input-label-size":(0,n.getFontSize)(r),"--input-asterisk-color":void 0}}}),m=(0,c.factory)((e,t)=>{let r=(0,s.useProps)("InputLabel",u,e),{classNames:n,className:i,style:c,styles:m,unstyled:h,vars:f,labelElement:y,size:g,required:I,htmlFor:x,onMouseDown:b,children:S,__staticSelector:z,variant:C,mod:P,...B}=(0,s.useProps)("InputLabel",u,r),w=(0,a.useStyles)({name:["InputWrapper",z],props:r,classes:p.A,className:i,style:c,classNames:n,styles:m,unstyled:h,rootSelector:"label",vars:f,varsResolver:v}),j=(0,d.useInputWrapperContext)(),_=(null==j?void 0:j.getStyles)||w;return(0,o.jsxs)(l.Box,{..._("label",(null==j?void 0:j.getStyles)?{className:i,style:c}:void 0),component:y,variant:C,size:g,ref:t,htmlFor:"label"===y?x:void 0,mod:[{required:I},P],onMouseDown:e=>{null==b||b(e),!e.defaultPrevented&&e.detail>1&&e.preventDefault()},...B,children:[S,I&&(0,o.jsx)("span",{..._("required"),"aria-hidden":!0,children:" *"})]})});m.classes=p.A,m.displayName="@mantine/core/InputLabel"},24225:(e,t,r)=>{r.d(t,{InputBase:()=>l});var o=r(95155);r(12115);var n=r(64511),i=r(54853),s=r(83746);let a={__staticSelector:"InputBase",withAria:!0},l=(0,n.polymorphicFactory)((e,t)=>{let{inputProps:r,wrapperProps:n,...l}=(0,s.useInputProps)("InputBase",a,e);return(0,o.jsx)(i.Input.Wrapper,{...n,children:(0,o.jsx)(i.Input,{...r,...l,ref:t})})});l.classes={...i.Input.classes,...i.Input.Wrapper.classes},l.displayName="@mantine/core/InputBase"},38294:(e,t,r)=>{r.d(t,{E:()=>o,z:()=>n}),r(12115),r(95155);let[o,n]=(0,r(49830).createOptionalContext)({size:"sm"})},39972:(e,t,r)=>{r.d(t,{A:()=>o});var o={wrapper:"m_6c018570",input:"m_8fb7ebe7",section:"m_82577fc2",placeholder:"m_88bacfd0",root:"m_46b77525",label:"m_8fdc1311",required:"m_78a94662",error:"m_8f816625",description:"m_fe47ce59"}},49560:(e,t,r)=>{r.d(t,{InputClearButton:()=>d});var o=r(95155);r(12115);var n=r(86028),i=r(43664),s=r(36960),a=r(95642),l=r(38294);let c={},d=(0,s.factory)((e,t)=>{let r=(0,i.useProps)("InputClearButton",c,e),{size:s,variant:d,vars:p,classNames:u,styles:v,...m}=r,h=(0,l.z)(),{resolvedClassNames:f,resolvedStyles:y}=(0,n.useResolvedStylesApi)({classNames:u,styles:v,props:r});return(0,o.jsx)(a.CloseButton,{variant:d||"transparent",ref:t,size:s||(null==h?void 0:h.size)||"sm",classNames:f,styles:y,__staticSelector:"InputClearButton",...m})});d.displayName="@mantine/core/InputClearButton"},49830:(e,t,r)=>{r.d(t,{createOptionalContext:()=>i});var o=r(95155),n=r(12115);function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=(0,n.createContext)(e);return[e=>{let{children:r,value:n}=e;return(0,o.jsx)(t.Provider,{value:n,children:r})},()=>(0,n.useContext)(t)]}},54853:(e,t,r)=>{r.d(t,{Input:()=>z});var o=r(95155),n=r(5903);r(12115);var i=r(56204),s=r(68918),a=r(43664),l=r(53791),c=r(99537),d=r(69604),p=r(64511),u=r(38294),v=r(49560),m=r(19394),h=r(68454),f=r(21770),y=r(5296),g=r(21355),I=r(71182),x=r(39972);let b={variant:"default",leftSectionPointerEvents:"none",rightSectionPointerEvents:"none",withAria:!0,withErrorStyles:!0},S=(0,s.createVarsResolver)((e,t,r)=>({wrapper:{"--input-margin-top":r.offsetTop?"calc(var(--mantine-spacing-xs) / 2)":void 0,"--input-margin-bottom":r.offsetBottom?"calc(var(--mantine-spacing-xs) / 2)":void 0,"--input-height":(0,i.getSize)(t.size,"input-height"),"--input-fz":(0,i.getFontSize)(t.size),"--input-radius":void 0===t.radius?void 0:(0,i.getRadius)(t.radius),"--input-left-section-width":void 0!==t.leftSectionWidth?(0,n.D)(t.leftSectionWidth):void 0,"--input-right-section-width":void 0!==t.rightSectionWidth?(0,n.D)(t.rightSectionWidth):void 0,"--input-padding-y":t.multiline?(0,i.getSize)(t.size,"input-padding-y"):void 0,"--input-left-section-pointer-events":t.leftSectionPointerEvents,"--input-right-section-pointer-events":t.rightSectionPointerEvents}})),z=(0,p.polymorphicFactory)((e,t)=>{let r=(0,a.useProps)("Input",b,e),{classNames:n,className:i,style:s,styles:p,unstyled:v,required:m,__staticSelector:h,__stylesApiProps:f,size:y,wrapperProps:I,error:z,disabled:C,leftSection:P,leftSectionProps:B,leftSectionWidth:w,rightSection:j,rightSectionProps:_,rightSectionWidth:W,rightSectionPointerEvents:A,leftSectionPointerEvents:N,variant:R,vars:D,pointer:E,multiline:k,radius:L,id:F,withAria:V,withErrorStyles:T,mod:H,inputSize:O,__clearSection:q,__clearable:M,__defaultRightSection:U,...Z}=r,{styleProps:G,rest:J}=(0,c.extractStyleProps)(Z),K=(0,g.useInputWrapperContext)(),Q={offsetBottom:null==K?void 0:K.offsetBottom,offsetTop:null==K?void 0:K.offsetTop},X=(0,l.useStyles)({name:["Input",h],props:f||r,classes:x.A,className:i,style:s,classNames:n,styles:p,unstyled:v,stylesCtx:Q,rootSelector:"wrapper",vars:D,varsResolver:S}),Y=V?{required:m,disabled:C,"aria-invalid":!!z,"aria-describedby":null==K?void 0:K.describedBy,id:(null==K?void 0:K.inputId)||F}:{},$=j||M&&q||U;return(0,o.jsx)(u.E,{value:{size:y||"sm"},children:(0,o.jsxs)(d.Box,{...X("wrapper"),...G,...I,mod:[{error:!!z&&T,pointer:E,disabled:C,multiline:k,"data-with-right-section":!!$,"data-with-left-section":!!P},H],variant:R,size:y,children:[P&&(0,o.jsx)("div",{...B,"data-position":"left",...X("section",{className:null==B?void 0:B.className,style:null==B?void 0:B.style}),children:P}),(0,o.jsx)(d.Box,{component:"input",...J,...Y,ref:t,required:m,mod:{disabled:C,error:!!z&&T},variant:R,__size:O,...X("input")}),$&&(0,o.jsx)("div",{..._,"data-position":"right",...X("section",{className:null==_?void 0:_.className,style:null==_?void 0:_.style}),children:$})]})})});z.classes=x.A,z.Wrapper=I.InputWrapper,z.Label=f.InputLabel,z.Error=h.InputError,z.Description=m.InputDescription,z.Placeholder=y.InputPlaceholder,z.ClearButton=v.InputClearButton,z.displayName="@mantine/core/Input"},64173:(e,t,r)=>{r.d(t,{useId:()=>a});var o=r(12115),n=r(73141),i=r(74275);let s=o["useId".toString()]||(()=>void 0);function a(e){let t=function(){let e=s();return e?"mantine-".concat(e.replace(/:/g,"")):""}(),[r,a]=(0,o.useState)(t);return((0,n.useIsomorphicEffect)(()=>{a((0,i.randomId)())},[]),"string"==typeof e)?e:"undefined"==typeof window?t:r}},68454:(e,t,r)=>{r.d(t,{InputError:()=>h});var o=r(95155),n=r(5903);r(12115);var i=r(56204),s=r(68918),a=r(43664),l=r(53791),c=r(69604),d=r(36960),p=r(21355),u=r(39972);let v={},m=(0,s.createVarsResolver)((e,t)=>{let{size:r}=t;return{error:{"--input-error-size":void 0===r?void 0:"calc(".concat((0,i.getFontSize)(r)," - ").concat((0,n.D)(2),")")}}}),h=(0,d.factory)((e,t)=>{let r=(0,a.useProps)("InputError",v,e),{classNames:n,className:i,style:s,styles:d,unstyled:h,vars:f,size:y,__staticSelector:g,__inheritStyles:I=!0,variant:x,...b}=r,S=(0,l.useStyles)({name:["InputWrapper",g],props:r,classes:u.A,className:i,style:s,classNames:n,styles:d,unstyled:h,rootSelector:"error",vars:f,varsResolver:m}),z=(0,p.useInputWrapperContext)(),C=I&&(null==z?void 0:z.getStyles)||S;return(0,o.jsx)(c.Box,{component:"p",ref:t,variant:x,size:y,...C("error",(null==z?void 0:z.getStyles)?{className:i,style:s}:void 0),...b})});h.classes=u.A,h.displayName="@mantine/core/InputError"},71182:(e,t,r)=>{r.d(t,{InputWrapper:()=>x});var o=r(95155),n=r(12115),i=r(64173),s=r(5903),a=r(56204),l=r(68918),c=r(43664),d=r(53791),p=r(69604),u=r(36960),v=r(19394),m=r(68454),h=r(21770),f=r(21355),y=r(39972);let g={labelElement:"label",inputContainer:e=>e,inputWrapperOrder:["label","description","input","error"]},I=(0,l.createVarsResolver)((e,t)=>{let{size:r}=t;return{label:{"--input-label-size":(0,a.getFontSize)(r),"--input-asterisk-color":void 0},error:{"--input-error-size":void 0===r?void 0:"calc(".concat((0,a.getFontSize)(r)," - ").concat((0,s.D)(2),")")},description:{"--input-description-size":void 0===r?void 0:"calc(".concat((0,a.getFontSize)(r)," - ").concat((0,s.D)(2),")")}}}),x=(0,u.factory)((e,t)=>{let r=(0,c.useProps)("InputWrapper",g,e),{classNames:s,className:a,style:l,styles:u,unstyled:x,vars:b,size:S,variant:z,__staticSelector:C,inputContainer:P,inputWrapperOrder:B,label:w,error:j,description:_,labelProps:W,descriptionProps:A,errorProps:N,labelElement:R,children:D,withAsterisk:E,id:k,required:L,__stylesApiProps:F,mod:V,...T}=r,H=(0,d.useStyles)({name:["InputWrapper",C],props:F||r,classes:y.A,className:a,style:l,classNames:s,styles:u,unstyled:x,vars:b,varsResolver:I}),O={size:S,variant:z,__staticSelector:C},q=(0,i.useId)(k),M=(null==N?void 0:N.id)||"".concat(q,"-error"),U=(null==A?void 0:A.id)||"".concat(q,"-description"),Z=!!j&&"boolean"!=typeof j,G=!!_,J="".concat(Z?M:""," ").concat(G?U:""),K=J.trim().length>0?J.trim():void 0,Q=(null==W?void 0:W.id)||"".concat(q,"-label"),X=w&&(0,o.jsx)(h.InputLabel,{labelElement:R,id:Q,htmlFor:q,required:"boolean"==typeof E?E:L,...O,...W,children:w},"label"),Y=G&&(0,o.jsx)(v.InputDescription,{...A,...O,size:(null==A?void 0:A.size)||O.size,id:(null==A?void 0:A.id)||U,children:_},"description"),$=(0,o.jsx)(n.Fragment,{children:P(D)},"input"),ee=Z&&(0,n.createElement)(m.InputError,{...N,...O,size:(null==N?void 0:N.size)||O.size,key:"error",id:(null==N?void 0:N.id)||M},j),et=B.map(e=>{switch(e){case"label":return X;case"input":return $;case"description":return Y;case"error":return ee;default:return null}});return(0,o.jsx)(f.D,{value:{getStyles:H,describedBy:K,inputId:q,labelId:Q,...function(e,t){let{hasDescription:r,hasError:o}=t,n=e.findIndex(e=>"input"===e),i=e.slice(0,n),s=e.slice(n+1),a=r&&i.includes("description")||o&&i.includes("error");return{offsetBottom:r&&s.includes("description")||o&&s.includes("error"),offsetTop:a}}(B,{hasDescription:G,hasError:Z})},children:(0,o.jsx)(p.Box,{ref:t,variant:z,size:S,mod:[{error:!!j},V],...H("root"),...T,children:et})})});x.classes=y.A,x.displayName="@mantine/core/InputWrapper"},74634:(e,t,r)=>{r.d(t,{Alert:()=>h});var o=r(95155),n=r(64173);r(12115);var i=r(56204),s=r(68918),a=r(43664),l=r(53791),c=r(69604),d=r(36960),p=r(95642),u={root:"m_66836ed3",wrapper:"m_a5d60502",body:"m_667c2793",title:"m_6a03f287",label:"m_698f4f23",icon:"m_667f2a6a",message:"m_7fa78076",closeButton:"m_87f54839"};let v={},m=(0,s.createVarsResolver)((e,t)=>{let{radius:r,color:o,variant:n,autoContrast:s}=t,a=e.variantColorResolver({color:o||e.primaryColor,theme:e,variant:n||"light",autoContrast:s});return{root:{"--alert-radius":void 0===r?void 0:(0,i.getRadius)(r),"--alert-bg":o||n?a.background:void 0,"--alert-color":a.color,"--alert-bd":o||n?a.border:void 0}}}),h=(0,d.factory)((e,t)=>{let r=(0,a.useProps)("Alert",v,e),{classNames:i,className:s,style:d,styles:h,unstyled:f,vars:y,radius:g,color:I,title:x,children:b,id:S,icon:z,withCloseButton:C,onClose:P,closeButtonLabel:B,variant:w,autoContrast:j,..._}=r,W=(0,l.useStyles)({name:"Alert",classes:u,props:r,className:s,style:d,classNames:i,styles:h,unstyled:f,vars:y,varsResolver:m}),A=(0,n.useId)(S),N=x&&"".concat(A,"-title")||void 0,R="".concat(A,"-body");return(0,o.jsx)(c.Box,{id:A,...W("root",{variant:w}),variant:w,ref:t,..._,role:"alert","aria-describedby":R,"aria-labelledby":N,children:(0,o.jsxs)("div",{...W("wrapper"),children:[z&&(0,o.jsx)("div",{...W("icon"),children:z}),(0,o.jsxs)("div",{...W("body"),children:[x&&(0,o.jsx)("div",{...W("title"),"data-with-close-button":C||void 0,children:(0,o.jsx)("span",{id:N,...W("label"),children:x})}),b&&(0,o.jsx)("div",{id:R,...W("message"),"data-variant":w,children:b})]}),C&&(0,o.jsx)(p.CloseButton,{...W("closeButton"),onClick:P,variant:"transparent",size:16,iconSize:16,"aria-label":B,unstyled:f})]})})});h.classes=u,h.displayName="@mantine/core/Alert"},83746:(e,t,r)=>{r.d(t,{useInputProps:()=>i}),r(12115),r(95155);var o=r(43664),n=r(99537);function i(e,t,r){let i=(0,o.useProps)(e,t,r),{label:s,description:a,error:l,required:c,classNames:d,styles:p,className:u,unstyled:v,__staticSelector:m,__stylesApiProps:h,errorProps:f,labelProps:y,descriptionProps:g,wrapperProps:I,id:x,size:b,style:S,inputContainer:z,inputWrapperOrder:C,withAsterisk:P,variant:B,vars:w,mod:j,..._}=i,{styleProps:W,rest:A}=(0,n.extractStyleProps)(_),N={label:s,description:a,error:l,required:c,classNames:d,className:u,__staticSelector:m,__stylesApiProps:h||i,errorProps:f,labelProps:y,descriptionProps:g,unstyled:v,styles:p,size:b,style:S,inputContainer:z,inputWrapperOrder:C,withAsterisk:P,variant:B,id:x,mod:j,...I};return{...A,classNames:d,styles:p,unstyled:v,wrapperProps:{...N,...W},inputProps:{required:c,classNames:d,styles:p,unstyled:v,size:b,__staticSelector:m,__stylesApiProps:h||i,error:l,variant:B,id:x}}}},86028:(e,t,r)=>{r.d(t,{useResolvedStylesApi:()=>s}),r(12115),r(95155);var o=r(3131),n=r(19787),i=r(84092);function s(e){let{classNames:t,styles:r,props:s,stylesCtx:a}=e,l=(0,o.useMantineTheme)();return{resolvedClassNames:(0,n.resolveClassNames)({theme:l,classNames:t,props:s,stylesCtx:a||void 0}),resolvedStyles:(0,i.resolveStyles)({theme:l,styles:r,props:s,stylesCtx:a||void 0})}}},93751:(e,t,r)=>{r.d(t,{Title:()=>h});var o=r(95155);r(12115);var n=r(68918),i=r(43664),s=r(53791),a=r(69604),l=r(36960),c=r(5903);let d=["h1","h2","h3","h4","h5","h6"],p=["xs","sm","md","lg","xl"];var u={root:"m_8a5d1357"};let v={order:1},m=(0,n.createVarsResolver)((e,t)=>{let{order:r,size:o,lineClamp:n,textWrap:i}=t,s=function(e,t){let r=void 0!==t?t:"h".concat(e);return d.includes(r)?{fontSize:"var(--mantine-".concat(r,"-font-size)"),fontWeight:"var(--mantine-".concat(r,"-font-weight)"),lineHeight:"var(--mantine-".concat(r,"-line-height)")}:p.includes(r)?{fontSize:"var(--mantine-font-size-".concat(r,")"),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}:{fontSize:(0,c.D)(r),fontWeight:"var(--mantine-h".concat(e,"-font-weight)"),lineHeight:"var(--mantine-h".concat(e,"-line-height)")}}(r||1,o);return{root:{"--title-fw":s.fontWeight,"--title-lh":s.lineHeight,"--title-fz":s.fontSize,"--title-line-clamp":"number"==typeof n?n.toString():void 0,"--title-text-wrap":i}}}),h=(0,l.factory)((e,t)=>{let r=(0,i.useProps)("Title",v,e),{classNames:n,className:l,style:c,styles:d,unstyled:p,order:h,vars:f,size:y,variant:g,lineClamp:I,textWrap:x,mod:b,...S}=r,z=(0,s.useStyles)({name:"Title",props:r,classes:u,className:l,style:c,classNames:n,styles:d,unstyled:p,vars:f,varsResolver:m});return[1,2,3,4,5,6].includes(h)?(0,o.jsx)(a.Box,{...z("root"),component:"h".concat(h),variant:g,ref:t,mod:[{order:h,"data-line-clamp":"number"==typeof I},b],size:y,...S}):null});h.classes=u,h.displayName="@mantine/core/Title"},95642:(e,t,r)=>{r.d(t,{CloseButton:()=>h});var o=r(95155),n=r(5903);r(12115);var i=r(56204),s=r(68918),a=r(43664),l=r(53791),c=r(64511),d=r(43608),p=r(4146),u={root:"m_86a44da5","root--subtle":"m_220c80f2"};let v={variant:"subtle"},m=(0,s.createVarsResolver)((e,t)=>{let{size:r,radius:o,iconSize:s}=t;return{root:{"--cb-size":(0,i.getSize)(r,"cb-size"),"--cb-radius":void 0===o?void 0:(0,i.getRadius)(o),"--cb-icon-size":(0,n.D)(s)}}}),h=(0,c.polymorphicFactory)((e,t)=>{let r=(0,a.useProps)("CloseButton",v,e),{iconSize:n,children:i,vars:s,radius:c,className:h,classNames:f,style:y,styles:g,unstyled:I,"data-disabled":x,disabled:b,variant:S,icon:z,mod:C,__staticSelector:P,...B}=r,w=(0,l.useStyles)({name:P||"CloseButton",props:r,className:h,style:y,classes:u,classNames:f,styles:g,unstyled:I,vars:s,varsResolver:m});return(0,o.jsxs)(d.UnstyledButton,{ref:t,...B,unstyled:I,variant:S,disabled:b,mod:[{disabled:b||x},C],...w("root",{variant:S,active:!b&&!x}),children:[z||(0,o.jsx)(p.CloseIcon,{}),i]})});h.classes=u,h.displayName="@mantine/core/CloseButton"},97287:(e,t,r)=>{r.d(t,{Paper:()=>v});var o=r(95155);r(12115);var n=r(56204),i=r(68918),s=r(43664),a=r(53791),l=r(69604),c=r(64511),d={root:"m_1b7284a3"};let p={},u=(0,i.createVarsResolver)((e,t)=>{let{radius:r,shadow:o}=t;return{root:{"--paper-radius":void 0===r?void 0:(0,n.getRadius)(r),"--paper-shadow":(0,n.getShadow)(o)}}}),v=(0,c.polymorphicFactory)((e,t)=>{let r=(0,s.useProps)("Paper",p,e),{classNames:n,className:i,style:c,styles:v,unstyled:m,withBorder:h,vars:f,radius:y,shadow:g,variant:I,mod:x,...b}=r,S=(0,a.useStyles)({name:"Paper",props:r,classes:d,className:i,style:c,classNames:n,styles:v,unstyled:m,vars:f,varsResolver:u});return(0,o.jsx)(l.Box,{ref:t,mod:[{"data-with-border":h},x],...S("root"),variant:I,...b})});v.classes=d,v.displayName="@mantine/core/Paper"}}]);