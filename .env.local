# Supabase Configuration

NEXT_PUBLIC_SUPABASE_URL=https://sumqnhdtdmslsxstabsq.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.FziDRFp4tJghnCrGUrXMlUlNuZr6suHHheloq5NWGwA
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.wE9RPTQMFYDuE97LOzfIZSpUjUAwiN-I4l8UqylNjNs

# Authentication
JWT_SECRET=4/q8i3/QmvuZATVwT1wDwqX1z/dvnvZOp7d27CrEF5w=

# Facebook Messenger Webhook
FACEBOOK_VERIFY_TOKEN=my_verify_token_123
FACEBOOK_PAGE_ACCESS_TOKEN=EAAJAD9s0bE4BO6pZBRuJytkZCGarrxa6gxPnGOOgL54nwDLV8Eo5jWLEUL4oZCFyVl1n3FuF40139KbwMXlZAuE7liFA42ltis2HAbcIryuShlRwY6WQELSkruS02qyH6kEwT2R6dX7PHohgZBlsU7kRzFrvzfZBDmz4ZCRg6lbrwjlI3eTZC6M8cZBP9q8Oo8yF3nlZCN7k5XmohzLxHRDXgzywZDZD

# LLM Provider Configuration
LLM_PROVIDER=gemini
# Options: gemini, openrouter, openai, anthropic

# API Keys (use only the one for your chosen provider)
GEMINI_API_KEY=AIzaSyDsTnrpEYwnlgVllJsy7Hwa67vQwCcjOeo
GEMINI_MODEL=gemini-pro

OPENROUTER_API_KEY=your_openrouter_api_key
OPENROUTER_MODEL=google/gemini-pro

OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo

ANTHROPIC_API_KEY=your_anthropic_api_key
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Bot Configuration
REPLY_TONE=friendly
# Options: professional, friendly, casual, formal

CONFIDENCE_THRESHOLD=0.8
# Range: 0.0 to 1.0

MAX_RESPONSE_LENGTH=500
# Maximum characters in bot responses

ENABLE_AUTO_REPLY=true
# Set to false to disable automatic responses

FALLBACK_MESSAGE=Thank you for your message. Our team will get back to you soon!
# Default message when bot can't provide a confident response

# Development
NODE_ENV=development
