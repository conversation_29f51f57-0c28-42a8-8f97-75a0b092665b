"use strict";exports.id=465,exports.ids=[465],exports.modules={16189:(e,t,r)=>{var l=r(65773);r.o(l,"useRouter")&&r.d(t,{useRouter:function(){return l.useRouter}}),r.o(l,"useSearchParams")&&r.d(t,{useSearchParams:function(){return l.useSearchParams}})},73716:e=>{e.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;if(Array.isArray(t)){if((l=t.length)!=r.length)return!1;for(a=l;0!=a--;)if(!e(t[a],r[a]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((l=(n=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(a=l;0!=a--;)if(!Object.prototype.hasOwnProperty.call(r,n[a]))return!1;for(a=l;0!=a--;){var l,a,n,u=n[a];if(!e(t[u],r[u]))return!1}return!0}return t!=t&&r!=r}},84349:(e,t,r)=>{r.d(t,{A:()=>l});var l=(0,r(6445).A)("outline","alert-circle","IconAlertCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])},96193:(e,t,r)=>{r.d(t,{m:()=>S});var l=r(43210);let a=l.useEffect;function n(e,t){a(()=>{if(e)return window.addEventListener(e,t),()=>window.removeEventListener(e,t)},[e])}function u(e){return null===e||"object"!=typeof e?{}:Object.keys(e).reduce((t,r)=>{let l=e[r];return null!=l&&!1!==l&&(t[r]=l),t},{})}function s(e,t){if(null===t||"object"!=typeof t)return{};let r={...t};return Object.keys(t).forEach(t=>{t.includes(`${String(e)}.`)&&delete r[t]}),r}function o(e,t){return parseInt(e.substring(t.length+1).split(".")[0],10)}function c(e,t,r,l){if(void 0===t)return r;let a=`${String(e)}`,n=r;-1===l&&(n=s(`${a}.${t}`,n));let u={...n},c=new Set;return Object.entries(n).filter(([e])=>{if(!e.startsWith(`${a}.`))return!1;let r=o(e,a);return!Number.isNaN(r)&&r>=t}).forEach(([e,t])=>{let r=o(e,a),n=e.replace(`${a}.${r}`,`${a}.${r+l}`);u[n]=t,c.add(n),c.has(e)||delete u[e]}),u}function i(e){return"string"!=typeof e?[]:e.split(".")}function f(e,t){let r=i(e);if(0===r.length||"object"!=typeof t||null===t)return;let l=t[r[0]];for(let e=1;e<r.length&&null!=l;e+=1)l=l[r[e]];return l}function d(e,t,r){"object"==typeof r.value&&(r.value=p(r.value)),r.enumerable&&!r.get&&!r.set&&r.configurable&&r.writable&&"__proto__"!==t?e[t]=r.value:Object.defineProperty(e,t,r)}function p(e){if("object"!=typeof e)return e;var t,r,l,a=0,n=Object.prototype.toString.call(e);if("[object Object]"===n?l=Object.create(e.__proto__||null):"[object Array]"===n?l=Array(e.length):"[object Set]"===n?(l=new Set,e.forEach(function(e){l.add(p(e))})):"[object Map]"===n?(l=new Map,e.forEach(function(e,t){l.set(p(t),p(e))})):"[object Date]"===n?l=new Date(+e):"[object RegExp]"===n?l=new RegExp(e.source,e.flags):"[object DataView]"===n?l=new e.constructor(p(e.buffer)):"[object ArrayBuffer]"===n?l=e.slice(0):"Array]"===n.slice(-6)&&(l=new e.constructor(e)),l){for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)d(l,r[a],Object.getOwnPropertyDescriptor(e,r[a]));for(a=0,r=Object.getOwnPropertyNames(e);a<r.length;a++)Object.hasOwnProperty.call(l,t=r[a])&&l[t]===e[t]||d(l,t,Object.getOwnPropertyDescriptor(e,t))}return l||e}function b(e,t,r){let l=i(e);if(0===l.length)return r;let a=p(r);if(1===l.length)return a[l[0]]=t,a;let n=a[l[0]];for(let e=1;e<l.length-1;e+=1){if(void 0===n)return a;n=n[l[e]]}return n[l[l.length-1]]=t,a}var h=r(73716);function y(e,t){let r=Object.keys(e);if("string"==typeof t){let l=r.filter(e=>e.startsWith(`${t}.`));return e[t]||l.some(t=>e[t])||!1}return r.some(t=>e[t])}function m(e,t){return e?`${e}-${t.toString()}`:t.toString()}let v=Symbol("root-rule");function g(e){let t=u(e);return{hasErrors:Object.keys(t).length>0,errors:t}}function k(e,t){return"function"==typeof e?g(e(t)):g(function e(t,r,l="",a={}){return"object"!=typeof t||null===t?a:Object.keys(t).reduce((a,n)=>{let u=t[n],s=`${""===l?"":`${l}.`}${n}`,o=f(s,r),c=!1;return"function"==typeof u&&(a[s]=u(o,r,s)),"object"==typeof u&&Array.isArray(o)&&(c=!0,o.forEach((t,l)=>e(u,r,`${s}.${l}`,a)),v in u&&(a[s]=u[v](o,r,s))),"object"==typeof u&&"object"==typeof o&&null!==o&&(c||e(u,r,s,a),v in u&&(a[s]=u[v](o,r,s))),a},a)}(e,t))}function E(e,t,r){if("string"!=typeof e)return{hasError:!1,error:null};let l=k(t,r),a=Object.keys(l.errors).find(t=>e.split(".").every((e,r)=>e===t.split(".")[r]));return{hasError:!!a,error:a?l.errors[a]:null}}function V(e,t){return!!t&&("boolean"==typeof t?t:!!Array.isArray(t)&&t.includes(e.replace(/[.][0-9]+/g,".__MANTINE_FORM_INDEX__")))}function S({name:e,mode:t="controlled",initialValues:r,initialErrors:a={},initialDirty:o={},initialTouched:i={},clearInputErrorOnChange:d=!0,validateInputOnChange:p=!1,validateInputOnBlur:v=!1,onValuesChange:g,transformValues:j=e=>e,enhanceGetInputProps:C,validate:$,onSubmitPreventDefault:O="always",touchTrigger:F="change"}={}){let D=function(e){let[t,r]=(0,l.useState)(u(e)),a=(0,l.useRef)(t),n=(0,l.useCallback)(e=>{r(t=>{let r=u("function"==typeof e?e(t):e);return a.current=r,r})},[]),s=(0,l.useCallback)(()=>n({}),[]),o=(0,l.useCallback)(e=>{void 0!==a.current[e]&&n(t=>{let r={...t};return delete r[e],r})},[t]),c=(0,l.useCallback)((e,t)=>{null==t||!1===t?o(e):a.current[e]!==t&&n(r=>({...r,[e]:t}))},[t]);return{errorsState:t,setErrors:n,clearErrors:s,setFieldError:c,clearFieldError:o}}(a),w=function({initialValues:e,onValuesChange:t,mode:r}){let a=(0,l.useRef)(!1),[n,u]=(0,l.useState)(e||{}),s=(0,l.useRef)(n),o=(0,l.useRef)(n),c=(0,l.useCallback)(({values:e,subscribers:l,updateState:a=!0,mergeWithPreviousValues:n=!0})=>{let o=s.current,c=e instanceof Function?e(s.current):e,i=n?{...o,...c}:c;s.current=i,a&&(u(i),"uncontrolled"===r&&(s.current=i)),t?.(i,o),l?.filter(Boolean).forEach(e=>e({updatedValues:i,previousValues:o}))},[t]),i=(0,l.useCallback)(e=>{let t=f(e.path,s.current),r=e.value instanceof Function?e.value(t):e.value;if(t!==r){let t=s.current,l=b(e.path,r,s.current);c({values:l,updateState:e.updateState}),e.subscribers?.filter(Boolean).forEach(r=>r({path:e.path,updatedValues:l,previousValues:t}))}},[c]),d=(0,l.useCallback)(e=>{o.current=e},[]),p=(0,l.useCallback)((e,t)=>{a.current||(a.current=!0,c({values:e,updateState:"controlled"===r}),d(e),t())},[c]),h=(0,l.useCallback)(()=>{c({values:o.current,updateState:!0,mergeWithPreviousValues:!1})},[c]),y=(0,l.useCallback)(()=>s.current,[]),m=(0,l.useCallback)(()=>o.current,[]),v=(0,l.useCallback)(e=>{let t=f(e,o.current);void 0!==t&&i({path:e,value:t,updateState:"uncontrolled"===r||void 0})},[i,r]);return{initialized:a,stateValues:n,refValues:s,valuesSnapshot:o,setValues:c,setFieldValue:i,resetValues:h,setValuesSnapshot:d,initialize:p,getValues:y,getValuesSnapshot:m,resetField:v}}({initialValues:r,onValuesChange:g,mode:t}),A=function({initialDirty:e,initialTouched:t,mode:r,$values:a}){let[n,u]=(0,l.useState)(t),[o,c]=(0,l.useState)(e),i=(0,l.useRef)(t),d=(0,l.useRef)(e),p=(0,l.useCallback)(e=>{let t="function"==typeof e?e(i.current):e;i.current=t,"controlled"===r&&u(t)},[]),b=(0,l.useCallback)((e,t=!1)=>{let l="function"==typeof e?e(d.current):e;d.current=l,("controlled"===r||t)&&c(l)},[]),m=(0,l.useCallback)(()=>p({}),[]),v=(0,l.useCallback)(e=>{let t=e?{...a.refValues.current,...e}:a.refValues.current;a.setValuesSnapshot(t),b({})},[]),g=(0,l.useCallback)((e,t)=>{p(r=>y(r,e)===t?r:{...r,[e]:t})},[]),k=(0,l.useCallback)((e,t,r)=>{b(r=>y(r,e)===t?r:{...r,[e]:t},r)},[]),E=(0,l.useCallback)((e,t)=>{let r=y(d.current,e),l=!h(f(e,a.getValuesSnapshot()),t),n=s(e,d.current);n[e]=l,b(n,r!==l)},[]),V=(0,l.useCallback)(e=>y(i.current,e),[]),S=(0,l.useCallback)(e=>b(t=>{if("string"!=typeof e)return t;let r=s(e,t);return(delete r[e],h(r,t))?t:r}),[]),j=(0,l.useCallback)(e=>{if(e){let t=f(e,d.current);return"boolean"==typeof t?t:!h(f(e,a.refValues.current),f(e,a.valuesSnapshot.current))}return Object.keys(d.current).length>0?y(d.current):!h(a.refValues.current,a.valuesSnapshot.current)},[]),C=(0,l.useCallback)(()=>d.current,[]),$=(0,l.useCallback)(()=>i.current,[]);return{touchedState:n,dirtyState:o,touchedRef:i,dirtyRef:d,setTouched:p,setDirty:b,resetDirty:v,resetTouched:m,isTouched:V,setFieldTouched:g,setFieldDirty:k,setTouchedState:u,setDirtyState:c,clearFieldDirty:S,isDirty:j,getDirty:C,getTouched:$,setCalculatedFieldDirty:E}}({initialDirty:o,initialTouched:i,$values:w,mode:t}),I=function({$values:e,$errors:t,$status:r}){let a=(0,l.useCallback)((l,a)=>{r.clearFieldDirty(l),t.setErrors(e=>(function(e,{from:t,to:r},l){let a=`${e}.${t}`,n=`${e}.${r}`,u={...l},s=new Set;return Object.keys(l).forEach(e=>{let t,r;if(!s.has(e)&&(e.startsWith(a)?(t=e,r=e.replace(a,n)):e.startsWith(n)&&(t=e.replace(n,a),r=e),t&&r)){let e=u[t],l=u[r];void 0===l?delete u[t]:u[t]=l,void 0===e?delete u[r]:u[r]=e,s.add(t),s.add(r)}}),u})(l,a,e)),e.setValues({values:function(e,{from:t,to:r},l){let a=f(e,l);if(!Array.isArray(a))return l;let n=[...a],u=a[t];return n.splice(t,1),n.splice(r,0,u),b(e,n,l)}(l,a,e.refValues.current),updateState:!0})},[]),n=(0,l.useCallback)((l,a)=>{r.clearFieldDirty(l),t.setErrors(e=>c(l,a,e,-1)),e.setValues({values:function(e,t,r){let l=f(e,r);return Array.isArray(l)?b(e,l.filter((e,r)=>r!==t),r):r}(l,a,e.refValues.current),updateState:!0})},[]);return{reorderListItem:a,removeListItem:n,insertListItem:(0,l.useCallback)((l,a,n)=>{r.clearFieldDirty(l),t.setErrors(e=>c(l,n,e,1)),e.setValues({values:function(e,t,r,l){let a=f(e,l);if(!Array.isArray(a))return l;let n=[...a];return n.splice("number"==typeof r?r:n.length,0,t),b(e,n,l)}(l,a,n,e.refValues.current),updateState:!0})},[]),replaceListItem:(0,l.useCallback)((t,l,a)=>{r.clearFieldDirty(t),e.setValues({values:function(e,t,r,l){let a=f(e,l);if(!Array.isArray(a)||a.length<=r)return l;let n=[...a];return n[r]=t,b(e,n,l)}(t,a,l,e.refValues.current),updateState:!0})},[])}}({$values:w,$errors:D,$status:A}),T=function({$status:e}){let t=(0,l.useRef)({}),r=(0,l.useCallback)((e,r)=>{(0,l.useEffect)(()=>(t.current[e]=t.current[e]||[],t.current[e].push(r),()=>{t.current[e]=t.current[e].filter(e=>e!==r)}),[r])},[]),a=(0,l.useCallback)(r=>t.current[r]?t.current[r].map(t=>l=>t({previousValue:f(r,l.previousValues),value:f(r,l.updatedValues),touched:e.isTouched(r),dirty:e.isDirty(r)})):[],[]);return{subscribers:t,watch:r,getFieldSubscribers:a}}({$status:A}),[L,P]=(0,l.useState)(0),[R,_]=(0,l.useState)({}),[x,M]=(0,l.useState)(!1),N=(0,l.useCallback)(()=>{w.resetValues(),D.clearErrors(),A.resetDirty(),A.resetTouched(),"uncontrolled"===t&&P(e=>e+1)},[]),W=(0,l.useCallback)(e=>{d&&D.clearErrors(),"uncontrolled"===t&&P(e=>e+1),Object.keys(T.subscribers.current).forEach(t=>{f(t,w.refValues.current)!==f(t,e)&&T.getFieldSubscribers(t).forEach(t=>t({previousValues:e,updatedValues:w.refValues.current}))})},[d]),z=(0,l.useCallback)(e=>{let r=w.refValues.current;w.initialize(e,()=>"uncontrolled"===t&&P(e=>e+1)),W(r)},[W]),B=(0,l.useCallback)((e,r,l)=>{let a=V(e,p),n=r instanceof Function?r(f(e,w.refValues.current)):r;A.setCalculatedFieldDirty(e,n),"change"===F&&A.setFieldTouched(e,!0),!a&&d&&D.clearFieldError(e),w.setFieldValue({path:e,value:r,updateState:"controlled"===t,subscribers:[...T.getFieldSubscribers(e),a?t=>{let r=E(e,$,t.updatedValues);r.hasError?D.setFieldError(e,r.error):D.clearFieldError(e)}:null,l?.forceUpdate!==!1&&"controlled"!==t?()=>_(t=>({...t,[e]:(t[e]||0)+1})):null]})},[g,$]),H=(0,l.useCallback)(e=>{let r=w.refValues.current;w.setValues({values:e,updateState:"controlled"===t}),W(r)},[g,W]),U=(0,l.useCallback)(()=>{let e=k($,w.refValues.current);return D.setErrors(e.errors),e},[$]),q=(0,l.useCallback)(e=>{let t=E(e,$,w.refValues.current);return t.hasError?D.setFieldError(e,t.error):D.clearFieldError(e),t},[$]),X=(0,l.useCallback)(e=>{e.preventDefault(),N()},[]),Z=(0,l.useCallback)(e=>e?!E(e,$,w.refValues.current).hasError:!k($,w.refValues.current).hasErrors,[$]),G=(0,l.useCallback)(t=>document.querySelector(`[data-path="${m(e,t)}"]`),[]),J={watch:T.watch,initialized:w.initialized.current,values:"uncontrolled"===t?w.refValues.current:w.stateValues,getValues:w.getValues,getInitialValues:w.getValuesSnapshot,setInitialValues:w.setValuesSnapshot,resetField:w.resetField,initialize:z,setValues:H,setFieldValue:B,submitting:x,setSubmitting:M,errors:D.errorsState,setErrors:D.setErrors,setFieldError:D.setFieldError,clearFieldError:D.clearFieldError,clearErrors:D.clearErrors,resetDirty:A.resetDirty,setTouched:A.setTouched,setDirty:A.setDirty,isTouched:A.isTouched,resetTouched:A.resetTouched,isDirty:A.isDirty,getTouched:A.getTouched,getDirty:A.getDirty,reorderListItem:I.reorderListItem,insertListItem:I.insertListItem,removeListItem:I.removeListItem,replaceListItem:I.replaceListItem,reset:N,validate:U,validateField:q,getInputProps:(r,{type:l="input",withError:a=!0,withFocus:n=!0,...u}={})=>{var s;let o={onChange:(s=e=>B(r,e,{forceUpdate:!1}),e=>{if(e)if("function"==typeof e)s(e);else if("object"==typeof e&&"nativeEvent"in e){let{currentTarget:t}=e;t instanceof HTMLInputElement?"checkbox"===t.type?s(t.checked):s(t.value):(t instanceof HTMLTextAreaElement||t instanceof HTMLSelectElement)&&s(t.value)}else s(e);else s(e)}),"data-path":m(e,r)};return a&&(o.error=D.errorsState[r]),"checkbox"===l?o["controlled"===t?"checked":"defaultChecked"]=f(r,w.refValues.current):o["controlled"===t?"value":"defaultValue"]=f(r,w.refValues.current),n&&(o.onFocus=()=>A.setFieldTouched(r,!0),o.onBlur=()=>{if(V(r,v)){let e=E(r,$,w.refValues.current);e.hasError?D.setFieldError(r,e.error):D.clearFieldError(r)}}),Object.assign(o,C?.({inputProps:o,field:r,options:{type:l,withError:a,withFocus:n,...u},form:J}))},onSubmit:(e,t)=>r=>{"always"===O&&r?.preventDefault();let l=U();if(l.hasErrors)"validation-failed"===O&&r?.preventDefault(),t?.(l.errors,w.refValues.current,r);else{let t=e?.(j(w.refValues.current),r);t instanceof Promise&&(M(!0),t.finally(()=>M(!1)))}},onReset:X,isValid:Z,getTransformedValues:e=>j(e||w.refValues.current),key:e=>`${L}-${e}-${R[e]||0}`,getInputNode:G};return e&&function(e){if(!/^[0-9a-zA-Z-]+$/.test(e))throw Error(`[@mantine/use-form] Form name "${e}" is invalid, it should contain only letters, numbers and dashes`)}(e),n(`mantine-form:${e}:set-field-value`,e=>J.setFieldValue(e.detail.path,e.detail.value)),n(`mantine-form:${e}:set-values`,e=>J.setValues(e.detail)),n(`mantine-form:${e}:set-initial-values`,e=>J.setInitialValues(e.detail)),n(`mantine-form:${e}:set-errors`,e=>J.setErrors(e.detail)),n(`mantine-form:${e}:set-field-error`,e=>J.setFieldError(e.detail.path,e.detail.error)),n(`mantine-form:${e}:clear-field-error`,e=>J.clearFieldError(e.detail)),n(`mantine-form:${e}:clear-errors`,J.clearErrors),n(`mantine-form:${e}:reset`,J.reset),n(`mantine-form:${e}:validate`,J.validate),n(`mantine-form:${e}:validate-field`,e=>J.validateField(e.detail)),n(`mantine-form:${e}:reorder-list-item`,e=>J.reorderListItem(e.detail.path,e.detail.payload)),n(`mantine-form:${e}:remove-list-item`,e=>J.removeListItem(e.detail.path,e.detail.index)),n(`mantine-form:${e}:insert-list-item`,e=>J.insertListItem(e.detail.path,e.detail.item,e.detail.index)),n(`mantine-form:${e}:set-dirty`,e=>J.setDirty(e.detail)),n(`mantine-form:${e}:set-touched`,e=>J.setTouched(e.detail)),n(`mantine-form:${e}:reset-dirty`,e=>J.resetDirty(e.detail)),n(`mantine-form:${e}:reset-touched`,J.resetTouched),J}}};