# Facebook Messenger Setup Guide

This guide will help you set up Facebook Messenger integration for testing on localhost and production.

## Prerequisites

- A Facebook account
- A Facebook Page (you'll create one if you don't have it)
- Your Next.js app running locally on `http://localhost:3000`

## Step 1: Create a Facebook App

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "My Apps" → "Create App"
3. Choose "Business" as the app type
4. Fill in the app details:
   - **App Name**: Your app name (e.g., "Product Management Bot")
   - **App Contact Email**: Your email
   - **Business Account**: Skip if you don't have one
5. Click "Create App"

## Step 2: Create or Select a Facebook Page

1. If you don't have a Facebook Page:
   - Go to [Facebook Pages](https://www.facebook.com/pages/create/)
   - Create a business page for testing
2. Note down your Page ID (you can find it in Page Settings → Page Info)

## Step 3: Set Up Messenger in Your App

1. In your Facebook App dashboard, click "Add Product"
2. Find "Messenger" and click "Set Up"
3. In the Messenger settings:

### Generate Page Access Token
1. In "Access Tokens" section
2. Select your Facebook Page
3. Click "Generate Token"
4. Copy the token - this is your `FACEBOOK_PAGE_ACCESS_TOKEN`
5. **Important**: Keep this token secure!

## Step 4: Set Up Webhook for Localhost Testing

### Option A: Using ngrok (Recommended for localhost)

1. Install ngrok:
   ```bash
   # Download from https://ngrok.com/download
   # Or install via npm
   npm install -g ngrok
   ```

2. Start your Next.js app:
   ```bash
   npm run dev
   ```

3. In another terminal, expose your localhost:
   ```bash
   https://dashboard.ngrok.com/get-started/your-authtoken
   ngrok config add-authtoken *************************************************
https://9a2f-27-34-64-220.ngrok-free.app/api/webhook/facebook
   ngrok http 3000
   ```

4. Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`)

### Option B: Using a Cloud Service

Deploy your app to Vercel, Netlify, or another service and use that URL.

## Step 5: Configure Webhook in Facebook

1. In your Facebook App → Messenger → Settings
2. In "Webhooks" section, click "Add Callback URL"
3. Enter your webhook URL:
   - **Callback URL**: `https://your-domain.com/api/webhook/facebook`
   - **Verify Token**: Create a random string (e.g., `my_verify_token_123`)
4. Subscribe to these webhook fields:
   - `messages`
   - `messaging_postbacks`
   - `message_deliveries`
   - `message_reads`

## Step 6: Configure Environment Variables

Create `.env.local` file with:

```bash
# Supabase (get from your Supabase project)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# JWT Secret (generate a secure random string)
# Option 1: Using OpenSSL
openssl rand -base64 32

# Option 2: Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# Option 3: Use any secure random string generator
JWT_SECRET=your_secure_random_jwt_secret

# Facebook Messenger
FACEBOOK_VERIFY_TOKEN=my_verify_token_123
FACEBOOK_PAGE_ACCESS_TOKEN=your_page_access_token_from_step_3

# LLM Provider (choose one)
LLM_PROVIDER=gemini
GEMINI_API_KEY=your_gemini_api_key

# Bot Configuration
REPLY_TONE=friendly
CONFIDENCE_THRESHOLD=0.8
ENABLE_AUTO_REPLY=true
```

## Step 7: Test the Webhook

1. Make sure your app is running and accessible via the webhook URL
2. In Facebook App → Messenger → Settings → Webhooks
3. Click "Test" next to your webhook
4. If successful, you'll see a green checkmark

## Step 8: Subscribe Page to App

1. In "Webhooks" section, find your page
2. Click "Subscribe" to connect your page to the webhook

## Step 9: Test Messaging

1. Go to your Facebook Page
2. Send a message to your page (you can do this from your personal Facebook account)
3. Check your app logs to see if the webhook receives the message
4. The bot should respond automatically based on your memory entries

## Troubleshooting

### Common Issues:

1. **Webhook verification fails**:
   - Check that `FACEBOOK_VERIFY_TOKEN` matches what you entered in Facebook
   - Ensure your app is accessible via HTTPS
   - Check the webhook URL is correct

2. **Messages not received**:
   - Verify the page is subscribed to your app
   - Check webhook fields are selected
   - Look at Facebook App → Messenger → Settings → Webhooks for error logs

3. **Bot not responding**:
   - Check your app logs for errors
   - Verify database connection
   - Check if memory entries exist in your database

### Testing Webhook Locally:

You can test the webhook endpoint directly:

```bash
# Test webhook verification
curl -X GET "http://localhost:3000/api/webhook/facebook?hub.mode=subscribe&hub.verify_token=my_verify_token_123&hub.challenge=test_challenge"

# Should return: test_challenge
```

## Security Notes

- Never commit your `.env.local` file to version control
- Use different tokens for development and production
- Regularly rotate your access tokens
- Monitor your webhook for unusual activity

## Production Deployment

When deploying to production:

1. Update `NODE_ENV=production` in your environment
2. Use production Supabase credentials
3. Update webhook URL to your production domain
4. Generate new, secure JWT secret
5. Consider using Facebook's webhook verification for additional security

## Getting API Keys

### Supabase:
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Go to Settings → API to get your keys

### Gemini API:
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create an API key

### OpenAI API:
1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create an API key

### JWT Secret:
Generate a secure random string:
```bash
# Using OpenSSL
openssl rand -base64 32

# Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

## Next Steps

After setup:
1. Visit your app homepage to check system status
2. Login to admin dashboard
3. Add some products
4. Test messaging from your Facebook page
5. Monitor message history in the admin panel
