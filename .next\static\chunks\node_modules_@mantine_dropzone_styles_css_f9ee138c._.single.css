/* [project]/node_modules/@mantine/dropzone/styles.css [app-client] (css) */
.m_d46a4834 {
  border: calc(.0625rem * var(--mantine-scale)) dashed;
  color: var(--mantine-color-text);
  padding: var(--mantine-spacing-md);
  border-radius: var(--dropzone-radius);
  cursor: pointer;
  user-select: none;
  transition: background-color .1s, border-color .1s;
  position: relative;
}

.m_d46a4834:where([data-loading]), .m_d46a4834:where(:not([data-activate-on-click])) {
  cursor: default;
}

:where([data-mantine-color-scheme="light"]) .m_d46a4834 {
  background-color: var(--mantine-color-white);
  border-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme="dark"]) .m_d46a4834 {
  background-color: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

@media (hover: hover) {
  :where([data-mantine-color-scheme="light"]) .m_d46a4834:hover:where([data-activate-on-click]:not([data-loading])) {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_d46a4834:hover:where([data-activate-on-click]:not([data-loading])) {
    background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {
  :where([data-mantine-color-scheme="light"]) .m_d46a4834:active:where([data-activate-on-click]:not([data-loading])) {
    background-color: var(--mantine-color-gray-0);
  }

  :where([data-mantine-color-scheme="dark"]) .m_d46a4834:active:where([data-activate-on-click]:not([data-loading])) {
    background-color: var(--mantine-color-dark-5);
  }
}

.m_d46a4834:where([data-accept]) {
  background-color: var(--dropzone-accept-bg);
  border-color: var(--dropzone-accept-bg);
  color: var(--dropzone-accept-color);
}

.m_d46a4834:where([data-reject]) {
  background-color: var(--dropzone-reject-bg);
  border-color: var(--dropzone-reject-bg);
  color: var(--dropzone-reject-color);
}

.m_b85f7144 {
  pointer-events: none;
  user-select: none;
}

.m_b85f7144:where([data-enable-pointer-events]) {
  pointer-events: all;
}

.m_96f6e9ad {
  background-color: var(--mantine-color-body);
  padding: var(--mantine-spacing-xs);
  flex-direction: column;
  transition: opacity .1s;
  display: flex;
  position: fixed;
  inset: 0;
}

.m_96f6e9ad .m_7946116d {
  flex: 1;
}

/*# sourceMappingURL=node_modules_%40mantine_dropzone_styles_css_f9ee138c._.single.css.map*/