{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ColorSchemeScript, MantineProvider } from '@mantine/core';\nimport { Notifications } from '@mantine/notifications';\nimport { ModalsProvider } from '@mantine/modals';\nimport '@mantine/core/styles.css';\nimport '@mantine/notifications/styles.css';\nimport '@mantine/dates/styles.css';\nimport '@mantine/dropzone/styles.css';\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Admin Dashboard - Product Management\",\n  description: \"Full-stack Next.js app with Mantine UI and Supabase\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <ColorSchemeScript />\n      </head>\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <MantineProvider>\n          <ModalsProvider>\n            <Notifications />\n            {children}\n          </ModalsProvider>\n        </MantineProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AAAA;AACA;AACA;;;;;;;;;;;;AAgBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;0BACC,cAAA,8OAAC,8MAAA,CAAA,oBAAiB;;;;;;;;;;0BAEpB,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;0BAEpE,cAAA,8OAAC,uLAAA,CAAA,kBAAe;8BACd,cAAA,8OAAC,6JAAA,CAAA,iBAAc;;0CACb,8OAAC,mKAAA,CAAA,gBAAa;;;;;4BACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/core/esm/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ColorSchemeScript = registerClientReference(\n    function() { throw new Error(\"Attempted to call ColorSchemeScript() from the server but ColorSchemeScript is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/core/esm/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.mjs <module evaluation>\",\n    \"ColorSchemeScript\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,6HACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/core/esm/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ColorSchemeScript = registerClientReference(\n    function() { throw new Error(\"Attempted to call ColorSchemeScript() from the server but ColorSchemeScript is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/core/esm/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.mjs\",\n    \"ColorSchemeScript\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,yGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "file": "ColorSchemeScript.mjs", "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/core/src/core/MantineProvider/ColorSchemeScript/ColorSchemeScript.tsx"], "sourcesContent": ["import type { MantineColorScheme } from '../theme.types';\n\nexport interface ColorSchemeScriptProps extends React.ComponentPropsWithoutRef<'script'> {\n  forceColorScheme?: 'light' | 'dark';\n  defaultColorScheme?: MantineColorScheme;\n  localStorageKey?: string;\n}\n\nconst getScript = ({\n  defaultColorScheme,\n  localStorageKey,\n  forceColorScheme,\n}: Pick<ColorSchemeScriptProps, 'defaultColorScheme' | 'localStorageKey' | 'forceColorScheme'>) =>\n  forceColorScheme\n    ? `document.documentElement.setAttribute(\"data-mantine-color-scheme\", '${forceColorScheme}');`\n    : `try {\n  var _colorScheme = window.localStorage.getItem(\"${localStorageKey}\");\n  var colorScheme = _colorScheme === \"light\" || _colorScheme === \"dark\" || _colorScheme === \"auto\" ? _colorScheme : \"${defaultColorScheme}\";\n  var computedColorScheme = colorScheme !== \"auto\" ? colorScheme : window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n  document.documentElement.setAttribute(\"data-mantine-color-scheme\", computedColorScheme);\n} catch (e) {}\n`;\n\nexport function ColorSchemeScript({\n  defaultColorScheme = 'light',\n  localStorageKey = 'mantine-color-scheme-value',\n  forceColorScheme,\n  ...others\n}: ColorSchemeScriptProps) {\n  const _defaultColorScheme = ['light', 'dark', 'auto'].includes(defaultColorScheme)\n    ? defaultColorScheme\n    : 'light';\n  return (\n    <script\n      {...others}\n      data-mantine-script\n      dangerouslySetInnerHTML={{\n        __html: getScript({\n          defaultColorScheme: _defaultColorScheme,\n          localStorageKey,\n          forceColorScheme,\n        }),\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/core/esm/core/MantineProvider/MantineProvider.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const HeadlessMantineProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call HeadlessMantineProvider() from the server but HeadlessMantineProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs <module evaluation>\",\n    \"HeadlessMantineProvider\",\n);\nexport const MantineProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call MantineProvider() from the server but <PERSON>tineProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs <module evaluation>\",\n    \"MantineProvider\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,yGACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,yGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/core/esm/core/MantineProvider/MantineProvider.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const HeadlessMantineProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call HeadlessMantineProvider() from the server but HeadlessMantineProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs\",\n    \"HeadlessMantineProvider\",\n);\nexport const MantineProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call MantineProvider() from the server but <PERSON>tineProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/core/esm/core/MantineProvider/MantineProvider.mjs\",\n    \"MantineProvider\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,qFACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "file": "MantineProvider.mjs", "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/core/src/core/MantineProvider/MantineProvider.tsx"], "sourcesContent": ["import './baseline.css';\nimport './global.css';\nimport './default-css-variables.css';\n\nimport { localStorageColorSchemeManager, MantineColorSchemeManager } from './color-scheme-managers';\nimport { MantineContext, MantineStylesTransform } from './Mantine.context';\nimport { MantineClasses } from './MantineClasses';\nimport { CSSVariablesResolver, MantineCssVariables } from './MantineCssVariables';\nimport { MantineThemeProvider } from './MantineThemeProvider';\nimport type { MantineColorScheme, MantineThemeOverride } from './theme.types';\nimport { useProviderColorScheme } from './use-mantine-color-scheme';\nimport { useRespectReduceMotion } from './use-respect-reduce-motion';\n\nexport interface MantineProviderProps {\n  /** Theme override object */\n  theme?: MantineThemeOverride;\n\n  /** Used to retrieve/set color scheme value in external storage, by default uses `window.localStorage` */\n  colorSchemeManager?: MantineColorSchemeManager;\n\n  /** Default color scheme value used when `colorSchemeManager` cannot retrieve value from external storage, `light` by default */\n  defaultColorScheme?: MantineColorScheme;\n\n  /** Forces color scheme value, if set, MantineProvider ignores `colorSchemeManager` and `defaultColorScheme` */\n  forceColorScheme?: 'light' | 'dark';\n\n  /** CSS selector to which CSS variables should be added, `:root` by default */\n  cssVariablesSelector?: string;\n\n  /** Determines whether theme CSS variables should be added to given `cssVariablesSelector`, `true` by default */\n  withCssVariables?: boolean;\n\n  /** Determines whether CSS variables should be deduplicated: if CSS variable has the same value as in default theme, it is not added in the runtime. `true` by default. */\n  deduplicateCssVariables?: boolean;\n\n  /** Function to resolve root element to set `data-mantine-color-scheme` attribute, must return undefined on server, `() => document.documentElement` by default */\n  getRootElement?: () => HTMLElement | undefined;\n\n  /** A prefix for components static classes (for example {selector}-Text-root), `mantine` by default */\n  classNamesPrefix?: string;\n\n  /** Function to generate nonce attribute added to all generated `<style />` tags */\n  getStyleNonce?: () => string;\n\n  /** Function to generate CSS variables based on theme object */\n  cssVariablesResolver?: CSSVariablesResolver;\n\n  /** Determines whether components should have static classes, for example, `mantine-Button-root`. `true` by default */\n  withStaticClasses?: boolean;\n\n  /** Determines whether global classes should be added with `<style />` tag. Global classes are required for `hiddenFrom`/`visibleFrom` and `lightHidden`/`darkHidden` props to work. `true` by default. */\n  withGlobalClasses?: boolean;\n\n  /** An object to transform `styles` and `sx` props into css classes, can be used with CSS-in-JS libraries */\n  stylesTransform?: MantineStylesTransform;\n\n  /** Your application */\n  children?: React.ReactNode;\n\n  /** Environment at which the provider is used, `'test'` environment disables all transitions and portals */\n  env?: 'default' | 'test';\n}\n\nexport function MantineProvider({\n  theme,\n  children,\n  getStyleNonce,\n  withStaticClasses = true,\n  withGlobalClasses = true,\n  deduplicateCssVariables = true,\n  withCssVariables = true,\n  cssVariablesSelector = ':root',\n  classNamesPrefix = 'mantine',\n  colorSchemeManager = localStorageColorSchemeManager(),\n  defaultColorScheme = 'light',\n  getRootElement = () => document.documentElement,\n  cssVariablesResolver,\n  forceColorScheme,\n  stylesTransform,\n  env,\n}: MantineProviderProps) {\n  const { colorScheme, setColorScheme, clearColorScheme } = useProviderColorScheme({\n    defaultColorScheme,\n    forceColorScheme,\n    manager: colorSchemeManager,\n    getRootElement,\n  });\n\n  useRespectReduceMotion({\n    respectReducedMotion: theme?.respectReducedMotion || false,\n    getRootElement,\n  });\n\n  return (\n    <MantineContext.Provider\n      value={{\n        colorScheme,\n        setColorScheme,\n        clearColorScheme,\n        getRootElement,\n        classNamesPrefix,\n        getStyleNonce,\n        cssVariablesResolver,\n        cssVariablesSelector,\n        withStaticClasses,\n        stylesTransform,\n        env,\n      }}\n    >\n      <MantineThemeProvider theme={theme}>\n        {withCssVariables && (\n          <MantineCssVariables\n            cssVariablesSelector={cssVariablesSelector}\n            deduplicateCssVariables={deduplicateCssVariables}\n          />\n        )}\n        {withGlobalClasses && <MantineClasses />}\n        {children}\n      </MantineThemeProvider>\n    </MantineContext.Provider>\n  );\n}\n\nMantineProvider.displayName = '@mantine/core/MantineProvider';\n\nexport interface HeadlessMantineProviderProps {\n  /** Theme override object */\n  theme?: MantineThemeOverride;\n\n  /** Your application */\n  children?: React.ReactNode;\n}\n\nexport function HeadlessMantineProvider({ children, theme }: HeadlessMantineProviderProps) {\n  return (\n    <MantineContext.Provider\n      value={{\n        colorScheme: 'auto',\n        setColorScheme: () => {},\n        clearColorScheme: () => {},\n        getRootElement: () => document.documentElement,\n        classNamesPrefix: 'mantine',\n        cssVariablesSelector: ':root',\n        withStaticClasses: false,\n        headless: true,\n      }}\n    >\n      <MantineThemeProvider theme={theme}>{children}</MantineThemeProvider>\n    </MantineContext.Provider>\n  );\n}\n\nHeadlessMantineProvider.displayName = '@mantine/core/HeadlessMantineProvider';\n"], "names": [], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/notifications/esm/Notifications.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Notifications = registerClientReference(\n    function() { throw new Error(\"Attempted to call Notifications() from the server but Notifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/notifications/esm/Notifications.mjs <module evaluation>\",\n    \"Notifications\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2FACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/notifications/esm/Notifications.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Notifications = registerClientReference(\n    function() { throw new Error(\"Attempted to call Notifications() from the server but Notifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/notifications/esm/Notifications.mjs\",\n    \"Notifications\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,uEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "file": "Notifications.mjs", "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/notifications/src/Notifications.tsx"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport {\n  Transition as _Transition,\n  TransitionGroup,\n  TransitionStatus,\n} from 'react-transition-group';\nimport {\n  BasePortalProps,\n  Box,\n  BoxProps,\n  createVarsResolver,\n  ElementProps,\n  factory,\n  Factory,\n  getDefaultZIndex,\n  OptionalPortal,\n  rem,\n  RemoveScroll,\n  StylesApiProps,\n  useMantineTheme,\n  useProps,\n  useStyles,\n} from '@mantine/core';\nimport { useDidUpdate, useForceUpdate, useReducedMotion } from '@mantine/hooks';\nimport {\n  getGroupedNotifications,\n  positions,\n} from './get-grouped-notifications/get-grouped-notifications';\nimport { getNotificationStateStyles } from './get-notification-state-styles';\nimport { NotificationContainer } from './NotificationContainer';\nimport {\n  hideNotification,\n  NotificationPosition,\n  notifications,\n  NotificationsStore,\n  notificationsStore,\n  useNotifications,\n} from './notifications.store';\nimport classes from './Notifications.module.css';\n\nconst Transition: any = _Transition;\n\nexport type NotificationsStylesNames = 'root' | 'notification';\nexport type NotificationsCssVariables = {\n  root: '--notifications-z-index' | '--notifications-container-width';\n};\n\nexport interface NotificationsProps\n  extends BoxProps,\n    StylesApiProps<NotificationsFactory>,\n    ElementProps<'div'> {\n  /** Notifications default position, `'bottom-right'` by default */\n  position?: NotificationPosition;\n\n  /** Auto close timeout for all notifications in ms, `false` to disable auto close, can be overwritten for individual notifications in `notifications.show` function, `4000` by default */\n  autoClose?: number | false;\n\n  /** Notification transition duration in ms, `250` by default */\n  transitionDuration?: number;\n\n  /** Notification width, cannot exceed 100%, `440` by default */\n  containerWidth?: number | string;\n\n  /** Notification `max-height`, used for transitions, `200` by default */\n  notificationMaxHeight?: number | string;\n\n  /** Maximum number of notifications displayed at a time, other new notifications will be added to queue, `5` by default */\n  limit?: number;\n\n  /** Notifications container z-index, `400` by default */\n  zIndex?: string | number;\n\n  /** Props passed down to the `Portal` component */\n  portalProps?: BasePortalProps;\n\n  /** Store for notifications state, can be used to create multiple instances of notifications system in your application */\n  store?: NotificationsStore;\n\n  /** Determines whether notifications container should be rendered inside `Portal`, `true` by default */\n  withinPortal?: boolean;\n}\n\nexport type NotificationsFactory = Factory<{\n  props: NotificationsProps;\n  ref: HTMLDivElement;\n  stylesNames: NotificationsStylesNames;\n  vars: NotificationsCssVariables;\n  staticComponents: {\n    show: typeof notifications.show;\n    hide: typeof notifications.hide;\n    update: typeof notifications.update;\n    clean: typeof notifications.clean;\n    cleanQueue: typeof notifications.cleanQueue;\n    updateState: typeof notifications.updateState;\n  };\n}>;\n\nconst defaultProps = {\n  position: 'bottom-right',\n  autoClose: 4000,\n  transitionDuration: 250,\n  containerWidth: 440,\n  notificationMaxHeight: 200,\n  limit: 5,\n  zIndex: getDefaultZIndex('overlay'),\n  store: notificationsStore,\n  withinPortal: true,\n} satisfies Partial<NotificationsProps>;\n\nconst varsResolver = createVarsResolver<NotificationsFactory>((_, { zIndex, containerWidth }) => ({\n  root: {\n    '--notifications-z-index': zIndex?.toString(),\n    '--notifications-container-width': rem(containerWidth),\n  },\n}));\n\nexport const Notifications = factory<NotificationsFactory>((_props, ref) => {\n  const props = useProps('Notifications', defaultProps, _props);\n  const {\n    classNames,\n    className,\n    style,\n    styles,\n    unstyled,\n    vars,\n    position,\n    autoClose,\n    transitionDuration,\n    containerWidth,\n    notificationMaxHeight,\n    limit,\n    zIndex,\n    store,\n    portalProps,\n    withinPortal,\n    ...others\n  } = props;\n\n  const theme = useMantineTheme();\n  const data = useNotifications(store);\n  const forceUpdate = useForceUpdate();\n  const shouldReduceMotion = useReducedMotion();\n  const refs = useRef<Record<string, HTMLDivElement>>({});\n  const previousLength = useRef<number>(0);\n\n  const reduceMotion = theme.respectReducedMotion ? shouldReduceMotion : false;\n  const duration = reduceMotion ? 1 : transitionDuration;\n\n  const getStyles = useStyles<NotificationsFactory>({\n    name: 'Notifications',\n    classes,\n    props,\n    className,\n    style,\n    classNames,\n    styles,\n    unstyled,\n    vars,\n    varsResolver,\n  });\n\n  useEffect(() => {\n    store?.updateState((current) => ({\n      ...current,\n      limit: limit || 5,\n      defaultPosition: position,\n    }));\n  }, [limit, position]);\n\n  useDidUpdate(() => {\n    if (data.notifications.length > previousLength.current) {\n      setTimeout(() => forceUpdate(), 0);\n    }\n    previousLength.current = data.notifications.length;\n  }, [data.notifications]);\n\n  const grouped = getGroupedNotifications(data.notifications, position);\n  const groupedComponents = positions.reduce(\n    (acc, pos) => {\n      acc[pos] = grouped[pos].map(({ style: notificationStyle, ...notification }) => (\n        <Transition\n          key={notification.id}\n          timeout={duration}\n          onEnter={() => refs.current[notification.id!].offsetHeight}\n          nodeRef={{ current: refs.current[notification.id!] }}\n        >\n          {(state: TransitionStatus) => (\n            <NotificationContainer\n              ref={(node) => {\n                if (node) {\n                  refs.current[notification.id!] = node;\n                }\n              }}\n              data={notification}\n              onHide={(id) => hideNotification(id, store)}\n              autoClose={autoClose}\n              {...getStyles('notification', {\n                style: {\n                  ...getNotificationStateStyles({\n                    state,\n                    position: pos,\n                    transitionDuration: duration,\n                    maxHeight: notificationMaxHeight,\n                  }),\n                  ...notificationStyle,\n                },\n              })}\n            />\n          )}\n        </Transition>\n      ));\n\n      return acc;\n    },\n    {} as Record<NotificationPosition, React.ReactNode>\n  );\n\n  return (\n    <OptionalPortal withinPortal={withinPortal} {...portalProps}>\n      <Box {...getStyles('root')} data-position=\"top-center\" ref={ref} {...others}>\n        <TransitionGroup>{groupedComponents['top-center']}</TransitionGroup>\n      </Box>\n\n      <Box {...getStyles('root')} data-position=\"top-left\" {...others}>\n        <TransitionGroup>{groupedComponents['top-left']}</TransitionGroup>\n      </Box>\n\n      <Box\n        {...getStyles('root', { className: RemoveScroll.classNames.fullWidth })}\n        data-position=\"top-right\"\n        {...others}\n      >\n        <TransitionGroup>{groupedComponents['top-right']}</TransitionGroup>\n      </Box>\n\n      <Box\n        {...getStyles('root', { className: RemoveScroll.classNames.fullWidth })}\n        data-position=\"bottom-right\"\n        {...others}\n      >\n        <TransitionGroup>{groupedComponents['bottom-right']}</TransitionGroup>\n      </Box>\n\n      <Box {...getStyles('root')} data-position=\"bottom-left\" {...others}>\n        <TransitionGroup>{groupedComponents['bottom-left']}</TransitionGroup>\n      </Box>\n\n      <Box {...getStyles('root')} data-position=\"bottom-center\" {...others}>\n        <TransitionGroup>{groupedComponents['bottom-center']}</TransitionGroup>\n      </Box>\n    </OptionalPortal>\n  );\n});\n\nNotifications.classes = classes;\nNotifications.displayName = '@mantine/notifications/Notifications';\nNotifications.show = notifications.show;\nNotifications.hide = notifications.hide;\nNotifications.update = notifications.update;\nNotifications.clean = notifications.clean;\nNotifications.cleanQueue = notifications.cleanQueue;\nNotifications.updateState = notifications.updateState;\n"], "names": ["_Transition"], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/modals/esm/ModalsProvider.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ModalsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ModalsProvider() from the server but ModalsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/modals/esm/ModalsProvider.mjs <module evaluation>\",\n    \"ModalsProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/modals/esm/ModalsProvider.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ModalsProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ModalsProvider() from the server but ModalsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@mantine/modals/esm/ModalsProvider.mjs\",\n    \"ModalsProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,iEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "file": "ModalsProvider.mjs", "sources": ["file:///E:/cozy/nextjs/node_modules/%40mantine/modals/src/ModalsProvider.tsx"], "sourcesContent": ["import { useCallback, useReducer, useRef } from 'react';\nimport { getDefaultZIndex, Modal } from '@mantine/core';\nimport { randomId } from '@mantine/hooks';\nimport { ConfirmModal } from './ConfirmModal';\nimport {\n  ConfirmLabels,\n  ContextModalProps,\n  ModalsContext,\n  ModalsContextProps,\n  ModalSettings,\n  OpenConfirmModal,\n  OpenContextModal,\n} from './context';\nimport { useModalsEvents } from './events';\nimport { modalsReducer } from './reducer';\n\nexport interface ModalsProviderProps {\n  /** Your app */\n  children?: React.ReactNode;\n\n  /** Predefined modals */\n  modals?: Record<string, React.FC<ContextModalProps<any>>>;\n\n  /** Shared Modal component props, applied for every modal */\n  modalProps?: ModalSettings;\n\n  /** Confirm modal labels */\n  labels?: ConfirmLabels;\n}\n\nfunction separateConfirmModalProps(props: OpenConfirmModal) {\n  if (!props) {\n    return { confirmProps: {}, modalProps: {} };\n  }\n\n  const {\n    id,\n    children,\n    onCancel,\n    onConfirm,\n    closeOnConfirm,\n    closeOnCancel,\n    cancelProps,\n    confirmProps,\n    groupProps,\n    labels,\n    ...others\n  } = props;\n\n  return {\n    confirmProps: {\n      id,\n      children,\n      onCancel,\n      onConfirm,\n      closeOnConfirm,\n      closeOnCancel,\n      cancelProps,\n      confirmProps,\n      groupProps,\n      labels,\n    },\n    modalProps: {\n      id,\n      ...others,\n    },\n  };\n}\n\nexport function ModalsProvider({ children, modalProps, labels, modals }: ModalsProviderProps) {\n  const [state, dispatch] = useReducer(modalsReducer, { modals: [], current: null });\n  const stateRef = useRef(state);\n  stateRef.current = state;\n\n  const closeAll = useCallback(\n    (canceled?: boolean) => {\n      dispatch({ type: 'CLOSE_ALL', canceled });\n    },\n    [stateRef, dispatch]\n  );\n\n  const openModal = useCallback(\n    ({ modalId, ...props }: ModalSettings) => {\n      const id = modalId || randomId();\n\n      dispatch({\n        type: 'OPEN',\n        modal: {\n          id,\n          type: 'content',\n          props,\n        },\n      });\n      return id;\n    },\n    [dispatch]\n  );\n\n  const openConfirmModal = useCallback(\n    ({ modalId, ...props }: OpenConfirmModal) => {\n      const id = modalId || randomId();\n      dispatch({\n        type: 'OPEN',\n        modal: {\n          id,\n          type: 'confirm',\n          props,\n        },\n      });\n      return id;\n    },\n    [dispatch]\n  );\n\n  const openContextModal = useCallback(\n    (modal: string, { modalId, ...props }: OpenContextModal) => {\n      const id = modalId || randomId();\n      dispatch({\n        type: 'OPEN',\n        modal: {\n          id,\n          type: 'context',\n          props,\n          ctx: modal,\n        },\n      });\n      return id;\n    },\n    [dispatch]\n  );\n\n  const closeModal = useCallback(\n    (id: string, canceled?: boolean) => {\n      dispatch({ type: 'CLOSE', modalId: id, canceled });\n    },\n    [stateRef, dispatch]\n  );\n\n  const updateModal = useCallback(\n    ({ modalId, ...newProps }: Partial<ModalSettings> & { modalId: string }) => {\n      dispatch({\n        type: 'UPDATE',\n        modalId,\n        newProps,\n      });\n    },\n    [dispatch]\n  );\n\n  const updateContextModal = useCallback(\n    ({ modalId, ...newProps }: { modalId: string } & Partial<OpenContextModal<any>>) => {\n      dispatch({ type: 'UPDATE', modalId, newProps });\n    },\n    [dispatch]\n  );\n\n  useModalsEvents({\n    openModal,\n    openConfirmModal,\n    openContextModal: ({ modal, ...payload }: any) => openContextModal(modal, payload),\n    closeModal,\n    closeContextModal: closeModal,\n    closeAllModals: closeAll,\n    updateModal,\n    updateContextModal,\n  });\n\n  const ctx: ModalsContextProps = {\n    modalProps: modalProps || {},\n    modals: state.modals,\n    openModal,\n    openConfirmModal,\n    openContextModal,\n    closeModal,\n    closeContextModal: closeModal,\n    closeAll,\n    updateModal,\n    updateContextModal,\n  };\n\n  const getCurrentModal = () => {\n    const currentModal = stateRef.current.current;\n    switch (currentModal?.type) {\n      case 'context': {\n        const { innerProps, ...rest } = currentModal.props;\n        const ContextModal = modals![currentModal.ctx];\n\n        return {\n          modalProps: rest,\n          content: <ContextModal innerProps={innerProps} context={ctx} id={currentModal.id} />,\n        };\n      }\n      case 'confirm': {\n        const { modalProps: separatedModalProps, confirmProps: separatedConfirmProps } =\n          separateConfirmModalProps(currentModal.props);\n\n        return {\n          modalProps: separatedModalProps,\n          content: (\n            <ConfirmModal\n              {...separatedConfirmProps}\n              id={currentModal.id}\n              labels={currentModal.props.labels || labels}\n            />\n          ),\n        };\n      }\n      case 'content': {\n        const { children: currentModalChildren, ...rest } = currentModal.props;\n\n        return {\n          modalProps: rest,\n          content: currentModalChildren,\n        };\n      }\n      default: {\n        return {\n          modalProps: {},\n          content: null,\n        };\n      }\n    }\n  };\n\n  const { modalProps: currentModalProps, content } = getCurrentModal();\n\n  return (\n    <ModalsContext.Provider value={ctx}>\n      <Modal\n        zIndex={getDefaultZIndex('modal') + 1}\n        {...modalProps}\n        {...currentModalProps}\n        opened={state.modals.length > 0}\n        onClose={() => closeModal(state.current?.id as any)}\n      >\n        {content}\n      </Modal>\n\n      {children}\n    </ModalsContext.Provider>\n  );\n}\n"], "names": [], "mappings": "", "ignoreList": [0], "debugId": null}}]}