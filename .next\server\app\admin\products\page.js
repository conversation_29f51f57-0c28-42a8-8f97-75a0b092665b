(()=>{var e={};e.id=575,e.ids=[575],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12913:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(60687),o=r(43210),a=r(16189),n=r(46521),i=r(6099),l=r(74610),c=r(99445),d=r(16537),p=r(10507),u=r(35096),h=r(6445),m=(0,h.A)("outline","logout","IconLogout",[["path",{d:"M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M9 12h12l-3 -3",key:"svg-1"}],["path",{d:"M18 15l3 -3",key:"svg-2"}]]),x=r(76887),g=r(44543),v=r(40437),f=(0,h.A)("outline","settings","IconSettings",[["path",{d:"M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]]),j=r(47874);function y({children:e}){let[t,r]=(0,o.useState)(null),[h,y]=(0,o.useState)(!0),b=(0,a.useRouter)(),k=async()=>{try{await fetch("/api/auth/logout",{method:"POST"}),j.notifications.show({title:"Success",message:"Logged out successfully",color:"green"}),b.push("/login")}catch(e){console.error("Logout error:",e)}};return h?(0,s.jsx)(n.Center,{h:"100vh",children:(0,s.jsx)(i.Loader,{size:"lg"})}):t?(0,s.jsxs)(l.AppShell,{navbar:{width:250,breakpoint:"sm"},header:{height:60},padding:"md",children:[(0,s.jsx)(l.AppShell.Header,{children:(0,s.jsxs)(c.Group,{h:"100%",px:"md",justify:"space-between",children:[(0,s.jsx)(d.Text,{size:"lg",fw:600,children:"Admin Dashboard"}),(0,s.jsxs)(c.Group,{children:[(0,s.jsxs)(d.Text,{size:"sm",c:"dimmed",children:["Welcome, ",t.username]}),(0,s.jsx)(p.Button,{variant:"subtle",leftSection:(0,s.jsx)(m,{size:"1rem"}),onClick:k,children:"Logout"})]})]})}),(0,s.jsxs)(l.AppShell.Navbar,{p:"md",children:[(0,s.jsx)(u.NavLink,{href:"/admin",label:"Dashboard",leftSection:(0,s.jsx)(x.A,{size:"1rem"})}),(0,s.jsx)(u.NavLink,{href:"/admin/products",label:"Products",leftSection:(0,s.jsx)(g.A,{size:"1rem"})}),(0,s.jsx)(u.NavLink,{href:"/admin/messages",label:"Messages",leftSection:(0,s.jsx)(v.A,{size:"1rem"})}),(0,s.jsx)(u.NavLink,{href:"/admin/settings",label:"Settings",leftSection:(0,s.jsx)(f,{size:"1rem"})})]}),(0,s.jsx)(l.AppShell.Main,{children:e})]}):null}},14491:(e,t,r)=>{Promise.resolve().then(r.bind(r,38571))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38571:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\cozy\\\\nextjs\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\cozy\\nextjs\\src\\app\\admin\\products\\page.tsx","default")},39145:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>I});var s=r(60687),o=r(43210),a=r(16537),n=r(46521),i=r(6099),l=r(68704),c=r(99445),d=r(23376),p=r(10507),u=r(91541),h=r(88767),m=r(39848),x=r(61279),g=r(74041),v=r(70045),f=r(6445),j=(0,f.A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]]),y=(0,f.A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]]),b=r(84349),k=(0,f.A)("outline","edit","IconEdit",[["path",{d:"M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1",key:"svg-0"}],["path",{d:"M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z",key:"svg-1"}],["path",{d:"M16 5l3 3",key:"svg-2"}]]),T=(0,f.A)("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]]),P=r(5597),S=r(47874),w=r(87455),A=r(65173),M=r(88349),z=r(94892),E=r(96193);function C({product:e,onSave:t,onCancel:r}){let[a,n]=(0,o.useState)(!1),i=(0,E.m)({initialValues:{name:e?.name||"",description:e?.description||"",price:e?.price||0,image_url:e?.image_url||"",specs:e?.specs?JSON.stringify(e.specs,null,2):"{}"},validate:{name:e=>e?null:"Product name is required",price:e=>e<=0?"Price must be greater than 0":null,specs:e=>{try{return JSON.parse(e),null}catch{return"Invalid JSON format"}}}}),d=async r=>{n(!0);try{let s=null;if(r.specs.trim())try{s=JSON.parse(r.specs)}catch(e){i.setFieldError("specs","Invalid JSON format"),n(!1);return}let o={name:r.name,description:r.description||null,price:r.price,image_url:r.image_url||null,specs:s},a=e?`/api/products/${e.id}`:"/api/products",l=e?"PUT":"POST",c=await fetch(a,{method:l,headers:{"Content-Type":"application/json"},body:JSON.stringify(o)}),d=await c.json();c.ok?(S.notifications.show({title:"Success",message:`Product ${e?"updated":"created"} successfully`,color:"green"}),t()):S.notifications.show({title:"Error",message:d.error||`Failed to ${e?"update":"create"} product`,color:"red"})}catch(e){console.error("Error saving product:",e),S.notifications.show({title:"Error",message:"An unexpected error occurred",color:"red"})}finally{n(!1)}};return(0,s.jsx)("form",{onSubmit:i.onSubmit(d),children:(0,s.jsxs)(l.Stack,{children:[(0,s.jsx)(u.TextInput,{label:"Product Name",placeholder:"Enter product name",required:!0,...i.getInputProps("name")}),(0,s.jsx)(A.Textarea,{label:"Description",placeholder:"Enter product description",rows:3,...i.getInputProps("description")}),(0,s.jsx)(M.NumberInput,{label:"Price",placeholder:"0.00",min:0,step:.01,decimalScale:2,fixedDecimalScale:!0,required:!0,...i.getInputProps("price")}),(0,s.jsx)(u.TextInput,{label:"Image URL",placeholder:"https://example.com/image.jpg",...i.getInputProps("image_url")}),(0,s.jsx)(z.JsonInput,{label:"Specifications (JSON)",placeholder:'{"color": "blue", "size": "medium"}',validationError:"Invalid JSON",formatOnBlur:!0,autosize:!0,minRows:4,...i.getInputProps("specs")}),(0,s.jsxs)(c.Group,{justify:"flex-end",mt:"md",children:[(0,s.jsx)(p.Button,{variant:"subtle",onClick:r,children:"Cancel"}),(0,s.jsxs)(p.Button,{type:"submit",loading:a,children:[e?"Update":"Create"," Product"]})]})]})})}function I(){let[e,t]=(0,o.useState)([]),[r,f]=(0,o.useState)(!0),[A,M]=(0,o.useState)(""),[z,E]=(0,o.useState)(null),[I,{open:_,close:N}]=(0,P.useDisclosure)(!1),L=async e=>{try{f(!0);let r=e?`/api/products?search=${encodeURIComponent(e)}`:"/api/products",s=await fetch(r),o=await s.json();s.ok?t(o.products||[]):S.notifications.show({title:"Error",message:o.error||"Failed to load products",color:"red"})}catch(e){console.error("Error loading products:",e),S.notifications.show({title:"Error",message:"Failed to load products",color:"red"})}finally{f(!1)}},O=()=>{L(A)},q=e=>{E(e),_()},D=e=>{w.modals.openConfirmModal({title:"Delete Product",children:(0,s.jsxs)(a.Text,{size:"sm",children:['Are you sure you want to delete "',e.name,'"? This action cannot be undone.']}),labels:{confirm:"Delete",cancel:"Cancel"},confirmProps:{color:"red"},onConfirm:()=>G(e.id)})},G=async e=>{try{let t=await fetch(`/api/products/${e}`,{method:"DELETE"});if(t.ok)S.notifications.show({title:"Success",message:"Product deleted successfully",color:"green"}),L();else{let e=await t.json();S.notifications.show({title:"Error",message:e.error||"Failed to delete product",color:"red"})}}catch(e){console.error("Error deleting product:",e),S.notifications.show({title:"Error",message:"Failed to delete product",color:"red"})}};return r&&0===e.length?(0,s.jsx)(n.Center,{h:400,children:(0,s.jsx)(i.Loader,{size:"lg"})}):(0,s.jsxs)(l.Stack,{children:[(0,s.jsxs)(c.Group,{justify:"space-between",children:[(0,s.jsx)(d.Title,{order:1,children:"Products"}),(0,s.jsx)(p.Button,{leftSection:(0,s.jsx)(j,{size:"1rem"}),onClick:()=>{E(null),_()},children:"Add Product"})]}),(0,s.jsxs)(c.Group,{children:[(0,s.jsx)(u.TextInput,{placeholder:"Search products...",leftSection:(0,s.jsx)(y,{size:"1rem"}),value:A,onChange:e=>M(e.currentTarget.value),onKeyPress:e=>"Enter"===e.key&&O(),style:{flex:1}}),(0,s.jsx)(p.Button,{onClick:O,children:"Search"})]}),0===e.length?(0,s.jsx)(h.Alert,{icon:(0,s.jsx)(b.A,{size:"1rem"}),title:"No products found",color:"blue",children:A?"No products match your search criteria.":'No products have been added yet. Click "Add Product" to get started.'}):(0,s.jsxs)(m.Table,{striped:!0,highlightOnHover:!0,children:[(0,s.jsx)(m.Table.Thead,{children:(0,s.jsxs)(m.Table.Tr,{children:[(0,s.jsx)(m.Table.Th,{children:"Name"}),(0,s.jsx)(m.Table.Th,{children:"Description"}),(0,s.jsx)(m.Table.Th,{children:"Price"}),(0,s.jsx)(m.Table.Th,{children:"Created"}),(0,s.jsx)(m.Table.Th,{children:"Actions"})]})}),(0,s.jsx)(m.Table.Tbody,{children:e.map(e=>(0,s.jsxs)(m.Table.Tr,{children:[(0,s.jsx)(m.Table.Td,{children:(0,s.jsx)(a.Text,{fw:500,children:e.name})}),(0,s.jsx)(m.Table.Td,{children:(0,s.jsx)(a.Text,{size:"sm",c:"dimmed",truncate:!0,children:e.description||"No description"})}),(0,s.jsx)(m.Table.Td,{children:(0,s.jsxs)(x.Badge,{variant:"light",color:"green",children:["$",e.price.toFixed(2)]})}),(0,s.jsx)(m.Table.Td,{children:(0,s.jsx)(a.Text,{size:"sm",c:"dimmed",children:new Date(e.created_at).toLocaleDateString()})}),(0,s.jsx)(m.Table.Td,{children:(0,s.jsxs)(c.Group,{gap:"xs",children:[(0,s.jsx)(g.ActionIcon,{variant:"subtle",color:"blue",onClick:()=>q(e),children:(0,s.jsx)(k,{size:"1rem"})}),(0,s.jsx)(g.ActionIcon,{variant:"subtle",color:"red",onClick:()=>D(e),children:(0,s.jsx)(T,{size:"1rem"})})]})})]},e.id))})]}),(0,s.jsx)(v.Modal,{opened:I,onClose:N,title:z?"Edit Product":"Add Product",size:"lg",children:(0,s.jsx)(C,{product:z,onSave:()=>{N(),L()},onCancel:N})})]})}},40437:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(6445).A)("outline","messages","IconMessages",[["path",{d:"M21 14l-3 -3h-7a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1h9a1 1 0 0 1 1 1v10",key:"svg-0"}],["path",{d:"M14 15v2a1 1 0 0 1 -1 1h-7l-3 3v-10a1 1 0 0 1 1 -1h2",key:"svg-1"}]])},40644:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),o=r(48088),a=r(88170),n=r.n(a),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38571)),"E:\\cozy\\nextjs\\src\\app\\admin\\products\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"E:\\cozy\\nextjs\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\cozy\\nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["E:\\cozy\\nextjs\\src\\app\\admin\\products\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/products/page",pathname:"/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},44543:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(6445).A)("outline","package","IconPackage",[["path",{d:"M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5",key:"svg-0"}],["path",{d:"M12 12l8 -4.5",key:"svg-1"}],["path",{d:"M12 12l0 9",key:"svg-2"}],["path",{d:"M12 12l-8 -4.5",key:"svg-3"}],["path",{d:"M16 5.25l-8 4.5",key:"svg-4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74579:(e,t,r)=>{Promise.resolve().then(r.bind(r,39145))},76887:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var s=(0,r(6445).A)("outline","dashboard","IconDashboard",[["path",{d:"M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M13.45 11.55l2.05 -2.05",key:"svg-1"}],["path",{d:"M6.4 20a9 9 0 1 1 11.2 0z",key:"svg-2"}]])},79551:e=>{"use strict";e.exports=require("url")},86454:(e,t,r)=>{Promise.resolve().then(r.bind(r,99111))},98078:(e,t,r)=>{Promise.resolve().then(r.bind(r,12913))},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\cozy\\\\nextjs\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\cozy\\nextjs\\src\\app\\admin\\layout.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,405,762,465,995],()=>r(40644));module.exports=s})();