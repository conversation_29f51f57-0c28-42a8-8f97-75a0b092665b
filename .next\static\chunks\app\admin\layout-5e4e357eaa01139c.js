(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[581],{9532:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(95155),a=s(12115),l=s(35695),n=s(8141),i=s(83347),o=s(16691),c=s(70112),u=s(58887),h=s(26903),d=s(72775),p=s(86467),g=(0,p.A)("outline","logout","IconLogout",[["path",{d:"M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M9 12h12l-3 -3",key:"svg-1"}],["path",{d:"M18 15l3 -3",key:"svg-2"}]]),v=s(28031),m=s(15011),f=s(26743),k=(0,p.A)("outline","settings","IconSettings",[["path",{d:"M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]]),x=s(6942);function y(e){let{children:t}=e,[s,p]=(0,a.useState)(null),[y,j]=(0,a.useState)(!0),b=(0,l.useRouter)();(0,a.useEffect)(()=>{w()},[]);let w=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let t=await e.json();p(t.user)}else b.push("/login")}catch(e){console.error("Auth check error:",e),b.push("/login")}finally{j(!1)}},A=async()=>{try{await fetch("/api/auth/logout",{method:"POST"}),x.notifications.show({title:"Success",message:"Logged out successfully",color:"green"}),b.push("/login")}catch(e){console.error("Logout error:",e)}};return y?(0,r.jsx)(n.Center,{h:"100vh",children:(0,r.jsx)(i.Loader,{size:"lg"})}):s?(0,r.jsxs)(o.AppShell,{navbar:{width:250,breakpoint:"sm"},header:{height:60},padding:"md",children:[(0,r.jsx)(o.AppShell.Header,{children:(0,r.jsxs)(c.Group,{h:"100%",px:"md",justify:"space-between",children:[(0,r.jsx)(u.Text,{size:"lg",fw:600,children:"Admin Dashboard"}),(0,r.jsxs)(c.Group,{children:[(0,r.jsxs)(u.Text,{size:"sm",c:"dimmed",children:["Welcome, ",s.username]}),(0,r.jsx)(h.Button,{variant:"subtle",leftSection:(0,r.jsx)(g,{size:"1rem"}),onClick:A,children:"Logout"})]})]})}),(0,r.jsxs)(o.AppShell.Navbar,{p:"md",children:[(0,r.jsx)(d.NavLink,{href:"/admin",label:"Dashboard",leftSection:(0,r.jsx)(v.A,{size:"1rem"})}),(0,r.jsx)(d.NavLink,{href:"/admin/products",label:"Products",leftSection:(0,r.jsx)(m.A,{size:"1rem"})}),(0,r.jsx)(d.NavLink,{href:"/admin/messages",label:"Messages",leftSection:(0,r.jsx)(f.A,{size:"1rem"})}),(0,r.jsx)(d.NavLink,{href:"/admin/settings",label:"Settings",leftSection:(0,r.jsx)(k,{size:"1rem"})})]}),(0,r.jsx)(o.AppShell.Main,{children:t})]}):null}},11187:(e,t,s)=>{"use strict";function r(e){let t="string"==typeof e&&e.includes("var(--mantine-scale)")?e.match(/^calc\((.*?)\)$/)?.[1].split("*")[0].trim():e;return"number"==typeof t?t:"string"==typeof t?t.includes("calc")||t.includes("var")?t:t.includes("px")?Number(t.replace("px","")):t.includes("rem")?16*Number(t.replace("rem","")):t.includes("em")?16*Number(t.replace("em","")):Number(t):NaN}s.d(t,{px:()=>r})},15011:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","package","IconPackage",[["path",{d:"M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5",key:"svg-0"}],["path",{d:"M12 12l8 -4.5",key:"svg-1"}],["path",{d:"M12 12l0 9",key:"svg-2"}],["path",{d:"M12 12l-8 -4.5",key:"svg-3"}],["path",{d:"M16 5.25l-8 4.5",key:"svg-4"}]])},26743:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","messages","IconMessages",[["path",{d:"M21 14l-3 -3h-7a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1h9a1 1 0 0 1 1 1v10",key:"svg-0"}],["path",{d:"M14 15v2a1 1 0 0 1 -1 1h-7l-3 3v-10a1 1 0 0 1 1 -1h2",key:"svg-1"}]])},28031:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var r=(0,s(86467).A)("outline","dashboard","IconDashboard",[["path",{d:"M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M13.45 11.55l2.05 -2.05",key:"svg-1"}],["path",{d:"M6.4 20a9 9 0 1 1 11.2 0z",key:"svg-2"}]])},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},57613:(e,t,s)=>{"use strict";s.d(t,{useUncontrolled:()=>a});var r=s(12115);function a(e){let{value:t,defaultValue:s,finalValue:a,onChange:l=()=>{}}=e,[n,i]=(0,r.useState)(void 0!==s?s:a);return void 0!==t?[t,l,!0]:[n,function(e){for(var t=arguments.length,s=Array(t>1?t-1:0),r=1;r<t;r++)s[r-1]=arguments[r];i(e),null==l||l(e,...s)},!1]}},61758:(e,t,s)=>{"use strict";s.d(t,{getSortedBreakpoints:()=>a});var r=s(69448);function a(e,t){let s=e.map(e=>({value:e,px:(0,r.getBreakpointValue)(e,t)}));return s.sort((e,t)=>e.px-t.px),s}},69448:(e,t,s)=>{"use strict";s.d(t,{getBreakpointValue:()=>a});var r=s(11187);function a(e,t){return e in t?(0,r.px)(t[e]):(0,r.px)(e)}},81178:(e,t,s)=>{Promise.resolve().then(s.bind(s,9532))},86467:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(12115),a={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let l=(e,t,s,l)=>{let n=(0,r.forwardRef)((s,n)=>{let{color:i="currentColor",size:o=24,stroke:c=2,title:u,className:h,children:d,...p}=s;return(0,r.createElement)("svg",{ref:n,...a[e],width:o,height:o,className:["tabler-icon","tabler-icon-".concat(t),h].join(" "),..."filled"===e?{fill:i}:{strokeWidth:c,stroke:i},...p},[u&&(0,r.createElement)("title",{key:"svg-title"},u),...l.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(d)?d:[d]])});return n.displayName="".concat(s),n}}},e=>{var t=t=>e(e.s=t);e.O(0,[865,903,913,221,441,684,358],()=>t(81178)),_N_E=e.O()}]);