{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_93d25a7a._.js", "server/edge/chunks/[root-of-the-server]__d43b0dd5._.js", "server/edge/chunks/edge-wrapper_22cde404.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "E7/TIYtSAPpNfSYmOXpcK1MSOa94o1/8pve+m9GAbRE=", "__NEXT_PREVIEW_MODE_ID": "2b768abf358b6d625a93c5d779a118a2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "72f87b4878b3e0c182c3454554e1b1bdac41a42614ae9e6beb82c1b06283e482", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c3ef1b6ddd3627b7c495fc2194a482793531f158c04bcdda7d20a5c522a84613"}}}, "sortedMiddleware": ["/"], "functions": {}}